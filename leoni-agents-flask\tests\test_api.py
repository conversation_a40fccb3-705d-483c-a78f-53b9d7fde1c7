"""
Tests pour l'API Flask
"""

import pytest
import json
from app import app


@pytest.fixture
def client():
    """Client de test Flask"""
    app.config['TESTING'] = True
    with app.test_client() as client:
        yield client


def test_health_check(client):
    """Test du point de santé de l'API"""
    response = client.get('/')
    assert response.status_code == 200
    
    data = json.loads(response.data)
    assert data['success'] is True
    assert 'services' in data['data']


def test_error_analysis_info(client):
    """Test des informations de l'agent d'analyse d'erreurs"""
    response = client.get('/api/error-analysis')
    assert response.status_code == 200
    
    data = json.loads(response.data)
    assert data['success'] is True
    assert 'name' in data['data']


def test_sql_generation_info(client):
    """Test des informations de l'agent de génération SQL"""
    response = client.get('/api/sql-generation')
    assert response.status_code == 200
    
    data = json.loads(response.data)
    assert data['success'] is True
    assert 'name' in data['data']


def test_error_analysis_validation(client):
    """Test de validation pour l'analyse d'erreurs"""
    # Test sans données
    response = client.post('/api/error-analysis', json={})
    assert response.status_code == 400
    
    # Test avec données incomplètes
    response = client.post('/api/error-analysis', json={
        'programFile': {'name': 'test.c', 'content': 'int main() {}'}
    })
    assert response.status_code == 400


def test_sql_generation_validation(client):
    """Test de validation pour la génération SQL"""
    # Test sans données
    response = client.post('/api/sql-generation', json={})
    assert response.status_code == 400
    
    # Test avec spécification vide
    response = client.post('/api/sql-generation', json={
        'specification': ''
    })
    assert response.status_code == 400


def test_error_analysis_mock(client, monkeypatch):
    """Test d'analyse d'erreurs avec mock"""
    # Mock de la réponse OpenAI
    def mock_analyze_files(self, program_file, error_file):
        return {
            'summary': 'Test analysis',
            'errors': [],
            'recommendations': ['Test recommendation']
        }
    
    # Appliquer le mock
    from services.error_analysis_service import ErrorAnalysisService
    monkeypatch.setattr(ErrorAnalysisService, 'analyze_files', mock_analyze_files)
    
    # Test avec données valides
    response = client.post('/api/error-analysis', json={
        'programFile': {
            'name': 'test.c',
            'content': 'int main() { return 0; }'
        },
        'errorFile': {
            'name': 'error.log',
            'content': 'Error: syntax error at line 1'
        }
    })
    
    assert response.status_code == 200
    data = json.loads(response.data)
    assert data['success'] is True
    assert 'summary' in data['data']


def test_sql_generation_mock(client, monkeypatch):
    """Test de génération SQL avec mock"""
    # Mock de la réponse OpenAI
    def mock_generate_sql(self, request_data):
        return {
            'sql': 'CREATE TABLE test (id INT PRIMARY KEY);',
            'explanation': 'Test SQL generation',
            'tables': ['test'],
            'operations': ['CREATE']
        }
    
    # Appliquer le mock
    from services.sql_generation_service import SQLGenerationService
    monkeypatch.setattr(SQLGenerationService, 'generate_sql', mock_generate_sql)
    
    # Test avec données valides
    response = client.post('/api/sql-generation', json={
        'specification': 'Create a simple test table with an ID field',
        'database_type': 'mysql',
        'include_comments': True
    })
    
    assert response.status_code == 200
    data = json.loads(response.data)
    assert data['success'] is True
    assert 'sql' in data['data']
