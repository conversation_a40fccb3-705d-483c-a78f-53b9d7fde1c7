"""
Service d'analyse d'erreurs
Équivalent de errorAnalysisAgent.ts en Python
"""

import json
import openai
from typing import Dict, Any
from models.types import (
    Agent, ProgramFile, ErrorAnalysisResult, ErrorAnalysis, 
    CodeContext, dataclass_to_dict
)
from utils.error_parser import parse_error_file
from utils.text_utils import extract_line_from_program
from config.openai_config import get_openai_client


class ErrorAnalysisService:
    """Service pour l'analyse d'erreurs avec IA"""
    
    def __init__(self):
        self.agent = Agent(
            id='error-analysis-agent',
            name='Agent d\'Analyse d\'Erreurs',
            description='Analyse les fichiers de programme et d\'erreur pour détecter les erreurs, leurs emplacements et proposer des solutions',
            role='Expert en analyse d\'erreurs et débogage de programmes',
            goal='Identifier, localiser et proposer des solutions pour les erreurs dans les programmes',
            prompt="""Tu es un expert en analyse d'erreurs et débogage de programmes. 
            Ton rôle est d'analyser les fichiers de programme et les fichiers d'erreur pour :
            1. Détecter et identifier les erreurs
            2. Localiser précisément où elles se produisent
            3. Expliquer les causes possibles
            4. Proposer des solutions concrètes et détaillées
            
            Tu dois fournir une analyse structurée et professionnelle avec des recommandations pratiques.""",
            tools=['file-analysis', 'error-parsing', 'solution-generation'],
            utils=['parseErrorFile', 'formatDate']
        )
        self.openai_client = get_openai_client()
    
    def analyze_files(self, program_file: Dict[str, Any], error_file: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyse les fichiers programme et erreur
        
        Args:
            program_file: Dictionnaire avec name et content
            error_file: Dictionnaire avec name et content
            
        Returns:
            Dictionnaire avec les résultats de l'analyse
        """
        try:
            # Convertir en objets ProgramFile
            prog_file = ProgramFile(
                name=program_file['name'],
                content=program_file['content'],
                type='program'
            )
            err_file = ProgramFile(
                name=error_file['name'],
                content=error_file['content'],
                type='error'
            )
            
            # Parser le fichier d'erreur
            parsed_errors = parse_error_file(err_file.content)
            
            # Limiter le contenu du programme pour éviter la limite de tokens
            program_content = prog_file.content
            if len(program_content) > 6000:
                start = program_content[:5000]
                end = program_content[-1000:]
                program_content = start + '\n\n... [CONTENU TRONQUÉ] ...\n\n' + end
            
            # Construire le prompt
            prompt = f"""{self.agent.prompt}

FICHIER PROGRAMME:
Nom: {prog_file.name}
Contenu (extrait):
{program_content}

FICHIER D'ERREUR:
Nom: {err_file.name}
Contenu:
{err_file.content}

ERREURS PARSÉES:
{json.dumps([dataclass_to_dict(error) for error in parsed_errors], indent=2, ensure_ascii=False)}

Analyse ces fichiers et fournis une réponse JSON structurée avec:
{{
  "summary": "Résumé général de l'analyse",
  "errors": [
    {{
      "error_type": "Type d'erreur",
      "location": "Emplacement précis",
      "description": "Description détaillée",
      "possible_causes": ["cause1", "cause2"],
      "solutions": ["solution1", "solution2"],
      "severity": "LOW|MEDIUM|HIGH|CRITICAL",
      "line_number": 123
    }}
  ],
  "recommendations": ["recommandation1", "recommandation2"]
}}

IMPORTANT: Si tu détectes un numéro de ligne dans les erreurs, inclus-le dans le champ "line_number"."""
            
            # Appel à OpenAI
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {
                        "role": "system",
                        "content": self.agent.prompt
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_tokens=2000
            )
            
            content = response.choices[0].message.content
            if not content:
                raise Exception('Aucune réponse reçue de l\'API OpenAI')
            
            # Parser la réponse JSON
            try:
                # Nettoyer la réponse (enlever les ```json si présents)
                clean_content = content.strip()
                if clean_content.startswith('```json'):
                    clean_content = clean_content[7:]
                if clean_content.endswith('```'):
                    clean_content = clean_content[:-3]
                
                result_data = json.loads(clean_content.strip())
                
                # Enrichir avec le contexte de code si des numéros de ligne sont détectés
                if 'errors' in result_data:
                    for error in result_data['errors']:
                        if error.get('line_number'):
                            context = extract_line_from_program(
                                prog_file.content, 
                                error['line_number']
                            )
                            error['code_context'] = context
                
                return result_data
                
            except json.JSONDecodeError as e:
                # Si le parsing JSON échoue, créer une réponse de fallback
                return {
                    "summary": "Analyse effectuée mais format de réponse non standard",
                    "errors": [{
                        "error_type": "Erreur de parsing",
                        "location": "Réponse IA",
                        "description": f"Réponse brute: {content[:500]}...",
                        "possible_causes": ["Format de réponse non JSON"],
                        "solutions": ["Réessayer l'analyse"],
                        "severity": "MEDIUM"
                    }],
                    "recommendations": ["Vérifier le format des fichiers d'entrée"]
                }
                
        except Exception as e:
            raise Exception(f"Erreur lors de l'analyse: {str(e)}")
    
    def get_agent_info(self) -> Dict[str, Any]:
        """Retourne les informations sur l'agent"""
        return dataclass_to_dict(self.agent)
