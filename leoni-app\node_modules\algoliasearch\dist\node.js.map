{"version": 3, "sources": ["../builds/node.ts", "../builds/models.ts"], "sourcesContent": ["// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nimport type { ClientOptions, RequestOptions } from '@algolia/client-common';\n\nimport type { AbtestingClient } from '@algolia/client-abtesting';\nimport { abtestingClient } from '@algolia/client-abtesting';\nimport type { AnalyticsClient } from '@algolia/client-analytics';\nimport { analyticsClient } from '@algolia/client-analytics';\nimport type { InsightsClient } from '@algolia/client-insights';\nimport { insightsClient } from '@algolia/client-insights';\nimport type { PersonalizationClient } from '@algolia/client-personalization';\nimport { personalizationClient } from '@algolia/client-personalization';\nimport type { QuerySuggestionsClient } from '@algolia/client-query-suggestions';\nimport { querySuggestionsClient } from '@algolia/client-query-suggestions';\nimport type { SearchClient } from '@algolia/client-search';\nimport { searchClient } from '@algolia/client-search';\nimport type { IngestionClient } from '@algolia/ingestion';\nimport { ingestionClient } from '@algolia/ingestion';\nimport type { MonitoringClient } from '@algolia/monitoring';\nimport { monitoringClient } from '@algolia/monitoring';\nimport type { RecommendClient } from '@algolia/recommend';\nimport { recommendClient } from '@algolia/recommend';\n\nimport type {\n  PartialUpdateObjectsOptions,\n  ReplaceAllObjectsOptions,\n  ReplaceAllObjectsWithTransformationResponse,\n  SaveObjectsOptions,\n} from '@algolia/client-search';\nimport type { WatchResponse } from '@algolia/ingestion';\n\nimport type {\n  AbtestingRegionOptions,\n  AnalyticsRegionOptions,\n  IngestionRegion,\n  IngestionRegionOptions,\n  InitClientOptions,\n  InsightsRegionOptions,\n  PersonalizationRegionOptions,\n  QuerySuggestionsRegionOptions,\n} from './models';\n\nexport * from './models';\n\nexport type Algoliasearch = SearchClient & {\n  initAbtesting: (initOptions: InitClientOptions & AbtestingRegionOptions) => AbtestingClient;\n  initAnalytics: (initOptions: InitClientOptions & AnalyticsRegionOptions) => AnalyticsClient;\n  initIngestion: (initOptions: InitClientOptions & IngestionRegionOptions) => IngestionClient;\n  initInsights: (initOptions: InitClientOptions & InsightsRegionOptions) => InsightsClient;\n  initMonitoring: (initOptions?: InitClientOptions) => MonitoringClient;\n  initPersonalization: (initOptions: InitClientOptions & PersonalizationRegionOptions) => PersonalizationClient;\n  initQuerySuggestions: (initOptions: InitClientOptions & QuerySuggestionsRegionOptions) => QuerySuggestionsClient;\n  initRecommend: (initOptions?: InitClientOptions) => RecommendClient;\n\n  // Bridge helpers to expose along with the search endpoints at the root of the API client\n\n  /**\n   * Helper: Similar to the `saveObjects` method but requires a Push connector (https://www.algolia.com/doc/guides/sending-and-managing-data/send-and-update-your-data/connectors/push/) to be created first, in order to transform records before indexing them to Algolia. The `region` must have been passed to the client instantiation method.\n   *\n   * @summary Save objects to an Algolia index by leveraging the Transformation pipeline setup using the Push connector (https://www.algolia.com/doc/guides/sending-and-managing-data/send-and-update-your-data/connectors/push/).\n   * @param saveObjects - The `saveObjects` object.\n   * @param saveObjects.indexName - The `indexName` to save `objects` in.\n   * @param saveObjects.objects - The array of `objects` to store in the given Algolia `indexName`.\n   * @param saveObjects.batchSize - The size of the chunk of `objects`. The number of `batch` calls will be equal to `length(objects) / batchSize`. Defaults to 1000.\n   * @param saveObjects.waitForTasks - Whether or not we should wait until every `batch` tasks has been processed, this operation may slow the total execution time of this method but is more reliable.\n   * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `push` method and merged with the transporter requestOptions.\n   */\n  saveObjectsWithTransformation: (\n    options: SaveObjectsOptions,\n    requestOptions?: RequestOptions | undefined,\n  ) => Promise<Array<WatchResponse>>;\n\n  /**\n   * Helper: Similar to the `partialUpdateObjects` method but requires a Push connector (https://www.algolia.com/doc/guides/sending-and-managing-data/send-and-update-your-data/connectors/push/) to be created first, in order to transform records before indexing them to Algolia. The `region` must have been passed to the client instantiation method.\n   *\n   * @summary Save objects to an Algolia index by leveraging the Transformation pipeline setup in the Push connector (https://www.algolia.com/doc/guides/sending-and-managing-data/send-and-update-your-data/connectors/push/).\n   * @param partialUpdateObjects - The `partialUpdateObjects` object.\n   * @param partialUpdateObjects.indexName - The `indexName` to update `objects` in.\n   * @param partialUpdateObjects.objects - The array of `objects` to update in the given Algolia `indexName`.\n   * @param partialUpdateObjects.createIfNotExists - To be provided if non-existing objects are passed, otherwise, the call will fail..\n   * @param partialUpdateObjects.batchSize - The size of the chunk of `objects`. The number of `batch` calls will be equal to `length(objects) / batchSize`. Defaults to 1000.\n   * @param partialUpdateObjects.waitForTasks - Whether or not we should wait until every `batch` tasks has been processed, this operation may slow the total execution time of this method but is more reliable.\n   * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `push` method and merged with the transporter requestOptions.\n   */\n  partialUpdateObjectsWithTransformation: (\n    options: PartialUpdateObjectsOptions,\n    requestOptions?: RequestOptions | undefined,\n  ) => Promise<Array<WatchResponse>>;\n\n  /**\n   * Helper: Similar to the `replaceAllObjects` method but requires a Push connector (https://www.algolia.com/doc/guides/sending-and-managing-data/send-and-update-your-data/connectors/push/) to be created first, in order to transform records before indexing them to Algolia. The `region` must have been passed to the client instantiation method.\n   *\n   * @summary Helper: Replaces all objects (records) in the given `index_name` by leveraging the Transformation pipeline setup in the Push connector (https://www.algolia.com/doc/guides/sending-and-managing-data/send-and-update-your-data/connectors/push/) with the given `objects`. A temporary index is created during this process in order to backup your data.\n   * @param replaceAllObjects - The `replaceAllObjects` object.\n   * @param replaceAllObjects.indexName - The `indexName` to replace `objects` in.\n   * @param replaceAllObjects.objects - The array of `objects` to store in the given Algolia `indexName`.\n   * @param replaceAllObjects.batchSize - The size of the chunk of `objects`. The number of `batch` calls will be equal to `objects.length / batchSize`. Defaults to 1000.\n   * @param replaceAllObjects.scopes - The `scopes` to keep from the index. Defaults to ['settings', 'rules', 'synonyms'].\n   * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `push`, `operationIndex` and `getEvent` method and merged with the transporter requestOptions.\n   */\n  replaceAllObjectsWithTransformation: (\n    options: ReplaceAllObjectsOptions,\n    requestOptions?: RequestOptions | undefined,\n  ) => Promise<ReplaceAllObjectsWithTransformationResponse>;\n};\n\nexport type TransformationOptions = {\n  // When provided, a second transporter will be created in order to leverage the `*WithTransformation` methods exposed by the Push connector (https://www.algolia.com/doc/guides/sending-and-managing-data/send-and-update-your-data/connectors/push/).\n  transformation?:\n    | {\n        // The region of your Algolia application ID, used to target the correct hosts of the transformation service.\n        region: IngestionRegion;\n      }\n    | undefined;\n};\n\nexport function algoliasearch(\n  appId: string,\n  apiKey: string,\n  options?: (ClientOptions & TransformationOptions) | undefined,\n): Algoliasearch {\n  if (!appId || typeof appId !== 'string') {\n    throw new Error('`appId` is missing.');\n  }\n\n  if (!apiKey || typeof apiKey !== 'string') {\n    throw new Error('`apiKey` is missing.');\n  }\n\n  const client = searchClient(appId, apiKey, options);\n\n  let ingestionTransporter: IngestionClient | undefined;\n\n  if (options?.transformation) {\n    if (!options.transformation.region) {\n      throw new Error('`region` must be provided when leveraging the transformation pipeline');\n    }\n\n    ingestionTransporter = ingestionClient(appId, apiKey, options.transformation.region, options);\n  }\n\n  return {\n    ...client,\n\n    async saveObjectsWithTransformation(\n      { indexName, objects, waitForTasks },\n      requestOptions,\n    ): Promise<Array<WatchResponse>> {\n      if (!ingestionTransporter) {\n        throw new Error('`transformation.region` must be provided at client instantiation before calling this method.');\n      }\n\n      if (!options?.transformation?.region) {\n        throw new Error('`region` must be provided when leveraging the transformation pipeline');\n      }\n\n      return ingestionTransporter.chunkedPush(\n        { indexName, objects, action: 'addObject', waitForTasks },\n        requestOptions,\n      );\n    },\n\n    async partialUpdateObjectsWithTransformation(\n      { indexName, objects, createIfNotExists, waitForTasks },\n      requestOptions,\n    ): Promise<Array<WatchResponse>> {\n      if (!ingestionTransporter) {\n        throw new Error('`transformation.region` must be provided at client instantiation before calling this method.');\n      }\n\n      if (!options?.transformation?.region) {\n        throw new Error('`region` must be provided when leveraging the transformation pipeline');\n      }\n\n      return ingestionTransporter.chunkedPush(\n        {\n          indexName,\n          objects,\n          action: createIfNotExists ? 'partialUpdateObject' : 'partialUpdateObjectNoCreate',\n          waitForTasks,\n        },\n        requestOptions,\n      );\n    },\n\n    async replaceAllObjectsWithTransformation(\n      { indexName, objects, batchSize, scopes }: ReplaceAllObjectsOptions,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<ReplaceAllObjectsWithTransformationResponse> {\n      if (!ingestionTransporter) {\n        throw new Error('`transformation.region` must be provided at client instantiation before calling this method.');\n      }\n\n      if (!options?.transformation?.region) {\n        throw new Error('`region` must be provided when leveraging the transformation pipeline');\n      }\n\n      const randomSuffix = Math.floor(Math.random() * 1000000) + 100000;\n      const tmpIndexName = `${indexName}_tmp_${randomSuffix}`;\n\n      if (scopes === undefined) {\n        scopes = ['settings', 'rules', 'synonyms'];\n      }\n\n      try {\n        let copyOperationResponse = await this.operationIndex(\n          {\n            indexName,\n            operationIndexParams: {\n              operation: 'copy',\n              destination: tmpIndexName,\n              scope: scopes,\n            },\n          },\n          requestOptions,\n        );\n\n        const watchResponses = await ingestionTransporter.chunkedPush(\n          { indexName: tmpIndexName, objects, waitForTasks: true, batchSize, referenceIndexName: indexName },\n          requestOptions,\n        );\n\n        await this.waitForTask({\n          indexName: tmpIndexName,\n          taskID: copyOperationResponse.taskID,\n        });\n\n        copyOperationResponse = await this.operationIndex(\n          {\n            indexName,\n            operationIndexParams: {\n              operation: 'copy',\n              destination: tmpIndexName,\n              scope: scopes,\n            },\n          },\n          requestOptions,\n        );\n        await this.waitForTask({\n          indexName: tmpIndexName,\n          taskID: copyOperationResponse.taskID,\n        });\n\n        const moveOperationResponse = await this.operationIndex(\n          {\n            indexName: tmpIndexName,\n            operationIndexParams: { operation: 'move', destination: indexName },\n          },\n          requestOptions,\n        );\n        await this.waitForTask({\n          indexName: tmpIndexName,\n          taskID: moveOperationResponse.taskID,\n        });\n\n        return { copyOperationResponse, watchResponses, moveOperationResponse };\n      } catch (error) {\n        await this.deleteIndex({ indexName: tmpIndexName });\n\n        throw error;\n      }\n    },\n\n    /**\n     * Get the value of the `algoliaAgent`, used by our libraries internally and telemetry system.\n     */\n    get _ua(): string {\n      return client.transporter.algoliaAgent.value;\n    },\n\n    initAbtesting: (initOptions: InitClientOptions & AbtestingRegionOptions): AbtestingClient => {\n      return abtestingClient(\n        initOptions.appId || appId,\n        initOptions.apiKey || apiKey,\n        initOptions.region,\n        initOptions.options,\n      );\n    },\n\n    initAnalytics: (initOptions: InitClientOptions & AnalyticsRegionOptions): AnalyticsClient => {\n      return analyticsClient(\n        initOptions.appId || appId,\n        initOptions.apiKey || apiKey,\n        initOptions.region,\n        initOptions.options,\n      );\n    },\n\n    initIngestion: (initOptions: InitClientOptions & IngestionRegionOptions): IngestionClient => {\n      return ingestionClient(\n        initOptions.appId || appId,\n        initOptions.apiKey || apiKey,\n        initOptions.region,\n        initOptions.options,\n      );\n    },\n\n    initInsights: (initOptions: InitClientOptions & InsightsRegionOptions): InsightsClient => {\n      return insightsClient(\n        initOptions.appId || appId,\n        initOptions.apiKey || apiKey,\n        initOptions.region,\n        initOptions.options,\n      );\n    },\n\n    initMonitoring: (initOptions: InitClientOptions = {}): MonitoringClient => {\n      return monitoringClient(initOptions.appId || appId, initOptions.apiKey || apiKey, initOptions.options);\n    },\n\n    initPersonalization: (initOptions: InitClientOptions & PersonalizationRegionOptions): PersonalizationClient => {\n      return personalizationClient(\n        initOptions.appId || appId,\n        initOptions.apiKey || apiKey,\n        initOptions.region,\n        initOptions.options,\n      );\n    },\n\n    initQuerySuggestions: (initOptions: InitClientOptions & QuerySuggestionsRegionOptions): QuerySuggestionsClient => {\n      return querySuggestionsClient(\n        initOptions.appId || appId,\n        initOptions.apiKey || apiKey,\n        initOptions.region,\n        initOptions.options,\n      );\n    },\n\n    initRecommend: (initOptions: InitClientOptions = {}): RecommendClient => {\n      return recommendClient(initOptions.appId || appId, initOptions.apiKey || apiKey, initOptions.options);\n    },\n  };\n}\n", "// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nimport type { Region as AbtestingRegion, RegionOptions as AbtestingRegionOptions } from '@algolia/client-abtesting';\nimport type { Region as AnalyticsRegion, RegionOptions as AnalyticsRegionOptions } from '@algolia/client-analytics';\nimport type { Region as InsightsRegion, RegionOptions as InsightsRegionOptions } from '@algolia/client-insights';\nimport type { Region as IngestionRegion, RegionOptions as IngestionRegionOptions } from '@algolia/ingestion';\n\nimport type {\n  Region as PersonalizationRegion,\n  RegionOptions as PersonalizationRegionOptions,\n} from '@algolia/client-personalization';\nimport type {\n  Region as QuerySuggestionsRegion,\n  RegionOptions as QuerySuggestionsRegionOptions,\n} from '@algolia/client-query-suggestions';\n\nimport type { Status } from '@algolia/client-abtesting';\nimport type { ClientOptions } from '@algolia/client-common';\nimport type { EventType } from '@algolia/client-personalization';\nimport type {\n  Action,\n  AdvancedSyntaxFeatures,\n  AlternativesAsExact,\n  Anchoring,\n  AroundPrecision,\n  AroundRadius,\n  AroundRadiusAll,\n  AutomaticFacetFilter,\n  AutomaticFacetFilters,\n  Banner,\n  BannerImage,\n  BannerImageUrl,\n  BannerLink,\n  BaseIndexSettings,\n  BaseSearchParams,\n  BaseSearchParamsWithoutQuery,\n  BaseSearchResponse,\n  BooleanString,\n  Condition,\n  Consequence,\n  ConsequenceHide,\n  ConsequenceParams,\n  ConsequenceQuery,\n  ConsequenceQueryObject,\n  CustomDeleteProps,\n  CustomGetProps,\n  CustomPostProps,\n  CustomPutProps,\n  DeleteSourceProps,\n  DeletedAtResponse,\n  Distinct,\n  Edit,\n  EditType,\n  ErrorBase,\n  ExactOnSingleWordQuery,\n  Exhaustive,\n  FacetFilters,\n  FacetHits,\n  FacetOrdering,\n  FacetStats,\n  Facets,\n  GetTaskProps,\n  HighlightResult,\n  HighlightResultOption,\n  Hit,\n  IgnorePlurals,\n  IndexSettingsAsSearchParams,\n  InsideBoundingBox,\n  Languages,\n  MatchLevel,\n  MatchedGeoLocation,\n  Mode,\n  NumericFilters,\n  OptionalFilters,\n  OptionalWords,\n  Params,\n  Personalization,\n  Promote,\n  PromoteObjectID,\n  PromoteObjectIDs,\n  QueryType,\n  Range,\n  RankingInfo,\n  ReRankingApplyFilter,\n  Redirect,\n  RedirectRuleIndexData,\n  RedirectRuleIndexMetadata,\n  RedirectURL,\n  RemoveStopWords,\n  RemoveWordsIfNoResults,\n  RenderingContent,\n  SearchForFacetValuesProps,\n  SearchForFacetValuesRequest,\n  SearchForFacetValuesResponse,\n  SearchHits,\n  SearchPagination,\n  SearchParams,\n  SearchParamsObject,\n  SearchParamsQuery,\n  SearchResponse,\n  SemanticSearch,\n  SnippetResult,\n  SnippetResultOption,\n  SortRemainingBy,\n  Source,\n  SupportedLanguage,\n  TagFilters,\n  TaskStatus,\n  TimeRange,\n  TypoTolerance,\n  TypoToleranceEnum,\n  Value,\n  Widgets,\n} from '@algolia/client-search';\nimport { apiClientVersion } from '@algolia/client-search';\nimport type { Event, EventStatus, WatchResponse } from '@algolia/ingestion';\n\nexport * from '@algolia/client-abtesting';\nexport * from '@algolia/client-analytics';\nexport * from '@algolia/client-insights';\nexport * from '@algolia/client-personalization';\nexport * from '@algolia/client-query-suggestions';\nexport * from '@algolia/client-search';\nexport * from '@algolia/ingestion';\nexport * from '@algolia/monitoring';\nexport * from '@algolia/recommend';\n\nexport type Region =\n  | AbtestingRegion\n  | AnalyticsRegion\n  | IngestionRegion\n  | InsightsRegion\n  | PersonalizationRegion\n  | QuerySuggestionsRegion;\nexport type RegionOptions =\n  | AbtestingRegionOptions\n  | AnalyticsRegionOptions\n  | IngestionRegionOptions\n  | InsightsRegionOptions\n  | PersonalizationRegionOptions\n  | QuerySuggestionsRegionOptions;\n\nexport type {\n  AbtestingRegion,\n  AbtestingRegionOptions,\n  AnalyticsRegion,\n  AnalyticsRegionOptions,\n  EventType,\n  IngestionRegion,\n  IngestionRegionOptions,\n  InsightsRegion,\n  InsightsRegionOptions,\n  PersonalizationRegion,\n  PersonalizationRegionOptions,\n  QuerySuggestionsRegion,\n  QuerySuggestionsRegionOptions,\n  Status,\n};\n\nexport type {\n  Action,\n  AdvancedSyntaxFeatures,\n  AlternativesAsExact,\n  Anchoring,\n  AroundPrecision,\n  AroundRadius,\n  AroundRadiusAll,\n  AutomaticFacetFilter,\n  AutomaticFacetFilters,\n  Banner,\n  BannerImage,\n  BannerImageUrl,\n  BannerLink,\n  BaseIndexSettings,\n  BaseSearchParams,\n  BaseSearchParamsWithoutQuery,\n  BaseSearchResponse,\n  BooleanString,\n  Condition,\n  Consequence,\n  ConsequenceHide,\n  ConsequenceParams,\n  ConsequenceQuery,\n  ConsequenceQueryObject,\n  CustomDeleteProps,\n  CustomGetProps,\n  CustomPostProps,\n  CustomPutProps,\n  DeleteSourceProps,\n  DeletedAtResponse,\n  Distinct,\n  Edit,\n  EditType,\n  ErrorBase,\n  Event,\n  EventStatus,\n  ExactOnSingleWordQuery,\n  Exhaustive,\n  FacetFilters,\n  FacetHits,\n  FacetOrdering,\n  FacetStats,\n  Facets,\n  GetTaskProps,\n  HighlightResult,\n  HighlightResultOption,\n  Hit,\n  IgnorePlurals,\n  IndexSettingsAsSearchParams,\n  InsideBoundingBox,\n  Languages,\n  MatchLevel,\n  MatchedGeoLocation,\n  Mode,\n  NumericFilters,\n  OptionalFilters,\n  OptionalWords,\n  Params,\n  Personalization,\n  Promote,\n  PromoteObjectID,\n  PromoteObjectIDs,\n  QueryType,\n  Range,\n  RankingInfo,\n  ReRankingApplyFilter,\n  Redirect,\n  RedirectRuleIndexData,\n  RedirectRuleIndexMetadata,\n  RedirectURL,\n  RemoveStopWords,\n  RemoveWordsIfNoResults,\n  RenderingContent,\n  SearchForFacetValuesProps,\n  SearchForFacetValuesRequest,\n  SearchForFacetValuesResponse,\n  SearchHits,\n  SearchPagination,\n  SearchParams,\n  SearchParamsObject,\n  SearchParamsQuery,\n  SearchResponse,\n  SemanticSearch,\n  SnippetResult,\n  SnippetResultOption,\n  SortRemainingBy,\n  Source,\n  SupportedLanguage,\n  TagFilters,\n  TaskStatus,\n  TimeRange,\n  TypoTolerance,\n  TypoToleranceEnum,\n  Value,\n  WatchResponse,\n  Widgets,\n};\n\nexport { apiClientVersion };\n\n/**\n * Options forwarded to the client initialized via the `init` method.\n */\nexport type InitClientOptions = Partial<{\n  /**\n   * App to target with the initialized client, defaults to the `algoliasearch` appId.\n   */\n  appId: string;\n  /**\n   * API key of the targeted app ID, defaults to the `algoliasearch` apiKey.\n   */\n  apiKey: string;\n  options: ClientOptions;\n}>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,SAAS,uBAAuB;AAEhC,SAAS,uBAAuB;AAEhC,SAAS,sBAAsB;AAE/B,SAAS,6BAA6B;AAEtC,SAAS,8BAA8B;AAEvC,SAAS,oBAAoB;AAE7B,SAAS,uBAAuB;AAEhC,SAAS,wBAAwB;AAEjC,SAAS,uBAAuB;;;ACrBhC;AAAA;AAAA;AAAA;AAqHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,SAAS,wBAAwB;AAGjC,uCAAc;AACd,uCAAc;AACd,sCAAc;AACd,6CAAc;AACd,+CAAc;AACd,oCAAc;AACd,gCAAc;AACd,iCAAc;AACd,gCAAc;;;ADnFd,yBAAc;AA0EP,SAAS,cACd,OACA,QACA,SACe;AACf,MAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACvC,UAAM,IAAI,MAAM,qBAAqB;AAAA,EACvC;AAEA,MAAI,CAAC,UAAU,OAAO,WAAW,UAAU;AACzC,UAAM,IAAI,MAAM,sBAAsB;AAAA,EACxC;AAEA,QAAM,SAAS,aAAa,OAAO,QAAQ,OAAO;AAElD,MAAI;AAEJ,MAAI,mCAAS,gBAAgB;AAC3B,QAAI,CAAC,QAAQ,eAAe,QAAQ;AAClC,YAAM,IAAI,MAAM,uEAAuE;AAAA,IACzF;AAEA,2BAAuB,gBAAgB,OAAO,QAAQ,QAAQ,eAAe,QAAQ,OAAO;AAAA,EAC9F;AAEA,SAAO;AAAA,IACL,GAAG;AAAA,IAEH,MAAM,8BACJ,EAAE,WAAW,SAAS,aAAa,GACnC,gBAC+B;AAnJrC;AAoJM,UAAI,CAAC,sBAAsB;AACzB,cAAM,IAAI,MAAM,8FAA8F;AAAA,MAChH;AAEA,UAAI,GAAC,wCAAS,mBAAT,mBAAyB,SAAQ;AACpC,cAAM,IAAI,MAAM,uEAAuE;AAAA,MACzF;AAEA,aAAO,qBAAqB;AAAA,QAC1B,EAAE,WAAW,SAAS,QAAQ,aAAa,aAAa;AAAA,QACxD;AAAA,MACF;AAAA,IACF;AAAA,IAEA,MAAM,uCACJ,EAAE,WAAW,SAAS,mBAAmB,aAAa,GACtD,gBAC+B;AArKrC;AAsKM,UAAI,CAAC,sBAAsB;AACzB,cAAM,IAAI,MAAM,8FAA8F;AAAA,MAChH;AAEA,UAAI,GAAC,wCAAS,mBAAT,mBAAyB,SAAQ;AACpC,cAAM,IAAI,MAAM,uEAAuE;AAAA,MACzF;AAEA,aAAO,qBAAqB;AAAA,QAC1B;AAAA,UACE;AAAA,UACA;AAAA,UACA,QAAQ,oBAAoB,wBAAwB;AAAA,UACpD;AAAA,QACF;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IAEA,MAAM,oCACJ,EAAE,WAAW,SAAS,WAAW,OAAO,GACxC,gBACsD;AA5L5D;AA6LM,UAAI,CAAC,sBAAsB;AACzB,cAAM,IAAI,MAAM,8FAA8F;AAAA,MAChH;AAEA,UAAI,GAAC,wCAAS,mBAAT,mBAAyB,SAAQ;AACpC,cAAM,IAAI,MAAM,uEAAuE;AAAA,MACzF;AAEA,YAAM,eAAe,KAAK,MAAM,KAAK,OAAO,IAAI,GAAO,IAAI;AAC3D,YAAM,eAAe,GAAG,SAAS,QAAQ,YAAY;AAErD,UAAI,WAAW,QAAW;AACxB,iBAAS,CAAC,YAAY,SAAS,UAAU;AAAA,MAC3C;AAEA,UAAI;AACF,YAAI,wBAAwB,MAAM,KAAK;AAAA,UACrC;AAAA,YACE;AAAA,YACA,sBAAsB;AAAA,cACpB,WAAW;AAAA,cACX,aAAa;AAAA,cACb,OAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA;AAAA,QACF;AAEA,cAAM,iBAAiB,MAAM,qBAAqB;AAAA,UAChD,EAAE,WAAW,cAAc,SAAS,cAAc,MAAM,WAAW,oBAAoB,UAAU;AAAA,UACjG;AAAA,QACF;AAEA,cAAM,KAAK,YAAY;AAAA,UACrB,WAAW;AAAA,UACX,QAAQ,sBAAsB;AAAA,QAChC,CAAC;AAED,gCAAwB,MAAM,KAAK;AAAA,UACjC;AAAA,YACE;AAAA,YACA,sBAAsB;AAAA,cACpB,WAAW;AAAA,cACX,aAAa;AAAA,cACb,OAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA;AAAA,QACF;AACA,cAAM,KAAK,YAAY;AAAA,UACrB,WAAW;AAAA,UACX,QAAQ,sBAAsB;AAAA,QAChC,CAAC;AAED,cAAM,wBAAwB,MAAM,KAAK;AAAA,UACvC;AAAA,YACE,WAAW;AAAA,YACX,sBAAsB,EAAE,WAAW,QAAQ,aAAa,UAAU;AAAA,UACpE;AAAA,UACA;AAAA,QACF;AACA,cAAM,KAAK,YAAY;AAAA,UACrB,WAAW;AAAA,UACX,QAAQ,sBAAsB;AAAA,QAChC,CAAC;AAED,eAAO,EAAE,uBAAuB,gBAAgB,sBAAsB;AAAA,MACxE,SAAS,OAAO;AACd,cAAM,KAAK,YAAY,EAAE,WAAW,aAAa,CAAC;AAElD,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,IAAI,MAAc;AAChB,aAAO,OAAO,YAAY,aAAa;AAAA,IACzC;AAAA,IAEA,eAAe,CAAC,gBAA6E;AAC3F,aAAO;AAAA,QACL,YAAY,SAAS;AAAA,QACrB,YAAY,UAAU;AAAA,QACtB,YAAY;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IAEA,eAAe,CAAC,gBAA6E;AAC3F,aAAO;AAAA,QACL,YAAY,SAAS;AAAA,QACrB,YAAY,UAAU;AAAA,QACtB,YAAY;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IAEA,eAAe,CAAC,gBAA6E;AAC3F,aAAO;AAAA,QACL,YAAY,SAAS;AAAA,QACrB,YAAY,UAAU;AAAA,QACtB,YAAY;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IAEA,cAAc,CAAC,gBAA2E;AACxF,aAAO;AAAA,QACL,YAAY,SAAS;AAAA,QACrB,YAAY,UAAU;AAAA,QACtB,YAAY;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IAEA,gBAAgB,CAAC,cAAiC,CAAC,MAAwB;AACzE,aAAO,iBAAiB,YAAY,SAAS,OAAO,YAAY,UAAU,QAAQ,YAAY,OAAO;AAAA,IACvG;AAAA,IAEA,qBAAqB,CAAC,gBAAyF;AAC7G,aAAO;AAAA,QACL,YAAY,SAAS;AAAA,QACrB,YAAY,UAAU;AAAA,QACtB,YAAY;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IAEA,sBAAsB,CAAC,gBAA2F;AAChH,aAAO;AAAA,QACL,YAAY,SAAS;AAAA,QACrB,YAAY,UAAU;AAAA,QACtB,YAAY;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IAEA,eAAe,CAAC,cAAiC,CAAC,MAAuB;AACvE,aAAO,gBAAgB,YAAY,SAAS,OAAO,YAAY,UAAU,QAAQ,YAAY,OAAO;AAAA,IACtG;AAAA,EACF;AACF;", "names": []}