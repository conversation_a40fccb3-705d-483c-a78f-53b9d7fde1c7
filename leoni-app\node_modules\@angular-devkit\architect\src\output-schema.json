{"$schema": "http://json-schema.org/draft-07/schema", "$id": "BuilderOutputSchema", "title": "Output schema for builders.", "type": "object", "properties": {"success": {"type": "boolean"}, "error": {"type": "string"}, "target": {"type": "object", "properties": {"project": {"type": "string"}, "target": {"type": "string"}, "configuration": {"type": "string"}}}, "info": {"type": "object"}}, "additionalProperties": true, "required": ["success"]}