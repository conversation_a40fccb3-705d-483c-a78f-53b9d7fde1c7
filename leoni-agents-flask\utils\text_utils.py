"""
Utilitaires pour le traitement de texte
Équivalent de textUtils.ts en Python
"""

import re
from typing import Dict, List, Any


def estimate_tokens(text: str) -> int:
    """
    Estime le nombre de tokens dans un texte
    Approximation: 1 token ≈ 4 caractères pour l'anglais/français
    
    Args:
        text: Texte à analyser
        
    Returns:
        Nombre estimé de tokens
    """
    return len(text) // 4


def exceeds_token_limit(text: str, max_tokens: int = 3000) -> bool:
    """
    Vérifie si un texte dépasse la limite de tokens
    
    Args:
        text: Texte à vérifier
        max_tokens: Limite maximale de tokens
        
    Returns:
        True si la limite est dépassée
    """
    return estimate_tokens(text) > max_tokens


def summarize_specification(specification: str, max_length: int = 8000) -> str:
    """
    Résume une spécification trop longue en gardant les parties importantes
    
    Args:
        specification: Spécification originale
        max_length: Longueur maximale du résumé
        
    Returns:
        Spécification résumée
    """
    if len(specification) <= max_length:
        return specification
    
    # Diviser en sections
    sections = specification.split('\n\n')
    
    # Garder le début et la fin
    start_length = max_length // 2
    end_length = max_length // 4
    
    start_text = specification[:start_length]
    end_text = specification[-end_length:]
    
    # Ajouter un marqueur de troncature
    summary = f"{start_text}\n\n... [SPÉCIFICATION TRONQUÉE - {len(specification)} caractères originaux] ...\n\n{end_text}"
    
    return summary


def prepare_specification_for_api(specification: str) -> Dict[str, Any]:
    """
    Prépare une spécification pour l'API en gérant la longueur
    
    Args:
        specification: Spécification originale
        
    Returns:
        Dictionnaire avec les informations de traitement
    """
    original_length = len(specification)
    original_tokens = estimate_tokens(specification)
    
    processed_text = specification
    was_truncated = False
    
    if exceeds_token_limit(specification):
        processed_text = summarize_specification(specification)
        was_truncated = True
    
    return {
        'processed_text': processed_text,
        'was_truncated': was_truncated,
        'original_length': original_length,
        'processed_length': len(processed_text),
        'estimated_tokens': estimate_tokens(processed_text)
    }


def extract_line_from_program(program_content: str, line_number: int, context: int = 2) -> Dict[str, Any]:
    """
    Extrait une ligne spécifique d'un programme avec son contexte
    
    Args:
        program_content: Contenu du programme
        line_number: Numéro de ligne à extraire (1-indexé)
        context: Nombre de lignes de contexte avant/après
        
    Returns:
        Dictionnaire avec la ligne cible et le contexte
    """
    lines = program_content.split('\n')
    
    # Vérifier que le numéro de ligne est valide
    if line_number < 1 or line_number > len(lines):
        return {
            'target_line': '',
            'context_lines': []
        }
    
    target_line = lines[line_number - 1] if line_number <= len(lines) else ''
    
    # Calculer les limites du contexte
    start_line = max(0, line_number - context - 1)
    end_line = min(len(lines), line_number + context)
    
    context_lines = []
    for i in range(start_line, end_line):
        context_lines.append({
            'number': i + 1,
            'content': lines[i] if i < len(lines) else '',
            'is_target': i == line_number - 1
        })
    
    return {
        'target_line': target_line,
        'context_lines': context_lines
    }


def clean_text(text: str) -> str:
    """
    Nettoie un texte en supprimant les caractères indésirables
    
    Args:
        text: Texte à nettoyer
        
    Returns:
        Texte nettoyé
    """
    # Supprimer les caractères de contrôle sauf les retours à la ligne
    cleaned = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
    
    # Normaliser les espaces
    cleaned = re.sub(r'[ \t]+', ' ', cleaned)
    
    # Normaliser les retours à la ligne
    cleaned = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned)
    
    return cleaned.strip()


def extract_keywords(text: str) -> List[str]:
    """
    Extrait les mots-clés d'un texte
    
    Args:
        text: Texte à analyser
        
    Returns:
        Liste des mots-clés
    """
    # Mots-clés techniques courants
    technical_keywords = [
        'CREATE', 'TABLE', 'INSERT', 'SELECT', 'UPDATE', 'DELETE',
        'INDEX', 'PRIMARY KEY', 'FOREIGN KEY', 'VARCHAR', 'INT',
        'error', 'exception', 'warning', 'debug', 'info',
        'function', 'procedure', 'trigger', 'view'
    ]
    
    # Extraire les mots du texte
    words = re.findall(r'\b[A-Za-z_][A-Za-z0-9_]*\b', text.upper())
    
    # Filtrer les mots-clés techniques
    keywords = [word for word in words if word in [kw.upper() for kw in technical_keywords]]
    
    # Supprimer les doublons et retourner
    return list(set(keywords))


def format_sql(sql: str) -> str:
    """
    Formate un script SQL pour une meilleure lisibilité
    
    Args:
        sql: Script SQL à formater
        
    Returns:
        Script SQL formaté
    """
    # Ajouter des retours à la ligne après les mots-clés principaux
    formatted = re.sub(r'\b(SELECT|FROM|WHERE|JOIN|GROUP BY|ORDER BY|HAVING)\b', r'\n\1', sql, flags=re.IGNORECASE)
    
    # Ajouter des retours à la ligne après les points-virgules
    formatted = re.sub(r';', ';\n', formatted)
    
    # Nettoyer les espaces multiples
    formatted = re.sub(r'\n\s*\n', '\n', formatted)
    formatted = re.sub(r'[ \t]+', ' ', formatted)
    
    return formatted.strip()
