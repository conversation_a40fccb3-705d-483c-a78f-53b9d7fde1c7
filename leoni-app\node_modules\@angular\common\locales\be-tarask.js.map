{"version": 3, "file": "be-tarask.js", "sourceRoot": "", "sources": ["be-tarask.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC;QACjC,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;QAClG,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC;QACtJ,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,WAAW,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,SAAS,EAAC,YAAY,EAAC,SAAS,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,QAAQ,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,QAAQ,EAAC,UAAU,EAAC,WAAW,EAAC,KAAK,EAAC,SAAS,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,aAAa,EAAC,WAAW,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,MAAM,EAAC,SAAS,EAAC,UAAU,EAAC,KAAK,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,YAAY,EAAC,UAAU,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,wBAAwB,EAAC,wBAAwB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,cAAc,EAAC,eAAe,EAAC,qBAAqB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,gBAAgB,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,aAAa,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,IAAI,EAAC,kBAAkB,EAAC,EAAC,KAAK,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,MAAM,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,MAAM,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,EAAC,MAAM,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n % 10 === 1 && !(n % 100 === 11))\n    return 1;\nif (n % 10 === Math.floor(n % 10) && (n % 10 >= 2 && n % 10 <= 4) && !(n % 100 >= 12 && n % 100 <= 14))\n    return 3;\nif (n % 10 === 0 || (n % 10 === Math.floor(n % 10) && (n % 10 >= 5 && n % 10 <= 9) || n % 100 === Math.floor(n % 100) && (n % 100 >= 11 && n % 100 <= 14)))\n    return 4;\nreturn 5;\n}\n\nexport default [\"be-tarask\",[[\"am\",\"pm\"],[\"AM\",\"PM\"],u],[[\"AM\",\"PM\"],u,u],[[\"н\",\"п\",\"а\",\"с\",\"ч\",\"п\",\"с\"],[\"нд\",\"пн\",\"аў\",\"ср\",\"чц\",\"пт\",\"сб\"],[\"нядзеля\",\"панядзелак\",\"аўторак\",\"серада\",\"чацвер\",\"пятніца\",\"субота\"],[\"нд\",\"пн\",\"аў\",\"ср\",\"чц\",\"пт\",\"сб\"]],u,[[\"с\",\"л\",\"с\",\"к\",\"м\",\"ч\",\"л\",\"ж\",\"в\",\"к\",\"л\",\"с\"],[\"сту\",\"лют\",\"сак\",\"кра\",\"мая\",\"чэр\",\"ліп\",\"жні\",\"вер\",\"кас\",\"ліс\",\"сне\"],[\"студзеня\",\"лютага\",\"сакавіка\",\"красавіка\",\"мая\",\"чэрвеня\",\"ліпеня\",\"жніўня\",\"верасня\",\"кастрычніка\",\"лістапада\",\"снежня\"]],[[\"с\",\"л\",\"с\",\"к\",\"м\",\"ч\",\"л\",\"ж\",\"в\",\"к\",\"л\",\"с\"],[\"сту\",\"лют\",\"сак\",\"кра\",\"май\",\"чэр\",\"ліп\",\"жні\",\"вер\",\"кас\",\"ліс\",\"сне\"],[\"студзень\",\"люты\",\"сакавік\",\"красавік\",\"май\",\"чэрвень\",\"ліпень\",\"жнівень\",\"верасень\",\"кастрычнік\",\"лістапад\",\"снежань\"]],[[\"да н.э.\",\"н.э.\"],u,[\"да нараджэння Хрыстова\",\"ад нараджэння Хрыстова\"]],1,[6,0],[\"d.MM.yy\",\"d MMM y 'г'.\",\"d MMMM y 'г'.\",\"EEEE, d MMMM y 'г'.\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss, zzzz\"],[\"{1}, {0}\",u,\"{1} 'у' {0}\",u],[\",\",\" \",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0 %\",\"#,##0.00 ¤\",\"#E0\"],\"BYN\",\"Br\",\"беларускі рубель\",{\"AUD\":[\"A$\"],\"BBD\":[u,\"Bds$\"],\"BMD\":[u,\"BD$\"],\"BRL\":[u,\"R$\"],\"BSD\":[u,\"B$\"],\"BYN\":[\"Br\"],\"BZD\":[u,\"BZ$\"],\"CAD\":[u,\"CA$\"],\"CUC\":[u,\"CUC$\"],\"CUP\":[u,\"$MN\"],\"DOP\":[u,\"RD$\"],\"FJD\":[u,\"FJ$\"],\"FKP\":[u,\"FK£\"],\"GYD\":[u,\"G$\"],\"ISK\":[u,\"Íkr\"],\"JMD\":[u,\"J$\"],\"KYD\":[u,\"CI$\"],\"LRD\":[u,\"L$\"],\"MXN\":[\"MX$\"],\"NAD\":[u,\"N$\"],\"NZD\":[u,\"NZ$\"],\"PHP\":[u,\"₱\"],\"RUB\":[\"₽\",\"руб.\"],\"SBD\":[u,\"SI$\"],\"SGD\":[u,\"S$\"],\"TTD\":[u,\"TT$\"],\"UYU\":[u,\"$U\"],\"XCD\":[\"EC$\"]},\"ltr\", plural];\n"]}