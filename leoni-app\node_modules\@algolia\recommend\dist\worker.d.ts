import * as _algolia_client_common from '@algolia/client-common';
import { CreateClientOptions, RequestOptions, ClientOptions } from '@algolia/client-common';

/**
 * Response, taskID, and deletion timestamp.
 */
type DeletedAtResponse = {
    /**
     * Unique identifier of a task.  A successful API response means that a task was added to a queue. It might not run immediately. You can check the task\'s progress with the [`task` operation](#tag/Indices/operation/getTask) and this `taskID`.
     */
    taskID: number;
    /**
     * Date and time when the object was deleted, in RFC 3339 format.
     */
    deletedAt: string;
};

/**
 * Task status, `published` if the task is completed, `notPublished` otherwise.
 */
type TaskStatus = 'published' | 'notPublished';

type GetRecommendTaskResponse = {
    status: TaskStatus;
};

/**
 * Range object with lower and upper values in meters to define custom ranges.
 */
type Range = {
    /**
     * Lower boundary of a range in meters. The Geo ranking criterion considers all records within the range to be equal.
     */
    from?: number | undefined;
    /**
     * Upper boundary of a range in meters. The Geo ranking criterion considers all records within the range to be equal.
     */
    value?: number | undefined;
};

/**
 * Precision of a coordinate-based search in meters to group results with similar distances.  The Geo ranking criterion considers all matches within the same range of distances to be equal.
 */
type AroundPrecision = number | Array<Range>;

/**
 * Return all records with a valid `_geoloc` attribute. Don\'t filter by distance.
 */
type AroundRadiusAll = 'all';

/**
 * Maximum radius for a search around a central location.  This parameter works in combination with the `aroundLatLng` and `aroundLatLngViaIP` parameters. By default, the search radius is determined automatically from the density of hits around the central location. The search radius is small if there are many hits close to the central coordinates.
 */
type AroundRadius = number | AroundRadiusAll;

/**
 * Filter the search by facet values, so that only records with the same facet values are retrieved.  **Prefer using the `filters` parameter, which supports all filter types and combinations with boolean operators.**  - `[filter1, filter2]` is interpreted as `filter1 AND filter2`. - `[[filter1, filter2], filter3]` is interpreted as `filter1 OR filter2 AND filter3`. - `facet:-value` is interpreted as `NOT facet:value`.  While it\'s best to avoid attributes that start with a `-`, you can still filter them by escaping with a backslash: `facet:\\-value`.
 */
type FacetFilters = Array<FacetFilters> | string;

type InsideBoundingBox = string | Array<Array<number>>;

/**
 * Filter by numeric facets.  **Prefer using the `filters` parameter, which supports all filter types and combinations with boolean operators.**  You can use numeric comparison operators: `<`, `<=`, `=`, `!=`, `>`, `>=`. Comparisons are precise up to 3 decimals. You can also provide ranges: `facet:<lower> TO <upper>`. The range includes the lower and upper boundaries. The same combination rules apply as for `facetFilters`.
 */
type NumericFilters = Array<NumericFilters> | string;

/**
 * Filters to promote or demote records in the search results.  Optional filters work like facet filters, but they don\'t exclude records from the search results. Records that match the optional filter rank before records that don\'t match. If you\'re using a negative filter `facet:-value`, matching records rank after records that don\'t match.  - Optional filters don\'t work on virtual replicas. - Optional filters are applied _after_ sort-by attributes. - Optional filters are applied _before_ custom ranking attributes (in the default [ranking](https://www.algolia.com/doc/guides/managing-results/relevance-overview/in-depth/ranking-criteria/)). - Optional filters don\'t work with numeric attributes.
 */
type OptionalFilters = Array<OptionalFilters> | string;

/**
 * ISO code for a supported language.
 */
type SupportedLanguage = 'af' | 'ar' | 'az' | 'bg' | 'bn' | 'ca' | 'cs' | 'cy' | 'da' | 'de' | 'el' | 'en' | 'eo' | 'es' | 'et' | 'eu' | 'fa' | 'fi' | 'fo' | 'fr' | 'ga' | 'gl' | 'he' | 'hi' | 'hu' | 'hy' | 'id' | 'is' | 'it' | 'ja' | 'ka' | 'kk' | 'ko' | 'ku' | 'ky' | 'lt' | 'lv' | 'mi' | 'mn' | 'mr' | 'ms' | 'mt' | 'nb' | 'nl' | 'no' | 'ns' | 'pl' | 'ps' | 'pt' | 'pt-br' | 'qu' | 'ro' | 'ru' | 'sk' | 'sq' | 'sv' | 'sw' | 'ta' | 'te' | 'th' | 'tl' | 'tn' | 'tr' | 'tt' | 'uk' | 'ur' | 'uz' | 'zh';

/**
 * Filter the search by values of the special `_tags` attribute.  **Prefer using the `filters` parameter, which supports all filter types and combinations with boolean operators.**  Different from regular facets, `_tags` can only be used for filtering (including or excluding records). You won\'t get a facet count. The same combination and escaping rules apply as for `facetFilters`.
 */
type TagFilters = Array<TagFilters> | string;

type BaseRecommendSearchParams = {
    /**
     * Keywords to be used instead of the search query to conduct a more broader search.  Using the `similarQuery` parameter changes other settings:  - `queryType` is set to `prefixNone`. - `removeStopWords` is set to true. - `words` is set as the first ranking criterion. - All remaining words are treated as `optionalWords`.  Since the `similarQuery` is supposed to do a broad search, they usually return many results. Combine it with `filters` to narrow down the list of results.
     */
    similarQuery?: string | undefined;
    /**
     * Filter expression to only include items that match the filter criteria in the response.  You can use these filter expressions:  - **Numeric filters.** `<facet> <op> <number>`, where `<op>` is one of `<`, `<=`, `=`, `!=`, `>`, `>=`. - **Ranges.** `<facet>:<lower> TO <upper>` where `<lower>` and `<upper>` are the lower and upper limits of the range (inclusive). - **Facet filters.** `<facet>:<value>` where `<facet>` is a facet attribute (case-sensitive) and `<value>` a facet value. - **Tag filters.** `_tags:<value>` or just `<value>` (case-sensitive). - **Boolean filters.** `<facet>: true | false`.  You can combine filters with `AND`, `OR`, and `NOT` operators with the following restrictions:  - You can only combine filters of the same type with `OR`.   **Not supported:** `facet:value OR num > 3`. - You can\'t use `NOT` with combinations of filters.   **Not supported:** `NOT(facet:value OR facet:value)` - You can\'t combine conjunctions (`AND`) with `OR`.   **Not supported:** `facet:value OR (facet:value AND facet:value)`  Use quotes around your filters, if the facet attribute name or facet value has spaces, keywords (`OR`, `AND`, `NOT`), or quotes. If a facet attribute is an array, the filter matches if it matches at least one element of the array.  For more information, see [Filters](https://www.algolia.com/doc/guides/managing-results/refine-results/filtering/).
     */
    filters?: string | undefined;
    facetFilters?: FacetFilters | undefined;
    optionalFilters?: OptionalFilters | undefined;
    numericFilters?: NumericFilters | undefined;
    tagFilters?: TagFilters | undefined;
    /**
     * Whether to sum all filter scores.  If true, all filter scores are summed. Otherwise, the maximum filter score is kept. For more information, see [filter scores](https://www.algolia.com/doc/guides/managing-results/refine-results/filtering/in-depth/filter-scoring/#accumulating-scores-with-sumorfiltersscores).
     */
    sumOrFiltersScores?: boolean | undefined;
    /**
     * Restricts a search to a subset of your searchable attributes. Attribute names are case-sensitive.
     */
    restrictSearchableAttributes?: Array<string> | undefined;
    /**
     * Facets for which to retrieve facet values that match the search criteria and the number of matching facet values.  To retrieve all facets, use the wildcard character `*`. For more information, see [facets](https://www.algolia.com/doc/guides/managing-results/refine-results/faceting/#contextual-facet-values-and-counts).
     */
    facets?: Array<string> | undefined;
    /**
     * Whether faceting should be applied after deduplication with `distinct`.  This leads to accurate facet counts when using faceting in combination with `distinct`. It\'s usually better to use `afterDistinct` modifiers in the `attributesForFaceting` setting, as `facetingAfterDistinct` only computes correct facet counts if all records have the same facet values for the `attributeForDistinct`.
     */
    facetingAfterDistinct?: boolean | undefined;
    /**
     * Coordinates for the center of a circle, expressed as a comma-separated string of latitude and longitude.  Only records included within a circle around this central location are included in the results. The radius of the circle is determined by the `aroundRadius` and `minimumAroundRadius` settings. This parameter is ignored if you also specify `insidePolygon` or `insideBoundingBox`.
     */
    aroundLatLng?: string | undefined;
    /**
     * Whether to obtain the coordinates from the request\'s IP address.
     */
    aroundLatLngViaIP?: boolean | undefined;
    aroundRadius?: AroundRadius | undefined;
    aroundPrecision?: AroundPrecision | undefined;
    /**
     * Minimum radius (in meters) for a search around a location when `aroundRadius` isn\'t set.
     */
    minimumAroundRadius?: number | undefined;
    insideBoundingBox?: InsideBoundingBox | null | undefined;
    /**
     * Coordinates of a polygon in which to search.  Polygons are defined by 3 to 10,000 points. Each point is represented by its latitude and longitude. Provide multiple polygons as nested arrays. For more information, see [filtering inside polygons](https://www.algolia.com/doc/guides/managing-results/refine-results/geolocation/#filtering-inside-rectangular-or-polygonal-areas). This parameter is ignored if you also specify `insideBoundingBox`.
     */
    insidePolygon?: Array<Array<number>> | undefined;
    /**
     * ISO language codes that adjust settings that are useful for processing natural language queries (as opposed to keyword searches):  - Sets `removeStopWords` and `ignorePlurals` to the list of provided languages. - Sets `removeWordsIfNoResults` to `allOptional`. - Adds a `natural_language` attribute to `ruleContexts` and `analyticsTags`.
     */
    naturalLanguages?: Array<SupportedLanguage> | undefined;
    /**
     * Assigns a rule context to the search query.  [Rule contexts](https://www.algolia.com/doc/guides/managing-results/rules/rules-overview/how-to/customize-search-results-by-platform/#whats-a-context) are strings that you can use to trigger matching rules.
     */
    ruleContexts?: Array<string> | undefined;
    /**
     * Impact that Personalization should have on this search.  The higher this value is, the more Personalization determines the ranking compared to other factors. For more information, see [Understanding Personalization impact](https://www.algolia.com/doc/guides/personalization/personalizing-results/in-depth/configuring-personalization/#understanding-personalization-impact).
     */
    personalizationImpact?: number | undefined;
    /**
     * Unique pseudonymous or anonymous user identifier.  This helps with analytics and click and conversion events. For more information, see [user token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     */
    userToken?: string | undefined;
    /**
     * Whether the search response should include detailed ranking information.
     */
    getRankingInfo?: boolean | undefined;
    /**
     * Whether to take into account an index\'s synonyms for this search.
     */
    synonyms?: boolean | undefined;
    /**
     * Whether to include a `queryID` attribute in the response.  The query ID is a unique identifier for a search query and is required for tracking [click and conversion events](https://www.algolia.com/guides/sending-events/getting-started/).
     */
    clickAnalytics?: boolean | undefined;
    /**
     * Whether this search will be included in Analytics.
     */
    analytics?: boolean | undefined;
    /**
     * Tags to apply to the query for [segmenting analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     */
    analyticsTags?: Array<string> | undefined;
    /**
     * Whether to include this search when calculating processing-time percentiles.
     */
    percentileComputation?: boolean | undefined;
    /**
     * Whether to enable A/B testing for this search.
     */
    enableABTest?: boolean | undefined;
};

type BaseIndexSettings = {
    /**
     * Attributes used for [faceting](https://www.algolia.com/doc/guides/managing-results/refine-results/faceting/).  Facets are attributes that let you categorize search results. They can be used for filtering search results. By default, no attribute is used for faceting. Attribute names are case-sensitive.  **Modifiers**  - `filterOnly(\"ATTRIBUTE\")`.   Allows the attribute to be used as a filter but doesn\'t evaluate the facet values.  - `searchable(\"ATTRIBUTE\")`.   Allows searching for facet values.  - `afterDistinct(\"ATTRIBUTE\")`.   Evaluates the facet count _after_ deduplication with `distinct`.   This ensures accurate facet counts.   You can apply this modifier to searchable facets: `afterDistinct(searchable(ATTRIBUTE))`.
     */
    attributesForFaceting?: Array<string> | undefined;
    /**
     * Creates [replica indices](https://www.algolia.com/doc/guides/managing-results/refine-results/sorting/in-depth/replicas/).  Replicas are copies of a primary index with the same records but different settings, synonyms, or rules. If you want to offer a different ranking or sorting of your search results, you\'ll use replica indices. All index operations on a primary index are automatically forwarded to its replicas. To add a replica index, you must provide the complete set of replicas to this parameter. If you omit a replica from this list, the replica turns into a regular, standalone index that will no longer be synced with the primary index.  **Modifier**  - `virtual(\"REPLICA\")`.   Create a virtual replica,   Virtual replicas don\'t increase the number of records and are optimized for [Relevant sorting](https://www.algolia.com/doc/guides/managing-results/refine-results/sorting/in-depth/relevant-sort/).
     */
    replicas?: Array<string> | undefined;
    /**
     * Maximum number of search results that can be obtained through pagination.  Higher pagination limits might slow down your search. For pagination limits above 1,000, the sorting of results beyond the 1,000th hit can\'t be guaranteed.
     */
    paginationLimitedTo?: number | undefined;
    /**
     * Attributes that can\'t be retrieved at query time.  This can be useful if you want to use an attribute for ranking or to [restrict access](https://www.algolia.com/doc/guides/security/api-keys/how-to/user-restricted-access-to-data/), but don\'t want to include it in the search results. Attribute names are case-sensitive.
     */
    unretrievableAttributes?: Array<string> | undefined;
    /**
     * Creates a list of [words which require exact matches](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/in-depth/configuring-typo-tolerance/#turn-off-typo-tolerance-for-certain-words). This also turns off [word splitting and concatenation](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/splitting-and-concatenation/) for the specified words.
     */
    disableTypoToleranceOnWords?: Array<string> | undefined;
    /**
     * Attributes, for which you want to support [Japanese transliteration](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/language-specific-configurations/#japanese-transliteration-and-type-ahead).  Transliteration supports searching in any of the Japanese writing systems. To support transliteration, you must set the indexing language to Japanese. Attribute names are case-sensitive.
     */
    attributesToTransliterate?: Array<string> | undefined;
    /**
     * Attributes for which to split [camel case](https://wikipedia.org/wiki/Camel_case) words. Attribute names are case-sensitive.
     */
    camelCaseAttributes?: Array<string> | undefined;
    /**
     * Searchable attributes to which Algolia should apply [word segmentation](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/how-to/customize-segmentation/) (decompounding). Attribute names are case-sensitive.  Compound words are formed by combining two or more individual words, and are particularly prevalent in Germanic languages—for example, \"firefighter\". With decompounding, the individual components are indexed separately.  You can specify different lists for different languages. Decompounding is supported for these languages: Dutch (`nl`), German (`de`), Finnish (`fi`), Danish (`da`), Swedish (`sv`), and Norwegian (`no`). Decompounding doesn\'t work for words with [non-spacing mark Unicode characters](https://www.charactercodes.net/category/non-spacing_mark). For example, `Gartenstühle` won\'t be decompounded if the `ü` consists of `u` (U+0075) and `◌̈` (U+0308).
     */
    decompoundedAttributes?: Record<string, unknown> | undefined;
    /**
     * Languages for language-specific processing steps, such as word detection and dictionary settings.  **You should always specify an indexing language.** If you don\'t specify an indexing language, the search engine uses all [supported languages](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/supported-languages/), or the languages you specified with the `ignorePlurals` or `removeStopWords` parameters. This can lead to unexpected search results. For more information, see [Language-specific configuration](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/language-specific-configurations/).
     */
    indexLanguages?: Array<SupportedLanguage> | undefined;
    /**
     * Searchable attributes for which you want to turn off [prefix matching](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/override-search-engine-defaults/#adjusting-prefix-search). Attribute names are case-sensitive.
     */
    disablePrefixOnAttributes?: Array<string> | undefined;
    /**
     * Whether arrays with exclusively non-negative integers should be compressed for better performance. If true, the compressed arrays may be reordered.
     */
    allowCompressionOfIntegerArray?: boolean | undefined;
    /**
     * Numeric attributes that can be used as [numerical filters](https://www.algolia.com/doc/guides/managing-results/rules/detecting-intent/how-to/applying-a-custom-filter-for-a-specific-query/#numerical-filters). Attribute names are case-sensitive.  By default, all numeric attributes are available as numerical filters. For faster indexing, reduce the number of numeric attributes.  To turn off filtering for all numeric attributes, specify an attribute that doesn\'t exist in your index, such as `NO_NUMERIC_FILTERING`.  **Modifier**  - `equalOnly(\"ATTRIBUTE\")`.   Support only filtering based on equality comparisons `=` and `!=`.
     */
    numericAttributesForFiltering?: Array<string> | undefined;
    /**
     * Control which non-alphanumeric characters are indexed.  By default, Algolia ignores [non-alphanumeric characters](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/how-to/how-to-search-in-hyphenated-attributes/#handling-non-alphanumeric-characters) like hyphen (`-`), plus (`+`), and parentheses (`(`,`)`). To include such characters, define them with `separatorsToIndex`.  Separators are all non-letter characters except spaces and currency characters, such as $€£¥.  With `separatorsToIndex`, Algolia treats separator characters as separate words. For example, in a search for \"Disney+\", Algolia considers \"Disney\" and \"+\" as two separate words.
     */
    separatorsToIndex?: string | undefined;
    /**
     * Attributes used for searching. Attribute names are case-sensitive.  By default, all attributes are searchable and the [Attribute](https://www.algolia.com/doc/guides/managing-results/relevance-overview/in-depth/ranking-criteria/#attribute) ranking criterion is turned off. With a non-empty list, Algolia only returns results with matches in the selected attributes. In addition, the Attribute ranking criterion is turned on: matches in attributes that are higher in the list of `searchableAttributes` rank first. To make matches in two attributes rank equally, include them in a comma-separated string, such as `\"title,alternate_title\"`. Attributes with the same priority are always unordered.  For more information, see [Searchable attributes](https://www.algolia.com/doc/guides/sending-and-managing-data/prepare-your-data/how-to/setting-searchable-attributes/).  **Modifier**  - `unordered(\"ATTRIBUTE\")`.   Ignore the position of a match within the attribute.  Without a modifier, matches at the beginning of an attribute rank higher than matches at the end.
     */
    searchableAttributes?: Array<string> | undefined;
    /**
     * An object with custom data.  You can store up to 32kB as custom data.
     */
    userData?: any | null | undefined;
    /**
     * Characters and their normalized replacements. This overrides Algolia\'s default [normalization](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/normalization/).
     */
    customNormalization?: {
        [key: string]: {
            [key: string]: string;
        };
    } | undefined;
    /**
     * Attribute that should be used to establish groups of results. Attribute names are case-sensitive.  All records with the same value for this attribute are considered a group. You can combine `attributeForDistinct` with the `distinct` search parameter to control how many items per group are included in the search results.  If you want to use the same attribute also for faceting, use the `afterDistinct` modifier of the `attributesForFaceting` setting. This applies faceting _after_ deduplication, which will result in accurate facet counts.
     */
    attributeForDistinct?: string | undefined;
    /**
     * Maximum number of facet values to return when [searching for facet values](https://www.algolia.com/doc/guides/managing-results/refine-results/faceting/#search-for-facet-values).
     */
    maxFacetHits?: number | undefined;
    /**
     * Characters for which diacritics should be preserved.  By default, Algolia removes diacritics from letters. For example, `é` becomes `e`. If this causes issues in your search, you can specify characters that should keep their diacritics.
     */
    keepDiacriticsOnCharacters?: string | undefined;
    /**
     * Attributes to use as [custom ranking](https://www.algolia.com/doc/guides/managing-results/must-do/custom-ranking/). Attribute names are case-sensitive.  The custom ranking attributes decide which items are shown first if the other ranking criteria are equal.  Records with missing values for your selected custom ranking attributes are always sorted last. Boolean attributes are sorted based on their alphabetical order.  **Modifiers**  - `asc(\"ATTRIBUTE\")`.   Sort the index by the values of an attribute, in ascending order.  - `desc(\"ATTRIBUTE\")`.   Sort the index by the values of an attribute, in descending order.  If you use two or more custom ranking attributes, [reduce the precision](https://www.algolia.com/doc/guides/managing-results/must-do/custom-ranking/how-to/controlling-custom-ranking-metrics-precision/) of your first attributes, or the other attributes will never be applied.
     */
    customRanking?: Array<string> | undefined;
};

type AdvancedSyntaxFeatures = 'exactPhrase' | 'excludeWords';

type AlternativesAsExact = 'ignorePlurals' | 'singleWordSynonym' | 'multiWordsSynonym' | 'ignoreConjugations';

/**
 * Determines how many records of a group are included in the search results.  Records with the same value for the `attributeForDistinct` attribute are considered a group. The `distinct` setting controls how many members of the group are returned. This is useful for [deduplication and grouping](https://www.algolia.com/doc/guides/managing-results/refine-results/grouping/#introducing-algolias-distinct-feature).  The `distinct` setting is ignored if `attributeForDistinct` is not set.
 */
type Distinct = boolean | number;

/**
 * Determines how the [Exact ranking criterion](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/override-search-engine-defaults/in-depth/adjust-exact-settings/#turn-off-exact-for-some-attributes) is computed when the search query has only one word.  - `attribute`.   The Exact ranking criterion is 1 if the query word and attribute value are the same.   For example, a search for \"road\" will match the value \"road\", but not \"road trip\".  - `none`.   The Exact ranking criterion is ignored on single-word searches.  - `word`.   The Exact ranking criterion is 1 if the query word is found in the attribute value.   The query word must have at least 3 characters and must not be a stop word.   Only exact matches will be highlighted,   partial and prefix matches won\'t.
 */
type ExactOnSingleWordQuery = 'attribute' | 'none' | 'word';

type BooleanString = 'true' | 'false';

/**
 * Treat singular, plurals, and other forms of declensions as equivalent. You should only use this feature for the languages used in your index.
 */
type IgnorePlurals = Array<SupportedLanguage> | BooleanString | boolean;

/**
 * Words that should be considered optional when found in the query.  By default, records must match all words in the search query to be included in the search results. Adding optional words can help to increase the number of search results by running an additional search query that doesn\'t include the optional words. For example, if the search query is \"action video\" and \"video\" is an optional word, the search engine runs two queries. One for \"action video\" and one for \"action\". Records that match all words are ranked higher.  For a search query with 4 or more words **and** all its words are optional, the number of matched words required for a record to be included in the search results increases for every 1,000 records:  - If `optionalWords` has less than 10 words, the required number of matched words increases by 1:   results 1 to 1,000 require 1 matched word, results 1,001 to 2000 need 2 matched words. - If `optionalWords` has 10 or more words, the number of required matched words increases by the number of optional words divided by 5 (rounded down).   For example, with 18 optional words: results 1 to 1,000 require 1 matched word, results 1,001 to 2000 need 4 matched words.  For more information, see [Optional words](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/empty-or-insufficient-results/#creating-a-list-of-optional-words).
 */
type OptionalWords = string | Array<string>;

/**
 * Determines if and how query words are interpreted as prefixes.  By default, only the last query word is treated as a prefix (`prefixLast`). To turn off prefix search, use `prefixNone`. Avoid `prefixAll`, which treats all query words as prefixes. This might lead to counterintuitive results and makes your search slower.  For more information, see [Prefix searching](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/override-search-engine-defaults/in-depth/prefix-searching/).
 */
type QueryType = 'prefixLast' | 'prefixAll' | 'prefixNone';

/**
 * Restrict [Dynamic Re-Ranking](https://www.algolia.com/doc/guides/algolia-ai/re-ranking/) to records that match these filters.
 */
type ReRankingApplyFilter = Array<ReRankingApplyFilter> | string;

/**
 * Removes stop words from the search query.  Stop words are common words like articles, conjunctions, prepositions, or pronouns that have little or no meaning on their own. In English, \"the\", \"a\", or \"and\" are stop words.  You should only use this feature for the languages used in your index.
 */
type RemoveStopWords = Array<SupportedLanguage> | boolean;

/**
 * Strategy for removing words from the query when it doesn\'t return any results. This helps to avoid returning empty search results.  - `none`.   No words are removed when a query doesn\'t return results.  - `lastWords`.   Treat the last (then second to last, then third to last) word as optional,   until there are results or at most 5 words have been removed.  - `firstWords`.   Treat the first (then second, then third) word as optional,   until there are results or at most 5 words have been removed.  - `allOptional`.   Treat all words as optional.  For more information, see [Remove words to improve results](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/empty-or-insufficient-results/in-depth/why-use-remove-words-if-no-results/).
 */
type RemoveWordsIfNoResults = 'none' | 'lastWords' | 'firstWords' | 'allOptional';

/**
 * Order of facet names.
 */
type IndexSettingsFacets = {
    /**
     * Explicit order of facets or facet values.  This setting lets you always show specific facets or facet values at the top of the list.
     */
    order?: Array<string> | undefined;
};

/**
 * Order of facet values that aren\'t explicitly positioned with the `order` setting.  - `count`.   Order remaining facet values by decreasing count.   The count is the number of matching records containing this facet value.  - `alpha`.   Sort facet values alphabetically.  - `hidden`.   Don\'t show facet values that aren\'t explicitly positioned.
 */
type SortRemainingBy = 'count' | 'alpha' | 'hidden';

type Value = {
    /**
     * Explicit order of facets or facet values.  This setting lets you always show specific facets or facet values at the top of the list.
     */
    order?: Array<string> | undefined;
    sortRemainingBy?: SortRemainingBy | undefined;
    /**
     * Hide facet values.
     */
    hide?: Array<string> | undefined;
};

/**
 * Order of facet names and facet values in your UI.
 */
type FacetOrdering = {
    facets?: IndexSettingsFacets | undefined;
    /**
     * Order of facet values. One object for each facet.
     */
    values?: {
        [key: string]: Value;
    } | undefined;
};

/**
 * The redirect rule container.
 */
type RedirectURL = {
    url?: string | undefined;
};

/**
 * URL for an image to show inside a banner.
 */
type BannerImageUrl = {
    url?: string | undefined;
};

/**
 * Image to show inside a banner.
 */
type BannerImage = {
    urls?: Array<BannerImageUrl> | undefined;
    title?: string | undefined;
};

/**
 * Link for a banner defined in the Merchandising Studio.
 */
type BannerLink = {
    url?: string | undefined;
};

/**
 * Banner with image and link to redirect users.
 */
type Banner = {
    image?: BannerImage | undefined;
    link?: BannerLink | undefined;
};

/**
 * Widgets returned from any rules that are applied to the current search.
 */
type Widgets = {
    /**
     * Banners defined in the Merchandising Studio for a given search.
     */
    banners?: Array<Banner> | undefined;
};

/**
 * Extra data that can be used in the search UI.  You can use this to control aspects of your search UI, such as the order of facet names and values without changing your frontend code.
 */
type RenderingContent = {
    facetOrdering?: FacetOrdering | undefined;
    redirect?: RedirectURL | undefined;
    widgets?: Widgets | undefined;
};

/**
 * - `min`. Return matches with the lowest number of typos.   For example, if you have matches without typos, only include those.   But if there are no matches without typos (with 1 typo), include matches with 1 typo (2 typos). - `strict`. Return matches with the two lowest numbers of typos.   With `strict`, the Typo ranking criterion is applied first in the `ranking` setting.
 */
type TypoToleranceEnum = 'min' | 'strict' | 'true' | 'false';

/**
 * Whether [typo tolerance](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/) is enabled and how it is applied.  If typo tolerance is true, `min`, or `strict`, [word splitting and concatenation](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/splitting-and-concatenation/) are also active.
 */
type TypoTolerance = boolean | TypoToleranceEnum;

type BaseRecommendIndexSettings = {
    /**
     * Attributes to include in the API response.  To reduce the size of your response, you can retrieve only some of the attributes. Attribute names are case-sensitive.  - `*` retrieves all attributes, except attributes included in the `customRanking` and `unretrievableAttributes` settings. - To retrieve all attributes except a specific one, prefix the attribute with a dash and combine it with the `*`: `[\"*\", \"-ATTRIBUTE\"]`. - The `objectID` attribute is always included.
     */
    attributesToRetrieve?: Array<string> | undefined;
    /**
     * Determines the order in which Algolia returns your results.  By default, each entry corresponds to a [ranking criteria](https://www.algolia.com/doc/guides/managing-results/relevance-overview/in-depth/ranking-criteria/). The tie-breaking algorithm sequentially applies each criterion in the order they\'re specified. If you configure a replica index for [sorting by an attribute](https://www.algolia.com/doc/guides/managing-results/refine-results/sorting/how-to/sort-by-attribute/), you put the sorting attribute at the top of the list.  **Modifiers**  - `asc(\"ATTRIBUTE\")`.   Sort the index by the values of an attribute, in ascending order. - `desc(\"ATTRIBUTE\")`.   Sort the index by the values of an attribute, in descending order.  Before you modify the default setting, you should test your changes in the dashboard, and by [A/B testing](https://www.algolia.com/doc/guides/ab-testing/what-is-ab-testing/).
     */
    ranking?: Array<string> | undefined;
    /**
     * Relevancy threshold below which less relevant results aren\'t included in the results.  You can only set `relevancyStrictness` on [virtual replica indices](https://www.algolia.com/doc/guides/managing-results/refine-results/sorting/in-depth/replicas/#what-are-virtual-replicas). Use this setting to strike a balance between the relevance and number of returned results.
     */
    relevancyStrictness?: number | undefined;
    /**
     * Attributes to highlight.  By default, all searchable attributes are highlighted. Use `*` to highlight all attributes or use an empty array `[]` to turn off highlighting. Attribute names are case-sensitive.  With highlighting, strings that match the search query are surrounded by HTML tags defined by `highlightPreTag` and `highlightPostTag`. You can use this to visually highlight matching parts of a search query in your UI.  For more information, see [Highlighting and snippeting](https://www.algolia.com/doc/guides/building-search-ui/ui-and-ux-patterns/highlighting-snippeting/js/).
     */
    attributesToHighlight?: Array<string> | undefined;
    /**
     * Attributes for which to enable snippets. Attribute names are case-sensitive.  Snippets provide additional context to matched words. If you enable snippets, they include 10 words, including the matched word. The matched word will also be wrapped by HTML tags for highlighting. You can adjust the number of words with the following notation: `ATTRIBUTE:NUMBER`, where `NUMBER` is the number of words to be extracted.
     */
    attributesToSnippet?: Array<string> | undefined;
    /**
     * HTML tag to insert before the highlighted parts in all highlighted results and snippets.
     */
    highlightPreTag?: string | undefined;
    /**
     * HTML tag to insert after the highlighted parts in all highlighted results and snippets.
     */
    highlightPostTag?: string | undefined;
    /**
     * String used as an ellipsis indicator when a snippet is truncated.
     */
    snippetEllipsisText?: string | undefined;
    /**
     * Whether to restrict highlighting and snippeting to items that at least partially matched the search query. By default, all items are highlighted and snippeted.
     */
    restrictHighlightAndSnippetArrays?: boolean | undefined;
    /**
     * Minimum number of characters a word in the search query must contain to accept matches with [one typo](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/in-depth/configuring-typo-tolerance/#configuring-word-length-for-typos).
     */
    minWordSizefor1Typo?: number | undefined;
    /**
     * Minimum number of characters a word in the search query must contain to accept matches with [two typos](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/in-depth/configuring-typo-tolerance/#configuring-word-length-for-typos).
     */
    minWordSizefor2Typos?: number | undefined;
    typoTolerance?: TypoTolerance | undefined;
    /**
     * Whether to allow typos on numbers in the search query.  Turn off this setting to reduce the number of irrelevant matches when searching in large sets of similar numbers.
     */
    allowTyposOnNumericTokens?: boolean | undefined;
    /**
     * Attributes for which you want to turn off [typo tolerance](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/). Attribute names are case-sensitive.  Returning only exact matches can help when:  - [Searching in hyphenated attributes](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/how-to/how-to-search-in-hyphenated-attributes/). - Reducing the number of matches when you have too many.   This can happen with attributes that are long blocks of text, such as product descriptions.  Consider alternatives such as `disableTypoToleranceOnWords` or adding synonyms if your attributes have intentional unusual spellings that might look like typos.
     */
    disableTypoToleranceOnAttributes?: Array<string> | undefined;
    ignorePlurals?: IgnorePlurals | undefined;
    removeStopWords?: RemoveStopWords | undefined;
    /**
     * Languages for language-specific query processing steps such as plurals, stop-word removal, and word-detection dictionaries.  This setting sets a default list of languages used by the `removeStopWords` and `ignorePlurals` settings. This setting also sets a dictionary for word detection in the logogram-based [CJK](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/normalization/#normalization-for-logogram-based-languages-cjk) languages. To support this, you must place the CJK language **first**.  **You should always specify a query language.** If you don\'t specify an indexing language, the search engine uses all [supported languages](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/supported-languages/), or the languages you specified with the `ignorePlurals` or `removeStopWords` parameters. This can lead to unexpected search results. For more information, see [Language-specific configuration](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/language-specific-configurations/).
     */
    queryLanguages?: Array<SupportedLanguage> | undefined;
    /**
     * Whether to split compound words in the query into their building blocks.  For more information, see [Word segmentation](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/language-specific-configurations/#splitting-compound-words). Word segmentation is supported for these languages: German, Dutch, Finnish, Swedish, and Norwegian. Decompounding doesn\'t work for words with [non-spacing mark Unicode characters](https://www.charactercodes.net/category/non-spacing_mark). For example, `Gartenstühle` won\'t be decompounded if the `ü` consists of `u` (U+0075) and `◌̈` (U+0308).
     */
    decompoundQuery?: boolean | undefined;
    /**
     * Whether to enable rules.
     */
    enableRules?: boolean | undefined;
    /**
     * Whether to enable Personalization.
     */
    enablePersonalization?: boolean | undefined;
    queryType?: QueryType | undefined;
    removeWordsIfNoResults?: RemoveWordsIfNoResults | undefined;
    /**
     * Whether to support phrase matching and excluding words from search queries.  Use the `advancedSyntaxFeatures` parameter to control which feature is supported.
     */
    advancedSyntax?: boolean | undefined;
    optionalWords?: OptionalWords | null | undefined;
    /**
     * Searchable attributes for which you want to [turn off the Exact ranking criterion](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/override-search-engine-defaults/in-depth/adjust-exact-settings/#turn-off-exact-for-some-attributes). Attribute names are case-sensitive.  This can be useful for attributes with long values, where the likelihood of an exact match is high, such as product descriptions. Turning off the Exact ranking criterion for these attributes favors exact matching on other attributes. This reduces the impact of individual attributes with a lot of content on ranking.
     */
    disableExactOnAttributes?: Array<string> | undefined;
    exactOnSingleWordQuery?: ExactOnSingleWordQuery | undefined;
    /**
     * Determine which plurals and synonyms should be considered an exact matches.  By default, Algolia treats singular and plural forms of a word, and single-word synonyms, as [exact](https://www.algolia.com/doc/guides/managing-results/relevance-overview/in-depth/ranking-criteria/#exact) matches when searching. For example:  - \"swimsuit\" and \"swimsuits\" are treated the same - \"swimsuit\" and \"swimwear\" are treated the same (if they are [synonyms](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/adding-synonyms/#regular-synonyms)).  - `ignorePlurals`.   Plurals and similar declensions added by the `ignorePlurals` setting are considered exact matches.  - `singleWordSynonym`.   Single-word synonyms, such as \"NY\" = \"NYC\", are considered exact matches.  - `multiWordsSynonym`.   Multi-word synonyms, such as \"NY\" = \"New York\", are considered exact matches.
     */
    alternativesAsExact?: Array<AlternativesAsExact> | undefined;
    /**
     * Advanced search syntax features you want to support.  - `exactPhrase`.   Phrases in quotes must match exactly.   For example, `sparkly blue \"iPhone case\"` only returns records with the exact string \"iPhone case\".  - `excludeWords`.   Query words prefixed with a `-` must not occur in a record.   For example, `search -engine` matches records that contain \"search\" but not \"engine\".  This setting only has an effect if `advancedSyntax` is true.
     */
    advancedSyntaxFeatures?: Array<AdvancedSyntaxFeatures> | undefined;
    distinct?: Distinct | undefined;
    /**
     * Whether to replace a highlighted word with the matched synonym.  By default, the original words are highlighted even if a synonym matches. For example, with `home` as a synonym for `house` and a search for `home`, records matching either \"home\" or \"house\" are included in the search results, and either \"home\" or \"house\" are highlighted.  With `replaceSynonymsInHighlight` set to `true`, a search for `home` still matches the same records, but all occurrences of \"house\" are replaced by \"home\" in the highlighted response.
     */
    replaceSynonymsInHighlight?: boolean | undefined;
    /**
     * Minimum proximity score for two matching words.  This adjusts the [Proximity ranking criterion](https://www.algolia.com/doc/guides/managing-results/relevance-overview/in-depth/ranking-criteria/#proximity) by equally scoring matches that are farther apart.  For example, if `minProximity` is 2, neighboring matches and matches with one word between them would have the same score.
     */
    minProximity?: number | undefined;
    /**
     * Properties to include in the API response of search and browse requests.  By default, all response properties are included. To reduce the response size, you can select which properties should be included.  An empty list may lead to an empty API response (except properties you can\'t exclude).  You can\'t exclude these properties: `message`, `warning`, `cursor`, `abTestVariantID`, or any property added by setting `getRankingInfo` to true.  Your search depends on the `hits` field. If you omit this field, searches won\'t return any results. Your UI might also depend on other properties, for example, for pagination. Before restricting the response size, check the impact on your search experience.
     */
    responseFields?: Array<string> | undefined;
    /**
     * Maximum number of facet values to return for each facet.
     */
    maxValuesPerFacet?: number | undefined;
    /**
     * Order in which to retrieve facet values.  - `count`.   Facet values are retrieved by decreasing count.   The count is the number of matching records containing this facet value.  - `alpha`.   Retrieve facet values alphabetically.  This setting doesn\'t influence how facet values are displayed in your UI (see `renderingContent`). For more information, see [facet value display](https://www.algolia.com/doc/guides/building-search-ui/ui-and-ux-patterns/facet-display/js/).
     */
    sortFacetValuesBy?: string | undefined;
    /**
     * Whether the best matching attribute should be determined by minimum proximity.  This setting only affects ranking if the Attribute ranking criterion comes before Proximity in the `ranking` setting. If true, the best matching attribute is selected based on the minimum proximity of multiple matches. Otherwise, the best matching attribute is determined by the order in the `searchableAttributes` setting.
     */
    attributeCriteriaComputedByMinProximity?: boolean | undefined;
    renderingContent?: RenderingContent | undefined;
    /**
     * Whether this search will use [Dynamic Re-Ranking](https://www.algolia.com/doc/guides/algolia-ai/re-ranking/).  This setting only has an effect if you activated Dynamic Re-Ranking for this index in the Algolia dashboard.
     */
    enableReRanking?: boolean | undefined;
    reRankingApplyFilter?: ReRankingApplyFilter | null | undefined;
};

/**
 * Index settings.
 */
type RecommendIndexSettings = BaseIndexSettings & BaseRecommendIndexSettings;

type SearchParamsQuery = {
    /**
     * Search query.
     */
    query?: string | undefined;
};

/**
 * Search parameters for filtering the recommendations.
 */
type RecommendSearchParams = BaseRecommendSearchParams & SearchParamsQuery & RecommendIndexSettings;

type BaseRecommendRequest = {
    /**
     * Index name (case-sensitive).
     */
    indexName: string;
    /**
     * Minimum score a recommendation must have to be included in the response.
     */
    threshold: number;
    /**
     * Maximum number of recommendations to retrieve. By default, all recommendations are returned and no fallback request is made. Depending on the available recommendations and the other request parameters, the actual number of recommendations may be lower than this value.
     */
    maxRecommendations?: number | undefined;
    queryParameters?: RecommendSearchParams | undefined;
};

/**
 * Frequently bought together model.  This model recommends items that have been purchased within 1 day with the item with the ID `objectID`.
 */
type FbtModel = 'bought-together';

type FrequentlyBoughtTogether = {
    model: FbtModel;
    /**
     * Unique record identifier.
     */
    objectID: string;
};

type BoughtTogetherQuery = BaseRecommendRequest & FrequentlyBoughtTogether;

type FallbackParams = RecommendSearchParams & Record<string, unknown>;

/**
 * Looking similar model.  This model recommends items that look similar to the item with the ID `objectID` based on image attributes in your index.
 */
type LookingSimilarModel = 'looking-similar';

type LookingSimilar = {
    model: LookingSimilarModel;
    /**
     * Unique record identifier.
     */
    objectID: string;
    fallbackParameters?: FallbackParams | undefined;
};

type LookingSimilarQuery = BaseRecommendRequest & LookingSimilar;

/**
 * Related products or similar content model.  This model recommends items that are similar to the item with the ID `objectID`. Similarity is determined from the user interactions and attributes.
 */
type RelatedModel = 'related-products';

type RelatedProducts = {
    model: RelatedModel;
    /**
     * Unique record identifier.
     */
    objectID: string;
    fallbackParameters?: FallbackParams | undefined;
};

type RelatedQuery = BaseRecommendRequest & RelatedProducts;

/**
 * Trending facet values model.  This model recommends trending facet values for the specified facet attribute.
 */
type TrendingFacetsModel = 'trending-facets';

type TrendingFacets = {
    /**
     * Facet attribute for which to retrieve trending facet values.
     */
    facetName: string;
    model: TrendingFacetsModel;
    fallbackParameters?: FallbackParams | undefined;
};

type TrendingFacetsQuery = BaseRecommendRequest & TrendingFacets;

/**
 * Trending items model.  Trending items are determined from the number of conversion events collected on them.
 */
type TrendingItemsModel = 'trending-items';

type TrendingItems = {
    /**
     * Facet attribute. To be used in combination with `facetValue`. If specified, only recommendations matching the facet filter will be returned.
     */
    facetName?: string | undefined;
    /**
     * Facet value. To be used in combination with `facetName`. If specified, only recommendations matching the facet filter will be returned.
     */
    facetValue?: string | undefined;
    model: TrendingItemsModel;
    fallbackParameters?: FallbackParams | undefined;
};

type TrendingItemsQuery = BaseRecommendRequest & TrendingItems;

type RecommendationsRequest = BoughtTogetherQuery | RelatedQuery | TrendingItemsQuery | TrendingFacetsQuery | LookingSimilarQuery;

/**
 * Recommend request body.
 */
type GetRecommendationsParams = {
    /**
     * Recommendation request with parameters depending on the requested model.
     */
    requests: Array<RecommendationsRequest>;
};

/**
 * Whether certain properties of the search response are calculated exhaustive (exact) or approximated.
 */
type Exhaustive = {
    /**
     * Whether the facet count is exhaustive (`true`) or approximate (`false`). See the [related discussion](https://support.algolia.com/hc/en-us/articles/4406975248145-Why-are-my-facet-and-hit-counts-not-accurate-).
     */
    facetsCount?: boolean | undefined;
    /**
     * The value is `false` if not all facet values are retrieved.
     */
    facetValues?: boolean | undefined;
    /**
     * Whether the `nbHits` is exhaustive (`true`) or approximate (`false`). When the query takes more than 50ms to be processed, the engine makes an approximation. This can happen when using complex filters on millions of records, when typo-tolerance was not exhaustive, or when enough hits have been retrieved (for example, after the engine finds 10,000 exact matches). `nbHits` is reported as non-exhaustive whenever an approximation is made, even if the approximation didn’t, in the end, impact the exhaustivity of the query.
     */
    nbHits?: boolean | undefined;
    /**
     * Rules matching exhaustivity. The value is `false` if rules were enable for this query, and could not be fully processed due a timeout. This is generally caused by the number of alternatives (such as typos) which is too large.
     */
    rulesMatch?: boolean | undefined;
    /**
     * Whether the typo search was exhaustive (`true`) or approximate (`false`). An approximation is done when the typo search query part takes more than 10% of the query budget (ie. 5ms by default) to be processed (this can happen when a lot of typo alternatives exist for the query). This field will not be included when typo-tolerance is entirely disabled.
     */
    typo?: boolean | undefined;
};

type FacetStats = {
    /**
     * Minimum value in the results.
     */
    min?: number | undefined;
    /**
     * Maximum value in the results.
     */
    max?: number | undefined;
    /**
     * Average facet value in the results.
     */
    avg?: number | undefined;
    /**
     * Sum of all values in the results.
     */
    sum?: number | undefined;
};

/**
 * Redirect rule data.
 */
type RedirectRuleIndexData = {
    ruleObjectID: string;
};

type RedirectRuleIndexMetadata = {
    /**
     * Source index for the redirect rule.
     */
    source: string;
    /**
     * Destination index for the redirect rule.
     */
    dest: string;
    /**
     * Reason for the redirect rule.
     */
    reason: string;
    /**
     * Redirect rule status.
     */
    succeed: boolean;
    data: RedirectRuleIndexData;
};

/**
 * [Redirect results to a URL](https://www.algolia.com/doc/guides/managing-results/rules/merchandising-and-promoting/how-to/redirects/), this this parameter is for internal use only.
 */
type Redirect = {
    index?: Array<RedirectRuleIndexMetadata> | undefined;
};

type BaseSearchResponse = Record<string, any> & {
    /**
     * A/B test ID. This is only included in the response for indices that are part of an A/B test.
     */
    abTestID?: number | undefined;
    /**
     * Variant ID. This is only included in the response for indices that are part of an A/B test.
     */
    abTestVariantID?: number | undefined;
    /**
     * Computed geographical location.
     */
    aroundLatLng?: string | undefined;
    /**
     * Distance from a central coordinate provided by `aroundLatLng`.
     */
    automaticRadius?: string | undefined;
    exhaustive?: Exhaustive | undefined;
    /**
     * Rules applied to the query.
     */
    appliedRules?: Array<Record<string, unknown>> | undefined;
    /**
     * See the `facetsCount` field of the `exhaustive` object in the response.
     */
    exhaustiveFacetsCount?: boolean | undefined;
    /**
     * See the `nbHits` field of the `exhaustive` object in the response.
     */
    exhaustiveNbHits?: boolean | undefined;
    /**
     * See the `typo` field of the `exhaustive` object in the response.
     */
    exhaustiveTypo?: boolean | undefined;
    /**
     * Facet counts.
     */
    facets?: {
        [key: string]: {
            [key: string]: number;
        };
    } | undefined;
    /**
     * Statistics for numerical facets.
     */
    facets_stats?: {
        [key: string]: FacetStats;
    } | undefined;
    /**
     * Index name used for the query.
     */
    index?: string | undefined;
    /**
     * Index name used for the query. During A/B testing, the targeted index isn\'t always the index used by the query.
     */
    indexUsed?: string | undefined;
    /**
     * Warnings about the query.
     */
    message?: string | undefined;
    /**
     * Number of hits selected and sorted by the relevant sort algorithm.
     */
    nbSortedHits?: number | undefined;
    /**
     * Post-[normalization](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/#what-does-normalization-mean) query string that will be searched.
     */
    parsedQuery?: string | undefined;
    /**
     * Time the server took to process the request, in milliseconds.
     */
    processingTimeMS?: number | undefined;
    /**
     * Experimental. List of processing steps and their times, in milliseconds. You can use this list to investigate performance issues.
     */
    processingTimingsMS?: Record<string, unknown> | undefined;
    /**
     * Markup text indicating which parts of the original query have been removed to retrieve a non-empty result set.
     */
    queryAfterRemoval?: string | undefined;
    redirect?: Redirect | undefined;
    renderingContent?: RenderingContent | undefined;
    /**
     * Time the server took to process the request, in milliseconds.
     */
    serverTimeMS?: number | undefined;
    /**
     * Host name of the server that processed the request.
     */
    serverUsed?: string | undefined;
    /**
     * An object with custom data.  You can store up to 32kB as custom data.
     */
    userData?: any | null | undefined;
    /**
     * Unique identifier for the query. This is used for [click analytics](https://www.algolia.com/doc/guides/analytics/click-analytics/).
     */
    queryID?: string | undefined;
    /**
     * Whether automatic events collection is enabled for the application.
     */
    _automaticInsights?: boolean | undefined;
};

/**
 * Whether the whole query string matches or only a part.
 */
type MatchLevel = 'none' | 'partial' | 'full';

/**
 * Surround words that match the query with HTML tags for highlighting.
 */
type HighlightResultOption = {
    /**
     * Highlighted attribute value, including HTML tags.
     */
    value: string;
    matchLevel: MatchLevel;
    /**
     * List of matched words from the search query.
     */
    matchedWords: Array<string>;
    /**
     * Whether the entire attribute value is highlighted.
     */
    fullyHighlighted?: boolean | undefined;
};

type HighlightResult = HighlightResultOption | {
    [key: string]: HighlightResult;
} | Array<HighlightResult>;

type MatchedGeoLocation = {
    /**
     * Latitude of the matched location.
     */
    lat?: number | undefined;
    /**
     * Longitude of the matched location.
     */
    lng?: number | undefined;
    /**
     * Distance between the matched location and the search location (in meters).
     */
    distance?: number | undefined;
};

type Personalization = {
    /**
     * The score of the filters.
     */
    filtersScore?: number | undefined;
    /**
     * The score of the ranking.
     */
    rankingScore?: number | undefined;
    /**
     * The score of the event.
     */
    score?: number | undefined;
};

/**
 * Object with detailed information about the record\'s ranking.
 */
type RankingInfo = {
    /**
     * Whether a filter matched the query.
     */
    filters?: number | undefined;
    /**
     * Position of the first matched word in the best matching attribute of the record.
     */
    firstMatchedWord: number;
    /**
     * Distance between the geo location in the search query and the best matching geo location in the record, divided by the geo precision (in meters).
     */
    geoDistance: number;
    /**
     * Precision used when computing the geo distance, in meters.
     */
    geoPrecision?: number | undefined;
    matchedGeoLocation?: MatchedGeoLocation | undefined;
    personalization?: Personalization | undefined;
    /**
     * Number of exactly matched words.
     */
    nbExactWords: number;
    /**
     * Number of typos encountered when matching the record.
     */
    nbTypos: number;
    /**
     * Whether the record was promoted by a rule.
     */
    promoted?: boolean | undefined;
    /**
     * Number of words between multiple matches in the query plus 1. For single word queries, `proximityDistance` is 0.
     */
    proximityDistance?: number | undefined;
    /**
     * Overall ranking of the record, expressed as a single integer. This attribute is internal.
     */
    userScore: number;
    /**
     * Number of matched words.
     */
    words?: number | undefined;
    /**
     * Whether the record is re-ranked.
     */
    promotedByReRanking?: boolean | undefined;
};

/**
 * Snippets that show the context around a matching search query.
 */
type SnippetResultOption = {
    /**
     * Highlighted attribute value, including HTML tags.
     */
    value: string;
    matchLevel: MatchLevel;
};

type SnippetResult = SnippetResultOption | {
    [key: string]: SnippetResult;
} | Array<SnippetResult>;

/**
 * Recommend hit.
 */
type RecommendHit = Record<string, any> & {
    /**
     * Unique record identifier.
     */
    objectID: string;
    /**
     * Surround words that match the query with HTML tags for highlighting.
     */
    _highlightResult?: {
        [key: string]: HighlightResult;
    } | undefined;
    /**
     * Snippets that show the context around a matching search query.
     */
    _snippetResult?: {
        [key: string]: SnippetResult;
    } | undefined;
    _rankingInfo?: RankingInfo | undefined;
    _distinctSeqID?: number | undefined;
    /**
     * Recommendation score.
     */
    _score?: number | undefined;
};

/**
 * Trending facet hit.
 */
type TrendingFacetHit = {
    /**
     * Recommendation score.
     */
    _score?: number | undefined;
    /**
     * Facet attribute. To be used in combination with `facetValue`. If specified, only recommendations matching the facet filter will be returned.
     */
    facetName: string;
    /**
     * Facet value. To be used in combination with `facetName`. If specified, only recommendations matching the facet filter will be returned.
     */
    facetValue: string;
};

type RecommendationsHit = RecommendHit | TrendingFacetHit;

type RecommendationsHits = {
    hits: Array<RecommendationsHit>;
};

type SearchPagination = {
    /**
     * Page of search results to retrieve.
     */
    page?: number | undefined;
    /**
     * Number of results (hits).
     */
    nbHits?: number | undefined;
    /**
     * Number of pages of results.
     */
    nbPages?: number | undefined;
    /**
     * Number of hits per page.
     */
    hitsPerPage?: number | undefined;
};

type RecommendationsResults = BaseSearchResponse & SearchPagination & RecommendationsHits;

type GetRecommendationsResponse = {
    results: Array<RecommendationsResults>;
};

/**
 * Condition that triggers the rule. If not specified, the rule is triggered for all recommendations.
 */
type Condition = {
    /**
     * Filter expression to only include items that match the filter criteria in the response.  You can use these filter expressions:  - **Numeric filters.** `<facet> <op> <number>`, where `<op>` is one of `<`, `<=`, `=`, `!=`, `>`, `>=`. - **Ranges.** `<facet>:<lower> TO <upper>` where `<lower>` and `<upper>` are the lower and upper limits of the range (inclusive). - **Facet filters.** `<facet>:<value>` where `<facet>` is a facet attribute (case-sensitive) and `<value>` a facet value. - **Tag filters.** `_tags:<value>` or just `<value>` (case-sensitive). - **Boolean filters.** `<facet>: true | false`.  You can combine filters with `AND`, `OR`, and `NOT` operators with the following restrictions:  - You can only combine filters of the same type with `OR`.   **Not supported:** `facet:value OR num > 3`. - You can\'t use `NOT` with combinations of filters.   **Not supported:** `NOT(facet:value OR facet:value)` - You can\'t combine conjunctions (`AND`) with `OR`.   **Not supported:** `facet:value OR (facet:value AND facet:value)`  Use quotes around your filters, if the facet attribute name or facet value has spaces, keywords (`OR`, `AND`, `NOT`), or quotes. If a facet attribute is an array, the filter matches if it matches at least one element of the array.  For more information, see [Filters](https://www.algolia.com/doc/guides/managing-results/refine-results/filtering/).
     */
    filters?: string | undefined;
    /**
     * An additional restriction that only triggers the rule, when the search has the same value as `ruleContexts` parameter. For example, if `context: mobile`, the rule is only triggered when the search request has a matching `ruleContexts: mobile`. A rule context must only contain alphanumeric characters.
     */
    context?: string | undefined;
};

/**
 * Object ID of the recommendation you want to exclude.
 */
type HideConsequenceObject = {
    /**
     * Unique record identifier.
     */
    objectID?: string | undefined;
};

/**
 * Facet attribute. Only recommendations with the same value (or only recommendations with a different value) as the original viewed item are included.
 */
type AutoFacetFilter = {
    /**
     * Facet attribute.
     */
    facet?: string | undefined;
    /**
     * Whether the filter is negative. If true, recommendations must not have the same value for the `facet` attribute. If false, recommendations must have the same value for the `facet` attribute.
     */
    negative?: boolean | undefined;
};

/**
 * Filter or boost recommendations matching a facet filter.
 */
type ParamsConsequence = {
    /**
     * Filter recommendations that match or don\'t match the same `facet:facet_value` combination as the viewed item.
     */
    automaticFacetFilters?: Array<AutoFacetFilter> | undefined;
    /**
     * Filter expression to only include items that match the filter criteria in the response.  You can use these filter expressions:  - **Numeric filters.** `<facet> <op> <number>`, where `<op>` is one of `<`, `<=`, `=`, `!=`, `>`, `>=`. - **Ranges.** `<facet>:<lower> TO <upper>` where `<lower>` and `<upper>` are the lower and upper limits of the range (inclusive). - **Facet filters.** `<facet>:<value>` where `<facet>` is a facet attribute (case-sensitive) and `<value>` a facet value. - **Tag filters.** `_tags:<value>` or just `<value>` (case-sensitive). - **Boolean filters.** `<facet>: true | false`.  You can combine filters with `AND`, `OR`, and `NOT` operators with the following restrictions:  - You can only combine filters of the same type with `OR`.   **Not supported:** `facet:value OR num > 3`. - You can\'t use `NOT` with combinations of filters.   **Not supported:** `NOT(facet:value OR facet:value)` - You can\'t combine conjunctions (`AND`) with `OR`.   **Not supported:** `facet:value OR (facet:value AND facet:value)`  Use quotes around your filters, if the facet attribute name or facet value has spaces, keywords (`OR`, `AND`, `NOT`), or quotes. If a facet attribute is an array, the filter matches if it matches at least one element of the array.  For more information, see [Filters](https://www.algolia.com/doc/guides/managing-results/refine-results/filtering/).
     */
    filters?: string | undefined;
    /**
     * Filters to promote or demote records in the search results.  Optional filters work like facet filters, but they don\'t exclude records from the search results. Records that match the optional filter rank before records that don\'t match. Matches with higher weights (`<score=N>`) rank before matches with lower weights. If you\'re using a negative filter `facet:-value`, matching records rank after records that don\'t match.
     */
    optionalFilters?: Array<string> | undefined;
};

/**
 * Object ID and position of the recommendation you want to pin.
 */
type PromoteConsequenceObject = {
    /**
     * Unique record identifier.
     */
    objectID?: string | undefined;
    /**
     * Index in the list of recommendations where to place this item.
     */
    position?: number | undefined;
};

/**
 * Effect of the rule.
 */
type Consequence = {
    /**
     * Exclude items from recommendations.
     */
    hide?: Array<HideConsequenceObject> | undefined;
    /**
     * Place items at specific positions in the list of recommendations.
     */
    promote?: Array<PromoteConsequenceObject> | undefined;
    params?: ParamsConsequence | undefined;
};

/**
 * Rule metadata.
 */
type RuleMetadata = {
    /**
     * Date and time when the object was updated, in RFC 3339 format.
     */
    lastUpdate?: string | undefined;
};

type TimeRange = {
    /**
     * When the rule should start to be active, in Unix epoch time.
     */
    from?: number | undefined;
    /**
     * When the rule should stop to be active, in Unix epoch time.
     */
    until?: number | undefined;
};

/**
 * Recommend rule.
 */
type RecommendRule = {
    _metadata?: RuleMetadata | undefined;
    /**
     * Unique identifier of a rule object.
     */
    objectID?: string | undefined;
    condition?: Condition | undefined;
    consequence?: Consequence | undefined;
    /**
     * Description of the rule\'s purpose. This can be helpful for display in the Algolia dashboard.
     */
    description?: string | undefined;
    /**
     * Indicates whether to enable the rule. If it isn\'t enabled, it isn\'t applied at query time.
     */
    enabled?: boolean | undefined;
    /**
     * Time periods when the rule is active.
     */
    validity?: Array<TimeRange> | undefined;
};

/**
 * Response, taskID, and update timestamp.
 */
type RecommendUpdatedAtResponse = {
    /**
     * Unique identifier of a task.  A successful API response means that a task was added to a queue. It might not run immediately. You can check the task\'s progress with the [`task` operation](#tag/Indices/operation/getTask) and this `taskID`.
     */
    taskID: number;
    /**
     * Date and time when the object was updated, in RFC 3339 format.
     */
    updatedAt: string;
};

type SearchRecommendRulesResponse = {
    /**
     * Recommend rules that match the search criteria.
     */
    hits: Array<RecommendRule>;
    /**
     * Number of results (hits).
     */
    nbHits: number;
    /**
     * Page of search results to retrieve.
     */
    page: number;
    /**
     * Number of pages of results.
     */
    nbPages: number;
};

type RecommendModels = 'related-products' | 'bought-together' | 'trending-facets' | 'trending-items';

/**
 * Recommend rules parameters.
 */
type SearchRecommendRulesParams = {
    /**
     * Search query.
     */
    query?: string | undefined;
    /**
     * Only search for rules with matching context.
     */
    context?: string | undefined;
    /**
     * Requested page of the API response.  Algolia uses `page` and `hitsPerPage` to control how search results are displayed ([paginated](https://www.algolia.com/doc/guides/building-search-ui/ui-and-ux-patterns/pagination/js/)).  - `hitsPerPage`: sets the number of search results (_hits_) displayed per page. - `page`: specifies the page number of the search results you want to retrieve. Page numbering starts at 0, so the first page is `page=0`, the second is `page=1`, and so on.  For example, to display 10 results per page starting from the third page, set `hitsPerPage` to 10 and `page` to 2.
     */
    page?: number | undefined;
    /**
     * Maximum number of hits per page.  Algolia uses `page` and `hitsPerPage` to control how search results are displayed ([paginated](https://www.algolia.com/doc/guides/building-search-ui/ui-and-ux-patterns/pagination/js/)).  - `hitsPerPage`: sets the number of search results (_hits_) displayed per page. - `page`: specifies the page number of the search results you want to retrieve. Page numbering starts at 0, so the first page is `page=0`, the second is `page=1`, and so on.  For example, to display 10 results per page starting from the third page, set `hitsPerPage` to 10 and `page` to 2.
     */
    hitsPerPage?: number | undefined;
    /**
     * Whether to only show rules where the value of their `enabled` property matches this parameter. If absent, show all rules, regardless of their `enabled` property.
     */
    enabled?: boolean | undefined;
    /**
     * Filter expression. This only searches for rules matching the filter expression.
     */
    filters?: string | undefined;
    /**
     * Include facets and facet values in the response. Use `[\'*\']` to include all facets.
     */
    facets?: Array<string> | undefined;
    /**
     * Maximum number of values to return for each facet.
     */
    maxValuesPerFacet?: number | undefined;
};

/**
 * Properties for the `batchRecommendRules` method.
 */
type BatchRecommendRulesProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * [Recommend model](https://www.algolia.com/doc/guides/algolia-recommend/overview/#recommend-models).
     */
    model: RecommendModels;
    recommendRule?: Array<RecommendRule> | undefined;
};
/**
 * Properties for the `customDelete` method.
 */
type CustomDeleteProps = {
    /**
     * Path of the endpoint, for example `1/newFeature`.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    } | undefined;
};
/**
 * Properties for the `customGet` method.
 */
type CustomGetProps = {
    /**
     * Path of the endpoint, for example `1/newFeature`.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    } | undefined;
};
/**
 * Properties for the `customPost` method.
 */
type CustomPostProps = {
    /**
     * Path of the endpoint, for example `1/newFeature`.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    } | undefined;
    /**
     * Parameters to send with the custom request.
     */
    body?: Record<string, unknown> | undefined;
};
/**
 * Properties for the `customPut` method.
 */
type CustomPutProps = {
    /**
     * Path of the endpoint, for example `1/newFeature`.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    } | undefined;
    /**
     * Parameters to send with the custom request.
     */
    body?: Record<string, unknown> | undefined;
};
/**
 * Properties for the `deleteRecommendRule` method.
 */
type DeleteRecommendRuleProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * [Recommend model](https://www.algolia.com/doc/guides/algolia-recommend/overview/#recommend-models).
     */
    model: RecommendModels;
    /**
     * Unique record identifier.
     */
    objectID: string;
};
/**
 * Properties for the `getRecommendRule` method.
 */
type GetRecommendRuleProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * [Recommend model](https://www.algolia.com/doc/guides/algolia-recommend/overview/#recommend-models).
     */
    model: RecommendModels;
    /**
     * Unique record identifier.
     */
    objectID: string;
};
/**
 * Properties for the `getRecommendStatus` method.
 */
type GetRecommendStatusProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * [Recommend model](https://www.algolia.com/doc/guides/algolia-recommend/overview/#recommend-models).
     */
    model: RecommendModels;
    /**
     * Unique task identifier.
     */
    taskID: number;
};
/**
 * Recommend method signature compatible with the `algoliasearch` v4 package. When using this signature, extra computation will be required to make it match the new signature.
 *
 * @deprecated This signature will be removed from the next major version, we recommend using the `GetRecommendationsParams` type for performances and future proof reasons.
 */
type LegacyGetRecommendationsParams = RecommendationsRequest[];
/**
 * Properties for the `searchRecommendRules` method.
 */
type SearchRecommendRulesProps = {
    /**
     * Name of the index on which to perform the operation.
     */
    indexName: string;
    /**
     * [Recommend model](https://www.algolia.com/doc/guides/algolia-recommend/overview/#recommend-models).
     */
    model: RecommendModels;
    searchRecommendRulesParams?: SearchRecommendRulesParams | undefined;
};

declare const apiClientVersion = "5.32.0";
declare function createRecommendClient({ appId: appIdOption, apiKey: apiKeyOption, authMode, algoliaAgents, ...options }: CreateClientOptions): {
    transporter: _algolia_client_common.Transporter;
    /**
     * The `appId` currently in use.
     */
    appId: string;
    /**
     * The `apiKey` currently in use.
     */
    apiKey: string;
    /**
     * Clears the cache of the transporter for the `requestsCache` and `responsesCache` properties.
     */
    clearCache(): Promise<void>;
    /**
     * Get the value of the `algoliaAgent`, used by our libraries internally and telemetry system.
     */
    readonly _ua: string;
    /**
     * Adds a `segment` to the `x-algolia-agent` sent with every requests.
     *
     * @param segment - The algolia agent (user-agent) segment to add.
     * @param version - The version of the agent.
     */
    addAlgoliaAgent(segment: string, version?: string | undefined): void;
    /**
     * Helper method to switch the API key used to authenticate the requests.
     *
     * @param params - Method params.
     * @param params.apiKey - The new API Key to use.
     */
    setClientApiKey({ apiKey }: {
        apiKey: string;
    }): void;
    /**
     * Create or update a batch of Recommend Rules  Each Recommend Rule is created or updated, depending on whether a Recommend Rule with the same `objectID` already exists. You may also specify `true` for `clearExistingRules`, in which case the batch will atomically replace all the existing Recommend Rules.  Recommend Rules are similar to Search Rules, except that the conditions and consequences apply to a [source item](/doc/guides/algolia-recommend/overview/#recommend-models) instead of a query. The main differences are the following: - Conditions `pattern` and `anchoring` are unavailable. - Condition `filters` triggers if the source item matches the specified filters. - Condition `filters` accepts numeric filters. - Consequence `params` only covers filtering parameters. - Consequence `automaticFacetFilters` doesn\'t require a facet value placeholder (it tries to match the data source item\'s attributes instead).
     *
     * Required API Key ACLs:
     *  - editSettings
     * @param batchRecommendRules - The batchRecommendRules object.
     * @param batchRecommendRules.indexName - Name of the index on which to perform the operation.
     * @param batchRecommendRules.model - [Recommend model](https://www.algolia.com/doc/guides/algolia-recommend/overview/#recommend-models).
     * @param batchRecommendRules.recommendRule - The recommendRule object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    batchRecommendRules({ indexName, model, recommendRule }: BatchRecommendRulesProps, requestOptions?: RequestOptions): Promise<RecommendUpdatedAtResponse>;
    /**
     * This method lets you send requests to the Algolia REST API.
     * @param customDelete - The customDelete object.
     * @param customDelete.path - Path of the endpoint, for example `1/newFeature`.
     * @param customDelete.parameters - Query parameters to apply to the current query.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customDelete({ path, parameters }: CustomDeleteProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * This method lets you send requests to the Algolia REST API.
     * @param customGet - The customGet object.
     * @param customGet.path - Path of the endpoint, for example `1/newFeature`.
     * @param customGet.parameters - Query parameters to apply to the current query.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customGet({ path, parameters }: CustomGetProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * This method lets you send requests to the Algolia REST API.
     * @param customPost - The customPost object.
     * @param customPost.path - Path of the endpoint, for example `1/newFeature`.
     * @param customPost.parameters - Query parameters to apply to the current query.
     * @param customPost.body - Parameters to send with the custom request.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customPost({ path, parameters, body }: CustomPostProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * This method lets you send requests to the Algolia REST API.
     * @param customPut - The customPut object.
     * @param customPut.path - Path of the endpoint, for example `1/newFeature`.
     * @param customPut.parameters - Query parameters to apply to the current query.
     * @param customPut.body - Parameters to send with the custom request.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customPut({ path, parameters, body }: CustomPutProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * Deletes a Recommend rule from a recommendation scenario.
     *
     * Required API Key ACLs:
     *  - editSettings
     * @param deleteRecommendRule - The deleteRecommendRule object.
     * @param deleteRecommendRule.indexName - Name of the index on which to perform the operation.
     * @param deleteRecommendRule.model - [Recommend model](https://www.algolia.com/doc/guides/algolia-recommend/overview/#recommend-models).
     * @param deleteRecommendRule.objectID - Unique record identifier.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    deleteRecommendRule({ indexName, model, objectID }: DeleteRecommendRuleProps, requestOptions?: RequestOptions): Promise<DeletedAtResponse>;
    /**
     * Retrieves a Recommend rule that you previously created in the Algolia dashboard.
     *
     * Required API Key ACLs:
     *  - settings
     * @param getRecommendRule - The getRecommendRule object.
     * @param getRecommendRule.indexName - Name of the index on which to perform the operation.
     * @param getRecommendRule.model - [Recommend model](https://www.algolia.com/doc/guides/algolia-recommend/overview/#recommend-models).
     * @param getRecommendRule.objectID - Unique record identifier.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getRecommendRule({ indexName, model, objectID }: GetRecommendRuleProps, requestOptions?: RequestOptions): Promise<RecommendRule>;
    /**
     * Checks the status of a given task.  Deleting a Recommend rule is asynchronous. When you delete a rule, a task is created on a queue and completed depending on the load on the server. The API response includes a task ID that you can use to check the status.
     *
     * Required API Key ACLs:
     *  - editSettings
     * @param getRecommendStatus - The getRecommendStatus object.
     * @param getRecommendStatus.indexName - Name of the index on which to perform the operation.
     * @param getRecommendStatus.model - [Recommend model](https://www.algolia.com/doc/guides/algolia-recommend/overview/#recommend-models).
     * @param getRecommendStatus.taskID - Unique task identifier.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getRecommendStatus({ indexName, model, taskID }: GetRecommendStatusProps, requestOptions?: RequestOptions): Promise<GetRecommendTaskResponse>;
    /**
     * Retrieves recommendations from selected AI models.
     *
     * Required API Key ACLs:
     *  - search
     * @param getRecommendationsParams - The getRecommendationsParams object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getRecommendations(getRecommendationsParams: GetRecommendationsParams | LegacyGetRecommendationsParams, requestOptions?: RequestOptions): Promise<GetRecommendationsResponse>;
    /**
     * Searches for Recommend rules.  Use an empty query to list all rules for this recommendation scenario.
     *
     * Required API Key ACLs:
     *  - settings
     * @param searchRecommendRules - The searchRecommendRules object.
     * @param searchRecommendRules.indexName - Name of the index on which to perform the operation.
     * @param searchRecommendRules.model - [Recommend model](https://www.algolia.com/doc/guides/algolia-recommend/overview/#recommend-models).
     * @param searchRecommendRules.searchRecommendRulesParams - The searchRecommendRulesParams object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    searchRecommendRules({ indexName, model, searchRecommendRulesParams }: SearchRecommendRulesProps, requestOptions?: RequestOptions): Promise<SearchRecommendRulesResponse>;
};

/**
 * Error.
 */
type ErrorBase = Record<string, any> & {
    message?: string | undefined;
};

type RecommendClient = ReturnType<typeof createRecommendClient>;

declare function recommendClient(appId: string, apiKey: string, options?: ClientOptions | undefined): RecommendClient;

export { type AdvancedSyntaxFeatures, type AlternativesAsExact, type AroundPrecision, type AroundRadius, type AroundRadiusAll, type AutoFacetFilter, type Banner, type BannerImage, type BannerImageUrl, type BannerLink, type BaseIndexSettings, type BaseRecommendIndexSettings, type BaseRecommendRequest, type BaseRecommendSearchParams, type BaseSearchResponse, type BatchRecommendRulesProps, type BooleanString, type BoughtTogetherQuery, type Condition, type Consequence, type CustomDeleteProps, type CustomGetProps, type CustomPostProps, type CustomPutProps, type DeleteRecommendRuleProps, type DeletedAtResponse, type Distinct, type ErrorBase, type ExactOnSingleWordQuery, type Exhaustive, type FacetFilters, type FacetOrdering, type FacetStats, type FallbackParams, type FbtModel, type FrequentlyBoughtTogether, type GetRecommendRuleProps, type GetRecommendStatusProps, type GetRecommendTaskResponse, type GetRecommendationsParams, type GetRecommendationsResponse, type HideConsequenceObject, type HighlightResult, type HighlightResultOption, type IgnorePlurals, type IndexSettingsFacets, type InsideBoundingBox, type LegacyGetRecommendationsParams, type LookingSimilar, type LookingSimilarModel, type LookingSimilarQuery, type MatchLevel, type MatchedGeoLocation, type NumericFilters, type OptionalFilters, type OptionalWords, type ParamsConsequence, type Personalization, type PromoteConsequenceObject, type QueryType, type Range, type RankingInfo, type ReRankingApplyFilter, type RecommendClient, type RecommendHit, type RecommendIndexSettings, type RecommendModels, type RecommendRule, type RecommendSearchParams, type RecommendUpdatedAtResponse, type RecommendationsHit, type RecommendationsHits, type RecommendationsRequest, type RecommendationsResults, type Redirect, type RedirectRuleIndexData, type RedirectRuleIndexMetadata, type RedirectURL, type RelatedModel, type RelatedProducts, type RelatedQuery, type RemoveStopWords, type RemoveWordsIfNoResults, type RenderingContent, type RuleMetadata, type SearchPagination, type SearchParamsQuery, type SearchRecommendRulesParams, type SearchRecommendRulesProps, type SearchRecommendRulesResponse, type SnippetResult, type SnippetResultOption, type SortRemainingBy, type SupportedLanguage, type TagFilters, type TaskStatus, type TimeRange, type TrendingFacetHit, type TrendingFacets, type TrendingFacetsModel, type TrendingFacetsQuery, type TrendingItems, type TrendingItemsModel, type TrendingItemsQuery, type TypoTolerance, type TypoToleranceEnum, type Value, type Widgets, apiClientVersion, recommendClient };
