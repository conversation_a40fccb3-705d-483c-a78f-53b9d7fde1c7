/* Styles globaux pour l'application Leoni Agents */

@import '@angular/material/prebuilt-themes/indigo-pink.css';

/* Variables CSS personnalisées */
:root {
  --primary-color: #002857;
  --primary-light: #003d7a;
  --accent-color: #ff7514;
  --accent-light: #ff8c42;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --text-primary: #333333;
  --text-secondary: #666666;
  --background-light: #f5f7fa;
  --background-gradient: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* Reset et styles de base */
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Roboto', sans-serif;
  background: var(--background-gradient);
  color: var(--text-primary);
  line-height: 1.6;
}

/* Styles pour les notifications */
.snackbar-success {
  background: var(--success-color) !important;
  color: white !important;
}

.snackbar-error {
  background: var(--error-color) !important;
  color: white !important;
}

.snackbar-warning {
  background: var(--warning-color) !important;
  color: white !important;
}

.snackbar-info {
  background: var(--primary-color) !important;
  color: white !important;
}

/* Styles pour les toasts */
.toast-container {
  .toast-success {
    background-color: var(--success-color);
  }
  
  .toast-error {
    background-color: var(--error-color);
  }
  
  .toast-warning {
    background-color: var(--warning-color);
  }
  
  .toast-info {
    background-color: var(--primary-color);
  }
}

/* Styles pour les cartes Material */
.mat-mdc-card {
  border-radius: 16px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
}

.mat-mdc-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15) !important;
}

/* Styles pour les boutons Material */
.mat-mdc-raised-button {
  border-radius: 12px !important;
  font-weight: 600 !important;
  text-transform: none !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
  transition: all 0.3s ease !important;
}

.mat-mdc-raised-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25) !important;
}

.mat-mdc-outlined-button,
.mat-mdc-stroked-button {
  border-radius: 12px !important;
  font-weight: 600 !important;
  text-transform: none !important;
  transition: all 0.3s ease !important;
}

/* Styles pour les champs de formulaire */
.mat-mdc-form-field {
  width: 100%;
}

.mat-mdc-text-field-wrapper {
  border-radius: 12px !important;
}

/* Styles pour les chips */
.mat-mdc-chip {
  border-radius: 20px !important;
  font-weight: 500 !important;
}

/* Styles pour la barre d'outils */
.mat-toolbar {
  border-radius: 0 !important;
}

/* Classes utilitaires */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.p-4 { padding: 2rem; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.d-flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.justify-center { justify-content: center; }
.align-center { align-items: center; }
.gap-1 { gap: 0.5rem; }
.gap-2 { gap: 1rem; }
.gap-3 { gap: 1.5rem; }

/* Styles pour le code */
.code-block {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.code-inline {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 0.2rem 0.4rem;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

/* Styles pour les listes */
.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  color: var(--text-secondary);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in {
  animation: slideIn 0.6s ease-out;
}

/* Responsive */
@media (max-width: 768px) {
  .mat-mdc-card {
    margin: 0.5rem;
  }
  
  .hero-title {
    font-size: 2rem !important;
  }
  
  .section-title {
    font-size: 1.5rem !important;
  }
}

/* Styles pour le scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-light);
}
