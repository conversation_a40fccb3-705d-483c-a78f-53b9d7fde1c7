"""
Service de génération SQL
Équivalent de sqlGenerationAgent.ts en Python
"""

import json
from typing import Dict, Any
from models.types import Agent, SQLGenerationRequest, dataclass_to_dict
from utils.text_utils import prepare_specification_for_api
from config.openai_config import get_openai_client


class SQLGenerationService:
    """Service pour la génération SQL avec IA"""
    
    def __init__(self):
        self.agent = Agent(
            id='sql-generation-agent',
            name='Agent de Génération SQL',
            description='Génère des scripts SQL à partir de spécifications fonctionnelles',
            role='Expert en bases de données et génération de scripts SQL',
            goal='Créer des scripts SQL optimisés et bien structurés à partir de spécifications',
            prompt="""Tu es un expert en bases de données et génération de scripts SQL.
            Ton rôle est de transformer des spécifications fonctionnelles en scripts SQL optimisés.
            
            Tu dois :
            1. Analyser les spécifications fonctionnelles
            2. Concevoir une structure de base de données appropriée
            3. Générer des scripts SQL propres et optimisés
            4. Inclure des commentaires explicatifs si demandé
            5. Respecter les bonnes pratiques du SGBD cible
            
            Tu fournis des scripts SQL professionnels, bien structurés et documentés.""",
            tools=['sql-generation', 'database-design', 'optimization'],
            utils=['formatSQL', 'validateSyntax']
        )
        self.openai_client = get_openai_client()
    
    def generate_sql(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Génère un script SQL à partir d'une spécification
        
        Args:
            request_data: Dictionnaire avec specification, database_type, include_comments
            
        Returns:
            Dictionnaire avec le script SQL généré et les métadonnées
        """
        try:
            # Extraire les paramètres
            specification = request_data.get('specification', '')
            database_type = request_data.get('database_type', 'mysql')
            include_comments = request_data.get('include_comments', True)
            
            if not specification.strip():
                raise ValueError('La spécification est requise')
            
            # Préparer la spécification (gestion de la longueur)
            prepared_spec = prepare_specification_for_api(specification)
            
            # Log des informations de traitement
            print(f"API SQL: Spécification {'tronquée' if prepared_spec['was_truncated'] else 'normale'}")
            print(f"Longueur: {prepared_spec['original_length']} → {prepared_spec['processed_length']} caractères")
            print(f"Tokens estimés: {prepared_spec['estimated_tokens']}")
            
            # Construire le prompt optimisé
            prompt = f"""Tu es un expert SQL. Génère un script {database_type.upper()} basé sur cette spécification.

SPÉCIFICATION:
{prepared_spec['processed_text']}

Réponds en JSON:
{{
  "sql": "Script SQL complet",
  "explanation": "Explication concise",
  "tables": ["table1", "table2"],
  "operations": ["CREATE", "INSERT"]
}}

IMPORTANT:
- Utilise la syntaxe {database_type.upper()}
- {'Inclus des commentaires explicatifs' if include_comments else 'Pas de commentaires'}
- Optimise les performances
- Respecte les conventions de nommage
- Assure-toi que le SQL est exécutable"""
            
            # Appel à OpenAI
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {
                        "role": "system",
                        "content": self.agent.prompt
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.2,
                max_tokens=2500
            )
            
            content = response.choices[0].message.content
            if not content:
                raise Exception('Aucune réponse reçue de l\'API OpenAI')
            
            # Parser la réponse JSON
            try:
                # Nettoyer la réponse (enlever les ```json si présents)
                clean_content = content.strip()
                if clean_content.startswith('```json'):
                    clean_content = clean_content[7:]
                if clean_content.endswith('```'):
                    clean_content = clean_content[:-3]
                
                result_data = json.loads(clean_content.strip())
                
                # Validation des champs requis
                required_fields = ['sql', 'explanation', 'tables', 'operations']
                for field in required_fields:
                    if field not in result_data:
                        result_data[field] = []
                
                # S'assurer que les listes sont bien des listes
                if not isinstance(result_data.get('tables'), list):
                    result_data['tables'] = []
                if not isinstance(result_data.get('operations'), list):
                    result_data['operations'] = []
                
                return result_data
                
            except json.JSONDecodeError as e:
                # Si le parsing JSON échoue, créer une réponse de fallback
                return {
                    "sql": content,  # Retourner le contenu brut comme SQL
                    "explanation": "Script SQL généré (format de réponse non standard)",
                    "tables": [],
                    "operations": ["UNKNOWN"]
                }
                
        except Exception as e:
            raise Exception(f"Erreur lors de la génération SQL: {str(e)}")
    
    def get_agent_info(self) -> Dict[str, Any]:
        """Retourne les informations sur l'agent"""
        return dataclass_to_dict(self.agent)
