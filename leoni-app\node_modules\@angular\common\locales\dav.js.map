{"version": 3, "file": "dav.js", "sourceRoot": "", "sources": ["dav.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,YAAY,EAAC,YAAY,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,gBAAgB,EAAC,kBAAkB,EAAC,eAAe,EAAC,iBAAiB,EAAC,eAAe,EAAC,iBAAiB,EAAC,cAAc,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,gBAAgB,EAAC,kBAAkB,EAAC,gBAAgB,EAAC,kBAAkB,EAAC,qBAAqB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,kBAAkB,EAAC,iBAAiB,EAAC,2BAA2B,EAAC,wBAAwB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,iBAAiB,EAAC,iBAAiB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,mBAAmB,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"dav\",[[\"Luma lwa K\",\"luma lwa p\"],u,u],u,[[\"J\",\"J\",\"K\",\"<PERSON>\",\"K\",\"K\",\"N\"],[\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>w\",\"Kad\",\"Kan\",\"Ka<PERSON>\",\"<PERSON>u\"],[\"<PERSON>uku ja jumwa\",\"<PERSON><PERSON>uka jimweri\",\"<PERSON><PERSON><PERSON> kawi\",\"<PERSON><PERSON><PERSON> kadadu\",\"<PERSON><PERSON>uka kana\",\"<PERSON><PERSON><PERSON> kasanu\",\"<PERSON><PERSON>a nguwo\"],[\"Ju<PERSON>\",\"<PERSON>\",\"<PERSON>w\",\"Kad\",\"Kan\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\"]],u,[[\"I\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"M\",\"W\",\"I\",\"<PERSON>\",\"<PERSON>\",\"I\"],[\"Imb\",\"Kaw\",\"Kad\",\"Kan\",\"Kas\",\"<PERSON>r\",\"<PERSON>fu\",\"<PERSON>n\",\"Ike\",\"Iku\",\"Imw\",\"Iwi\"],[\"<PERSON>ri ghwa imbiri\",\"<PERSON>ri ghwa kawi\",\"<PERSON>ri ghwa kadadu\",\"<PERSON>ri ghwa kana\",\"<PERSON>ri ghwa kasanu\",\"<PERSON>ri ghwa ka<PERSON>adu\",\"<PERSON>ri ghwa mfungade\",\"<PERSON>ri ghwa wunyanya\",\"Mori ghwa ikenda\",\"Mori ghwa ikumi\",\"Mori ghwa ikumi na imweri\",\"Mori ghwa ikumi na iwi\"]],u,[[\"KK\",\"BK\"],u,[\"Kabla ya Kristo\",\"Baada ya Kristo\"]],0,[6,0],[\"dd/MM/y\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"KES\",\"Ksh\",\"Shilingi ya Kenya\",{\"JPY\":[\"JP¥\",\"¥\"],\"KES\":[\"Ksh\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}