import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatToolbarModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule
  ],
  template: `
    <mat-toolbar class="header-toolbar">
      <div class="header-container">
        <!-- Logo et titre -->
        <div class="logo-section" routerLink="/">
          <div class="logo">
            <mat-icon class="logo-icon">smart_toy</mat-icon>
          </div>
          <div class="title-section">
            <h1 class="app-title">Leoni Agents</h1>
            <span class="app-subtitle">Intelligence Artificielle</span>
          </div>
        </div>

        <!-- Navigation -->
        <nav class="navigation">
          <a mat-button 
             routerLink="/" 
             routerLinkActive="active"
             [routerLinkActiveOptions]="{exact: true}"
             class="nav-link">
            <mat-icon>home</mat-icon>
            <span>Accueil</span>
          </a>
          
          <a mat-button 
             routerLink="/error-analysis" 
             routerLinkActive="active"
             class="nav-link">
            <mat-icon>bug_report</mat-icon>
            <span>Analyse d'Erreurs</span>
          </a>
          
          <a mat-button 
             routerLink="/sql-generation" 
             routerLinkActive="active"
             class="nav-link">
            <mat-icon>storage</mat-icon>
            <span>Génération SQL</span>
          </a>
        </nav>

        <!-- Menu mobile -->
        <div class="mobile-menu">
          <button mat-icon-button [matMenuTriggerFor]="mobileNav">
            <mat-icon>menu</mat-icon>
          </button>
          
          <mat-menu #mobileNav="matMenu">
            <a mat-menu-item routerLink="/">
              <mat-icon>home</mat-icon>
              <span>Accueil</span>
            </a>
            <a mat-menu-item routerLink="/error-analysis">
              <mat-icon>bug_report</mat-icon>
              <span>Analyse d'Erreurs</span>
            </a>
            <a mat-menu-item routerLink="/sql-generation">
              <mat-icon>storage</mat-icon>
              <span>Génération SQL</span>
            </a>
          </mat-menu>
        </div>
      </div>
    </mat-toolbar>
  `,
  styles: [`
    .header-toolbar {
      background: linear-gradient(135deg, #002857 0%, #003d7a 100%);
      color: white;
      box-shadow: 0 4px 20px rgba(0, 40, 87, 0.3);
      position: sticky;
      top: 0;
      z-index: 1000;
    }

    .header-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
    }

    .logo-section {
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: transform 0.2s ease;
    }

    .logo-section:hover {
      transform: scale(1.02);
    }

    .logo {
      margin-right: 1rem;
    }

    .logo-icon {
      font-size: 2.5rem;
      width: 2.5rem;
      height: 2.5rem;
      color: #ff7514;
      filter: drop-shadow(0 2px 4px rgba(255, 117, 20, 0.3));
    }

    .title-section {
      display: flex;
      flex-direction: column;
    }

    .app-title {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 700;
      background: linear-gradient(45deg, #ffffff, #ff7514);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .app-subtitle {
      font-size: 0.75rem;
      opacity: 0.8;
      font-weight: 300;
      margin-top: -2px;
    }

    .navigation {
      display: flex;
      gap: 0.5rem;
    }

    .nav-link {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: white !important;
      border-radius: 8px;
      transition: all 0.3s ease;
      padding: 0.5rem 1rem;
    }

    .nav-link:hover {
      background: rgba(255, 255, 255, 0.1);
      transform: translateY(-1px);
    }

    .nav-link.active {
      background: linear-gradient(45deg, #ff7514, #ff8c42);
      box-shadow: 0 2px 8px rgba(255, 117, 20, 0.3);
    }

    .nav-link mat-icon {
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
    }

    .mobile-menu {
      display: none;
    }

    /* Responsive */
    @media (max-width: 768px) {
      .navigation {
        display: none;
      }

      .mobile-menu {
        display: block;
      }

      .app-title {
        font-size: 1.2rem;
      }

      .app-subtitle {
        font-size: 0.7rem;
      }

      .logo-icon {
        font-size: 2rem;
        width: 2rem;
        height: 2rem;
      }
    }

    @media (max-width: 480px) {
      .header-container {
        padding: 0 0.5rem;
      }

      .title-section {
        display: none;
      }
    }
  `]
})
export class HeaderComponent {}
