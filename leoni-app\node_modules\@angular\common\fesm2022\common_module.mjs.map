{"version": 3, "file": "common_module.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/location/hash_location_strategy.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/i18n/currencies.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/i18n/locale_data_api.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/i18n/format_date.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/i18n/format_number.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/i18n/localization.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/directives/ng_class.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/directives/ng_component_outlet.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/directives/ng_for_of.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/directives/ng_if.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/directives/ng_switch.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/directives/ng_plural.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/directives/ng_style.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/directives/ng_template_outlet.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/directives/index.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/pipes/invalid_pipe_argument_error.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/pipes/async_pipe.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/pipes/case_conversion_pipes.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/pipes/date_pipe_config.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/pipes/date_pipe.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/pipes/i18n_plural_pipe.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/pipes/i18n_select_pipe.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/pipes/json_pipe.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/pipes/keyvalue_pipe.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/pipes/number_pipe.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/pipes/slice_pipe.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/pipes/index.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/common_module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Inject, Injectable, OnDestroy, Optional} from '@angular/core';\n\nimport {APP_BASE_HREF, LocationStrategy} from './location_strategy';\nimport {LocationChangeListener, PlatformLocation} from './platform_location';\nimport {joinWithSlash, normalizeQueryParams} from './util';\n\n/**\n * @description\n * A {@link LocationStrategy} used to configure the {@link Location} service to\n * represent its state in the\n * [hash fragment](https://en.wikipedia.org/wiki/Uniform_Resource_Locator#Syntax)\n * of the browser's URL.\n *\n * For instance, if you call `location.go('/foo')`, the browser's URL will become\n * `example.com#/foo`.\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example common/location/ts/hash_location_component.ts region='LocationComponent'}\n *\n * @publicApi\n */\n@Injectable()\nexport class HashLocationStrategy extends LocationStrategy implements OnDestroy {\n  private _baseHref: string = '';\n  private _removeListenerFns: (() => void)[] = [];\n\n  constructor(\n    private _platformLocation: PlatformLocation,\n    @Optional() @Inject(APP_BASE_HREF) _baseHref?: string,\n  ) {\n    super();\n    if (_baseHref != null) {\n      this._baseHref = _baseHref;\n    }\n  }\n\n  /** @docs-private */\n  ngOnDestroy(): void {\n    while (this._removeListenerFns.length) {\n      this._removeListenerFns.pop()!();\n    }\n  }\n\n  override onPopState(fn: LocationChangeListener): void {\n    this._removeListenerFns.push(\n      this._platformLocation.onPopState(fn),\n      this._platformLocation.onHashChange(fn),\n    );\n  }\n\n  override getBaseHref(): string {\n    return this._baseHref;\n  }\n\n  override path(includeHash: boolean = false): string {\n    // the hash value is always prefixed with a `#`\n    // and if it is empty then it will stay empty\n    const path = this._platformLocation.hash ?? '#';\n\n    return path.length > 0 ? path.substring(1) : path;\n  }\n\n  override prepareExternalUrl(internal: string): string {\n    const url = joinWithSlash(this._baseHref, internal);\n    return url.length > 0 ? '#' + url : url;\n  }\n\n  override pushState(state: any, title: string, path: string, queryParams: string) {\n    const url =\n      this.prepareExternalUrl(path + normalizeQueryParams(queryParams)) ||\n      this._platformLocation.pathname;\n    this._platformLocation.pushState(state, title, url);\n  }\n\n  override replaceState(state: any, title: string, path: string, queryParams: string) {\n    const url =\n      this.prepareExternalUrl(path + normalizeQueryParams(queryParams)) ||\n      this._platformLocation.pathname;\n    this._platformLocation.replaceState(state, title, url);\n  }\n\n  override forward(): void {\n    this._platformLocation.forward();\n  }\n\n  override back(): void {\n    this._platformLocation.back();\n  }\n\n  override getState(): unknown {\n    return this._platformLocation.getState();\n  }\n\n  override historyGo(relativePosition: number = 0): void {\n    this._platformLocation.historyGo?.(relativePosition);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nexport type CurrenciesSymbols = [string] | [string | undefined, string];\n\n/** @internal */\nexport const CURRENCIES_EN: {[code: string]: CurrenciesSymbols | [string | undefined, string | undefined, number]} = {\"ADP\":[undefined,undefined,0],\"AFN\":[undefined,\"؋\",0],\"ALL\":[undefined,undefined,0],\"AMD\":[undefined,\"֏\",2],\"AOA\":[undefined,\"Kz\"],\"ARS\":[undefined,\"$\"],\"AUD\":[\"A$\",\"$\"],\"AZN\":[undefined,\"₼\"],\"BAM\":[undefined,\"KM\"],\"BBD\":[undefined,\"$\"],\"BDT\":[undefined,\"৳\"],\"BHD\":[undefined,undefined,3],\"BIF\":[undefined,undefined,0],\"BMD\":[undefined,\"$\"],\"BND\":[undefined,\"$\"],\"BOB\":[undefined,\"Bs\"],\"BRL\":[\"R$\"],\"BSD\":[undefined,\"$\"],\"BWP\":[undefined,\"P\"],\"BYN\":[undefined,undefined,2],\"BYR\":[undefined,undefined,0],\"BZD\":[undefined,\"$\"],\"CAD\":[\"CA$\",\"$\",2],\"CHF\":[undefined,undefined,2],\"CLF\":[undefined,undefined,4],\"CLP\":[undefined,\"$\",0],\"CNY\":[\"CN¥\",\"¥\"],\"COP\":[undefined,\"$\",2],\"CRC\":[undefined,\"₡\",2],\"CUC\":[undefined,\"$\"],\"CUP\":[undefined,\"$\"],\"CZK\":[undefined,\"Kč\",2],\"DJF\":[undefined,undefined,0],\"DKK\":[undefined,\"kr\",2],\"DOP\":[undefined,\"$\"],\"EGP\":[undefined,\"E£\"],\"ESP\":[undefined,\"₧\",0],\"EUR\":[\"€\"],\"FJD\":[undefined,\"$\"],\"FKP\":[undefined,\"£\"],\"GBP\":[\"£\"],\"GEL\":[undefined,\"₾\"],\"GHS\":[undefined,\"GH₵\"],\"GIP\":[undefined,\"£\"],\"GNF\":[undefined,\"FG\",0],\"GTQ\":[undefined,\"Q\"],\"GYD\":[undefined,\"$\",2],\"HKD\":[\"HK$\",\"$\"],\"HNL\":[undefined,\"L\"],\"HRK\":[undefined,\"kn\"],\"HUF\":[undefined,\"Ft\",2],\"IDR\":[undefined,\"Rp\",2],\"ILS\":[\"₪\"],\"INR\":[\"₹\"],\"IQD\":[undefined,undefined,0],\"IRR\":[undefined,undefined,0],\"ISK\":[undefined,\"kr\",0],\"ITL\":[undefined,undefined,0],\"JMD\":[undefined,\"$\"],\"JOD\":[undefined,undefined,3],\"JPY\":[\"¥\",undefined,0],\"KHR\":[undefined,\"៛\"],\"KMF\":[undefined,\"CF\",0],\"KPW\":[undefined,\"₩\",0],\"KRW\":[\"₩\",undefined,0],\"KWD\":[undefined,undefined,3],\"KYD\":[undefined,\"$\"],\"KZT\":[undefined,\"₸\"],\"LAK\":[undefined,\"₭\",0],\"LBP\":[undefined,\"L£\",0],\"LKR\":[undefined,\"Rs\"],\"LRD\":[undefined,\"$\"],\"LTL\":[undefined,\"Lt\"],\"LUF\":[undefined,undefined,0],\"LVL\":[undefined,\"Ls\"],\"LYD\":[undefined,undefined,3],\"MGA\":[undefined,\"Ar\",0],\"MGF\":[undefined,undefined,0],\"MMK\":[undefined,\"K\",0],\"MNT\":[undefined,\"₮\",2],\"MRO\":[undefined,undefined,0],\"MUR\":[undefined,\"Rs\",2],\"MXN\":[\"MX$\",\"$\"],\"MYR\":[undefined,\"RM\"],\"NAD\":[undefined,\"$\"],\"NGN\":[undefined,\"₦\"],\"NIO\":[undefined,\"C$\"],\"NOK\":[undefined,\"kr\",2],\"NPR\":[undefined,\"Rs\"],\"NZD\":[\"NZ$\",\"$\"],\"OMR\":[undefined,undefined,3],\"PHP\":[\"₱\"],\"PKR\":[undefined,\"Rs\",2],\"PLN\":[undefined,\"zł\"],\"PYG\":[undefined,\"₲\",0],\"RON\":[undefined,\"lei\"],\"RSD\":[undefined,undefined,0],\"RUB\":[undefined,\"₽\"],\"RWF\":[undefined,\"RF\",0],\"SBD\":[undefined,\"$\"],\"SEK\":[undefined,\"kr\",2],\"SGD\":[undefined,\"$\"],\"SHP\":[undefined,\"£\"],\"SLE\":[undefined,undefined,2],\"SLL\":[undefined,undefined,0],\"SOS\":[undefined,undefined,0],\"SRD\":[undefined,\"$\"],\"SSP\":[undefined,\"£\"],\"STD\":[undefined,undefined,0],\"STN\":[undefined,\"Db\"],\"SYP\":[undefined,\"£\",0],\"THB\":[undefined,\"฿\"],\"TMM\":[undefined,undefined,0],\"TND\":[undefined,undefined,3],\"TOP\":[undefined,\"T$\"],\"TRL\":[undefined,undefined,0],\"TRY\":[undefined,\"₺\"],\"TTD\":[undefined,\"$\"],\"TWD\":[\"NT$\",\"$\",2],\"TZS\":[undefined,undefined,2],\"UAH\":[undefined,\"₴\"],\"UGX\":[undefined,undefined,0],\"USD\":[\"$\"],\"UYI\":[undefined,undefined,0],\"UYU\":[undefined,\"$\"],\"UYW\":[undefined,undefined,4],\"UZS\":[undefined,undefined,2],\"VEF\":[undefined,\"Bs\",2],\"VND\":[\"₫\",undefined,0],\"VUV\":[undefined,undefined,0],\"XAF\":[\"FCFA\",undefined,0],\"XCD\":[\"EC$\",\"$\"],\"XOF\":[\"F CFA\",undefined,0],\"XPF\":[\"CFPF\",undefined,0],\"XXX\":[\"¤\"],\"YER\":[undefined,undefined,0],\"ZAR\":[undefined,\"R\"],\"ZMK\":[undefined,undefined,0],\"ZMW\":[undefined,\"ZK\"],\"ZWD\":[undefined,undefined,0]};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ɵCurrencyIndex,\n  ɵExtraLocaleDataIndex,\n  ɵfindLocaleData,\n  ɵgetLocaleCurrencyCode,\n  ɵgetLocalePluralCase,\n  ɵLocaleDataIndex,\n  ɵRuntimeError as RuntimeError,\n} from '@angular/core';\n\nimport {CURRENCIES_EN, CurrenciesSymbols} from './currencies';\nimport {RuntimeErrorCode} from '../errors';\n\n/**\n * Format styles that can be used to represent numbers.\n * @see {@link getLocaleNumberFormat}\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated `getLocaleNumberFormat` is deprecated\n */\nexport enum NumberFormatStyle {\n  Decimal,\n  Percent,\n  Currency,\n  Scientific,\n}\n\n/**\n * Plurality cases used for translating plurals to different languages.\n *\n * @see {@link NgPlural}\n * @see {@link NgPluralCase}\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated `getLocalePluralCase` is deprecated\n */\nexport enum Plural {\n  Zero = 0,\n  One = 1,\n  Two = 2,\n  Few = 3,\n  Many = 4,\n  Other = 5,\n}\n\n/**\n * Context-dependant translation forms for strings.\n * Typically the standalone version is for the nominative form of the word,\n * and the format version is used for the genitive case.\n * @see [CLDR website](http://cldr.unicode.org/translation/date-time-1/date-time#TOC-Standalone-vs.-Format-Styles)\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated locale data getters are deprecated\n */\nexport enum FormStyle {\n  Format,\n  Standalone,\n}\n\n/**\n * String widths available for translations.\n * The specific character widths are locale-specific.\n * Examples are given for the word \"Sunday\" in English.\n *\n * @publicApi\n *\n * @deprecated locale data getters are deprecated\n */\nexport enum TranslationWidth {\n  /** 1 character for `en-US`. For example: 'S' */\n  Narrow,\n  /** 3 characters for `en-US`. For example: 'Sun' */\n  Abbreviated,\n  /** Full length for `en-US`. For example: \"Sunday\" */\n  Wide,\n  /** 2 characters for `en-US`, For example: \"Su\" */\n  Short,\n}\n\n/**\n * String widths available for date-time formats.\n * The specific character widths are locale-specific.\n * Examples are given for `en-US`.\n *\n * @see {@link getLocaleDateFormat}\n * @see {@link getLocaleTimeFormat}\n * @see {@link getLocaleDateTimeFormat}\n * @see [Internationalization (i18n) Guide](guide/i18n)\n * @publicApi\n *\n * @deprecated 18.0\n * Date locale data getters are deprecated\n */\nexport enum FormatWidth {\n  /**\n   * For `en-US`, `'M/d/yy, h:mm a'`\n   * (Example: `6/15/15, 9:03 AM`)\n   */\n  Short,\n  /**\n   * For `en-US`, `'MMM d, y, h:mm:ss a'`\n   * (Example: `Jun 15, 2015, 9:03:01 AM`)\n   */\n  Medium,\n  /**\n   * For `en-US`, `'MMMM d, y, h:mm:ss a z'`\n   * (Example: `June 15, 2015 at 9:03:01 AM GMT+1`)\n   */\n  Long,\n  /**\n   * For `en-US`, `'EEEE, MMMM d, y, h:mm:ss a zzzz'`\n   * (Example: `Monday, June 15, 2015 at 9:03:01 AM GMT+01:00`)\n   */\n  Full,\n}\n\n// This needs to be an object literal, rather than an enum, because TypeScript 5.4+\n// doesn't allow numeric keys and we have `Infinity` and `NaN`.\n/**\n * Symbols that can be used to replace placeholders in number patterns.\n * Examples are based on `en-US` values.\n *\n * @see {@link getLocaleNumberSymbol}\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated `getLocaleNumberSymbol` is deprecated\n *\n * @object-literal-as-enum\n */\nexport const NumberSymbol = {\n  /**\n   * Decimal separator.\n   * For `en-US`, the dot character.\n   * Example: 2,345`.`67\n   */\n  Decimal: 0,\n  /**\n   * Grouping separator, typically for thousands.\n   * For `en-US`, the comma character.\n   * Example: 2`,`345.67\n   */\n  Group: 1,\n  /**\n   * List-item separator.\n   * Example: \"one, two, and three\"\n   */\n  List: 2,\n  /**\n   * Sign for percentage (out of 100).\n   * Example: 23.4%\n   */\n  PercentSign: 3,\n  /**\n   * Sign for positive numbers.\n   * Example: +23\n   */\n  PlusSign: 4,\n  /**\n   * Sign for negative numbers.\n   * Example: -23\n   */\n  MinusSign: 5,\n  /**\n   * Computer notation for exponential value (n times a power of 10).\n   * Example: 1.2E3\n   */\n  Exponential: 6,\n  /**\n   * Human-readable format of exponential.\n   * Example: 1.2x103\n   */\n  SuperscriptingExponent: 7,\n  /**\n   * Sign for permille (out of 1000).\n   * Example: 23.4‰\n   */\n  PerMille: 8,\n  /**\n   * Infinity, can be used with plus and minus.\n   * Example: ∞, +∞, -∞\n   */\n  Infinity: 9,\n  /**\n   * Not a number.\n   * Example: NaN\n   */\n  NaN: 10,\n  /**\n   * Symbol used between time units.\n   * Example: 10:52\n   */\n  TimeSeparator: 11,\n  /**\n   * Decimal separator for currency values (fallback to `Decimal`).\n   * Example: $2,345.67\n   */\n  CurrencyDecimal: 12,\n  /**\n   * Group separator for currency values (fallback to `Group`).\n   * Example: $2,345.67\n   */\n  CurrencyGroup: 13,\n} as const;\n\nexport type NumberSymbol = (typeof NumberSymbol)[keyof typeof NumberSymbol];\n\n/**\n * The value for each day of the week, based on the `en-US` locale\n *\n * @publicApi\n *\n * @deprecated Week locale getters are deprecated\n */\nexport enum WeekDay {\n  Sunday = 0,\n  Monday,\n  Tuesday,\n  Wednesday,\n  Thursday,\n  Friday,\n  Saturday,\n}\n\n/**\n * Retrieves the locale ID from the currently loaded locale.\n * The loaded locale could be, for example, a global one rather than a regional one.\n * @param locale A locale code, such as `fr-FR`.\n * @returns The locale code. For example, `fr`.\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated Angular recommends relying on the `Intl` API for i18n.\n * This function serves no purpose when relying on the `Intl` API.\n */\nexport function getLocaleId(locale: string): string {\n  return ɵfindLocaleData(locale)[ɵLocaleDataIndex.LocaleId];\n}\n\n/**\n * Retrieves day period strings for the given locale.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param formStyle The required grammatical form.\n * @param width The required character width.\n * @returns An array of localized period strings. For example, `[AM, PM]` for `en-US`.\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated Angular recommends relying on the `Intl` API for i18n.\n * Use `Intl.DateTimeFormat` for date formating instead.\n */\nexport function getLocaleDayPeriods(\n  locale: string,\n  formStyle: FormStyle,\n  width: TranslationWidth,\n): Readonly<[string, string]> {\n  const data = ɵfindLocaleData(locale);\n  const amPmData = <[string, string][][]>[\n    data[ɵLocaleDataIndex.DayPeriodsFormat],\n    data[ɵLocaleDataIndex.DayPeriodsStandalone],\n  ];\n  const amPm = getLastDefinedValue(amPmData, formStyle);\n  return getLastDefinedValue(amPm, width);\n}\n\n/**\n * Retrieves days of the week for the given locale, using the Gregorian calendar.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param formStyle The required grammatical form.\n * @param width The required character width.\n * @returns An array of localized name strings.\n * For example,`[Sunday, Monday, ... Saturday]` for `en-US`.\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated Angular recommends relying on the `Intl` API for i18n.\n * Use `Intl.DateTimeFormat` for date formating instead.\n */\nexport function getLocaleDayNames(\n  locale: string,\n  formStyle: FormStyle,\n  width: TranslationWidth,\n): ReadonlyArray<string> {\n  const data = ɵfindLocaleData(locale);\n  const daysData = <string[][][]>[\n    data[ɵLocaleDataIndex.DaysFormat],\n    data[ɵLocaleDataIndex.DaysStandalone],\n  ];\n  const days = getLastDefinedValue(daysData, formStyle);\n  return getLastDefinedValue(days, width);\n}\n\n/**\n * Retrieves months of the year for the given locale, using the Gregorian calendar.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param formStyle The required grammatical form.\n * @param width The required character width.\n * @returns An array of localized name strings.\n * For example,  `[January, February, ...]` for `en-US`.\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated Angular recommends relying on the `Intl` API for i18n.\n * Use `Intl.DateTimeFormat` for date formating instead.\n */\nexport function getLocaleMonthNames(\n  locale: string,\n  formStyle: FormStyle,\n  width: TranslationWidth,\n): ReadonlyArray<string> {\n  const data = ɵfindLocaleData(locale);\n  const monthsData = <string[][][]>[\n    data[ɵLocaleDataIndex.MonthsFormat],\n    data[ɵLocaleDataIndex.MonthsStandalone],\n  ];\n  const months = getLastDefinedValue(monthsData, formStyle);\n  return getLastDefinedValue(months, width);\n}\n\n/**\n * Retrieves Gregorian-calendar eras for the given locale.\n * @param locale A locale code for the locale format rules to use.\n * @param width The required character width.\n\n * @returns An array of localized era strings.\n * For example, `[AD, BC]` for `en-US`.\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated Angular recommends relying on the `Intl` API for i18n.\n * Use `Intl.DateTimeFormat` for date formating instead.\n */\nexport function getLocaleEraNames(\n  locale: string,\n  width: TranslationWidth,\n): Readonly<[string, string]> {\n  const data = ɵfindLocaleData(locale);\n  const erasData = <[string, string][]>data[ɵLocaleDataIndex.Eras];\n  return getLastDefinedValue(erasData, width);\n}\n\n/**\n * Retrieves the first day of the week for the given locale.\n *\n * @param locale A locale code for the locale format rules to use.\n * @returns A day index number, using the 0-based week-day index for `en-US`\n * (Sunday = 0, Monday = 1, ...).\n * For example, for `fr-FR`, returns 1 to indicate that the first day is Monday.\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated Angular recommends relying on the `Intl` API for i18n.\n * Intl's [`getWeekInfo`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/getWeekInfo) has partial support (Chromium M99 & Safari 17).\n * You may want to rely on the following alternatives:\n * - Libraries like [`Luxon`](https://moment.github.io/luxon/#/) rely on `Intl` but fallback on the ISO 8601 definition (monday) if `getWeekInfo` is not supported.\n * - Other librairies like [`date-fns`](https://date-fns.org/), [`day.js`](https://day.js.org/en/) or [`weekstart`](https://www.npmjs.com/package/weekstart) library provide their own locale based data for the first day of the week.\n */\nexport function getLocaleFirstDayOfWeek(locale: string): WeekDay {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.FirstDayOfWeek];\n}\n\n/**\n * Range of week days that are considered the week-end for the given locale.\n *\n * @param locale A locale code for the locale format rules to use.\n * @returns The range of day values, `[startDay, endDay]`.\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated Angular recommends relying on the `Intl` API for i18n.\n * Intl's [`getWeekInfo`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/getWeekInfo) has partial support (Chromium M99 & Safari 17).\n * Libraries like [`Luxon`](https://moment.github.io/luxon/#/) rely on `Intl` but fallback on the ISO 8601 definition (Saturday+Sunday) if `getWeekInfo` is not supported .\n */\nexport function getLocaleWeekEndRange(locale: string): [WeekDay, WeekDay] {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.WeekendRange];\n}\n\n/**\n * Retrieves a localized date-value formatting string.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param width The format type.\n * @returns The localized formatting string.\n * @see {@link FormatWidth}\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated Angular recommends relying on the `Intl` API for i18n.\n * Use `Intl.DateTimeFormat` for date formating instead.\n */\nexport function getLocaleDateFormat(locale: string, width: FormatWidth): string {\n  const data = ɵfindLocaleData(locale);\n  return getLastDefinedValue(data[ɵLocaleDataIndex.DateFormat], width);\n}\n\n/**\n * Retrieves a localized time-value formatting string.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param width The format type.\n * @returns The localized formatting string.\n * @see {@link FormatWidth}\n * @see [Internationalization (i18n) Guide](guide/i18n)\n\n * @publicApi\n * @deprecated Angular recommends relying on the `Intl` API for i18n.\n * Use `Intl.DateTimeFormat` for date formating instead.\n */\nexport function getLocaleTimeFormat(locale: string, width: FormatWidth): string {\n  const data = ɵfindLocaleData(locale);\n  return getLastDefinedValue(data[ɵLocaleDataIndex.TimeFormat], width);\n}\n\n/**\n * Retrieves a localized date-time formatting string.\n *\n * @param locale A locale code for the locale format rules to use.\n * @param width The format type.\n * @returns The localized formatting string.\n * @see {@link FormatWidth}\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated Angular recommends relying on the `Intl` API for i18n.\n * Use `Intl.DateTimeFormat` for date formating instead.\n */\nexport function getLocaleDateTimeFormat(locale: string, width: FormatWidth): string {\n  const data = ɵfindLocaleData(locale);\n  const dateTimeFormatData = <string[]>data[ɵLocaleDataIndex.DateTimeFormat];\n  return getLastDefinedValue(dateTimeFormatData, width);\n}\n\n/**\n * Retrieves a localized number symbol that can be used to replace placeholders in number formats.\n * @param locale The locale code.\n * @param symbol The symbol to localize. Must be one of `NumberSymbol`.\n * @returns The character for the localized symbol.\n * @see {@link NumberSymbol}\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated Angular recommends relying on the `Intl` API for i18n.\n * Use `Intl.NumberFormat` to format numbers instead.\n */\nexport function getLocaleNumberSymbol(locale: string, symbol: NumberSymbol): string {\n  const data = ɵfindLocaleData(locale);\n  const res = data[ɵLocaleDataIndex.NumberSymbols][symbol];\n  if (typeof res === 'undefined') {\n    if (symbol === NumberSymbol.CurrencyDecimal) {\n      return data[ɵLocaleDataIndex.NumberSymbols][NumberSymbol.Decimal];\n    } else if (symbol === NumberSymbol.CurrencyGroup) {\n      return data[ɵLocaleDataIndex.NumberSymbols][NumberSymbol.Group];\n    }\n  }\n  return res;\n}\n\n/**\n * Retrieves a number format for a given locale.\n *\n * Numbers are formatted using patterns, like `#,###.00`. For example, the pattern `#,###.00`\n * when used to format the number 12345.678 could result in \"12'345,678\". That would happen if the\n * grouping separator for your language is an apostrophe, and the decimal separator is a comma.\n *\n * <b>Important:</b> The characters `.` `,` `0` `#` (and others below) are special placeholders\n * that stand for the decimal separator, and so on, and are NOT real characters.\n * You must NOT \"translate\" the placeholders. For example, don't change `.` to `,` even though in\n * your language the decimal point is written with a comma. The symbols should be replaced by the\n * local equivalents, using the appropriate `NumberSymbol` for your language.\n *\n * Here are the special characters used in number patterns:\n *\n * | Symbol | Meaning |\n * |--------|---------|\n * | . | Replaced automatically by the character used for the decimal point. |\n * | , | Replaced by the \"grouping\" (thousands) separator. |\n * | 0 | Replaced by a digit (or zero if there aren't enough digits). |\n * | # | Replaced by a digit (or nothing if there aren't enough). |\n * | ¤ | Replaced by a currency symbol, such as $ or USD. |\n * | % | Marks a percent format. The % symbol may change position, but must be retained. |\n * | E | Marks a scientific format. The E symbol may change position, but must be retained. |\n * | ' | Special characters used as literal characters are quoted with ASCII single quotes. |\n *\n * @param locale A locale code for the locale format rules to use.\n * @param type The type of numeric value to be formatted (such as `Decimal` or `Currency`.)\n * @returns The localized format string.\n * @see {@link NumberFormatStyle}\n * @see [CLDR website](http://cldr.unicode.org/translation/number-patterns)\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated Angular recommends relying on the `Intl` API for i18n.\n * Let `Intl.NumberFormat` determine the number format instead\n */\nexport function getLocaleNumberFormat(locale: string, type: NumberFormatStyle): string {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.NumberFormats][type];\n}\n\n/**\n * Retrieves the symbol used to represent the currency for the main country\n * corresponding to a given locale. For example, '$' for `en-US`.\n *\n * @param locale A locale code for the locale format rules to use.\n * @returns The localized symbol character,\n * or `null` if the main country cannot be determined.\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated Use the `Intl` API to format a currency with from currency code\n */\nexport function getLocaleCurrencySymbol(locale: string): string | null {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.CurrencySymbol] || null;\n}\n\n/**\n * Retrieves the name of the currency for the main country corresponding\n * to a given locale. For example, 'US Dollar' for `en-US`.\n * @param locale A locale code for the locale format rules to use.\n * @returns The currency name,\n * or `null` if the main country cannot be determined.\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated Use the `Intl` API to format a currency with from currency code\n */\nexport function getLocaleCurrencyName(locale: string): string | null {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.CurrencyName] || null;\n}\n\n/**\n * Retrieves the default currency code for the given locale.\n *\n * The default is defined as the first currency which is still in use.\n *\n * @param locale The code of the locale whose currency code we want.\n * @returns The code of the default currency for the given locale.\n *\n * @publicApi\n *\n * @deprecated We recommend you create a map of locale to ISO 4217 currency codes.\n * Time relative currency data is provided by the CLDR project. See https://www.unicode.org/cldr/charts/44/supplemental/detailed_territory_currency_information.html\n */\nexport function getLocaleCurrencyCode(locale: string): string | null {\n  return ɵgetLocaleCurrencyCode(locale);\n}\n\n/**\n * Retrieves the currency values for a given locale.\n * @param locale A locale code for the locale format rules to use.\n * @returns The currency values.\n * @see [Internationalization (i18n) Guide](guide/i18n)\n */\nfunction getLocaleCurrencies(locale: string): {[code: string]: CurrenciesSymbols} {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.Currencies];\n}\n\n/**\n * @publicApi\n *\n * @deprecated Angular recommends relying on the `Intl` API for i18n.\n * Use `Intl.PluralRules` instead\n */\nexport const getLocalePluralCase: (locale: string) => (value: number) => Plural =\n  ɵgetLocalePluralCase;\n\nfunction checkFullData(data: any) {\n  if (!data[ɵLocaleDataIndex.ExtraData]) {\n    throw new RuntimeError(\n      RuntimeErrorCode.MISSING_EXTRA_LOCALE_DATA_FOR_LOCALE,\n      ngDevMode &&\n        `Missing extra locale data for the locale \"${\n          data[ɵLocaleDataIndex.LocaleId]\n        }\". Use \"registerLocaleData\" to load new data. See the \"I18n guide\" on angular.io to know more.`,\n    );\n  }\n}\n\n/**\n * Retrieves locale-specific rules used to determine which day period to use\n * when more than one period is defined for a locale.\n *\n * There is a rule for each defined day period. The\n * first rule is applied to the first day period and so on.\n * Fall back to AM/PM when no rules are available.\n *\n * A rule can specify a period as time range, or as a single time value.\n *\n * This functionality is only available when you have loaded the full locale data.\n * See the [\"I18n guide\"](guide/i18n/format-data-locale).\n *\n * @param locale A locale code for the locale format rules to use.\n * @returns The rules for the locale, a single time value or array of *from-time, to-time*,\n * or null if no periods are available.\n *\n * @see {@link getLocaleExtraDayPeriods}\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated Angular recommends relying on the `Intl` API for i18n.\n * Let `Intl.DateTimeFormat` determine the day period instead.\n */\nexport function getLocaleExtraDayPeriodRules(locale: string): (Time | [Time, Time])[] {\n  const data = ɵfindLocaleData(locale);\n  checkFullData(data);\n  const rules = data[ɵLocaleDataIndex.ExtraData][ɵExtraLocaleDataIndex.ExtraDayPeriodsRules] || [];\n  return rules.map((rule: string | [string, string]) => {\n    if (typeof rule === 'string') {\n      return extractTime(rule);\n    }\n    return [extractTime(rule[0]), extractTime(rule[1])];\n  });\n}\n\n/**\n * Retrieves locale-specific day periods, which indicate roughly how a day is broken up\n * in different languages.\n * For example, for `en-US`, periods are morning, noon, afternoon, evening, and midnight.\n *\n * This functionality is only available when you have loaded the full locale data.\n * See the [\"I18n guide\"](guide/i18n/format-data-locale).\n *\n * @param locale A locale code for the locale format rules to use.\n * @param formStyle The required grammatical form.\n * @param width The required character width.\n * @returns The translated day-period strings.\n * @see {@link getLocaleExtraDayPeriodRules}\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated Angular recommends relying on the `Intl` API for i18n.\n * To extract a day period use `Intl.DateTimeFormat` with the `dayPeriod` option instead.\n */\nexport function getLocaleExtraDayPeriods(\n  locale: string,\n  formStyle: FormStyle,\n  width: TranslationWidth,\n): string[] {\n  const data = ɵfindLocaleData(locale);\n  checkFullData(data);\n  const dayPeriodsData = <string[][][]>[\n    data[ɵLocaleDataIndex.ExtraData][ɵExtraLocaleDataIndex.ExtraDayPeriodFormats],\n    data[ɵLocaleDataIndex.ExtraData][ɵExtraLocaleDataIndex.ExtraDayPeriodStandalone],\n  ];\n  const dayPeriods = getLastDefinedValue(dayPeriodsData, formStyle) || [];\n  return getLastDefinedValue(dayPeriods, width) || [];\n}\n\n/**\n * Retrieves the writing direction of a specified locale\n * @param locale A locale code for the locale format rules to use.\n * @publicApi\n * @returns 'rtl' or 'ltr'\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @deprecated Angular recommends relying on the `Intl` API for i18n.\n * For dates and numbers, let `Intl.DateTimeFormat()` and `Intl.NumberFormat()` determine the writing direction.\n * The `Intl` alternative [`getTextInfo`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/getTextInfo).\n * has only partial support (Chromium M99 & Safari 17).\n * 3rd party alternatives like [`rtl-detect`](https://www.npmjs.com/package/rtl-detect) can work around this issue.\n */\nexport function getLocaleDirection(locale: string): 'ltr' | 'rtl' {\n  const data = ɵfindLocaleData(locale);\n  return data[ɵLocaleDataIndex.Directionality];\n}\n\n/**\n * Retrieves the first value that is defined in an array, going backwards from an index position.\n *\n * To avoid repeating the same data (as when the \"format\" and \"standalone\" forms are the same)\n * add the first value to the locale data arrays, and add other values only if they are different.\n *\n * @param data The data array to retrieve from.\n * @param index A 0-based index into the array to start from.\n * @returns The value immediately before the given index position.\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n */\nfunction getLastDefinedValue<T>(data: T[], index: number): T {\n  for (let i = index; i > -1; i--) {\n    if (typeof data[i] !== 'undefined') {\n      return data[i];\n    }\n  }\n  throw new RuntimeError(\n    RuntimeErrorCode.LOCALE_DATA_UNDEFINED,\n    ngDevMode && 'Locale data API: locale data undefined',\n  );\n}\n\n/**\n * Represents a time value with hours and minutes.\n *\n * @publicApi\n *\n * @deprecated Locale date getters are deprecated\n */\nexport type Time = {\n  hours: number;\n  minutes: number;\n};\n\n/**\n * Extracts the hours and minutes from a string like \"15:45\"\n */\nfunction extractTime(time: string): Time {\n  const [h, m] = time.split(':');\n  return {hours: +h, minutes: +m};\n}\n\n/**\n * Retrieves the currency symbol for a given currency code.\n *\n * For example, for the default `en-US` locale, the code `USD` can\n * be represented by the narrow symbol `$` or the wide symbol `US$`.\n *\n * @param code The currency code.\n * @param format The format, `wide` or `narrow`.\n * @param locale A locale code for the locale format rules to use.\n *\n * @returns The symbol, or the currency code if no symbol is available.\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated Angular recommends relying on the `Intl` API for i18n.\n * You can use `Intl.NumberFormat().formatToParts()` to extract the currency symbol.\n * For example: `Intl.NumberFormat('en', {style:'currency', currency: 'USD'}).formatToParts().find(part => part.type === 'currency').value`\n * returns `$` for USD currency code in the `en` locale.\n * Note: `US$` is a currency symbol for the `en-ca` locale but not the `en-us` locale.\n */\nexport function getCurrencySymbol(code: string, format: 'wide' | 'narrow', locale = 'en'): string {\n  const currency = getLocaleCurrencies(locale)[code] || CURRENCIES_EN[code] || [];\n  const symbolNarrow = currency[ɵCurrencyIndex.SymbolNarrow];\n\n  if (format === 'narrow' && typeof symbolNarrow === 'string') {\n    return symbolNarrow;\n  }\n\n  return currency[ɵCurrencyIndex.Symbol] || code;\n}\n\n// Most currencies have cents, that's why the default is 2\nconst DEFAULT_NB_OF_CURRENCY_DIGITS = 2;\n\n/**\n * Reports the number of decimal digits for a given currency.\n * The value depends upon the presence of cents in that particular currency.\n *\n * @param code The currency code.\n * @returns The number of decimal digits, typically 0 or 2.\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n *\n * @deprecated Angular recommends relying on the `Intl` API for i18n.\n * This function should not be used anymore. Let `Intl.NumberFormat` determine the number of digits to display for the currency\n */\nexport function getNumberOfCurrencyDigits(code: string): number {\n  let digits;\n  const currency = CURRENCIES_EN[code];\n  if (currency) {\n    digits = currency[ɵCurrencyIndex.NbOfDigits];\n  }\n  return typeof digits === 'number' ? digits : DEFAULT_NB_OF_CURRENCY_DIGITS;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ɵRuntimeError as RuntimeError,\n  ɵformatRuntimeError as formatRuntimeError,\n} from '@angular/core';\n\nimport {\n  FormatWidth,\n  FormStyle,\n  getLocaleDateFormat,\n  getLocaleDateTimeFormat,\n  getLocaleDayNames,\n  getLocaleDayPeriods,\n  getLocaleEraNames,\n  getLocaleExtraDayPeriodRules,\n  getLocaleExtraDayPeriods,\n  getLocaleId,\n  getLocaleMonthNames,\n  getLocaleNumberSymbol,\n  getLocaleTimeFormat,\n  NumberSymbol,\n  TranslationWidth,\n} from './locale_data_api';\nimport {RuntimeErrorCode} from '../errors';\n\nexport const ISO8601_DATE_REGEX =\n  /^(\\d{4,})-?(\\d\\d)-?(\\d\\d)(?:T(\\d\\d)(?::?(\\d\\d)(?::?(\\d\\d)(?:\\.(\\d+))?)?)?(Z|([+-])(\\d\\d):?(\\d\\d))?)?$/;\n//    1        2       3         4          5          6          7          8  9     10      11\nconst NAMED_FORMATS: {[localeId: string]: {[format: string]: string}} = {};\nconst DATE_FORMATS_SPLIT =\n  /((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\\s\\S]*)/;\n\nconst enum ZoneWidth {\n  Short,\n  ShortGMT,\n  Long,\n  Extended,\n}\n\nconst enum DateType {\n  FullYear,\n  Month,\n  Date,\n  Hours,\n  Minutes,\n  Seconds,\n  FractionalSeconds,\n  Day,\n}\n\nconst enum TranslationType {\n  DayPeriods,\n  Days,\n  Months,\n  Eras,\n}\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a date according to locale rules.\n *\n * @param value The date to format, as a Date, or a number (milliseconds since UTC epoch)\n * or an [ISO date-time string](https://www.w3.org/TR/NOTE-datetime).\n * @param format The date-time components to include. See `DatePipe` for details.\n * @param locale A locale code for the locale format rules to use.\n * @param timezone The time zone. A time zone offset from GMT (such as `'+0430'`).\n * If not specified, uses host system settings.\n *\n * @returns The formatted date string.\n *\n * @see {@link DatePipe}\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n */\nexport function formatDate(\n  value: string | number | Date,\n  format: string,\n  locale: string,\n  timezone?: string,\n): string {\n  let date = toDate(value);\n  const namedFormat = getNamedFormat(locale, format);\n  format = namedFormat || format;\n\n  let parts: string[] = [];\n  let match;\n  while (format) {\n    match = DATE_FORMATS_SPLIT.exec(format);\n    if (match) {\n      parts = parts.concat(match.slice(1));\n      const part = parts.pop();\n      if (!part) {\n        break;\n      }\n      format = part;\n    } else {\n      parts.push(format);\n      break;\n    }\n  }\n\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    assertValidDateFormat(parts);\n  }\n\n  let dateTimezoneOffset = date.getTimezoneOffset();\n  if (timezone) {\n    dateTimezoneOffset = timezoneToOffset(timezone, dateTimezoneOffset);\n    date = convertTimezoneToLocal(date, timezone, true);\n  }\n\n  let text = '';\n  parts.forEach((value) => {\n    const dateFormatter = getDateFormatter(value);\n    text += dateFormatter\n      ? dateFormatter(date, locale, dateTimezoneOffset)\n      : value === \"''\"\n        ? \"'\"\n        : value.replace(/(^'|'$)/g, '').replace(/''/g, \"'\");\n  });\n\n  return text;\n}\n\n/**\n * Asserts that the given date format is free from common mistakes.  Throws an\n * error if one is found (except for the case of all \"Y\", in which case we just\n * log a warning).  This should only be called in development mode.\n */\nfunction assertValidDateFormat(parts: string[]) {\n  if (parts.some((part) => /^Y+$/.test(part)) && !parts.some((part) => /^w+$/.test(part))) {\n    // \"Y\" indicates \"week-based year\", which differs from the actual calendar\n    // year for a few days around Jan 1 most years.  Unless \"w\" is also\n    // present (e.g. a date like \"2024-W52\") this is likely a mistake.  Users\n    // probably meant \"y\" instead.\n    const message = `Suspicious use of week-based year \"Y\" in date pattern \"${parts.join(\n      '',\n    )}\". Did you mean to use calendar year \"y\" instead?`;\n    if (parts.length === 1) {\n      // NOTE: allow \"YYYY\" with just a warning, since it's used in tests.\n      console.error(formatRuntimeError(RuntimeErrorCode.SUSPICIOUS_DATE_FORMAT, message));\n    } else {\n      throw new RuntimeError(RuntimeErrorCode.SUSPICIOUS_DATE_FORMAT, message);\n    }\n  }\n}\n\n/**\n * Create a new Date object with the given date value, and the time set to midnight.\n *\n * We cannot use `new Date(year, month, date)` because it maps years between 0 and 99 to 1900-1999.\n * See: https://github.com/angular/angular/issues/40377\n *\n * Note that this function returns a Date object whose time is midnight in the current locale's\n * timezone. In the future we might want to change this to be midnight in UTC, but this would be a\n * considerable breaking change.\n */\nfunction createDate(year: number, month: number, date: number): Date {\n  // The `newDate` is set to midnight (UTC) on January 1st 1970.\n  // - In PST this will be December 31st 1969 at 4pm.\n  // - In GMT this will be January 1st 1970 at 1am.\n  // Note that they even have different years, dates and months!\n  const newDate = new Date(0);\n\n  // `setFullYear()` allows years like 0001 to be set correctly. This function does not\n  // change the internal time of the date.\n  // Consider calling `setFullYear(2019, 8, 20)` (September 20, 2019).\n  // - In PST this will now be September 20, 2019 at 4pm\n  // - In GMT this will now be September 20, 2019 at 1am\n\n  newDate.setFullYear(year, month, date);\n  // We want the final date to be at local midnight, so we reset the time.\n  // - In PST this will now be September 20, 2019 at 12am\n  // - In GMT this will now be September 20, 2019 at 12am\n  newDate.setHours(0, 0, 0);\n\n  return newDate;\n}\n\nfunction getNamedFormat(locale: string, format: string): string {\n  const localeId = getLocaleId(locale);\n  NAMED_FORMATS[localeId] ??= {};\n\n  if (NAMED_FORMATS[localeId][format]) {\n    return NAMED_FORMATS[localeId][format];\n  }\n\n  let formatValue = '';\n  switch (format) {\n    case 'shortDate':\n      formatValue = getLocaleDateFormat(locale, FormatWidth.Short);\n      break;\n    case 'mediumDate':\n      formatValue = getLocaleDateFormat(locale, FormatWidth.Medium);\n      break;\n    case 'longDate':\n      formatValue = getLocaleDateFormat(locale, FormatWidth.Long);\n      break;\n    case 'fullDate':\n      formatValue = getLocaleDateFormat(locale, FormatWidth.Full);\n      break;\n    case 'shortTime':\n      formatValue = getLocaleTimeFormat(locale, FormatWidth.Short);\n      break;\n    case 'mediumTime':\n      formatValue = getLocaleTimeFormat(locale, FormatWidth.Medium);\n      break;\n    case 'longTime':\n      formatValue = getLocaleTimeFormat(locale, FormatWidth.Long);\n      break;\n    case 'fullTime':\n      formatValue = getLocaleTimeFormat(locale, FormatWidth.Full);\n      break;\n    case 'short':\n      const shortTime = getNamedFormat(locale, 'shortTime');\n      const shortDate = getNamedFormat(locale, 'shortDate');\n      formatValue = formatDateTime(getLocaleDateTimeFormat(locale, FormatWidth.Short), [\n        shortTime,\n        shortDate,\n      ]);\n      break;\n    case 'medium':\n      const mediumTime = getNamedFormat(locale, 'mediumTime');\n      const mediumDate = getNamedFormat(locale, 'mediumDate');\n      formatValue = formatDateTime(getLocaleDateTimeFormat(locale, FormatWidth.Medium), [\n        mediumTime,\n        mediumDate,\n      ]);\n      break;\n    case 'long':\n      const longTime = getNamedFormat(locale, 'longTime');\n      const longDate = getNamedFormat(locale, 'longDate');\n      formatValue = formatDateTime(getLocaleDateTimeFormat(locale, FormatWidth.Long), [\n        longTime,\n        longDate,\n      ]);\n      break;\n    case 'full':\n      const fullTime = getNamedFormat(locale, 'fullTime');\n      const fullDate = getNamedFormat(locale, 'fullDate');\n      formatValue = formatDateTime(getLocaleDateTimeFormat(locale, FormatWidth.Full), [\n        fullTime,\n        fullDate,\n      ]);\n      break;\n  }\n  if (formatValue) {\n    NAMED_FORMATS[localeId][format] = formatValue;\n  }\n  return formatValue;\n}\n\nfunction formatDateTime(str: string, opt_values: string[]) {\n  if (opt_values) {\n    str = str.replace(/\\{([^}]+)}/g, function (match, key) {\n      return opt_values != null && key in opt_values ? opt_values[key] : match;\n    });\n  }\n  return str;\n}\n\nfunction padNumber(\n  num: number,\n  digits: number,\n  minusSign = '-',\n  trim?: boolean,\n  negWrap?: boolean,\n): string {\n  let neg = '';\n  if (num < 0 || (negWrap && num <= 0)) {\n    if (negWrap) {\n      num = -num + 1;\n    } else {\n      num = -num;\n      neg = minusSign;\n    }\n  }\n  let strNum = String(num);\n  while (strNum.length < digits) {\n    strNum = '0' + strNum;\n  }\n  if (trim) {\n    strNum = strNum.slice(strNum.length - digits);\n  }\n  return neg + strNum;\n}\n\nfunction formatFractionalSeconds(milliseconds: number, digits: number): string {\n  const strMs = padNumber(milliseconds, 3);\n  return strMs.substring(0, digits);\n}\n\n/**\n * Returns a date formatter that transforms a date into its locale digit representation\n */\nfunction dateGetter(\n  name: DateType,\n  size: number,\n  offset: number = 0,\n  trim = false,\n  negWrap = false,\n): DateFormatter {\n  return function (date: Date, locale: string): string {\n    let part = getDatePart(name, date);\n    if (offset > 0 || part > -offset) {\n      part += offset;\n    }\n\n    if (name === DateType.Hours) {\n      if (part === 0 && offset === -12) {\n        part = 12;\n      }\n    } else if (name === DateType.FractionalSeconds) {\n      return formatFractionalSeconds(part, size);\n    }\n\n    const localeMinus = getLocaleNumberSymbol(locale, NumberSymbol.MinusSign);\n    return padNumber(part, size, localeMinus, trim, negWrap);\n  };\n}\n\nfunction getDatePart(part: DateType, date: Date): number {\n  switch (part) {\n    case DateType.FullYear:\n      return date.getFullYear();\n    case DateType.Month:\n      return date.getMonth();\n    case DateType.Date:\n      return date.getDate();\n    case DateType.Hours:\n      return date.getHours();\n    case DateType.Minutes:\n      return date.getMinutes();\n    case DateType.Seconds:\n      return date.getSeconds();\n    case DateType.FractionalSeconds:\n      return date.getMilliseconds();\n    case DateType.Day:\n      return date.getDay();\n    default:\n      throw new RuntimeError(\n        RuntimeErrorCode.UNKNOWN_DATE_TYPE_VALUE,\n        ngDevMode && `Unknown DateType value \"${part}\".`,\n      );\n  }\n}\n\n/**\n * Returns a date formatter that transforms a date into its locale string representation\n */\nfunction dateStrGetter(\n  name: TranslationType,\n  width: TranslationWidth,\n  form: FormStyle = FormStyle.Format,\n  extended = false,\n): DateFormatter {\n  return function (date: Date, locale: string): string {\n    return getDateTranslation(date, locale, name, width, form, extended);\n  };\n}\n\n/**\n * Returns the locale translation of a date for a given form, type and width\n */\nfunction getDateTranslation(\n  date: Date,\n  locale: string,\n  name: TranslationType,\n  width: TranslationWidth,\n  form: FormStyle,\n  extended: boolean,\n) {\n  switch (name) {\n    case TranslationType.Months:\n      return getLocaleMonthNames(locale, form, width)[date.getMonth()];\n    case TranslationType.Days:\n      return getLocaleDayNames(locale, form, width)[date.getDay()];\n    case TranslationType.DayPeriods:\n      const currentHours = date.getHours();\n      const currentMinutes = date.getMinutes();\n      if (extended) {\n        const rules = getLocaleExtraDayPeriodRules(locale);\n        const dayPeriods = getLocaleExtraDayPeriods(locale, form, width);\n        const index = rules.findIndex((rule) => {\n          if (Array.isArray(rule)) {\n            // morning, afternoon, evening, night\n            const [from, to] = rule;\n            const afterFrom = currentHours >= from.hours && currentMinutes >= from.minutes;\n            const beforeTo =\n              currentHours < to.hours || (currentHours === to.hours && currentMinutes < to.minutes);\n            // We must account for normal rules that span a period during the day (e.g. 6am-9am)\n            // where `from` is less (earlier) than `to`. But also rules that span midnight (e.g.\n            // 10pm - 5am) where `from` is greater (later!) than `to`.\n            //\n            // In the first case the current time must be BOTH after `from` AND before `to`\n            // (e.g. 8am is after 6am AND before 10am).\n            //\n            // In the second case the current time must be EITHER after `from` OR before `to`\n            // (e.g. 4am is before 5am but not after 10pm; and 11pm is not before 5am but it is\n            // after 10pm).\n            if (from.hours < to.hours) {\n              if (afterFrom && beforeTo) {\n                return true;\n              }\n            } else if (afterFrom || beforeTo) {\n              return true;\n            }\n          } else {\n            // noon or midnight\n            if (rule.hours === currentHours && rule.minutes === currentMinutes) {\n              return true;\n            }\n          }\n          return false;\n        });\n        if (index !== -1) {\n          return dayPeriods[index];\n        }\n      }\n      // if no rules for the day periods, we use am/pm by default\n      return getLocaleDayPeriods(locale, form, <TranslationWidth>width)[currentHours < 12 ? 0 : 1];\n    case TranslationType.Eras:\n      return getLocaleEraNames(locale, <TranslationWidth>width)[date.getFullYear() <= 0 ? 0 : 1];\n    default:\n      // This default case is not needed by TypeScript compiler, as the switch is exhaustive.\n      // However Closure Compiler does not understand that and reports an error in typed mode.\n      // The `throw new Error` below works around the problem, and the unexpected: never variable\n      // makes sure tsc still checks this code is unreachable.\n      const unexpected: never = name;\n      throw new RuntimeError(\n        RuntimeErrorCode.UNEXPECTED_TRANSLATION_TYPE,\n        ngDevMode && `unexpected translation type ${unexpected}`,\n      );\n  }\n}\n\n/**\n * Returns a date formatter that transforms a date and an offset into a timezone with ISO8601 or\n * GMT format depending on the width (eg: short = +0430, short:GMT = GMT+4, long = GMT+04:30,\n * extended = +04:30)\n */\nfunction timeZoneGetter(width: ZoneWidth): DateFormatter {\n  return function (date: Date, locale: string, offset: number) {\n    const zone = -1 * offset;\n    const minusSign = getLocaleNumberSymbol(locale, NumberSymbol.MinusSign);\n    const hours = zone > 0 ? Math.floor(zone / 60) : Math.ceil(zone / 60);\n    switch (width) {\n      case ZoneWidth.Short:\n        return (\n          (zone >= 0 ? '+' : '') +\n          padNumber(hours, 2, minusSign) +\n          padNumber(Math.abs(zone % 60), 2, minusSign)\n        );\n      case ZoneWidth.ShortGMT:\n        return 'GMT' + (zone >= 0 ? '+' : '') + padNumber(hours, 1, minusSign);\n      case ZoneWidth.Long:\n        return (\n          'GMT' +\n          (zone >= 0 ? '+' : '') +\n          padNumber(hours, 2, minusSign) +\n          ':' +\n          padNumber(Math.abs(zone % 60), 2, minusSign)\n        );\n      case ZoneWidth.Extended:\n        if (offset === 0) {\n          return 'Z';\n        } else {\n          return (\n            (zone >= 0 ? '+' : '') +\n            padNumber(hours, 2, minusSign) +\n            ':' +\n            padNumber(Math.abs(zone % 60), 2, minusSign)\n          );\n        }\n      default:\n        throw new RuntimeError(\n          RuntimeErrorCode.UNKNOWN_ZONE_WIDTH,\n          ngDevMode && `Unknown zone width \"${width}\"`,\n        );\n    }\n  };\n}\n\nconst JANUARY = 0;\nconst THURSDAY = 4;\nfunction getFirstThursdayOfYear(year: number) {\n  const firstDayOfYear = createDate(year, JANUARY, 1).getDay();\n  return createDate(\n    year,\n    0,\n    1 + (firstDayOfYear <= THURSDAY ? THURSDAY : THURSDAY + 7) - firstDayOfYear,\n  );\n}\n\n/**\n *  ISO Week starts on day 1 (Monday) and ends with day 0 (Sunday)\n */\nexport function getThursdayThisIsoWeek(datetime: Date) {\n  // getDay returns 0-6 range with sunday as 0.\n  const currentDay = datetime.getDay();\n\n  // On a Sunday, read the previous Thursday since ISO weeks start on Monday.\n  const deltaToThursday = currentDay === 0 ? -3 : THURSDAY - currentDay;\n\n  return createDate(\n    datetime.getFullYear(),\n    datetime.getMonth(),\n    datetime.getDate() + deltaToThursday,\n  );\n}\n\nfunction weekGetter(size: number, monthBased = false): DateFormatter {\n  return function (date: Date, locale: string) {\n    let result;\n    if (monthBased) {\n      const nbDaysBefore1stDayOfMonth =\n        new Date(date.getFullYear(), date.getMonth(), 1).getDay() - 1;\n      const today = date.getDate();\n      result = 1 + Math.floor((today + nbDaysBefore1stDayOfMonth) / 7);\n    } else {\n      const thisThurs = getThursdayThisIsoWeek(date);\n      // Some days of a year are part of next year according to ISO 8601.\n      // Compute the firstThurs from the year of this week's Thursday\n      const firstThurs = getFirstThursdayOfYear(thisThurs.getFullYear());\n      const diff = thisThurs.getTime() - firstThurs.getTime();\n      result = 1 + Math.round(diff / 6.048e8); // 6.048e8 ms per week\n    }\n\n    return padNumber(result, size, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign));\n  };\n}\n\n/**\n * Returns a date formatter that provides the week-numbering year for the input date.\n */\nfunction weekNumberingYearGetter(size: number, trim = false): DateFormatter {\n  return function (date: Date, locale: string) {\n    const thisThurs = getThursdayThisIsoWeek(date);\n    const weekNumberingYear = thisThurs.getFullYear();\n    return padNumber(\n      weekNumberingYear,\n      size,\n      getLocaleNumberSymbol(locale, NumberSymbol.MinusSign),\n      trim,\n    );\n  };\n}\n\ntype DateFormatter = (date: Date, locale: string, offset: number) => string;\n\nconst DATE_FORMATS: {[format: string]: DateFormatter} = {};\n\n// Based on CLDR formats:\n// See complete list: http://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n// See also explanations: http://cldr.unicode.org/translation/date-time\n// TODO(ocombe): support all missing cldr formats: U, Q, D, F, e, j, J, C, A, v, V, X, x\nfunction getDateFormatter(format: string): DateFormatter | null {\n  if (DATE_FORMATS[format]) {\n    return DATE_FORMATS[format];\n  }\n  let formatter;\n  switch (format) {\n    // Era name (AD/BC)\n    case 'G':\n    case 'GG':\n    case 'GGG':\n      formatter = dateStrGetter(TranslationType.Eras, TranslationWidth.Abbreviated);\n      break;\n    case 'GGGG':\n      formatter = dateStrGetter(TranslationType.Eras, TranslationWidth.Wide);\n      break;\n    case 'GGGGG':\n      formatter = dateStrGetter(TranslationType.Eras, TranslationWidth.Narrow);\n      break;\n\n    // 1 digit representation of the year, e.g. (AD 1 => 1, AD 199 => 199)\n    case 'y':\n      formatter = dateGetter(DateType.FullYear, 1, 0, false, true);\n      break;\n    // 2 digit representation of the year, padded (00-99). (e.g. AD 2001 => 01, AD 2010 => 10)\n    case 'yy':\n      formatter = dateGetter(DateType.FullYear, 2, 0, true, true);\n      break;\n    // 3 digit representation of the year, padded (000-999). (e.g. AD 2001 => 01, AD 2010 => 10)\n    case 'yyy':\n      formatter = dateGetter(DateType.FullYear, 3, 0, false, true);\n      break;\n    // 4 digit representation of the year (e.g. AD 1 => 0001, AD 2010 => 2010)\n    case 'yyyy':\n      formatter = dateGetter(DateType.FullYear, 4, 0, false, true);\n      break;\n\n    // 1 digit representation of the week-numbering year, e.g. (AD 1 => 1, AD 199 => 199)\n    case 'Y':\n      formatter = weekNumberingYearGetter(1);\n      break;\n    // 2 digit representation of the week-numbering year, padded (00-99). (e.g. AD 2001 => 01, AD\n    // 2010 => 10)\n    case 'YY':\n      formatter = weekNumberingYearGetter(2, true);\n      break;\n    // 3 digit representation of the week-numbering year, padded (000-999). (e.g. AD 1 => 001, AD\n    // 2010 => 2010)\n    case 'YYY':\n      formatter = weekNumberingYearGetter(3);\n      break;\n    // 4 digit representation of the week-numbering year (e.g. AD 1 => 0001, AD 2010 => 2010)\n    case 'YYYY':\n      formatter = weekNumberingYearGetter(4);\n      break;\n\n    // Month of the year (1-12), numeric\n    case 'M':\n    case 'L':\n      formatter = dateGetter(DateType.Month, 1, 1);\n      break;\n    case 'MM':\n    case 'LL':\n      formatter = dateGetter(DateType.Month, 2, 1);\n      break;\n\n    // Month of the year (January, ...), string, format\n    case 'MMM':\n      formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Abbreviated);\n      break;\n    case 'MMMM':\n      formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Wide);\n      break;\n    case 'MMMMM':\n      formatter = dateStrGetter(TranslationType.Months, TranslationWidth.Narrow);\n      break;\n\n    // Month of the year (January, ...), string, standalone\n    case 'LLL':\n      formatter = dateStrGetter(\n        TranslationType.Months,\n        TranslationWidth.Abbreviated,\n        FormStyle.Standalone,\n      );\n      break;\n    case 'LLLL':\n      formatter = dateStrGetter(\n        TranslationType.Months,\n        TranslationWidth.Wide,\n        FormStyle.Standalone,\n      );\n      break;\n    case 'LLLLL':\n      formatter = dateStrGetter(\n        TranslationType.Months,\n        TranslationWidth.Narrow,\n        FormStyle.Standalone,\n      );\n      break;\n\n    // Week of the year (1, ... 52)\n    case 'w':\n      formatter = weekGetter(1);\n      break;\n    case 'ww':\n      formatter = weekGetter(2);\n      break;\n\n    // Week of the month (1, ...)\n    case 'W':\n      formatter = weekGetter(1, true);\n      break;\n\n    // Day of the month (1-31)\n    case 'd':\n      formatter = dateGetter(DateType.Date, 1);\n      break;\n    case 'dd':\n      formatter = dateGetter(DateType.Date, 2);\n      break;\n\n    // Day of the Week StandAlone (1, 1, Mon, Monday, M, Mo)\n    case 'c':\n    case 'cc':\n      formatter = dateGetter(DateType.Day, 1);\n      break;\n    case 'ccc':\n      formatter = dateStrGetter(\n        TranslationType.Days,\n        TranslationWidth.Abbreviated,\n        FormStyle.Standalone,\n      );\n      break;\n    case 'cccc':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Wide, FormStyle.Standalone);\n      break;\n    case 'ccccc':\n      formatter = dateStrGetter(\n        TranslationType.Days,\n        TranslationWidth.Narrow,\n        FormStyle.Standalone,\n      );\n      break;\n    case 'cccccc':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Short, FormStyle.Standalone);\n      break;\n\n    // Day of the Week\n    case 'E':\n    case 'EE':\n    case 'EEE':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Abbreviated);\n      break;\n    case 'EEEE':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Wide);\n      break;\n    case 'EEEEE':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Narrow);\n      break;\n    case 'EEEEEE':\n      formatter = dateStrGetter(TranslationType.Days, TranslationWidth.Short);\n      break;\n\n    // Generic period of the day (am-pm)\n    case 'a':\n    case 'aa':\n    case 'aaa':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Abbreviated);\n      break;\n    case 'aaaa':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Wide);\n      break;\n    case 'aaaaa':\n      formatter = dateStrGetter(TranslationType.DayPeriods, TranslationWidth.Narrow);\n      break;\n\n    // Extended period of the day (midnight, at night, ...), standalone\n    case 'b':\n    case 'bb':\n    case 'bbb':\n      formatter = dateStrGetter(\n        TranslationType.DayPeriods,\n        TranslationWidth.Abbreviated,\n        FormStyle.Standalone,\n        true,\n      );\n      break;\n    case 'bbbb':\n      formatter = dateStrGetter(\n        TranslationType.DayPeriods,\n        TranslationWidth.Wide,\n        FormStyle.Standalone,\n        true,\n      );\n      break;\n    case 'bbbbb':\n      formatter = dateStrGetter(\n        TranslationType.DayPeriods,\n        TranslationWidth.Narrow,\n        FormStyle.Standalone,\n        true,\n      );\n      break;\n\n    // Extended period of the day (midnight, night, ...), standalone\n    case 'B':\n    case 'BB':\n    case 'BBB':\n      formatter = dateStrGetter(\n        TranslationType.DayPeriods,\n        TranslationWidth.Abbreviated,\n        FormStyle.Format,\n        true,\n      );\n      break;\n    case 'BBBB':\n      formatter = dateStrGetter(\n        TranslationType.DayPeriods,\n        TranslationWidth.Wide,\n        FormStyle.Format,\n        true,\n      );\n      break;\n    case 'BBBBB':\n      formatter = dateStrGetter(\n        TranslationType.DayPeriods,\n        TranslationWidth.Narrow,\n        FormStyle.Format,\n        true,\n      );\n      break;\n\n    // Hour in AM/PM, (1-12)\n    case 'h':\n      formatter = dateGetter(DateType.Hours, 1, -12);\n      break;\n    case 'hh':\n      formatter = dateGetter(DateType.Hours, 2, -12);\n      break;\n\n    // Hour of the day (0-23)\n    case 'H':\n      formatter = dateGetter(DateType.Hours, 1);\n      break;\n    // Hour in day, padded (00-23)\n    case 'HH':\n      formatter = dateGetter(DateType.Hours, 2);\n      break;\n\n    // Minute of the hour (0-59)\n    case 'm':\n      formatter = dateGetter(DateType.Minutes, 1);\n      break;\n    case 'mm':\n      formatter = dateGetter(DateType.Minutes, 2);\n      break;\n\n    // Second of the minute (0-59)\n    case 's':\n      formatter = dateGetter(DateType.Seconds, 1);\n      break;\n    case 'ss':\n      formatter = dateGetter(DateType.Seconds, 2);\n      break;\n\n    // Fractional second\n    case 'S':\n      formatter = dateGetter(DateType.FractionalSeconds, 1);\n      break;\n    case 'SS':\n      formatter = dateGetter(DateType.FractionalSeconds, 2);\n      break;\n    case 'SSS':\n      formatter = dateGetter(DateType.FractionalSeconds, 3);\n      break;\n\n    // Timezone ISO8601 short format (-0430)\n    case 'Z':\n    case 'ZZ':\n    case 'ZZZ':\n      formatter = timeZoneGetter(ZoneWidth.Short);\n      break;\n    // Timezone ISO8601 extended format (-04:30)\n    case 'ZZZZZ':\n      formatter = timeZoneGetter(ZoneWidth.Extended);\n      break;\n\n    // Timezone GMT short format (GMT+4)\n    case 'O':\n    case 'OO':\n    case 'OOO':\n    // Should be location, but fallback to format O instead because we don't have the data yet\n    case 'z':\n    case 'zz':\n    case 'zzz':\n      formatter = timeZoneGetter(ZoneWidth.ShortGMT);\n      break;\n    // Timezone GMT long format (GMT+0430)\n    case 'OOOO':\n    case 'ZZZZ':\n    // Should be location, but fallback to format O instead because we don't have the data yet\n    case 'zzzz':\n      formatter = timeZoneGetter(ZoneWidth.Long);\n      break;\n    default:\n      return null;\n  }\n  DATE_FORMATS[format] = formatter;\n  return formatter;\n}\n\nfunction timezoneToOffset(timezone: string, fallback: number): number {\n  // Support: IE 11 only, Edge 13-15+\n  // IE/Edge do not \"understand\" colon (`:`) in timezone\n  timezone = timezone.replace(/:/g, '');\n  const requestedTimezoneOffset = Date.parse('Jan 01, 1970 00:00:00 ' + timezone) / 60000;\n  return isNaN(requestedTimezoneOffset) ? fallback : requestedTimezoneOffset;\n}\n\nfunction addDateMinutes(date: Date, minutes: number) {\n  date = new Date(date.getTime());\n  date.setMinutes(date.getMinutes() + minutes);\n  return date;\n}\n\nfunction convertTimezoneToLocal(date: Date, timezone: string, reverse: boolean): Date {\n  const reverseValue = reverse ? -1 : 1;\n  const dateTimezoneOffset = date.getTimezoneOffset();\n  const timezoneOffset = timezoneToOffset(timezone, dateTimezoneOffset);\n  return addDateMinutes(date, reverseValue * (timezoneOffset - dateTimezoneOffset));\n}\n\n/**\n * Converts a value to date.\n *\n * Supported input formats:\n * - `Date`\n * - number: timestamp\n * - string: numeric (e.g. \"1234\"), ISO and date strings in a format supported by\n *   [Date.parse()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/parse).\n *   Note: ISO strings without time return a date without timeoffset.\n *\n * Throws if unable to convert to a date.\n */\nexport function toDate(value: string | number | Date): Date {\n  if (isDate(value)) {\n    return value;\n  }\n\n  if (typeof value === 'number' && !isNaN(value)) {\n    return new Date(value);\n  }\n\n  if (typeof value === 'string') {\n    value = value.trim();\n\n    if (/^(\\d{4}(-\\d{1,2}(-\\d{1,2})?)?)$/.test(value)) {\n      /* For ISO Strings without time the day, month and year must be extracted from the ISO String\n      before Date creation to avoid time offset and errors in the new Date.\n      If we only replace '-' with ',' in the ISO String (\"2015,01,01\"), and try to create a new\n      date, some browsers (e.g. IE 9) will throw an invalid Date error.\n      If we leave the '-' (\"2015-01-01\") and try to create a new Date(\"2015-01-01\") the timeoffset\n      is applied.\n      Note: ISO months are 0 for January, 1 for February, ... */\n      const [y, m = 1, d = 1] = value.split('-').map((val: string) => +val);\n      return createDate(y, m - 1, d);\n    }\n\n    const parsedNb = parseFloat(value);\n\n    // any string that only contains numbers, like \"1234\" but not like \"1234hello\"\n    if (!isNaN((value as any) - parsedNb)) {\n      return new Date(parsedNb);\n    }\n\n    let match: RegExpMatchArray | null;\n    if ((match = value.match(ISO8601_DATE_REGEX))) {\n      return isoStringToDate(match);\n    }\n  }\n\n  const date = new Date(value as any);\n  if (!isDate(date)) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_TO_DATE_CONVERSION,\n      ngDevMode && `Unable to convert \"${value}\" into a date`,\n    );\n  }\n  return date;\n}\n\n/**\n * Converts a date in ISO8601 to a Date.\n * Used instead of `Date.parse` because of browser discrepancies.\n */\nexport function isoStringToDate(match: RegExpMatchArray): Date {\n  const date = new Date(0);\n  let tzHour = 0;\n  let tzMin = 0;\n\n  // match[8] means that the string contains \"Z\" (UTC) or a timezone like \"+01:00\" or \"+0100\"\n  const dateSetter = match[8] ? date.setUTCFullYear : date.setFullYear;\n  const timeSetter = match[8] ? date.setUTCHours : date.setHours;\n\n  // if there is a timezone defined like \"+01:00\" or \"+0100\"\n  if (match[9]) {\n    tzHour = Number(match[9] + match[10]);\n    tzMin = Number(match[9] + match[11]);\n  }\n  dateSetter.call(date, Number(match[1]), Number(match[2]) - 1, Number(match[3]));\n  const h = Number(match[4] || 0) - tzHour;\n  const m = Number(match[5] || 0) - tzMin;\n  const s = Number(match[6] || 0);\n  // The ECMAScript specification (https://www.ecma-international.org/ecma-262/5.1/#sec-*********)\n  // defines that `DateTime` milliseconds should always be rounded down, so that `999.9ms`\n  // becomes `999ms`.\n  const ms = Math.floor(parseFloat('0.' + (match[7] || 0)) * 1000);\n  timeSetter.call(date, h, m, s, ms);\n  return date;\n}\n\nexport function isDate(value: any): value is Date {\n  return value instanceof Date && !isNaN(value.valueOf());\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ɵRuntimeError as RuntimeError} from '@angular/core';\n\nimport {\n  getLocaleNumberFormat,\n  getLocaleNumberSymbol,\n  getNumberOfCurrencyDigits,\n  NumberFormatStyle,\n  NumberSymbol,\n} from './locale_data_api';\nimport {RuntimeErrorCode} from '../errors';\n\nexport const NUMBER_FORMAT_REGEXP = /^(\\d+)?\\.((\\d+)(-(\\d+))?)?$/;\nconst MAX_DIGITS = 22;\nconst DECIMAL_SEP = '.';\nconst ZERO_CHAR = '0';\nconst PATTERN_SEP = ';';\nconst GROUP_SEP = ',';\nconst DIGIT_CHAR = '#';\nconst CURRENCY_CHAR = '¤';\nconst PERCENT_CHAR = '%';\n\n/**\n * Transforms a number to a locale string based on a style and a format.\n */\nfunction formatNumberToLocaleString(\n  value: number,\n  pattern: ParsedNumberFormat,\n  locale: string,\n  groupSymbol: NumberSymbol,\n  decimalSymbol: NumberSymbol,\n  digitsInfo?: string,\n  isPercent = false,\n): string {\n  let formattedText = '';\n  let isZero = false;\n\n  if (!isFinite(value)) {\n    formattedText = getLocaleNumberSymbol(locale, NumberSymbol.Infinity);\n  } else {\n    let parsedNumber = parseNumber(value);\n\n    if (isPercent) {\n      parsedNumber = toPercent(parsedNumber);\n    }\n\n    let minInt = pattern.minInt;\n    let minFraction = pattern.minFrac;\n    let maxFraction = pattern.maxFrac;\n\n    if (digitsInfo) {\n      const parts = digitsInfo.match(NUMBER_FORMAT_REGEXP);\n      if (parts === null) {\n        throw new RuntimeError(\n          RuntimeErrorCode.INVALID_DIGIT_INFO,\n          ngDevMode && `${digitsInfo} is not a valid digit info`,\n        );\n      }\n      const minIntPart = parts[1];\n      const minFractionPart = parts[3];\n      const maxFractionPart = parts[5];\n      if (minIntPart != null) {\n        minInt = parseIntAutoRadix(minIntPart);\n      }\n      if (minFractionPart != null) {\n        minFraction = parseIntAutoRadix(minFractionPart);\n      }\n      if (maxFractionPart != null) {\n        maxFraction = parseIntAutoRadix(maxFractionPart);\n      } else if (minFractionPart != null && minFraction > maxFraction) {\n        maxFraction = minFraction;\n      }\n    }\n\n    roundNumber(parsedNumber, minFraction, maxFraction);\n\n    let digits = parsedNumber.digits;\n    let integerLen = parsedNumber.integerLen;\n    const exponent = parsedNumber.exponent;\n    let decimals = [];\n    isZero = digits.every((d) => !d);\n\n    // pad zeros for small numbers\n    for (; integerLen < minInt; integerLen++) {\n      digits.unshift(0);\n    }\n\n    // pad zeros for small numbers\n    for (; integerLen < 0; integerLen++) {\n      digits.unshift(0);\n    }\n\n    // extract decimals digits\n    if (integerLen > 0) {\n      decimals = digits.splice(integerLen, digits.length);\n    } else {\n      decimals = digits;\n      digits = [0];\n    }\n\n    // format the integer digits with grouping separators\n    const groups = [];\n    if (digits.length >= pattern.lgSize) {\n      groups.unshift(digits.splice(-pattern.lgSize, digits.length).join(''));\n    }\n\n    while (digits.length > pattern.gSize) {\n      groups.unshift(digits.splice(-pattern.gSize, digits.length).join(''));\n    }\n\n    if (digits.length) {\n      groups.unshift(digits.join(''));\n    }\n\n    formattedText = groups.join(getLocaleNumberSymbol(locale, groupSymbol));\n\n    // append the decimal digits\n    if (decimals.length) {\n      formattedText += getLocaleNumberSymbol(locale, decimalSymbol) + decimals.join('');\n    }\n\n    if (exponent) {\n      formattedText += getLocaleNumberSymbol(locale, NumberSymbol.Exponential) + '+' + exponent;\n    }\n  }\n\n  if (value < 0 && !isZero) {\n    formattedText = pattern.negPre + formattedText + pattern.negSuf;\n  } else {\n    formattedText = pattern.posPre + formattedText + pattern.posSuf;\n  }\n\n  return formattedText;\n}\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a number as currency using locale rules.\n *\n * @param value The number to format.\n * @param locale A locale code for the locale format rules to use.\n * @param currency A string containing the currency symbol or its name,\n * such as \"$\" or \"Canadian Dollar\". Used in output string, but does not affect the operation\n * of the function.\n * @param currencyCode The [ISO 4217](https://en.wikipedia.org/wiki/ISO_4217)\n * currency code, such as `USD` for the US dollar and `EUR` for the euro.\n * Used to determine the number of digits in the decimal part.\n * @param digitsInfo Decimal representation options, specified by a string in the following format:\n * `{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}`. See `DecimalPipe` for more details.\n *\n * @returns The formatted currency value.\n *\n * @see {@link formatNumber}\n * @see {@link DecimalPipe}\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n */\nexport function formatCurrency(\n  value: number,\n  locale: string,\n  currency: string,\n  currencyCode?: string,\n  digitsInfo?: string,\n): string {\n  const format = getLocaleNumberFormat(locale, NumberFormatStyle.Currency);\n  const pattern = parseNumberFormat(format, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign));\n\n  pattern.minFrac = getNumberOfCurrencyDigits(currencyCode!);\n  pattern.maxFrac = pattern.minFrac;\n\n  const res = formatNumberToLocaleString(\n    value,\n    pattern,\n    locale,\n    NumberSymbol.CurrencyGroup,\n    NumberSymbol.CurrencyDecimal,\n    digitsInfo,\n  );\n  return (\n    res\n      .replace(CURRENCY_CHAR, currency)\n      // if we have 2 time the currency character, the second one is ignored\n      .replace(CURRENCY_CHAR, '')\n      // If there is a spacing between currency character and the value and\n      // the currency character is suppressed by passing an empty string, the\n      // spacing character would remain as part of the string. Then we\n      // should remove it.\n      .trim()\n  );\n}\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a number as a percentage according to locale rules.\n *\n * @param value The number to format.\n * @param locale A locale code for the locale format rules to use.\n * @param digitsInfo Decimal representation options, specified by a string in the following format:\n * `{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}`. See `DecimalPipe` for more details.\n *\n * @returns The formatted percentage value.\n *\n * @see {@link formatNumber}\n * @see {@link DecimalPipe}\n * @see [Internationalization (i18n) Guide](guide/i18n)\n * @publicApi\n *\n */\nexport function formatPercent(value: number, locale: string, digitsInfo?: string): string {\n  const format = getLocaleNumberFormat(locale, NumberFormatStyle.Percent);\n  const pattern = parseNumberFormat(format, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign));\n  const res = formatNumberToLocaleString(\n    value,\n    pattern,\n    locale,\n    NumberSymbol.Group,\n    NumberSymbol.Decimal,\n    digitsInfo,\n    true,\n  );\n  return res.replace(\n    new RegExp(PERCENT_CHAR, 'g'),\n    getLocaleNumberSymbol(locale, NumberSymbol.PercentSign),\n  );\n}\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a number as text, with group sizing, separator, and other\n * parameters based on the locale.\n *\n * @param value The number to format.\n * @param locale A locale code for the locale format rules to use.\n * @param digitsInfo Decimal representation options, specified by a string in the following format:\n * `{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}`. See `DecimalPipe` for more details.\n *\n * @returns The formatted text string.\n * @see [Internationalization (i18n) Guide](guide/i18n)\n *\n * @publicApi\n */\nexport function formatNumber(value: number, locale: string, digitsInfo?: string): string {\n  const format = getLocaleNumberFormat(locale, NumberFormatStyle.Decimal);\n  const pattern = parseNumberFormat(format, getLocaleNumberSymbol(locale, NumberSymbol.MinusSign));\n  return formatNumberToLocaleString(\n    value,\n    pattern,\n    locale,\n    NumberSymbol.Group,\n    NumberSymbol.Decimal,\n    digitsInfo,\n  );\n}\n\ninterface ParsedNumberFormat {\n  minInt: number;\n  // the minimum number of digits required in the fraction part of the number\n  minFrac: number;\n  // the maximum number of digits required in the fraction part of the number\n  maxFrac: number;\n  // the prefix for a positive number\n  posPre: string;\n  // the suffix for a positive number\n  posSuf: string;\n  // the prefix for a negative number (e.g. `-` or `(`))\n  negPre: string;\n  // the suffix for a negative number (e.g. `)`)\n  negSuf: string;\n  // number of digits in each group of separated digits\n  gSize: number;\n  // number of digits in the last group of digits before the decimal separator\n  lgSize: number;\n}\n\nfunction parseNumberFormat(format: string, minusSign = '-'): ParsedNumberFormat {\n  const p = {\n    minInt: 1,\n    minFrac: 0,\n    maxFrac: 0,\n    posPre: '',\n    posSuf: '',\n    negPre: '',\n    negSuf: '',\n    gSize: 0,\n    lgSize: 0,\n  };\n\n  const patternParts = format.split(PATTERN_SEP);\n  const positive = patternParts[0];\n  const negative = patternParts[1];\n\n  const positiveParts =\n      positive.indexOf(DECIMAL_SEP) !== -1\n        ? positive.split(DECIMAL_SEP)\n        : [\n            positive.substring(0, positive.lastIndexOf(ZERO_CHAR) + 1),\n            positive.substring(positive.lastIndexOf(ZERO_CHAR) + 1),\n          ],\n    integer = positiveParts[0],\n    fraction = positiveParts[1] || '';\n\n  p.posPre = integer.substring(0, integer.indexOf(DIGIT_CHAR));\n\n  for (let i = 0; i < fraction.length; i++) {\n    const ch = fraction.charAt(i);\n    if (ch === ZERO_CHAR) {\n      p.minFrac = p.maxFrac = i + 1;\n    } else if (ch === DIGIT_CHAR) {\n      p.maxFrac = i + 1;\n    } else {\n      p.posSuf += ch;\n    }\n  }\n\n  const groups = integer.split(GROUP_SEP);\n  p.gSize = groups[1] ? groups[1].length : 0;\n  p.lgSize = groups[2] || groups[1] ? (groups[2] || groups[1]).length : 0;\n\n  if (negative) {\n    const trunkLen = positive.length - p.posPre.length - p.posSuf.length,\n      pos = negative.indexOf(DIGIT_CHAR);\n\n    p.negPre = negative.substring(0, pos).replace(/'/g, '');\n    p.negSuf = negative.slice(pos + trunkLen).replace(/'/g, '');\n  } else {\n    p.negPre = minusSign + p.posPre;\n    p.negSuf = p.posSuf;\n  }\n\n  return p;\n}\n\ninterface ParsedNumber {\n  // an array of digits containing leading zeros as necessary\n  digits: number[];\n  // the exponent for numbers that would need more than `MAX_DIGITS` digits in `d`\n  exponent: number;\n  // the number of the digits in `d` that are to the left of the decimal point\n  integerLen: number;\n}\n\n// Transforms a parsed number into a percentage by multiplying it by 100\nfunction toPercent(parsedNumber: ParsedNumber): ParsedNumber {\n  // if the number is 0, don't do anything\n  if (parsedNumber.digits[0] === 0) {\n    return parsedNumber;\n  }\n\n  // Getting the current number of decimals\n  const fractionLen = parsedNumber.digits.length - parsedNumber.integerLen;\n  if (parsedNumber.exponent) {\n    parsedNumber.exponent += 2;\n  } else {\n    if (fractionLen === 0) {\n      parsedNumber.digits.push(0, 0);\n    } else if (fractionLen === 1) {\n      parsedNumber.digits.push(0);\n    }\n    parsedNumber.integerLen += 2;\n  }\n\n  return parsedNumber;\n}\n\n/**\n * Parses a number.\n * Significant bits of this parse algorithm came from https://github.com/MikeMcl/big.js/\n */\nfunction parseNumber(num: number): ParsedNumber {\n  let numStr = Math.abs(num) + '';\n  let exponent = 0,\n    digits,\n    integerLen;\n  let i, j, zeros;\n\n  // Decimal point?\n  if ((integerLen = numStr.indexOf(DECIMAL_SEP)) > -1) {\n    numStr = numStr.replace(DECIMAL_SEP, '');\n  }\n\n  // Exponential form?\n  if ((i = numStr.search(/e/i)) > 0) {\n    // Work out the exponent.\n    if (integerLen < 0) integerLen = i;\n    integerLen += +numStr.slice(i + 1);\n    numStr = numStr.substring(0, i);\n  } else if (integerLen < 0) {\n    // There was no decimal point or exponent so it is an integer.\n    integerLen = numStr.length;\n  }\n\n  // Count the number of leading zeros.\n  for (i = 0; numStr.charAt(i) === ZERO_CHAR; i++) {\n    /* empty */\n  }\n\n  if (i === (zeros = numStr.length)) {\n    // The digits are all zero.\n    digits = [0];\n    integerLen = 1;\n  } else {\n    // Count the number of trailing zeros\n    zeros--;\n    while (numStr.charAt(zeros) === ZERO_CHAR) zeros--;\n\n    // Trailing zeros are insignificant so ignore them\n    integerLen -= i;\n    digits = [];\n    // Convert string to array of digits without leading/trailing zeros.\n    for (j = 0; i <= zeros; i++, j++) {\n      digits[j] = Number(numStr.charAt(i));\n    }\n  }\n\n  // If the number overflows the maximum allowed digits then use an exponent.\n  if (integerLen > MAX_DIGITS) {\n    digits = digits.splice(0, MAX_DIGITS - 1);\n    exponent = integerLen - 1;\n    integerLen = 1;\n  }\n\n  return {digits, exponent, integerLen};\n}\n\n/**\n * Round the parsed number to the specified number of decimal places\n * This function changes the parsedNumber in-place\n */\nfunction roundNumber(parsedNumber: ParsedNumber, minFrac: number, maxFrac: number) {\n  if (minFrac > maxFrac) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_NUMBER_OF_DIGITS_AFTER_FRACTION,\n      ngDevMode &&\n        `The minimum number of digits after fraction (${minFrac}) is higher than the maximum (${maxFrac}).`,\n    );\n  }\n\n  let digits = parsedNumber.digits;\n  let fractionLen = digits.length - parsedNumber.integerLen;\n  const fractionSize = Math.min(Math.max(minFrac, fractionLen), maxFrac);\n\n  // The index of the digit to where rounding is to occur\n  let roundAt = fractionSize + parsedNumber.integerLen;\n  let digit = digits[roundAt];\n\n  if (roundAt > 0) {\n    // Drop fractional digits beyond `roundAt`\n    digits.splice(Math.max(parsedNumber.integerLen, roundAt));\n\n    // Set non-fractional digits beyond `roundAt` to 0\n    for (let j = roundAt; j < digits.length; j++) {\n      digits[j] = 0;\n    }\n  } else {\n    // We rounded to zero so reset the parsedNumber\n    fractionLen = Math.max(0, fractionLen);\n    parsedNumber.integerLen = 1;\n    digits.length = Math.max(1, (roundAt = fractionSize + 1));\n    digits[0] = 0;\n    for (let i = 1; i < roundAt; i++) digits[i] = 0;\n  }\n\n  if (digit >= 5) {\n    if (roundAt - 1 < 0) {\n      for (let k = 0; k > roundAt; k--) {\n        digits.unshift(0);\n        parsedNumber.integerLen++;\n      }\n      digits.unshift(1);\n      parsedNumber.integerLen++;\n    } else {\n      digits[roundAt - 1]++;\n    }\n  }\n\n  // Pad out with zeros to get the required fraction length\n  for (; fractionLen < Math.max(0, fractionSize); fractionLen++) digits.push(0);\n\n  let dropTrailingZeros = fractionSize !== 0;\n  // Minimal length = nb of decimals required + current nb of integers\n  // Any number besides that is optional and can be removed if it's a trailing 0\n  const minLen = minFrac + parsedNumber.integerLen;\n  // Do any carrying, e.g. a digit was rounded up to 10\n  const carry = digits.reduceRight(function (carry, d, i, digits) {\n    d = d + carry;\n    digits[i] = d < 10 ? d : d - 10; // d % 10\n    if (dropTrailingZeros) {\n      // Do not keep meaningless fractional trailing zeros (e.g. 15.52000 --> 15.52)\n      if (digits[i] === 0 && i >= minLen) {\n        digits.pop();\n      } else {\n        dropTrailingZeros = false;\n      }\n    }\n    return d >= 10 ? 1 : 0; // Math.floor(d / 10);\n  }, 0);\n  if (carry) {\n    digits.unshift(carry);\n    parsedNumber.integerLen++;\n  }\n}\n\nexport function parseIntAutoRadix(text: string): number {\n  const result: number = parseInt(text);\n  if (isNaN(result)) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INTEGER_LITERAL,\n      ngDevMode && 'Invalid integer literal when parsing ' + text,\n    );\n  }\n  return result;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Inject, Injectable, LOCALE_ID, ɵRuntimeError as RuntimeError} from '@angular/core';\n\nimport {getLocalePluralCase, Plural} from './locale_data_api';\nimport {RuntimeErrorCode} from '../errors';\n\n/**\n * @publicApi\n */\n@Injectable({\n  providedIn: 'root',\n  useFactory: (locale: string) => new NgLocaleLocalization(locale),\n  deps: [LOCALE_ID],\n})\nexport abstract class NgLocalization {\n  abstract getPluralCategory(value: any, locale?: string): string;\n}\n\n/**\n * Returns the plural category for a given value.\n * - \"=value\" when the case exists,\n * - the plural category otherwise\n */\nexport function getPluralCategory(\n  value: number,\n  cases: string[],\n  ngLocalization: NgLocalization,\n  locale?: string,\n): string {\n  let key = `=${value}`;\n\n  if (cases.indexOf(key) > -1) {\n    return key;\n  }\n\n  key = ngLocalization.getPluralCategory(value, locale);\n\n  if (cases.indexOf(key) > -1) {\n    return key;\n  }\n\n  if (cases.indexOf('other') > -1) {\n    return 'other';\n  }\n\n  throw new RuntimeError(\n    RuntimeErrorCode.NO_PLURAL_MESSAGE_FOUND,\n    ngDevMode && `No plural message found for value \"${value}\"`,\n  );\n}\n\n/**\n * Returns the plural case based on the locale\n *\n * @publicApi\n */\n@Injectable()\nexport class NgLocaleLocalization extends NgLocalization {\n  constructor(@Inject(LOCALE_ID) protected locale: string) {\n    super();\n  }\n\n  override getPluralCategory(value: any, locale?: string): string {\n    const plural = getLocalePluralCase(locale || this.locale)(value);\n\n    switch (plural) {\n      case Plural.Zero:\n        return 'zero';\n      case Plural.One:\n        return 'one';\n      case Plural.Two:\n        return 'two';\n      case Plural.Few:\n        return 'few';\n      case Plural.Many:\n        return 'many';\n      default:\n        return 'other';\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {\n  Directive,\n  Do<PERSON>he<PERSON>,\n  ElementRef,\n  Input,\n  Renderer2,\n  ɵstringify as stringify,\n} from '@angular/core';\n\ntype NgClassSupportedTypes = string[] | Set<string> | {[klass: string]: any} | null | undefined;\n\nconst WS_REGEXP = /\\s+/;\n\nconst EMPTY_ARRAY: string[] = [];\n\n/**\n * Represents internal object used to track state of each CSS class. There are 3 different (boolean)\n * flags that, combined together, indicate state of a given CSS class:\n * - enabled: indicates if a class should be present in the DOM (true) or not (false);\n * - changed: tracks if a class was toggled (added or removed) during the custom dirty-checking\n * process; changed classes must be synchronized with the DOM;\n * - touched: tracks if a class is present in the current object bound to the class / ngClass input;\n * classes that are not present any more can be removed from the internal data structures;\n */\ninterface CssClassState {\n  // PERF: could use a bit mask to represent state as all fields are boolean flags\n  enabled: boolean;\n  changed: boolean;\n  touched: boolean;\n}\n\n/**\n * @ngModule CommonModule\n *\n * @usageNotes\n * ```html\n * <some-element [ngClass]=\"stringExp|arrayExp|objExp|Set\">...</some-element>\n *\n * <some-element [ngClass]=\"{'class1 class2 class3' : true}\">...</some-element>\n * ```\n *\n * For more simple use cases you can use the [class bindings](/guide/templates/binding#css-class-and-style-property-bindings) directly.\n * It doesn't require importing a directive.\n *\n * ```html\n * <some-element [class]=\"'first second'\">...</some-element>\n *\n * <some-element [class.expanded]=\"isExpanded\">...</some-element>\n *\n * <some-element [class]=\"['first', 'second']\">...</some-element>\n *\n * <some-element [class]=\"{'first': true, 'second': true, 'third': false}\">...</some-element>\n * ```\n * @description\n *\n * Adds and removes CSS classes on an HTML element.\n *\n * The CSS classes are updated as follows, depending on the type of the expression evaluation:\n * - `string` - the CSS classes listed in the string (space delimited) are added,\n * - `Array` - the CSS classes declared as Array elements are added,\n * - `Object` - keys are CSS classes that get added when the expression given in the value\n *              evaluates to a truthy value, otherwise they are removed.\n *\n *\n * @see [Class bindings](/guide/templates/binding#css-class-and-style-property-bindings)\n *\n * @publicApi\n */\n@Directive({\n  selector: '[ngClass]',\n})\nexport class NgClass implements DoCheck {\n  private initialClasses = EMPTY_ARRAY;\n  private rawClass: NgClassSupportedTypes;\n\n  private stateMap = new Map<string, CssClassState>();\n\n  constructor(\n    private _ngEl: ElementRef,\n    private _renderer: Renderer2,\n  ) {}\n\n  @Input('class')\n  set klass(value: string) {\n    this.initialClasses = value != null ? value.trim().split(WS_REGEXP) : EMPTY_ARRAY;\n  }\n\n  @Input('ngClass')\n  set ngClass(value: string | string[] | Set<string> | {[klass: string]: any} | null | undefined) {\n    this.rawClass = typeof value === 'string' ? value.trim().split(WS_REGEXP) : value;\n  }\n\n  /*\n  The NgClass directive uses the custom change detection algorithm for its inputs. The custom\n  algorithm is necessary since inputs are represented as complex object or arrays that need to be\n  deeply-compared.\n\n  This algorithm is perf-sensitive since NgClass is used very frequently and its poor performance\n  might negatively impact runtime performance of the entire change detection cycle. The design of\n  this algorithm is making sure that:\n  - there is no unnecessary DOM manipulation (CSS classes are added / removed from the DOM only when\n  needed), even if references to bound objects change;\n  - there is no memory allocation if nothing changes (even relatively modest memory allocation\n  during the change detection cycle can result in GC pauses for some of the CD cycles).\n\n  The algorithm works by iterating over the set of bound classes, staring with [class] binding and\n  then going over [ngClass] binding. For each CSS class name:\n  - check if it was seen before (this information is tracked in the state map) and if its value\n  changed;\n  - mark it as \"touched\" - names that are not marked are not present in the latest set of binding\n  and we can remove such class name from the internal data structures;\n\n  After iteration over all the CSS class names we've got data structure with all the information\n  necessary to synchronize changes to the DOM - it is enough to iterate over the state map, flush\n  changes to the DOM and reset internal data structures so those are ready for the next change\n  detection cycle.\n   */\n  ngDoCheck(): void {\n    // classes from the [class] binding\n    for (const klass of this.initialClasses) {\n      this._updateState(klass, true);\n    }\n\n    // classes from the [ngClass] binding\n    const rawClass = this.rawClass;\n    if (Array.isArray(rawClass) || rawClass instanceof Set) {\n      for (const klass of rawClass) {\n        this._updateState(klass, true);\n      }\n    } else if (rawClass != null) {\n      for (const klass of Object.keys(rawClass)) {\n        this._updateState(klass, Boolean(rawClass[klass]));\n      }\n    }\n\n    this._applyStateDiff();\n  }\n\n  private _updateState(klass: string, nextEnabled: boolean) {\n    const state = this.stateMap.get(klass);\n    if (state !== undefined) {\n      if (state.enabled !== nextEnabled) {\n        state.changed = true;\n        state.enabled = nextEnabled;\n      }\n      state.touched = true;\n    } else {\n      this.stateMap.set(klass, {enabled: nextEnabled, changed: true, touched: true});\n    }\n  }\n\n  private _applyStateDiff() {\n    for (const stateEntry of this.stateMap) {\n      const klass = stateEntry[0];\n      const state = stateEntry[1];\n\n      if (state.changed) {\n        this._toggleClass(klass, state.enabled);\n        state.changed = false;\n      } else if (!state.touched) {\n        // A class that was previously active got removed from the new collection of classes -\n        // remove from the DOM as well.\n        if (state.enabled) {\n          this._toggleClass(klass, false);\n        }\n        this.stateMap.delete(klass);\n      }\n\n      state.touched = false;\n    }\n  }\n\n  private _toggleClass(klass: string, enabled: boolean): void {\n    if (ngDevMode) {\n      if (typeof klass !== 'string') {\n        throw new Error(\n          `NgClass can only toggle CSS classes expressed as strings, got ${stringify(klass)}`,\n        );\n      }\n    }\n    klass = klass.trim();\n    if (klass.length > 0) {\n      klass.split(WS_REGEXP).forEach((klass) => {\n        if (enabled) {\n          this._renderer.addClass(this._ngEl.nativeElement, klass);\n        } else {\n          this._renderer.removeClass(this._ngEl.nativeElement, klass);\n        }\n      });\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentRef,\n  createNgModule,\n  Directive,\n  DoCheck,\n  EnvironmentInjector,\n  Injector,\n  Input,\n  NgModuleFactory,\n  NgModuleRef,\n  OnChanges,\n  OnDestroy,\n  SimpleChanges,\n  Type,\n  ViewContainerRef,\n} from '@angular/core';\n\n/**\n * Instantiates a {@link /api/core/Component Component} type and inserts its Host View into the current View.\n * `NgComponentOutlet` provides a declarative approach for dynamic component creation.\n *\n * `NgComponentOutlet` requires a component type, if a falsy value is set the view will clear and\n * any existing component will be destroyed.\n *\n * @usageNotes\n *\n * ### Fine tune control\n *\n * You can control the component creation process by using the following optional attributes:\n *\n * * `ngComponentOutletInputs`: Optional component inputs object, which will be bind to the\n * component.\n *\n * * `ngComponentOutletInjector`: Optional custom {@link Injector} that will be used as parent for\n * the Component. Defaults to the injector of the current view container.\n *\n * * `ngComponentOutletEnvironmentInjector`: Optional custom {@link EnvironmentInjector} which will\n * provide the component's environment.\n *\n * * `ngComponentOutletContent`: Optional list of projectable nodes to insert into the content\n * section of the component, if it exists.\n *\n * * `ngComponentOutletNgModule`: Optional NgModule class reference to allow loading another\n * module dynamically, then loading a component from that module.\n *\n * * `ngComponentOutletNgModuleFactory`: Deprecated config option that allows providing optional\n * NgModule factory to allow loading another module dynamically, then loading a component from that\n * module. Use `ngComponentOutletNgModule` instead.\n *\n * ### Syntax\n *\n * Simple\n * ```html\n * <ng-container *ngComponentOutlet=\"componentTypeExpression\"></ng-container>\n * ```\n *\n * With inputs\n * ```html\n * <ng-container *ngComponentOutlet=\"componentTypeExpression;\n *                                   inputs: inputsExpression;\">\n * </ng-container>\n * ```\n *\n * Customized injector/content\n * ```html\n * <ng-container *ngComponentOutlet=\"componentTypeExpression;\n *                                   injector: injectorExpression;\n *                                   content: contentNodesExpression;\">\n * </ng-container>\n * ```\n *\n * Customized NgModule reference\n * ```html\n * <ng-container *ngComponentOutlet=\"componentTypeExpression;\n *                                   ngModule: ngModuleClass;\">\n * </ng-container>\n * ```\n *\n * ### A simple example\n *\n * {@example common/ngComponentOutlet/ts/module.ts region='SimpleExample'}\n *\n * A more complete example with additional options:\n *\n * {@example common/ngComponentOutlet/ts/module.ts region='CompleteExample'}\n *\n * @publicApi\n * @ngModule CommonModule\n */\n@Directive({\n  selector: '[ngComponentOutlet]',\n  exportAs: 'ngComponentOutlet',\n})\nexport class NgComponentOutlet<T = any> implements OnChanges, DoCheck, OnDestroy {\n  // TODO(crisbeto): this should be `Type<T>`, but doing so broke a few\n  // targets in a TGP so we need to do it in a major version.\n  /** Component that should be rendered in the outlet. */\n  @Input() ngComponentOutlet: Type<any> | null = null;\n\n  @Input() ngComponentOutletInputs?: Record<string, unknown>;\n  @Input() ngComponentOutletInjector?: Injector;\n  @Input() ngComponentOutletEnvironmentInjector?: EnvironmentInjector;\n  @Input() ngComponentOutletContent?: any[][];\n\n  @Input() ngComponentOutletNgModule?: Type<any>;\n  /**\n   * @deprecated This input is deprecated, use `ngComponentOutletNgModule` instead.\n   */\n  @Input() ngComponentOutletNgModuleFactory?: NgModuleFactory<any>;\n\n  private _componentRef: ComponentRef<T> | undefined;\n  private _moduleRef: NgModuleRef<any> | undefined;\n\n  /**\n   * A helper data structure that allows us to track inputs that were part of the\n   * ngComponentOutletInputs expression. Tracking inputs is necessary for proper removal of ones\n   * that are no longer referenced.\n   */\n  private _inputsUsed = new Map<string, boolean>();\n\n  /**\n   * Gets the instance of the currently-rendered component.\n   * Will be null if no component has been rendered.\n   */\n  get componentInstance(): T | null {\n    return this._componentRef?.instance ?? null;\n  }\n\n  constructor(private _viewContainerRef: ViewContainerRef) {}\n\n  private _needToReCreateNgModuleInstance(changes: SimpleChanges): boolean {\n    // Note: square brackets property accessor is safe for Closure compiler optimizations (the\n    // `changes` argument of the `ngOnChanges` lifecycle hook retains the names of the fields that\n    // were changed).\n    return (\n      changes['ngComponentOutletNgModule'] !== undefined ||\n      changes['ngComponentOutletNgModuleFactory'] !== undefined\n    );\n  }\n\n  private _needToReCreateComponentInstance(changes: SimpleChanges): boolean {\n    // Note: square brackets property accessor is safe for Closure compiler optimizations (the\n    // `changes` argument of the `ngOnChanges` lifecycle hook retains the names of the fields that\n    // were changed).\n    return (\n      changes['ngComponentOutlet'] !== undefined ||\n      changes['ngComponentOutletContent'] !== undefined ||\n      changes['ngComponentOutletInjector'] !== undefined ||\n      changes['ngComponentOutletEnvironmentInjector'] !== undefined ||\n      this._needToReCreateNgModuleInstance(changes)\n    );\n  }\n\n  /** @docs-private */\n  ngOnChanges(changes: SimpleChanges) {\n    if (this._needToReCreateComponentInstance(changes)) {\n      this._viewContainerRef.clear();\n      this._inputsUsed.clear();\n      this._componentRef = undefined;\n\n      if (this.ngComponentOutlet) {\n        const injector = this.ngComponentOutletInjector || this._viewContainerRef.parentInjector;\n\n        if (this._needToReCreateNgModuleInstance(changes)) {\n          this._moduleRef?.destroy();\n\n          if (this.ngComponentOutletNgModule) {\n            this._moduleRef = createNgModule(\n              this.ngComponentOutletNgModule,\n              getParentInjector(injector),\n            );\n          } else if (this.ngComponentOutletNgModuleFactory) {\n            this._moduleRef = this.ngComponentOutletNgModuleFactory.create(\n              getParentInjector(injector),\n            );\n          } else {\n            this._moduleRef = undefined;\n          }\n        }\n\n        this._componentRef = this._viewContainerRef.createComponent(this.ngComponentOutlet, {\n          injector,\n          ngModuleRef: this._moduleRef,\n          projectableNodes: this.ngComponentOutletContent,\n          environmentInjector: this.ngComponentOutletEnvironmentInjector,\n        });\n      }\n    }\n  }\n\n  /** @docs-private */\n  ngDoCheck() {\n    if (this._componentRef) {\n      if (this.ngComponentOutletInputs) {\n        for (const inputName of Object.keys(this.ngComponentOutletInputs)) {\n          this._inputsUsed.set(inputName, true);\n        }\n      }\n\n      this._applyInputStateDiff(this._componentRef);\n    }\n  }\n\n  /** @docs-private */\n  ngOnDestroy() {\n    this._moduleRef?.destroy();\n  }\n\n  private _applyInputStateDiff(componentRef: ComponentRef<unknown>) {\n    for (const [inputName, touched] of this._inputsUsed) {\n      if (!touched) {\n        // The input that was previously active no longer exists and needs to be set to undefined.\n        componentRef.setInput(inputName, undefined);\n        this._inputsUsed.delete(inputName);\n      } else {\n        // Since touched is true, it can be asserted that the inputs object is not empty.\n        componentRef.setInput(inputName, this.ngComponentOutletInputs![inputName]);\n        this._inputsUsed.set(inputName, false);\n      }\n    }\n  }\n}\n\n// Helper function that returns an Injector instance of a parent NgModule.\nfunction getParentInjector(injector: Injector): Injector {\n  const parentNgModule = injector.get(NgModuleRef);\n  return parentNgModule.injector;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Directive,\n  DoCheck,\n  EmbeddedViewRef,\n  Input,\n  IterableChangeRecord,\n  IterableChanges,\n  IterableDiffer,\n  IterableDiffers,\n  NgIterable,\n  ɵRuntimeError as RuntimeError,\n  TemplateRef,\n  TrackByFunction,\n  ViewContainerRef,\n} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../errors';\n\n/**\n * @publicApi\n *\n * @deprecated 20.0\n * The `ngFor` directive is deprecated. Use the `@for` block instead.\n */\nexport class NgForOfContext<T, U extends NgIterable<T> = NgIterable<T>> {\n  constructor(\n    /** Reference to the current item from the collection. */\n    public $implicit: T,\n\n    /**\n     * The value of the iterable expression. Useful when the expression is\n     * more complex then a property access, for example when using the async pipe\n     * (`userStreams | async`).\n     */\n    public ngForOf: U,\n\n    /** Returns an index of the current item in the collection. */\n    public index: number,\n\n    /** Returns total amount of items in the collection. */\n    public count: number,\n  ) {}\n\n  // Indicates whether this is the first item in the collection.\n  get first(): boolean {\n    return this.index === 0;\n  }\n\n  // Indicates whether this is the last item in the collection.\n  get last(): boolean {\n    return this.index === this.count - 1;\n  }\n\n  // Indicates whether an index of this item in the collection is even.\n  get even(): boolean {\n    return this.index % 2 === 0;\n  }\n\n  // Indicates whether an index of this item in the collection is odd.\n  get odd(): boolean {\n    return !this.even;\n  }\n}\n\n/**\n * A [structural directive](guide/directives/structural-directives) that renders\n * a template for each item in a collection.\n * The directive is placed on an element, which becomes the parent\n * of the cloned templates.\n *\n * The `ngForOf` directive is generally used in the\n * [shorthand form](guide/directives/structural-directives#asterisk) `*ngFor`.\n * In this form, the template to be rendered for each iteration is the content\n * of an anchor element containing the directive.\n *\n * The following example shows the shorthand syntax with some options,\n * contained in an `<li>` element.\n *\n * ```html\n * <li *ngFor=\"let item of items; index as i; trackBy: trackByFn\">...</li>\n * ```\n *\n * The shorthand form expands into a long form that uses the `ngForOf` selector\n * on an `<ng-template>` element.\n * The content of the `<ng-template>` element is the `<li>` element that held the\n * short-form directive.\n *\n * Here is the expanded version of the short-form example.\n *\n * ```html\n * <ng-template ngFor let-item [ngForOf]=\"items\" let-i=\"index\" [ngForTrackBy]=\"trackByFn\">\n *   <li>...</li>\n * </ng-template>\n * ```\n *\n * Angular automatically expands the shorthand syntax as it compiles the template.\n * The context for each embedded view is logically merged to the current component\n * context according to its lexical position.\n *\n * When using the shorthand syntax, Angular allows only [one structural directive\n * on an element](guide/directives/structural-directives#one-per-element).\n * If you want to iterate conditionally, for example,\n * put the `*ngIf` on a container element that wraps the `*ngFor` element.\n * For further discussion, see\n * [Structural Directives](guide/directives/structural-directives#one-per-element).\n *\n * @usageNotes\n *\n * ### Local variables\n *\n * `NgForOf` provides exported values that can be aliased to local variables.\n * For example:\n *\n *  ```html\n * <li *ngFor=\"let user of users; index as i; first as isFirst\">\n *    {{i}}/{{users.length}}. {{user}} <span *ngIf=\"isFirst\">default</span>\n * </li>\n * ```\n *\n * The following exported values can be aliased to local variables:\n *\n * - `$implicit: T`: The value of the individual items in the iterable (`ngForOf`).\n * - `ngForOf: NgIterable<T>`: The value of the iterable expression. Useful when the expression is\n * more complex then a property access, for example when using the async pipe (`userStreams |\n * async`).\n * - `index: number`: The index of the current item in the iterable.\n * - `count: number`: The length of the iterable.\n * - `first: boolean`: True when the item is the first item in the iterable.\n * - `last: boolean`: True when the item is the last item in the iterable.\n * - `even: boolean`: True when the item has an even index in the iterable.\n * - `odd: boolean`: True when the item has an odd index in the iterable.\n *\n * ### Change propagation\n *\n * When the contents of the iterator changes, `NgForOf` makes the corresponding changes to the DOM:\n *\n * * When an item is added, a new instance of the template is added to the DOM.\n * * When an item is removed, its template instance is removed from the DOM.\n * * When items are reordered, their respective templates are reordered in the DOM.\n *\n * Angular uses object identity to track insertions and deletions within the iterator and reproduce\n * those changes in the DOM. This has important implications for animations and any stateful\n * controls that are present, such as `<input>` elements that accept user input. Inserted rows can\n * be animated in, deleted rows can be animated out, and unchanged rows retain any unsaved state\n * such as user input.\n * For more on animations, see [Transitions and Triggers](guide/animations/transition-and-triggers).\n *\n * The identities of elements in the iterator can change while the data does not.\n * This can happen, for example, if the iterator is produced from an RPC to the server, and that\n * RPC is re-run. Even if the data hasn't changed, the second response produces objects with\n * different identities, and Angular must tear down the entire DOM and rebuild it (as if all old\n * elements were deleted and all new elements inserted).\n *\n * To avoid this expensive operation, you can customize the default tracking algorithm.\n * by supplying the `trackBy` option to `NgForOf`.\n * `trackBy` takes a function that has two arguments: `index` and `item`.\n * If `trackBy` is given, Angular tracks changes by the return value of the function.\n *\n * @see [Structural Directives](guide/directives/structural-directives)\n * @ngModule CommonModule\n * @publicApi\n *\n * @deprecated 20.0\n * Use the `@for` block instead. Intent to remove in v22\n */\n@Directive({\n  selector: '[ngFor][ngForOf]',\n})\nexport class NgForOf<T, U extends NgIterable<T> = NgIterable<T>> implements DoCheck {\n  /**\n   * The value of the iterable expression, which can be used as a\n   * [template input variable](guide/directives/structural-directives#shorthand).\n   * @deprecated The `ngFor` directive is deprecated. Use the `@for` block instead.\n   */\n  @Input()\n  set ngForOf(ngForOf: (U & NgIterable<T>) | undefined | null) {\n    this._ngForOf = ngForOf;\n    this._ngForOfDirty = true;\n  }\n  /**\n   * Specifies a custom `TrackByFunction` to compute the identity of items in an iterable.\n   *\n   * If a custom `TrackByFunction` is not provided, `NgForOf` will use the item's [object\n   * identity](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is)\n   * as the key.\n   *\n   * `NgForOf` uses the computed key to associate items in an iterable with DOM elements\n   * it produces for these items.\n   *\n   * A custom `TrackByFunction` is useful to provide good user experience in cases when items in an\n   * iterable rendered using `NgForOf` have a natural identifier (for example, custom ID or a\n   * primary key), and this iterable could be updated with new object instances that still\n   * represent the same underlying entity (for example, when data is re-fetched from the server,\n   * and the iterable is recreated and re-rendered, but most of the data is still the same).\n   *\n   * @see {@link TrackByFunction}\n   * @deprecated The `ngFor` directive is deprecated. Use the `@for` block instead.\n   */\n  @Input()\n  set ngForTrackBy(fn: TrackByFunction<T>) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && fn != null && typeof fn !== 'function') {\n      console.warn(\n        `trackBy must be a function, but received ${JSON.stringify(fn)}. ` +\n          `See https://angular.dev/api/common/NgForOf#change-propagation for more information.`,\n      );\n    }\n    this._trackByFn = fn;\n  }\n\n  get ngForTrackBy(): TrackByFunction<T> {\n    return this._trackByFn;\n  }\n\n  private _ngForOf: U | undefined | null = null;\n  private _ngForOfDirty: boolean = true;\n  private _differ: IterableDiffer<T> | null = null;\n  // waiting for microsoft/typescript#43662 to allow the return type `TrackByFunction|undefined` for\n  // the getter\n  private _trackByFn!: TrackByFunction<T>;\n\n  constructor(\n    private _viewContainer: ViewContainerRef,\n    private _template: TemplateRef<NgForOfContext<T, U>>,\n    private _differs: IterableDiffers,\n  ) {}\n\n  /**\n   * A reference to the template that is stamped out for each item in the iterable.\n   * @see [template reference variable](guide/templates/variables#template-reference-variables)\n   * @deprecated The `ngFor` directive is deprecated. Use the `@for` block instead.\n   */\n  @Input()\n  set ngForTemplate(value: TemplateRef<NgForOfContext<T, U>>) {\n    // TODO(TS2.1): make TemplateRef<Partial<NgForRowOf<T>>> once we move to TS v2.1\n    // The current type is too restrictive; a template that just uses index, for example,\n    // should be acceptable.\n    if (value) {\n      this._template = value;\n    }\n  }\n\n  /**\n   * Applies the changes when needed.\n   * @docs-private\n   */\n  ngDoCheck(): void {\n    if (this._ngForOfDirty) {\n      this._ngForOfDirty = false;\n      // React on ngForOf changes only once all inputs have been initialized\n      const value = this._ngForOf;\n      if (!this._differ && value) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          try {\n            // CAUTION: this logic is duplicated for production mode below, as the try-catch\n            // is only present in development builds.\n            this._differ = this._differs.find(value).create(this.ngForTrackBy);\n          } catch {\n            let errorMessage =\n              `Cannot find a differ supporting object '${value}' of type '` +\n              `${getTypeName(value)}'. NgFor only supports binding to Iterables, such as Arrays.`;\n            if (typeof value === 'object') {\n              errorMessage += ' Did you mean to use the keyvalue pipe?';\n            }\n            throw new RuntimeError(RuntimeErrorCode.NG_FOR_MISSING_DIFFER, errorMessage);\n          }\n        } else {\n          // CAUTION: this logic is duplicated for development mode above, as the try-catch\n          // is only present in development builds.\n          this._differ = this._differs.find(value).create(this.ngForTrackBy);\n        }\n      }\n    }\n    if (this._differ) {\n      const changes = this._differ.diff(this._ngForOf);\n      if (changes) this._applyChanges(changes);\n    }\n  }\n\n  private _applyChanges(changes: IterableChanges<T>) {\n    const viewContainer = this._viewContainer;\n    changes.forEachOperation(\n      (\n        item: IterableChangeRecord<T>,\n        adjustedPreviousIndex: number | null,\n        currentIndex: number | null,\n      ) => {\n        if (item.previousIndex == null) {\n          // NgForOf is never \"null\" or \"undefined\" here because the differ detected\n          // that a new item needs to be inserted from the iterable. This implies that\n          // there is an iterable value for \"_ngForOf\".\n          viewContainer.createEmbeddedView(\n            this._template,\n            new NgForOfContext<T, U>(item.item, this._ngForOf!, -1, -1),\n            currentIndex === null ? undefined : currentIndex,\n          );\n        } else if (currentIndex == null) {\n          viewContainer.remove(adjustedPreviousIndex === null ? undefined : adjustedPreviousIndex);\n        } else if (adjustedPreviousIndex !== null) {\n          const view = viewContainer.get(adjustedPreviousIndex)!;\n          viewContainer.move(view, currentIndex);\n          applyViewChange(view as EmbeddedViewRef<NgForOfContext<T, U>>, item);\n        }\n      },\n    );\n\n    for (let i = 0, ilen = viewContainer.length; i < ilen; i++) {\n      const viewRef = <EmbeddedViewRef<NgForOfContext<T, U>>>viewContainer.get(i);\n      const context = viewRef.context;\n      context.index = i;\n      context.count = ilen;\n      context.ngForOf = this._ngForOf!;\n    }\n\n    changes.forEachIdentityChange((record: any) => {\n      const viewRef = <EmbeddedViewRef<NgForOfContext<T, U>>>viewContainer.get(record.currentIndex);\n      applyViewChange(viewRef, record);\n    });\n  }\n\n  /**\n   * Asserts the correct type of the context for the template that `NgForOf` will render.\n   *\n   * The presence of this method is a signal to the Ivy template type-check compiler that the\n   * `NgForOf` structural directive renders its template with a specific context type.\n   */\n  static ngTemplateContextGuard<T, U extends NgIterable<T>>(\n    dir: NgForOf<T, U>,\n    ctx: any,\n  ): ctx is NgForOfContext<T, U> {\n    return true;\n  }\n}\n\n// Also export the `NgForOf` class as `NgFor` to improve the DX for\n// cases when the directive is used as standalone, so the class name\n// matches the CSS selector (*ngFor).\nexport {NgForOf as NgFor};\n\nfunction applyViewChange<T>(\n  view: EmbeddedViewRef<NgForOfContext<T>>,\n  record: IterableChangeRecord<T>,\n) {\n  view.context.$implicit = record.item;\n}\n\nfunction getTypeName(type: any): string {\n  return type['name'] || typeof type;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Directive,\n  EmbeddedViewRef,\n  Input,\n  TemplateRef,\n  ViewContainerRef,\n  ɵstringify as stringify,\n  ɵRuntimeError as RuntimeError,\n} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../errors';\n\n/**\n * A structural directive that conditionally includes a template based on the value of\n * an expression coerced to <PERSON><PERSON><PERSON>.\n * When the expression evaluates to true, <PERSON><PERSON> renders the template\n * provided in a `then` clause, and when  false or null,\n * <PERSON><PERSON> renders the template provided in an optional `else` clause. The default\n * template for the `else` clause is blank.\n *\n * A [shorthand form](guide/directives/structural-directives#asterisk) of the directive,\n * `*ngIf=\"condition\"`, is generally used, provided\n * as an attribute of the anchor element for the inserted template.\n * <PERSON><PERSON> expands this into a more explicit version, in which the anchor element\n * is contained in an `<ng-template>` element.\n *\n * Simple form with shorthand syntax:\n *\n * ```html\n * <div *ngIf=\"condition\">Content to render when condition is true.</div>\n * ```\n *\n * Simple form with expanded syntax:\n *\n * ```html\n * <ng-template [ngIf]=\"condition\"><div>Content to render when condition is\n * true.</div></ng-template>\n * ```\n *\n * Form with an \"else\" block:\n *\n * ```html\n * <div *ngIf=\"condition; else elseBlock\">Content to render when condition is true.</div>\n * <ng-template #elseBlock>Content to render when condition is false.</ng-template>\n * ```\n *\n * Shorthand form with \"then\" and \"else\" blocks:\n *\n * ```html\n * <div *ngIf=\"condition; then thenBlock else elseBlock\"></div>\n * <ng-template #thenBlock>Content to render when condition is true.</ng-template>\n * <ng-template #elseBlock>Content to render when condition is false.</ng-template>\n * ```\n *\n * Form with storing the value locally:\n *\n * ```html\n * <div *ngIf=\"condition as value; else elseBlock\">{{value}}</div>\n * <ng-template #elseBlock>Content to render when value is null.</ng-template>\n * ```\n *\n * @usageNotes\n *\n * The `*ngIf` directive is most commonly used to conditionally show an inline template,\n * as seen in the following  example.\n * The default `else` template is blank.\n *\n * {@example common/ngIf/ts/module.ts region='NgIfSimple'}\n *\n * ### Showing an alternative template using `else`\n *\n * To display a template when `expression` evaluates to false, use an `else` template\n * binding as shown in the following example.\n * The `else` binding points to an `<ng-template>`  element labeled `#elseBlock`.\n * The template can be defined anywhere in the component view, but is typically placed right after\n * `ngIf` for readability.\n *\n * {@example common/ngIf/ts/module.ts region='NgIfElse'}\n *\n * ### Using an external `then` template\n *\n * In the previous example, the then-clause template is specified inline, as the content of the\n * tag that contains the `ngIf` directive. You can also specify a template that is defined\n * externally, by referencing a labeled `<ng-template>` element. When you do this, you can\n * change which template to use at runtime, as shown in the following example.\n *\n * {@example common/ngIf/ts/module.ts region='NgIfThenElse'}\n *\n * ### Storing a conditional result in a variable\n *\n * You might want to show a set of properties from the same object. If you are waiting\n * for asynchronous data, the object can be undefined.\n * In this case, you can use `ngIf` and store the result of the condition in a local\n * variable as shown in the following example.\n *\n * {@example common/ngIf/ts/module.ts region='NgIfAs'}\n *\n * This code uses only one `AsyncPipe`, so only one subscription is created.\n * The conditional statement stores the result of `userStream|async` in the local variable `user`.\n * You can then bind the local `user` repeatedly.\n *\n * The conditional displays the data only if `userStream` returns a value,\n * so you don't need to use the\n * safe-navigation-operator (`?.`)\n * to guard against null values when accessing properties.\n * You can display an alternative template while waiting for the data.\n *\n * ### Shorthand syntax\n *\n * The shorthand syntax `*ngIf` expands into two separate template specifications\n * for the \"then\" and \"else\" clauses. For example, consider the following shorthand statement,\n * that is meant to show a loading page while waiting for data to be loaded.\n *\n * ```html\n * <div class=\"hero-list\" *ngIf=\"heroes else loading\">\n *  ...\n * </div>\n *\n * <ng-template #loading>\n *  <div>Loading...</div>\n * </ng-template>\n * ```\n *\n * You can see that the \"else\" clause references the `<ng-template>`\n * with the `#loading` label, and the template for the \"then\" clause\n * is provided as the content of the anchor element.\n *\n * However, when Angular expands the shorthand syntax, it creates\n * another `<ng-template>` tag, with `ngIf` and `ngIfElse` directives.\n * The anchor element containing the template for the \"then\" clause becomes\n * the content of this unlabeled `<ng-template>` tag.\n *\n * ```html\n * <ng-template [ngIf]=\"heroes\" [ngIfElse]=\"loading\">\n *  <div class=\"hero-list\">\n *   ...\n *  </div>\n * </ng-template>\n *\n * <ng-template #loading>\n *  <div>Loading...</div>\n * </ng-template>\n * ```\n *\n * The presence of the implicit template object has implications for the nesting of\n * structural directives. For more on this subject, see\n * [Structural Directives](guide/directives/structural-directives#one-per-element).\n *\n * @ngModule CommonModule\n * @publicApi\n *\n * @deprecated 20.0\n * Use the `@if` block instead. Intent to remove in v22\n */\n@Directive({\n  selector: '[ngIf]',\n})\nexport class NgIf<T = unknown> {\n  private _context: NgIfContext<T> = new NgIfContext<T>();\n  private _thenTemplateRef: TemplateRef<NgIfContext<T>> | null = null;\n  private _elseTemplateRef: TemplateRef<NgIfContext<T>> | null = null;\n  private _thenViewRef: EmbeddedViewRef<NgIfContext<T>> | null = null;\n  private _elseViewRef: EmbeddedViewRef<NgIfContext<T>> | null = null;\n\n  constructor(\n    private _viewContainer: ViewContainerRef,\n    templateRef: TemplateRef<NgIfContext<T>>,\n  ) {\n    this._thenTemplateRef = templateRef;\n  }\n\n  /**\n   * The Boolean expression to evaluate as the condition for showing a template.\n   * @deprecated Use the `@if` block instead. Intent to remove in v22\n   */\n  @Input()\n  set ngIf(condition: T) {\n    this._context.$implicit = this._context.ngIf = condition;\n    this._updateView();\n  }\n\n  /**\n   * A template to show if the condition expression evaluates to true.\n   * @deprecated Use the `@if` block instead. Intent to remove in v22\n   */\n  @Input()\n  set ngIfThen(templateRef: TemplateRef<NgIfContext<T>> | null) {\n    assertTemplate(templateRef, (typeof ngDevMode === 'undefined' || ngDevMode) && 'ngIfThen');\n    this._thenTemplateRef = templateRef;\n    this._thenViewRef = null; // clear previous view if any.\n    this._updateView();\n  }\n\n  /**\n   * A template to show if the condition expression evaluates to false.\n   * @deprecated Use the `@if` block instead. Intent to remove in v22\n   */\n  @Input()\n  set ngIfElse(templateRef: TemplateRef<NgIfContext<T>> | null) {\n    assertTemplate(templateRef, (typeof ngDevMode === 'undefined' || ngDevMode) && 'ngIfElse');\n    this._elseTemplateRef = templateRef;\n    this._elseViewRef = null; // clear previous view if any.\n    this._updateView();\n  }\n\n  private _updateView() {\n    if (this._context.$implicit) {\n      if (!this._thenViewRef) {\n        this._viewContainer.clear();\n        this._elseViewRef = null;\n        if (this._thenTemplateRef) {\n          this._thenViewRef = this._viewContainer.createEmbeddedView(\n            this._thenTemplateRef,\n            this._context,\n          );\n        }\n      }\n    } else {\n      if (!this._elseViewRef) {\n        this._viewContainer.clear();\n        this._thenViewRef = null;\n        if (this._elseTemplateRef) {\n          this._elseViewRef = this._viewContainer.createEmbeddedView(\n            this._elseTemplateRef,\n            this._context,\n          );\n        }\n      }\n    }\n  }\n\n  /** @internal */\n  public static ngIfUseIfTypeGuard: void;\n\n  /**\n   * Assert the correct type of the expression bound to the `ngIf` input within the template.\n   *\n   * The presence of this static field is a signal to the Ivy template type check compiler that\n   * when the `NgIf` structural directive renders its template, the type of the expression bound\n   * to `ngIf` should be narrowed in some way. For `NgIf`, the binding expression itself is used to\n   * narrow its type, which allows the strictNullChecks feature of TypeScript to work with `NgIf`.\n   */\n  static ngTemplateGuard_ngIf: 'binding';\n\n  /**\n   * Asserts the correct type of the context for the template that `NgIf` will render.\n   *\n   * The presence of this method is a signal to the Ivy template type-check compiler that the\n   * `NgIf` structural directive renders its template with a specific context type.\n   */\n  static ngTemplateContextGuard<T>(\n    dir: NgIf<T>,\n    ctx: any,\n  ): ctx is NgIfContext<Exclude<T, false | 0 | '' | null | undefined>> {\n    return true;\n  }\n}\n\n/**\n * @publicApi\n *\n * @deprecated 20.0\n * The ngIf directive is deprecated in favor of the @if block instead.\n */\nexport class NgIfContext<T = unknown> {\n  public $implicit: T = null!;\n  public ngIf: T = null!;\n}\n\nfunction assertTemplate(\n  templateRef: TemplateRef<any> | null,\n  property: string | false | null,\n): void {\n  if (templateRef && !templateRef.createEmbeddedView) {\n    throw new RuntimeError(\n      RuntimeErrorCode.NG_IF_NOT_A_TEMPLATE_REF,\n      (typeof ngDevMode === 'undefined' || ngDevMode) &&\n        `${property} must be a TemplateRef, but received '${stringify(templateRef)}'.`,\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Directive,\n  DoCheck,\n  Host,\n  Input,\n  Optional,\n  ɵRuntimeError as RuntimeError,\n  TemplateRef,\n  ViewContainerRef,\n} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../errors';\n\nexport class SwitchView {\n  private _created = false;\n\n  constructor(\n    private _viewContainerRef: ViewContainerRef,\n    private _templateRef: TemplateRef<Object>,\n  ) {}\n\n  create(): void {\n    this._created = true;\n    this._viewContainerRef.createEmbeddedView(this._templateRef);\n  }\n\n  destroy(): void {\n    this._created = false;\n    this._viewContainerRef.clear();\n  }\n\n  enforceState(created: boolean) {\n    if (created && !this._created) {\n      this.create();\n    } else if (!created && this._created) {\n      this.destroy();\n    }\n  }\n}\n\n/**\n * @ngModule CommonModule\n *\n * @description\n * The `[ngSwitch]` directive on a container specifies an expression to match against.\n * The expressions to match are provided by `ngSwitchCase` directives on views within the container.\n * - Every view that matches is rendered.\n * - If there are no matches, a view with the `ngSwitchDefault` directive is rendered.\n * - Elements within the `[NgSwitch]` statement but outside of any `NgSwitchCase`\n * or `ngSwitchDefault` directive are preserved at the location.\n *\n * @usageNotes\n * Define a container element for the directive, and specify the switch expression\n * to match against as an attribute:\n *\n * ```html\n * <container-element [ngSwitch]=\"switch_expression\">\n * ```\n *\n * Within the container, `*ngSwitchCase` statements specify the match expressions\n * as attributes. Include `*ngSwitchDefault` as the final case.\n *\n * ```html\n * <container-element [ngSwitch]=\"switch_expression\">\n *    <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n * ...\n *    <some-element *ngSwitchDefault>...</some-element>\n * </container-element>\n * ```\n *\n * ### Usage Examples\n *\n * The following example shows how to use more than one case to display the same view:\n *\n * ```html\n * <container-element [ngSwitch]=\"switch_expression\">\n *   <!-- the same view can be shown in more than one case -->\n *   <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n *   <some-element *ngSwitchCase=\"match_expression_2\">...</some-element>\n *   <some-other-element *ngSwitchCase=\"match_expression_3\">...</some-other-element>\n *   <!--default case when there are no matches -->\n *   <some-element *ngSwitchDefault>...</some-element>\n * </container-element>\n * ```\n *\n * The following example shows how cases can be nested:\n * ```html\n * <container-element [ngSwitch]=\"switch_expression\">\n *       <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n *       <some-element *ngSwitchCase=\"match_expression_2\">...</some-element>\n *       <some-other-element *ngSwitchCase=\"match_expression_3\">...</some-other-element>\n *       <ng-container *ngSwitchCase=\"match_expression_3\">\n *         <!-- use a ng-container to group multiple root nodes -->\n *         <inner-element></inner-element>\n *         <inner-other-element></inner-other-element>\n *       </ng-container>\n *       <some-element *ngSwitchDefault>...</some-element>\n *     </container-element>\n * ```\n *\n * @publicApi\n * @see {@link NgSwitchCase}\n * @see {@link NgSwitchDefault}\n * @see [Structural Directives](guide/directives/structural-directives)\n *\n * @deprecated 20.0\n * Use the `@switch` block instead. Intent to remove in v22\n */\n@Directive({\n  selector: '[ngSwitch]',\n})\nexport class NgSwitch {\n  private _defaultViews: SwitchView[] = [];\n  private _defaultUsed = false;\n  private _caseCount = 0;\n  private _lastCaseCheckIndex = 0;\n  private _lastCasesMatched = false;\n  private _ngSwitch: any;\n\n  /** @deprecated Use the `@switch` block instead. Intent to remove in v22 */\n  @Input()\n  set ngSwitch(newValue: any) {\n    this._ngSwitch = newValue;\n    if (this._caseCount === 0) {\n      this._updateDefaultCases(true);\n    }\n  }\n\n  /** @internal */\n  _addCase(): number {\n    return this._caseCount++;\n  }\n\n  /** @internal */\n  _addDefault(view: SwitchView) {\n    this._defaultViews.push(view);\n  }\n\n  /** @internal */\n  _matchCase(value: any): boolean {\n    const matched = value === this._ngSwitch;\n    this._lastCasesMatched ||= matched;\n    this._lastCaseCheckIndex++;\n    if (this._lastCaseCheckIndex === this._caseCount) {\n      this._updateDefaultCases(!this._lastCasesMatched);\n      this._lastCaseCheckIndex = 0;\n      this._lastCasesMatched = false;\n    }\n    return matched;\n  }\n\n  private _updateDefaultCases(useDefault: boolean) {\n    if (this._defaultViews.length > 0 && useDefault !== this._defaultUsed) {\n      this._defaultUsed = useDefault;\n      for (const defaultView of this._defaultViews) {\n        defaultView.enforceState(useDefault);\n      }\n    }\n  }\n}\n\n/**\n * @ngModule CommonModule\n *\n * @description\n * Provides a switch case expression to match against an enclosing `ngSwitch` expression.\n * When the expressions match, the given `NgSwitchCase` template is rendered.\n * If multiple match expressions match the switch expression value, all of them are displayed.\n *\n * @usageNotes\n *\n * Within a switch container, `*ngSwitchCase` statements specify the match expressions\n * as attributes. Include `*ngSwitchDefault` as the final case.\n *\n * ```html\n * <container-element [ngSwitch]=\"switch_expression\">\n *   <some-element *ngSwitchCase=\"match_expression_1\">...</some-element>\n *   ...\n *   <some-element *ngSwitchDefault>...</some-element>\n * </container-element>\n * ```\n *\n * Each switch-case statement contains an in-line HTML template or template reference\n * that defines the subtree to be selected if the value of the match expression\n * matches the value of the switch expression.\n *\n * As of Angular v17 the NgSwitch directive uses strict equality comparison (`===`) instead of\n * loose equality (`==`) to match different cases.\n *\n * @publicApi\n * @see {@link NgSwitch}\n * @see {@link NgSwitchDefault}\n *\n * @deprecated 20.0\n * Use the `@case` block within a `@switch` block instead. Intent to remove in v22\n */\n@Directive({\n  selector: '[ngSwitchCase]',\n})\nexport class NgSwitchCase implements DoCheck {\n  private _view: SwitchView;\n  /**\n   * Stores the HTML template to be selected on match.\n   * @deprecated Use the `@case` block within a `@switch` block instead. Intent to remove in v22\n   */\n  @Input() ngSwitchCase: any;\n\n  constructor(\n    viewContainer: ViewContainerRef,\n    templateRef: TemplateRef<Object>,\n    @Optional() @Host() private ngSwitch: NgSwitch,\n  ) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !ngSwitch) {\n      throwNgSwitchProviderNotFoundError('ngSwitchCase', 'NgSwitchCase');\n    }\n\n    ngSwitch._addCase();\n    this._view = new SwitchView(viewContainer, templateRef);\n  }\n\n  /**\n   * Performs case matching. For internal use only.\n   * @docs-private\n   */\n  ngDoCheck() {\n    this._view.enforceState(this.ngSwitch._matchCase(this.ngSwitchCase));\n  }\n}\n\n/**\n * @ngModule CommonModule\n *\n * @description\n *\n * Creates a view that is rendered when no `NgSwitchCase` expressions\n * match the `NgSwitch` expression.\n * This statement should be the final case in an `NgSwitch`.\n *\n * @publicApi\n * @see {@link NgSwitch}\n * @see {@link NgSwitchCase}\n *\n * @deprecated 20.0\n * Use the `@default` block within a `@switch` block instead. Intent to remove in v22\n */\n@Directive({\n  selector: '[ngSwitchDefault]',\n})\nexport class NgSwitchDefault {\n  constructor(\n    viewContainer: ViewContainerRef,\n    templateRef: TemplateRef<Object>,\n    @Optional() @Host() ngSwitch: NgSwitch,\n  ) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !ngSwitch) {\n      throwNgSwitchProviderNotFoundError('ngSwitchDefault', 'NgSwitchDefault');\n    }\n\n    ngSwitch._addDefault(new SwitchView(viewContainer, templateRef));\n  }\n}\n\nfunction throwNgSwitchProviderNotFoundError(attrName: string, directiveName: string): never {\n  throw new RuntimeError(\n    RuntimeErrorCode.PARENT_NG_SWITCH_NOT_FOUND,\n    `An element with the \"${attrName}\" attribute ` +\n      `(matching the \"${directiveName}\" directive) must be located inside an element with the \"ngSwitch\" attribute ` +\n      `(matching \"NgSwitch\" directive)`,\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Attribute, Directive, Host, Input, TemplateRef, ViewContainerRef} from '@angular/core';\n\nimport {getPluralCategory, NgLocalization} from '../i18n/localization';\n\nimport {SwitchView} from './ng_switch';\n\n/**\n * @ngModule CommonModule\n *\n * @usageNotes\n * ```html\n * <some-element [ngPlural]=\"value\">\n *   <ng-template ngPluralCase=\"=0\">there is nothing</ng-template>\n *   <ng-template ngPluralCase=\"=1\">there is one</ng-template>\n *   <ng-template ngPluralCase=\"few\">there are a few</ng-template>\n * </some-element>\n * ```\n *\n * @description\n *\n * Adds / removes DOM sub-trees based on a numeric value. Tailored for pluralization.\n *\n * Displays DOM sub-trees that match the switch expression value, or failing that, DOM sub-trees\n * that match the switch expression's pluralization category.\n *\n * To use this directive you must provide a container element that sets the `[ngPlural]` attribute\n * to a switch expression. Inner elements with a `[ngPluralCase]` will display based on their\n * expression:\n * - if `[ngPluralCase]` is set to a value starting with `=`, it will only display if the value\n *   matches the switch expression exactly,\n * - otherwise, the view will be treated as a \"category match\", and will only display if exact\n *   value matches aren't found and the value maps to its category for the defined locale.\n *\n * See http://cldr.unicode.org/index/cldr-spec/plural-rules\n *\n * @publicApi\n */\n@Directive({\n  selector: '[ngPlural]',\n})\nexport class NgPlural {\n  private _activeView?: SwitchView;\n  private _caseViews: {[k: string]: SwitchView} = {};\n\n  constructor(private _localization: NgLocalization) {}\n\n  @Input()\n  set ngPlural(value: number) {\n    this._updateView(value);\n  }\n\n  addCase(value: string, switchView: SwitchView): void {\n    this._caseViews[value] = switchView;\n  }\n\n  private _updateView(switchValue: number): void {\n    this._clearViews();\n\n    const cases = Object.keys(this._caseViews);\n    const key = getPluralCategory(switchValue, cases, this._localization);\n    this._activateView(this._caseViews[key]);\n  }\n\n  private _clearViews() {\n    if (this._activeView) this._activeView.destroy();\n  }\n\n  private _activateView(view: SwitchView) {\n    if (view) {\n      this._activeView = view;\n      this._activeView.create();\n    }\n  }\n}\n\n/**\n * @ngModule CommonModule\n *\n * @description\n *\n * Creates a view that will be added/removed from the parent {@link NgPlural} when the\n * given expression matches the plural expression according to CLDR rules.\n *\n * @usageNotes\n * ```html\n * <some-element [ngPlural]=\"value\">\n *   <ng-template ngPluralCase=\"=0\">...</ng-template>\n *   <ng-template ngPluralCase=\"other\">...</ng-template>\n * </some-element>\n *```\n *\n * See {@link NgPlural} for more details and example.\n *\n * @publicApi\n */\n@Directive({\n  selector: '[ngPluralCase]',\n})\nexport class NgPluralCase {\n  constructor(\n    @Attribute('ngPluralCase') public value: string,\n    template: TemplateRef<Object>,\n    viewContainer: ViewContainerRef,\n    @Host() ngPlural: NgPlural,\n  ) {\n    const isANumber: boolean = !isNaN(Number(value));\n    ngPlural.addCase(isANumber ? `=${value}` : value, new SwitchView(viewContainer, template));\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {\n  Directive,\n  Do<PERSON>heck,\n  ElementRef,\n  Input,\n  KeyValueChanges,\n  KeyValueDiffer,\n  KeyValueDiffers,\n  Renderer2,\n  RendererStyleFlags2,\n} from '@angular/core';\n\n/**\n * @ngModule CommonModule\n *\n * @usageNotes\n *\n * Set the width of the containing element to a pixel value returned by an expression.\n *\n * ```html\n * <some-element [ngStyle]=\"{'max-width.px': widthExp}\">...</some-element>\n * ```\n *\n * Set a collection of style values using an expression that returns key-value pairs.\n *\n * ```html\n * <some-element [ngStyle]=\"objExp\">...</some-element>\n * ```\n *\n * For more simple use cases you can use the [style bindings](/guide/templates/binding#css-class-and-style-property-bindings) directly.\n * It doesn't require importing a directive.\n *\n * Set the font of the containing element to the result of an expression.\n *\n * ```html\n * <some-element [style]=\"{'font-style': styleExp}\">...</some-element>\n * ```\n *\n * @description\n *\n * An attribute directive that updates styles for the containing HTML element.\n * Sets one or more style properties, specified as colon-separated key-value pairs.\n * The key is a style name, with an optional `.<unit>` suffix\n * (such as 'top.px', 'font-style.em').\n * The value is an expression to be evaluated.\n * The resulting non-null value, expressed in the given unit,\n * is assigned to the given style property.\n * If the result of evaluation is null, the corresponding style is removed.\n *\n * @see [Style bindings](/guide/templates/binding#css-class-and-style-property-bindings)\n *\n * @publicApi\n */\n@Directive({\n  selector: '[ngStyle]',\n})\nexport class NgStyle implements DoCheck {\n  private _ngStyle: {[key: string]: string} | null | undefined = null;\n  private _differ: KeyValueDiffer<string, string | number> | null = null;\n\n  constructor(\n    private _ngEl: ElementRef,\n    private _differs: KeyValueDiffers,\n    private _renderer: Renderer2,\n  ) {}\n\n  @Input('ngStyle')\n  set ngStyle(values: {[klass: string]: any} | null | undefined) {\n    this._ngStyle = values;\n    if (!this._differ && values) {\n      this._differ = this._differs.find(values).create();\n    }\n  }\n\n  ngDoCheck() {\n    if (this._differ) {\n      const changes = this._differ.diff(this._ngStyle!);\n      if (changes) {\n        this._applyChanges(changes);\n      }\n    }\n  }\n\n  private _setStyle(nameAndUnit: string, value: string | number | null | undefined): void {\n    const [name, unit] = nameAndUnit.split('.');\n    const flags = name.indexOf('-') === -1 ? undefined : (RendererStyleFlags2.DashCase as number);\n\n    if (value != null) {\n      this._renderer.setStyle(\n        this._ngEl.nativeElement,\n        name,\n        unit ? `${value}${unit}` : value,\n        flags,\n      );\n    } else {\n      this._renderer.removeStyle(this._ngEl.nativeElement, name, flags);\n    }\n  }\n\n  private _applyChanges(changes: KeyValueChanges<string, string | number>): void {\n    changes.forEachRemovedItem((record) => this._setStyle(record.key, null));\n    changes.forEachAddedItem((record) => this._setStyle(record.key, record.currentValue));\n    changes.forEachChangedItem((record) => this._setStyle(record.key, record.currentValue));\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Directive,\n  EmbeddedViewRef,\n  Injector,\n  Input,\n  OnChanges,\n  SimpleChange,\n  SimpleChanges,\n  TemplateRef,\n  ViewContainerRef,\n} from '@angular/core';\n\n/**\n * @ngModule CommonModule\n *\n * @description\n *\n * Inserts an embedded view from a prepared `TemplateRef`.\n *\n * You can attach a context object to the `EmbeddedViewRef` by setting `[ngTemplateOutletContext]`.\n * `[ngTemplateOutletContext]` should be an object, the object's keys will be available for binding\n * by the local template `let` declarations.\n *\n * @usageNotes\n * ```html\n * <ng-container *ngTemplateOutlet=\"templateRefExp; context: contextExp\"></ng-container>\n * ```\n *\n * Using the key `$implicit` in the context object will set its value as default.\n *\n * ### Example\n *\n * {@example common/ngTemplateOutlet/ts/module.ts region='NgTemplateOutlet'}\n *\n * @publicApi\n */\n@Directive({\n  selector: '[ngTemplateOutlet]',\n})\nexport class NgTemplateOutlet<C = unknown> implements OnChanges {\n  private _viewRef: EmbeddedViewRef<C> | null = null;\n\n  /**\n   * A context object to attach to the {@link EmbeddedViewRef}. This should be an\n   * object, the object's keys will be available for binding by the local template `let`\n   * declarations.\n   * Using the key `$implicit` in the context object will set its value as default.\n   */\n  @Input() public ngTemplateOutletContext: C | null | undefined = null;\n\n  /**\n   * A string defining the template reference and optionally the context object for the template.\n   */\n  @Input() public ngTemplateOutlet: TemplateRef<C> | null | undefined = null;\n\n  /** Injector to be used within the embedded view. */\n  @Input() public ngTemplateOutletInjector: Injector | null | undefined = null;\n\n  constructor(private _viewContainerRef: ViewContainerRef) {}\n\n  ngOnChanges(changes: SimpleChanges) {\n    if (this._shouldRecreateView(changes)) {\n      const viewContainerRef = this._viewContainerRef;\n\n      if (this._viewRef) {\n        viewContainerRef.remove(viewContainerRef.indexOf(this._viewRef));\n      }\n\n      // If there is no outlet, clear the destroyed view ref.\n      if (!this.ngTemplateOutlet) {\n        this._viewRef = null;\n        return;\n      }\n\n      // Create a context forward `Proxy` that will always bind to the user-specified context,\n      // without having to destroy and re-create views whenever the context changes.\n      const viewContext = this._createContextForwardProxy();\n      this._viewRef = viewContainerRef.createEmbeddedView(this.ngTemplateOutlet, viewContext, {\n        injector: this.ngTemplateOutletInjector ?? undefined,\n      });\n    }\n  }\n\n  /**\n   * We need to re-create existing embedded view if either is true:\n   * - the outlet changed.\n   * - the injector changed.\n   */\n  private _shouldRecreateView(changes: SimpleChanges): boolean {\n    return !!changes['ngTemplateOutlet'] || !!changes['ngTemplateOutletInjector'];\n  }\n\n  /**\n   * For a given outlet instance, we create a proxy object that delegates\n   * to the user-specified context. This allows changing, or swapping out\n   * the context object completely without having to destroy/re-create the view.\n   */\n  private _createContextForwardProxy(): C {\n    return <C>new Proxy(\n      {},\n      {\n        set: (_target, prop, newValue) => {\n          if (!this.ngTemplateOutletContext) {\n            return false;\n          }\n          return Reflect.set(this.ngTemplateOutletContext, prop, newValue);\n        },\n        get: (_target, prop, receiver) => {\n          if (!this.ngTemplateOutletContext) {\n            return undefined;\n          }\n          return Reflect.get(this.ngTemplateOutletContext, prop, receiver);\n        },\n      },\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Provider} from '@angular/core';\n\nimport {NgClass} from './ng_class';\nimport {NgComponentOutlet} from './ng_component_outlet';\nimport {Ng<PERSON>or, NgForOf, NgForOfContext} from './ng_for_of';\nimport {NgIf, NgIfContext} from './ng_if';\nimport {NgPlural, NgPluralCase} from './ng_plural';\nimport {NgStyle} from './ng_style';\nimport {Ng<PERSON><PERSON>, NgSwitchCase, NgSwitchDefault} from './ng_switch';\nimport {NgTemplateOutlet} from './ng_template_outlet';\n\nexport {\n  NgClass,\n  NgComponentOutlet,\n  NgFor,\n  NgForOf,\n  NgForOfContext,\n  NgIf,\n  NgIfContext,\n  NgPlural,\n  NgPluralCase,\n  Ng<PERSON><PERSON>le,\n  NgS<PERSON>,\n  Ng<PERSON><PERSON>Case,\n  <PERSON><PERSON><PERSON>Default,\n  NgTemplateOutlet,\n};\n\n/**\n * A collection of Angular directives that are likely to be used in each and every Angular\n * application.\n */\nexport const COMMON_DIRECTIVES: Provider[] = [\n  NgClass,\n  NgComponentOutlet,\n  NgForOf,\n  NgIf,\n  NgTemplateOutlet,\n  NgStyle,\n  NgSwitch,\n  NgSwitchCase,\n  NgSwitchDefault,\n  NgPlural,\n  NgPluralCase,\n];\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Type, ɵRuntimeError as RuntimeError, ɵstringify as stringify} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../errors';\n\nexport function invalidPipeArgumentError(type: Type<any>, value: Object) {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_PIPE_ARGUMENT,\n    ngDevMode && `InvalidPipeArgument: '${value}' for pipe '${stringify(type)}'`,\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ChangeDetectorRef,\n  EventEmitter,\n  OnDestroy,\n  Pipe,\n  PipeTransform,\n  untracked,\n  ɵisPromise,\n  ɵisSubscribable,\n  ɵINTERNAL_APPLICATION_ERROR_HANDLER as INTERNAL_APPLICATION_ERROR_HANDLER,\n  inject,\n} from '@angular/core';\nimport type {Observable, Subscribable, Unsubscribable} from 'rxjs';\n\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\ninterface SubscriptionStrategy {\n  createSubscription(\n    async: Subscribable<any> | PromiseLike<any>,\n    updateLatestValue: any,\n    onError: (e: unknown) => void,\n  ): Unsubscribable | PromiseLike<any>;\n  dispose(subscription: Unsubscribable | PromiseLike<any>): void;\n}\n\nclass SubscribableStrategy implements SubscriptionStrategy {\n  createSubscription(\n    async: Subscribable<any>,\n    updateLatestValue: any,\n    onError: (e: unknown) => void,\n  ): Unsubscribable {\n    // Subscription can be side-effectful, and we don't want any signal reads which happen in the\n    // side effect of the subscription to be tracked by a component's template when that\n    // subscription is triggered via the async pipe. So we wrap the subscription in `untracked` to\n    // decouple from the current reactive context.\n    //\n    // `untracked` also prevents signal _writes_ which happen in the subscription side effect from\n    // being treated as signal writes during the template evaluation (which throws errors).\n    return untracked(() =>\n      async.subscribe({\n        next: updateLatestValue,\n        error: onError,\n      }),\n    );\n  }\n\n  dispose(subscription: Unsubscribable): void {\n    // See the comment in `createSubscription` above on the use of `untracked`.\n    untracked(() => subscription.unsubscribe());\n  }\n}\n\nclass PromiseStrategy implements SubscriptionStrategy {\n  createSubscription(\n    async: PromiseLike<any>,\n    updateLatestValue: ((v: any) => any) | null,\n    onError: ((e: unknown) => void) | null,\n  ): Unsubscribable {\n    // According to the promise specification, promises are not cancellable by default.\n    // Once a promise is created, it will either resolve or reject, and it doesn't\n    // provide a built-in mechanism to cancel it.\n    // There may be situations where a promise is provided, and it either resolves after\n    // the pipe has been destroyed or never resolves at all. If the promise never\n    // resolves — potentially due to factors beyond our control, such as third-party\n    // libraries — this can lead to a memory leak.\n    // When we use `async.then(updateLatestValue)`, the engine captures a reference to the\n    // `updateLatestValue` function. This allows the promise to invoke that function when it\n    // resolves. In this case, the promise directly captures a reference to the\n    // `updateLatestValue` function. If the promise resolves later, it retains a reference\n    // to the original `updateLatestValue`, meaning that even if the context where\n    // `updateLatestValue` was defined has been destroyed, the function reference remains in memory.\n    // This can lead to memory leaks if `updateLatestValue` is no longer needed or if it holds\n    // onto resources that should be released.\n    // When we do `async.then(v => ...)` the promise captures a reference to the lambda\n    // function (the arrow function).\n    // When we assign `updateLatestValue = null` within the context of an `unsubscribe` function,\n    // we're changing the reference of `updateLatestValue` in the current scope to `null`.\n    // The lambda will no longer have access to it after the assignment, effectively\n    // preventing any further calls to the original function and allowing it to be garbage collected.\n    async.then(\n      // Using optional chaining because we may have set it to `null`; since the promise\n      // is async, the view might be destroyed by the time the promise resolves.\n      (v) => updateLatestValue?.(v),\n      (e) => onError?.(e),\n    );\n    return {\n      unsubscribe: () => {\n        updateLatestValue = null;\n        onError = null;\n      },\n    };\n  }\n\n  dispose(subscription: Unsubscribable): void {\n    subscription.unsubscribe();\n  }\n}\n\nconst _promiseStrategy = new PromiseStrategy();\nconst _subscribableStrategy = new SubscribableStrategy();\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Unwraps a value from an asynchronous primitive.\n *\n * The `async` pipe subscribes to an `Observable` or `Promise` and returns the latest value it has\n * emitted. When a new value is emitted, the `async` pipe marks the component to be checked for\n * changes. When the component gets destroyed, the `async` pipe unsubscribes automatically to avoid\n * potential memory leaks. When the reference of the expression changes, the `async` pipe\n * automatically unsubscribes from the old `Observable` or `Promise` and subscribes to the new one.\n *\n * @usageNotes\n *\n * ### Examples\n *\n * This example binds a `Promise` to the view. Clicking the `Resolve` button resolves the\n * promise.\n *\n * {@example common/pipes/ts/async_pipe.ts region='AsyncPipePromise'}\n *\n * It's also possible to use `async` with Observables. The example below binds the `time` Observable\n * to the view. The Observable continuously updates the view with the current time.\n *\n * {@example common/pipes/ts/async_pipe.ts region='AsyncPipeObservable'}\n *\n * @publicApi\n */\n@Pipe({\n  name: 'async',\n  pure: false,\n})\nexport class AsyncPipe implements OnDestroy, PipeTransform {\n  private _ref: ChangeDetectorRef | null;\n  private _latestValue: any = null;\n  private markForCheckOnValueUpdate = true;\n\n  private _subscription: Unsubscribable | PromiseLike<any> | null = null;\n  private _obj: Subscribable<any> | PromiseLike<any> | EventEmitter<any> | null = null;\n  private _strategy: SubscriptionStrategy | null = null;\n  private readonly applicationErrorHandler = inject(INTERNAL_APPLICATION_ERROR_HANDLER);\n\n  constructor(ref: ChangeDetectorRef) {\n    // Assign `ref` into `this._ref` manually instead of declaring `_ref` in the constructor\n    // parameter list, as the type of `this._ref` includes `null` unlike the type of `ref`.\n    this._ref = ref;\n  }\n\n  ngOnDestroy(): void {\n    if (this._subscription) {\n      this._dispose();\n    }\n    // Clear the `ChangeDetectorRef` and its association with the view data, to mitigate\n    // potential memory leaks in Observables that could otherwise cause the view data to\n    // be retained.\n    // https://github.com/angular/angular/issues/17624\n    this._ref = null;\n  }\n\n  // NOTE(@benlesh): Because Observable has deprecated a few call patterns for `subscribe`,\n  // TypeScript has a hard time matching Observable to Subscribable, for more information\n  // see https://github.com/microsoft/TypeScript/issues/43643\n\n  transform<T>(obj: Observable<T> | Subscribable<T> | PromiseLike<T>): T | null;\n  transform<T>(obj: null | undefined): null;\n  transform<T>(obj: Observable<T> | Subscribable<T> | PromiseLike<T> | null | undefined): T | null;\n  transform<T>(obj: Observable<T> | Subscribable<T> | PromiseLike<T> | null | undefined): T | null {\n    if (!this._obj) {\n      if (obj) {\n        try {\n          // Only call `markForCheck` if the value is updated asynchronously.\n          // Synchronous updates _during_ subscription should not wastefully mark for check -\n          // this value is already going to be returned from the transform function.\n          this.markForCheckOnValueUpdate = false;\n          this._subscribe(obj);\n        } finally {\n          this.markForCheckOnValueUpdate = true;\n        }\n      }\n      return this._latestValue;\n    }\n\n    if (obj !== this._obj) {\n      this._dispose();\n      return this.transform(obj);\n    }\n\n    return this._latestValue;\n  }\n\n  private _subscribe(obj: Subscribable<any> | PromiseLike<any> | EventEmitter<any>): void {\n    this._obj = obj;\n    this._strategy = this._selectStrategy(obj);\n    this._subscription = this._strategy.createSubscription(\n      obj,\n      (value: Object) => this._updateLatestValue(obj, value),\n      (e) => this.applicationErrorHandler(e),\n    );\n  }\n\n  private _selectStrategy(\n    obj: Subscribable<any> | PromiseLike<any> | EventEmitter<any>,\n  ): SubscriptionStrategy {\n    if (ɵisPromise(obj)) {\n      return _promiseStrategy;\n    }\n\n    if (ɵisSubscribable(obj)) {\n      return _subscribableStrategy;\n    }\n\n    throw invalidPipeArgumentError(AsyncPipe, obj);\n  }\n\n  private _dispose(): void {\n    // Note: `dispose` is only called if a subscription has been initialized before, indicating\n    // that `this._strategy` is also available.\n    this._strategy!.dispose(this._subscription!);\n    this._latestValue = null;\n    this._subscription = null;\n    this._obj = null;\n  }\n\n  private _updateLatestValue(async: any, value: Object): void {\n    if (async === this._obj) {\n      this._latestValue = value;\n      if (this.markForCheckOnValueUpdate) {\n        this._ref?.markForCheck();\n      }\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Pipe, PipeTransform} from '@angular/core';\n\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\n/**\n * Transforms text to all lower case.\n *\n * @see {@link UpperCasePipe}\n * @see {@link TitleCasePipe}\n * @usageNotes\n *\n * The following example defines a view that allows the user to enter\n * text, and then uses the pipe to convert the input text to all lower case.\n *\n * {@example common/pipes/ts/lowerupper_pipe.ts region='LowerUpperPipe'}\n *\n * @ngModule CommonModule\n * @publicApi\n */\n@Pipe({\n  name: 'lowercase',\n})\nexport class LowerCasePipe implements PipeTransform {\n  /**\n   * @param value The string to transform to lower case.\n   */\n  transform(value: string): string;\n  transform(value: null | undefined): null;\n  transform(value: string | null | undefined): string | null;\n  transform(value: string | null | undefined): string | null {\n    if (value == null) return null;\n    if (typeof value !== 'string') {\n      throw invalidPipeArgumentError(LowerCasePipe, value);\n    }\n    return value.toLowerCase();\n  }\n}\n\n//\n// Regex below matches any Unicode word and number compatible with ES5. In ES2018 the same result\n// can be achieved by using /[0-9\\p{L}]\\S*/gu and also known as Unicode Property Escapes\n// (https://2ality.com/2017/07/regexp-unicode-property-escapes.html). Since there is no\n// transpilation of this functionality down to ES5 without external tool, the only solution is\n// to use already transpiled form. Example can be found here -\n// https://mothereff.in/regexpu#input=var+regex+%3D+%2F%5B0-9%5Cp%7BL%7D%5D%5CS*%2Fgu%3B%0A%0A&unicodePropertyEscape=1\n//\n\nconst unicodeWordMatch =\n  /(?:[0-9A-Za-z\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05D0-\\u05EA\\u05EF-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086A\\u0870-\\u0887\\u0889-\\u088E\\u08A0-\\u08C9\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u09FC\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C5D\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D04-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E86-\\u0E8A\\u0E8C-\\u0EA3\\u0EA5\\u0EA7-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16F1-\\u16F8\\u1700-\\u1711\\u171F-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1878\\u1880-\\u1884\\u1887-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4C\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C88\\u1C90-\\u1CBA\\u1CBD-\\u1CBF\\u1CE9-\\u1CEC\\u1CEE-\\u1CF3\\u1CF5\\u1CF6\\u1CFA\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2183\\u2184\\u2C00-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005\\u3006\\u3031-\\u3035\\u303B\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312F\\u3131-\\u318E\\u31A0-\\u31BF\\u31F0-\\u31FF\\u3400-\\u4DBF\\u4E00-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6E5\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7CA\\uA7D0\\uA7D1\\uA7D3\\uA7D5-\\uA7D9\\uA7F2-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA8FE\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB69\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDF00-\\uDF1F\\uDF2D-\\uDF40\\uDF42-\\uDF49\\uDF50-\\uDF75\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF]|\\uD801[\\uDC00-\\uDC9D\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDD70-\\uDD7A\\uDD7C-\\uDD8A\\uDD8C-\\uDD92\\uDD94\\uDD95\\uDD97-\\uDDA1\\uDDA3-\\uDDB1\\uDDB3-\\uDDB9\\uDDBB\\uDDBC\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67\\uDF80-\\uDF85\\uDF87-\\uDFB0\\uDFB2-\\uDFBA]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00\\uDE10-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE35\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE4\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2\\uDD00-\\uDD23\\uDE80-\\uDEA9\\uDEB0\\uDEB1\\uDF00-\\uDF1C\\uDF27\\uDF30-\\uDF45\\uDF70-\\uDF81\\uDFB0-\\uDFC4\\uDFE0-\\uDFF6]|\\uD804[\\uDC03-\\uDC37\\uDC71\\uDC72\\uDC75\\uDC83-\\uDCAF\\uDCD0-\\uDCE8\\uDD03-\\uDD26\\uDD44\\uDD47\\uDD50-\\uDD72\\uDD76\\uDD83-\\uDDB2\\uDDC1-\\uDDC4\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE2B\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEDE\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3D\\uDF50\\uDF5D-\\uDF61]|\\uD805[\\uDC00-\\uDC34\\uDC47-\\uDC4A\\uDC5F-\\uDC61\\uDC80-\\uDCAF\\uDCC4\\uDCC5\\uDCC7\\uDD80-\\uDDAE\\uDDD8-\\uDDDB\\uDE00-\\uDE2F\\uDE44\\uDE80-\\uDEAA\\uDEB8\\uDF00-\\uDF1A\\uDF40-\\uDF46]|\\uD806[\\uDC00-\\uDC2B\\uDCA0-\\uDCDF\\uDCFF-\\uDD06\\uDD09\\uDD0C-\\uDD13\\uDD15\\uDD16\\uDD18-\\uDD2F\\uDD3F\\uDD41\\uDDA0-\\uDDA7\\uDDAA-\\uDDD0\\uDDE1\\uDDE3\\uDE00\\uDE0B-\\uDE32\\uDE3A\\uDE50\\uDE5C-\\uDE89\\uDE9D\\uDEB0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC2E\\uDC40\\uDC72-\\uDC8F\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD30\\uDD46\\uDD60-\\uDD65\\uDD67\\uDD68\\uDD6A-\\uDD89\\uDD98\\uDEE0-\\uDEF2\\uDFB0]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC80-\\uDD43]|\\uD80B[\\uDF90-\\uDFF0]|[\\uD80C\\uD81C-\\uD820\\uD822\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879\\uD880-\\uD883][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2E]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDE70-\\uDEBE\\uDED0-\\uDEED\\uDF00-\\uDF2F\\uDF40-\\uDF43\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDE40-\\uDE7F\\uDF00-\\uDF4A\\uDF50\\uDF93-\\uDF9F\\uDFE0\\uDFE1\\uDFE3]|\\uD821[\\uDC00-\\uDFF7]|\\uD823[\\uDC00-\\uDCD5\\uDD00-\\uDD08]|\\uD82B[\\uDFF0-\\uDFF3\\uDFF5-\\uDFFB\\uDFFD\\uDFFE]|\\uD82C[\\uDC00-\\uDD22\\uDD50-\\uDD52\\uDD64-\\uDD67\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB]|\\uD837[\\uDF00-\\uDF1E]|\\uD838[\\uDD00-\\uDD2C\\uDD37-\\uDD3D\\uDD4E\\uDE90-\\uDEAD\\uDEC0-\\uDEEB]|\\uD839[\\uDFE0-\\uDFE6\\uDFE8-\\uDFEB\\uDFED\\uDFEE\\uDFF0-\\uDFFE]|\\uD83A[\\uDC00-\\uDCC4\\uDD00-\\uDD43\\uDD4B]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDEDF\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF38\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D]|\\uD884[\\uDC00-\\uDF4A])\\S*/g;\n\n/**\n * Transforms text to title case.\n * Capitalizes the first letter of each word and transforms the\n * rest of the word to lower case.\n * Words are delimited by any whitespace character, such as a space, tab, or line-feed character.\n *\n * @see {@link LowerCasePipe}\n * @see {@link UpperCasePipe}\n *\n * @usageNotes\n * The following example shows the result of transforming various strings into title case.\n *\n * {@example common/pipes/ts/titlecase_pipe.ts region='TitleCasePipe'}\n *\n * @ngModule CommonModule\n * @publicApi\n */\n@Pipe({\n  name: 'titlecase',\n})\nexport class TitleCasePipe implements PipeTransform {\n  /**\n   * @param value The string to transform to title case.\n   */\n  transform(value: string): string;\n  transform(value: null | undefined): null;\n  transform(value: string | null | undefined): string | null;\n  transform(value: string | null | undefined): string | null {\n    if (value == null) return null;\n    if (typeof value !== 'string') {\n      throw invalidPipeArgumentError(TitleCasePipe, value);\n    }\n\n    return value.replace(\n      unicodeWordMatch,\n      (txt) => txt[0].toUpperCase() + txt.slice(1).toLowerCase(),\n    );\n  }\n}\n\n/**\n * Transforms text to all upper case.\n * @see {@link LowerCasePipe}\n * @see {@link TitleCasePipe}\n *\n * @ngModule CommonModule\n * @publicApi\n */\n@Pipe({\n  name: 'uppercase',\n})\nexport class UpperCasePipe implements PipeTransform {\n  /**\n   * @param value The string to transform to upper case.\n   */\n  transform(value: string): string;\n  transform(value: null | undefined): null;\n  transform(value: string | null | undefined): string | null;\n  transform(value: string | null | undefined): string | null {\n    if (value == null) return null;\n    if (typeof value !== 'string') {\n      throw invalidPipeArgumentError(UpperCasePipe, value);\n    }\n    return value.toUpperCase();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * An interface that describes the date pipe configuration, which can be provided using the\n * `DATE_PIPE_DEFAULT_OPTIONS` token.\n *\n * @see {@link DATE_PIPE_DEFAULT_OPTIONS}\n *\n * @publicApi\n */\nexport interface DatePipeConfig {\n  dateFormat?: string;\n  timezone?: string;\n}\n\n/**\n * The default date format of Angular date pipe, which corresponds to the following format:\n * `'MMM d,y'` (e.g. `Jun 15, 2015`)\n */\nexport const DEFAULT_DATE_FORMAT = 'mediumDate';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Inject, InjectionToken, LOCALE_ID, Optional, Pipe, PipeTransform} from '@angular/core';\n\nimport {formatDate} from '../i18n/format_date';\n\nimport {DatePipeConfig, DEFAULT_DATE_FORMAT} from './date_pipe_config';\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\n/**\n * Optionally-provided default timezone to use for all instances of `DatePipe` (such as `'+0430'`).\n * If the value isn't provided, the `DatePipe` will use the end-user's local system timezone.\n *\n * @deprecated use DATE_PIPE_DEFAULT_OPTIONS token to configure DatePipe\n */\nexport const DATE_PIPE_DEFAULT_TIMEZONE = new InjectionToken<string>(\n  ngDevMode ? 'DATE_PIPE_DEFAULT_TIMEZONE' : '',\n);\n\n/**\n * DI token that allows to provide default configuration for the `DatePipe` instances in an\n * application. The value is an object which can include the following fields:\n * - `dateFormat`: configures the default date format. If not provided, the `DatePipe`\n * will use the 'mediumDate' as a value.\n * - `timezone`: configures the default timezone. If not provided, the `DatePipe` will\n * use the end-user's local system timezone.\n *\n * @see {@link DatePipeConfig}\n *\n * @usageNotes\n *\n * Various date pipe default values can be overwritten by providing this token with\n * the value that has this interface.\n *\n * For example:\n *\n * Override the default date format by providing a value using the token:\n * ```ts\n * providers: [\n *   {provide: DATE_PIPE_DEFAULT_OPTIONS, useValue: {dateFormat: 'shortDate'}}\n * ]\n * ```\n *\n * Override the default timezone by providing a value using the token:\n * ```ts\n * providers: [\n *   {provide: DATE_PIPE_DEFAULT_OPTIONS, useValue: {timezone: '-1200'}}\n * ]\n * ```\n */\nexport const DATE_PIPE_DEFAULT_OPTIONS = new InjectionToken<DatePipeConfig>(\n  ngDevMode ? 'DATE_PIPE_DEFAULT_OPTIONS' : '',\n);\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a date value according to locale rules.\n *\n * `DatePipe` is executed only when it detects a pure change to the input value.\n * A pure change is either a change to a primitive input value\n * (such as `String`, `Number`, `Boolean`, or `Symbol`),\n * or a changed object reference (such as `Date`, `Array`, `Function`, or `Object`).\n *\n * Note that mutating a `Date` object does not cause the pipe to be rendered again.\n * To ensure that the pipe is executed, you must create a new `Date` object.\n *\n * Only the `en-US` locale data comes with Angular. To localize dates\n * in another language, you must import the corresponding locale data.\n * See the [I18n guide](guide/i18n/format-data-locale) for more information.\n *\n * The time zone of the formatted value can be specified either by passing it in as the second\n * parameter of the pipe, or by setting the default through the `DATE_PIPE_DEFAULT_OPTIONS`\n * injection token. The value that is passed in as the second parameter takes precedence over\n * the one defined using the injection token.\n *\n * @see {@link formatDate}\n *\n *\n * @usageNotes\n *\n * The result of this pipe is not reevaluated when the input is mutated. To avoid the need to\n * reformat the date on every change-detection cycle, treat the date as an immutable object\n * and change the reference when the pipe needs to run again.\n *\n * ### Pre-defined format options\n *\n * | Option        | Equivalent to                       | Examples (given in `en-US` locale)              |\n * |---------------|-------------------------------------|-------------------------------------------------|\n * | `'short'`     | `'M/d/yy, h:mm a'`                  | `6/15/15, 9:03 AM`                              |\n * | `'medium'`    | `'MMM d, y, h:mm:ss a'`             | `Jun 15, 2015, 9:03:01 AM`                      |\n * | `'long'`      | `'MMMM d, y, h:mm:ss a z'`          | `June 15, 2015 at 9:03:01 AM GMT+1`             |\n * | `'full'`      | `'EEEE, MMMM d, y, h:mm:ss a zzzz'` | `Monday, June 15, 2015 at 9:03:01 AM GMT+01:00` |\n * | `'shortDate'` | `'M/d/yy'`                          | `6/15/15`                                       |\n * | `'mediumDate'`| `'MMM d, y'`                        | `Jun 15, 2015`                                  |\n * | `'longDate'`  | `'MMMM d, y'`                       | `June 15, 2015`                                 |\n * | `'fullDate'`  | `'EEEE, MMMM d, y'`                 | `Monday, June 15, 2015`                         |\n * | `'shortTime'` | `'h:mm a'`                          | `9:03 AM`                                       |\n * | `'mediumTime'`| `'h:mm:ss a'`                       | `9:03:01 AM`                                    |\n * | `'longTime'`  | `'h:mm:ss a z'`                     | `9:03:01 AM GMT+1`                              |\n * | `'fullTime'`  | `'h:mm:ss a zzzz'`                  | `9:03:01 AM GMT+01:00`                          |\n *\n * ### Custom format options\n *\n * You can construct a format string using symbols to specify the components\n * of a date-time value, as described in the following table.\n * Format details depend on the locale.\n * Fields marked with (*) are only available in the extra data set for the given locale.\n *\n *  | Field type              | Format      | Description                                                   | Example Value                                              |\n *  |-------------------------|-------------|---------------------------------------------------------------|------------------------------------------------------------|\n *  | Era                     | G, GG & GGG | Abbreviated                                                   | AD                                                         |\n *  |                         | GGGG        | Wide                                                          | Anno Domini                                                |\n *  |                         | GGGGG       | Narrow                                                        | A                                                          |\n *  | Year                    | y           | Numeric: minimum digits                                       | 2, 20, 201, 2017, 20173                                    |\n *  |                         | yy          | Numeric: 2 digits + zero padded                               | 02, 20, 01, 17, 73                                         |\n *  |                         | yyy         | Numeric: 3 digits + zero padded                               | 002, 020, 201, 2017, 20173                                 |\n *  |                         | yyyy        | Numeric: 4 digits or more + zero padded                       | 0002, 0020, 0201, 2017, 20173                              |\n *  | ISO Week-numbering year | Y           | Numeric: minimum digits                                       | 2, 20, 201, 2017, 20173                                    |\n *  |                         | YY          | Numeric: 2 digits + zero padded                               | 02, 20, 01, 17, 73                                         |\n *  |                         | YYY         | Numeric: 3 digits + zero padded                               | 002, 020, 201, 2017, 20173                                 |\n *  |                         | YYYY        | Numeric: 4 digits or more + zero padded                       | 0002, 0020, 0201, 2017, 20173                              |\n *  | Month                   | M           | Numeric: 1 digit                                              | 9, 12                                                      |\n *  |                         | MM          | Numeric: 2 digits + zero padded                               | 09, 12                                                     |\n *  |                         | MMM         | Abbreviated                                                   | Sep                                                        |\n *  |                         | MMMM        | Wide                                                          | September                                                  |\n *  |                         | MMMMM       | Narrow                                                        | S                                                          |\n *  | Month standalone        | L           | Numeric: 1 digit                                              | 9, 12                                                      |\n *  |                         | LL          | Numeric: 2 digits + zero padded                               | 09, 12                                                     |\n *  |                         | LLL         | Abbreviated                                                   | Sep                                                        |\n *  |                         | LLLL        | Wide                                                          | September                                                  |\n *  |                         | LLLLL       | Narrow                                                        | S                                                          |\n *  | ISO Week of year        | w           | Numeric: minimum digits                                       | 1... 53                                                    |\n *  |                         | ww          | Numeric: 2 digits + zero padded                               | 01... 53                                                   |\n *  | Week of month           | W           | Numeric: 1 digit                                              | 1... 5                                                     |\n *  | Day of month            | d           | Numeric: minimum digits                                       | 1                                                          |\n *  |                         | dd          | Numeric: 2 digits + zero padded                               | 01                                                         |\n *  | Week day                | E, EE & EEE | Abbreviated                                                   | Tue                                                        |\n *  |                         | EEEE        | Wide                                                          | Tuesday                                                    |\n *  |                         | EEEEE       | Narrow                                                        | T                                                          |\n *  |                         | EEEEEE      | Short                                                         | Tu                                                         |\n *  | Week day standalone     | c, cc       | Numeric: 1 digit                                              | 2                                                          |\n *  |                         | ccc         | Abbreviated                                                   | Tue                                                        |\n *  |                         | cccc        | Wide                                                          | Tuesday                                                    |\n *  |                         | ccccc       | Narrow                                                        | T                                                          |\n *  |                         | cccccc      | Short                                                         | Tu                                                         |\n *  | Period                  | a, aa & aaa | Abbreviated                                                   | am/pm or AM/PM                                             |\n *  |                         | aaaa        | Wide (fallback to `a` when missing)                           | ante meridiem/post meridiem                                |\n *  |                         | aaaaa       | Narrow                                                        | a/p                                                        |\n *  | Period*                 | B, BB & BBB | Abbreviated                                                   | mid.                                                       |\n *  |                         | BBBB        | Wide                                                          | am, pm, midnight, noon, morning, afternoon, evening, night |\n *  |                         | BBBBB       | Narrow                                                        | md                                                         |\n *  | Period standalone*      | b, bb & bbb | Abbreviated                                                   | mid.                                                       |\n *  |                         | bbbb        | Wide                                                          | am, pm, midnight, noon, morning, afternoon, evening, night |\n *  |                         | bbbbb       | Narrow                                                        | md                                                         |\n *  | Hour 1-12               | h           | Numeric: minimum digits                                       | 1, 12                                                      |\n *  |                         | hh          | Numeric: 2 digits + zero padded                               | 01, 12                                                     |\n *  | Hour 0-23               | H           | Numeric: minimum digits                                       | 0, 23                                                      |\n *  |                         | HH          | Numeric: 2 digits + zero padded                               | 00, 23                                                     |\n *  | Minute                  | m           | Numeric: minimum digits                                       | 8, 59                                                      |\n *  |                         | mm          | Numeric: 2 digits + zero padded                               | 08, 59                                                     |\n *  | Second                  | s           | Numeric: minimum digits                                       | 0... 59                                                    |\n *  |                         | ss          | Numeric: 2 digits + zero padded                               | 00... 59                                                   |\n *  | Fractional seconds      | S           | Numeric: 1 digit                                              | 0... 9                                                     |\n *  |                         | SS          | Numeric: 2 digits + zero padded                               | 00... 99                                                   |\n *  |                         | SSS         | Numeric: 3 digits + zero padded (= milliseconds)              | 000... 999                                                 |\n *  | Zone                    | z, zz & zzz | Short specific non location format (fallback to O)            | GMT-8                                                      |\n *  |                         | zzzz        | Long specific non location format (fallback to OOOO)          | GMT-08:00                                                  |\n *  |                         | Z, ZZ & ZZZ | ISO8601 basic format                                          | -0800                                                      |\n *  |                         | ZZZZ        | Long localized GMT format                                     | GMT-8:00                                                   |\n *  |                         | ZZZZZ       | ISO8601 extended format + Z indicator for offset 0 (= XXXXX)  | -08:00                                                     |\n *  |                         | O, OO & OOO | Short localized GMT format                                    | GMT-8                                                      |\n *  |                         | OOOO        | Long localized GMT format                                     | GMT-08:00                                                  |\n *\n *\n * ### Format examples\n *\n * These examples transform a date into various formats,\n * assuming that `dateObj` is a JavaScript `Date` object for\n * year: 2015, month: 6, day: 15, hour: 21, minute: 43, second: 11,\n * given in the local time for the `en-US` locale.\n *\n * ```\n * {{ dateObj | date }}               // output is 'Jun 15, 2015'\n * {{ dateObj | date:'medium' }}      // output is 'Jun 15, 2015, 9:43:11 PM'\n * {{ dateObj | date:'shortTime' }}   // output is '9:43 PM'\n * {{ dateObj | date:'mm:ss' }}       // output is '43:11'\n * {{ dateObj | date:\"MMM dd, yyyy 'at' hh:mm a\" }}  // output is 'Jun 15, 2015 at 09:43 PM'\n * ```\n *\n * ### Usage example\n *\n * The following component uses a date pipe to display the current date in different formats.\n *\n * ```angular-ts\n * @Component({\n *  selector: 'date-pipe',\n *  template: `<div>\n *    <p>Today is {{today | date}}</p>\n *    <p>Or if you prefer, {{today | date:'fullDate'}}</p>\n *    <p>The time is {{today | date:'h:mm a z'}}</p>\n *  </div>`\n * })\n * // Get the current date and time as a date-time value.\n * export class DatePipeComponent {\n *   today: number = Date.now();\n * }\n * ```\n *\n * @publicApi\n */\n@Pipe({\n  name: 'date',\n})\nexport class DatePipe implements PipeTransform {\n  constructor(\n    @Inject(LOCALE_ID) private locale: string,\n    @Inject(DATE_PIPE_DEFAULT_TIMEZONE) @Optional() private defaultTimezone?: string | null,\n    @Inject(DATE_PIPE_DEFAULT_OPTIONS) @Optional() private defaultOptions?: DatePipeConfig | null,\n  ) {}\n\n  /**\n   * @param value The date expression: a `Date` object,  a number\n   * (milliseconds since UTC epoch), or an ISO string (https://www.w3.org/TR/NOTE-datetime).\n   * @param format The date/time components to include, using predefined options or a\n   * custom format string.  When not provided, the `DatePipe` looks for the value using the\n   * `DATE_PIPE_DEFAULT_OPTIONS` injection token (and reads the `dateFormat` property).\n   * If the token is not configured, the `mediumDate` is used as a value.\n   * @param timezone A timezone offset (such as `'+0430'`). When not provided, the `DatePipe`\n   * looks for the value using the `DATE_PIPE_DEFAULT_OPTIONS` injection token (and reads\n   * the `timezone` property). If the token is not configured, the end-user's local system\n   * timezone is used as a value.\n   * @param locale A locale code for the locale format rules to use.\n   * When not supplied, uses the value of `LOCALE_ID`, which is `en-US` by default.\n   * See [Setting your app locale](guide/i18n/locale-id).\n   *\n   * @see {@link DATE_PIPE_DEFAULT_OPTIONS}\n   *\n   * @returns A date string in the desired format.\n   */\n  transform(\n    value: Date | string | number,\n    format?: string,\n    timezone?: string,\n    locale?: string,\n  ): string | null;\n  transform(value: null | undefined, format?: string, timezone?: string, locale?: string): null;\n  transform(\n    value: Date | string | number | null | undefined,\n    format?: string,\n    timezone?: string,\n    locale?: string,\n  ): string | null;\n  transform(\n    value: Date | string | number | null | undefined,\n    format?: string,\n    timezone?: string,\n    locale?: string,\n  ): string | null {\n    if (value == null || value === '' || value !== value) return null;\n\n    try {\n      const _format = format ?? this.defaultOptions?.dateFormat ?? DEFAULT_DATE_FORMAT;\n      const _timezone =\n        timezone ?? this.defaultOptions?.timezone ?? this.defaultTimezone ?? undefined;\n      return formatDate(value, _format, locale || this.locale, _timezone);\n    } catch (error) {\n      throw invalidPipeArgumentError(DatePipe, (error as Error).message);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Pipe, PipeTransform} from '@angular/core';\n\nimport {getPluralCategory, NgLocalization} from '../i18n/localization';\n\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\nconst _INTERPOLATION_REGEXP: RegExp = /#/g;\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Maps a value to a string that pluralizes the value according to locale rules.\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example common/pipes/ts/i18n_pipe.ts region='I18nPluralPipeComponent'}\n *\n * @publicApi\n */\n@Pipe({\n  name: 'i18nPlural',\n})\nexport class I18nPluralPipe implements PipeTransform {\n  constructor(private _localization: NgLocalization) {}\n\n  /**\n   * @param value the number to be formatted\n   * @param pluralMap an object that mimics the ICU format, see\n   * https://unicode-org.github.io/icu/userguide/format_parse/messages/.\n   * @param locale a `string` defining the locale to use (uses the current {@link LOCALE_ID} by\n   * default).\n   */\n  transform(\n    value: number | null | undefined,\n    pluralMap: {[count: string]: string},\n    locale?: string,\n  ): string {\n    if (value == null) return '';\n\n    if (typeof pluralMap !== 'object' || pluralMap === null) {\n      throw invalidPipeArgumentError(I18nPluralPipe, pluralMap);\n    }\n\n    const key = getPluralCategory(value, Object.keys(pluralMap), this._localization, locale);\n\n    return pluralMap[key].replace(_INTERPOLATION_REGEXP, value.toString());\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Pipe, PipeTransform} from '@angular/core';\n\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Generic selector that displays the string that matches the current value.\n *\n * If none of the keys of the `mapping` match the `value`, then the content\n * of the `other` key is returned when present, otherwise an empty string is returned.\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example common/pipes/ts/i18n_pipe.ts region='I18nSelectPipeComponent'}\n *\n * @publicApi\n */\n@Pipe({\n  name: 'i18nSelect',\n})\nexport class I18nSelectPipe implements PipeTransform {\n  /**\n   * @param value a string to be internationalized.\n   * @param mapping an object that indicates the text that should be displayed\n   * for different values of the provided `value`.\n   */\n  transform(value: string | null | undefined, mapping: {[key: string]: string}): string {\n    if (value == null) return '';\n\n    if (typeof mapping !== 'object' || typeof value !== 'string') {\n      throw invalidPipeArgumentError(I18nSelectPipe, mapping);\n    }\n\n    if (mapping.hasOwnProperty(value)) {\n      return mapping[value];\n    }\n\n    if (mapping.hasOwnProperty('other')) {\n      return mapping['other'];\n    }\n\n    return '';\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Pipe, PipeTransform} from '@angular/core';\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Converts a value into its JSON-format representation.  Useful for debugging.\n *\n * @usageNotes\n *\n * The following component uses a JSON pipe to convert an object\n * to JSON format, and displays the string in both formats for comparison.\n *\n * {@example common/pipes/ts/json_pipe.ts region='JsonPipe'}\n *\n * @publicApi\n */\n@Pipe({\n  name: 'json',\n  pure: false,\n})\nexport class JsonPipe implements PipeTransform {\n  /**\n   * @param value A value of any type to convert into a JSON-format string.\n   */\n  transform(value: any): string {\n    return JSON.stringify(value, null, 2);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  KeyValueChangeRecord,\n  KeyValueChanges,\n  KeyValueDiffer,\n  KeyValueDiffers,\n  Pipe,\n  PipeTransform,\n} from '@angular/core';\n\nfunction makeKeyValuePair<K, V>(key: K, value: V): KeyValue<K, V> {\n  return {key: key, value: value};\n}\n\n/**\n * A key value pair.\n * Usually used to represent the key value pairs from a Map or Object.\n *\n * @publicApi\n */\nexport interface KeyValue<K, V> {\n  key: K;\n  value: V;\n}\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Transforms Object or Map into an array of key value pairs.\n *\n * The output array will be ordered by keys.\n * By default the comparator will be by Unicode point value.\n * You can optionally pass a compareFn if your keys are complex types.\n * Passing `null` as the compareFn will use natural ordering of the input.\n *\n * @usageNotes\n * ### Examples\n *\n * This examples show how an Object or a Map can be iterated by ngFor with the use of this\n * keyvalue pipe.\n *\n * {@example common/pipes/ts/keyvalue_pipe.ts region='KeyValuePipe'}\n *\n * @publicApi\n */\n@Pipe({\n  name: 'keyvalue',\n  pure: false,\n})\nexport class KeyValuePipe implements PipeTransform {\n  constructor(private readonly differs: KeyValueDiffers) {}\n\n  private differ!: KeyValueDiffer<any, any>;\n  private keyValues: Array<KeyValue<any, any>> = [];\n  private compareFn: ((a: KeyValue<any, any>, b: KeyValue<any, any>) => number) | null =\n    defaultComparator;\n\n  /*\n   * NOTE: when the `input` value is a simple Record<K, V> object, the keys are extracted with\n   * Object.keys(). This means that even if the `input` type is Record<number, V> the keys are\n   * compared/returned as `string`s.\n   */\n  transform<K, V>(\n    input: ReadonlyMap<K, V>,\n    compareFn?: ((a: KeyValue<K, V>, b: KeyValue<K, V>) => number) | null,\n  ): Array<KeyValue<K, V>>;\n  transform<K extends number, V>(\n    input: Record<K, V>,\n    compareFn?: ((a: KeyValue<string, V>, b: KeyValue<string, V>) => number) | null,\n  ): Array<KeyValue<string, V>>;\n  transform<K extends string, V>(\n    input: Record<K, V> | ReadonlyMap<K, V>,\n    compareFn?: ((a: KeyValue<K, V>, b: KeyValue<K, V>) => number) | null,\n  ): Array<KeyValue<K, V>>;\n  transform(\n    input: null | undefined,\n    compareFn?: ((a: KeyValue<unknown, unknown>, b: KeyValue<unknown, unknown>) => number) | null,\n  ): null;\n  transform<K, V>(\n    input: ReadonlyMap<K, V> | null | undefined,\n    compareFn?: ((a: KeyValue<K, V>, b: KeyValue<K, V>) => number) | null,\n  ): Array<KeyValue<K, V>> | null;\n  transform<K extends number, V>(\n    input: Record<K, V> | null | undefined,\n    compareFn?: ((a: KeyValue<string, V>, b: KeyValue<string, V>) => number) | null,\n  ): Array<KeyValue<string, V>> | null;\n  transform<K extends string, V>(\n    input: Record<K, V> | ReadonlyMap<K, V> | null | undefined,\n    compareFn?: ((a: KeyValue<K, V>, b: KeyValue<K, V>) => number) | null,\n  ): Array<KeyValue<K, V>> | null;\n  transform<K, V>(\n    input: undefined | null | {[key: string]: V; [key: number]: V} | ReadonlyMap<K, V>,\n    compareFn: ((a: KeyValue<K, V>, b: KeyValue<K, V>) => number) | null = defaultComparator,\n  ): Array<KeyValue<K, V>> | null {\n    if (!input || (!(input instanceof Map) && typeof input !== 'object')) {\n      return null;\n    }\n\n    // make a differ for whatever type we've been passed in\n    this.differ ??= this.differs.find(input).create();\n\n    const differChanges: KeyValueChanges<K, V> | null = this.differ.diff(input as any);\n    const compareFnChanged = compareFn !== this.compareFn;\n\n    if (differChanges) {\n      this.keyValues = [];\n      differChanges.forEachItem((r: KeyValueChangeRecord<K, V>) => {\n        this.keyValues.push(makeKeyValuePair(r.key, r.currentValue!));\n      });\n    }\n    if (differChanges || compareFnChanged) {\n      if (compareFn) {\n        this.keyValues.sort(compareFn);\n      }\n      this.compareFn = compareFn;\n    }\n    return this.keyValues;\n  }\n}\n\nexport function defaultComparator<K, V>(\n  keyValueA: KeyValue<K, V>,\n  keyValueB: KeyValue<K, V>,\n): number {\n  const a = keyValueA.key;\n  const b = keyValueB.key;\n  // If both keys are the same, return 0 (no sorting needed).\n  if (a === b) return 0;\n  // If one of the keys is `null` or `undefined`, place it at the end of the sort.\n  if (a == null) return 1; // `a` comes after `b`.\n  if (b == null) return -1; // `b` comes after `a`.\n  // If both keys are strings, compare them lexicographically.\n  if (typeof a == 'string' && typeof b == 'string') {\n    return a < b ? -1 : 1;\n  }\n  // If both keys are numbers, sort them numerically.\n  if (typeof a == 'number' && typeof b == 'number') {\n    return a - b;\n  }\n  // If both keys are booleans, sort `false` before `true`.\n  if (typeof a == 'boolean' && typeof b == 'boolean') {\n    return a < b ? -1 : 1;\n  }\n  // Fallback case: if keys are of different types, compare their string representations.\n  const aString = String(a);\n  const bString = String(b);\n  // Compare the string representations lexicographically.\n  return aString == bString ? 0 : aString < bString ? -1 : 1;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  DEFAULT_CURRENCY_CODE,\n  Inject,\n  LOCALE_ID,\n  Pipe,\n  PipeTransform,\n  ɵRuntimeError as RuntimeError,\n} from '@angular/core';\n\nimport {formatCurrency, formatNumber, formatPercent} from '../i18n/format_number';\nimport {getCurrencySymbol} from '../i18n/locale_data_api';\n\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\nimport {RuntimeErrorCode} from '../errors';\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Formats a value according to digit options and locale rules.\n * Locale determines group sizing and separator,\n * decimal point character, and other locale-specific configurations.\n *\n * @see {@link formatNumber}\n *\n * @usageNotes\n *\n * ### digitsInfo\n *\n * The value's decimal representation is specified by the `digitsInfo`\n * parameter, written in the following format:<br>\n *\n * ```\n * {minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}\n * ```\n *\n *  - `minIntegerDigits`:\n * The minimum number of integer digits before the decimal point.\n * Default is 1.\n *\n * - `minFractionDigits`:\n * The minimum number of digits after the decimal point.\n * Default is 0.\n *\n *  - `maxFractionDigits`:\n * The maximum number of digits after the decimal point.\n * Default is 3.\n *\n * If the formatted value is truncated it will be rounded using the \"to-nearest\" method:\n *\n * ```\n * {{3.6 | number: '1.0-0'}}\n * <!--will output '4'-->\n *\n * {{-3.6 | number:'1.0-0'}}\n * <!--will output '-4'-->\n * ```\n *\n * ### locale\n *\n * `locale` will format a value according to locale rules.\n * Locale determines group sizing and separator,\n * decimal point character, and other locale-specific configurations.\n *\n * When not supplied, uses the value of `LOCALE_ID`, which is `en-US` by default.\n *\n * See [Setting your app locale](guide/i18n/locale-id).\n *\n * ### Example\n *\n * The following code shows how the pipe transforms values\n * according to various format specifications,\n * where the caller's default locale is `en-US`.\n *\n * {@example common/pipes/ts/number_pipe.ts region='NumberPipe'}\n *\n * @publicApi\n */\n@Pipe({\n  name: 'number',\n})\nexport class DecimalPipe implements PipeTransform {\n  constructor(@Inject(LOCALE_ID) private _locale: string) {}\n\n  /**\n   * @param value The value to be formatted.\n   * @param digitsInfo Sets digit and decimal representation.\n   * [See more](#digitsinfo).\n   * @param locale Specifies what locale format rules to use.\n   * [See more](#locale).\n   */\n  transform(value: number | string, digitsInfo?: string, locale?: string): string | null;\n  transform(value: null | undefined, digitsInfo?: string, locale?: string): null;\n  transform(\n    value: number | string | null | undefined,\n    digitsInfo?: string,\n    locale?: string,\n  ): string | null;\n  transform(\n    value: number | string | null | undefined,\n    digitsInfo?: string,\n    locale?: string,\n  ): string | null {\n    if (!isValue(value)) return null;\n\n    locale ||= this._locale;\n\n    try {\n      const num = strToNumber(value);\n      return formatNumber(num, locale, digitsInfo);\n    } catch (error) {\n      throw invalidPipeArgumentError(DecimalPipe, (error as Error).message);\n    }\n  }\n}\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Transforms a number to a percentage\n * string, formatted according to locale rules that determine group sizing and\n * separator, decimal-point character, and other locale-specific\n * configurations.\n *\n * @see {@link formatPercent}\n *\n * @usageNotes\n * The following code shows how the pipe transforms numbers\n * into text strings, according to various format specifications,\n * where the caller's default locale is `en-US`.\n *\n * {@example common/pipes/ts/percent_pipe.ts region='PercentPipe'}\n *\n * @publicApi\n */\n@Pipe({\n  name: 'percent',\n})\nexport class PercentPipe implements PipeTransform {\n  constructor(@Inject(LOCALE_ID) private _locale: string) {}\n\n  transform(value: number | string, digitsInfo?: string, locale?: string): string | null;\n  transform(value: null | undefined, digitsInfo?: string, locale?: string): null;\n  transform(\n    value: number | string | null | undefined,\n    digitsInfo?: string,\n    locale?: string,\n  ): string | null;\n  /**\n   *\n   * @param value The number to be formatted as a percentage.\n   * @param digitsInfo Decimal representation options, specified by a string\n   * in the following format:<br>\n   * <code>{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}</code>.\n   *   - `minIntegerDigits`: The minimum number of integer digits before the decimal point.\n   * Default is `1`.\n   *   - `minFractionDigits`: The minimum number of digits after the decimal point.\n   * Default is `0`.\n   *   - `maxFractionDigits`: The maximum number of digits after the decimal point.\n   * Default is `0`.\n   * @param locale A locale code for the locale format rules to use.\n   * When not supplied, uses the value of `LOCALE_ID`, which is `en-US` by default.\n   * See [Setting your app locale](guide/i18n/locale-id).\n   */\n  transform(\n    value: number | string | null | undefined,\n    digitsInfo?: string,\n    locale?: string,\n  ): string | null {\n    if (!isValue(value)) return null;\n    locale ||= this._locale;\n    try {\n      const num = strToNumber(value);\n      return formatPercent(num, locale, digitsInfo);\n    } catch (error) {\n      throw invalidPipeArgumentError(PercentPipe, (error as Error).message);\n    }\n  }\n}\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Transforms a number to a currency string, formatted according to locale rules\n * that determine group sizing and separator, decimal-point character,\n * and other locale-specific configurations.\n *\n *\n * @see {@link getCurrencySymbol}\n * @see {@link formatCurrency}\n *\n * @usageNotes\n * The following code shows how the pipe transforms numbers\n * into text strings, according to various format specifications,\n * where the caller's default locale is `en-US`.\n *\n * {@example common/pipes/ts/currency_pipe.ts region='CurrencyPipe'}\n *\n * @publicApi\n */\n@Pipe({\n  name: 'currency',\n})\nexport class CurrencyPipe implements PipeTransform {\n  constructor(\n    @Inject(LOCALE_ID) private _locale: string,\n    @Inject(DEFAULT_CURRENCY_CODE) private _defaultCurrencyCode: string = 'USD',\n  ) {}\n  /**\n   *\n   * @param value The number to be formatted as currency.\n   * @param currencyCode The [ISO 4217](https://en.wikipedia.org/wiki/ISO_4217) currency code,\n   * such as `USD` for the US dollar and `EUR` for the euro. The default currency code can be\n   * configured using the `DEFAULT_CURRENCY_CODE` injection token.\n   * @param display The format for the currency indicator. One of the following:\n   *   - `code`: Show the code (such as `USD`).\n   *   - `symbol`(default): Show the symbol (such as `$`).\n   *   - `symbol-narrow`: Use the narrow symbol for locales that have two symbols for their\n   * currency.\n   * For example, the Canadian dollar CAD has the symbol `CA$` and the symbol-narrow `$`. If the\n   * locale has no narrow symbol, uses the standard symbol for the locale.\n   *   - String: Use the given string value instead of a code or a symbol.\n   * For example, an empty string will suppress the currency & symbol.\n   *   - Boolean (marked deprecated in v5): `true` for symbol and false for `code`.\n   *\n   * @param digitsInfo Decimal representation options, specified by a string\n   * in the following format:<br>\n   * <code>{minIntegerDigits}.{minFractionDigits}-{maxFractionDigits}</code>.\n   *   - `minIntegerDigits`: The minimum number of integer digits before the decimal point.\n   * Default is `1`.\n   *   - `minFractionDigits`: The minimum number of digits after the decimal point.\n   * Default is `2`.\n   *   - `maxFractionDigits`: The maximum number of digits after the decimal point.\n   * Default is `2`.\n   * If not provided, the number will be formatted with the proper amount of digits,\n   * depending on what the [ISO 4217](https://en.wikipedia.org/wiki/ISO_4217) specifies.\n   * For example, the Canadian dollar has 2 digits, whereas the Chilean peso has none.\n   * @param locale A locale code for the locale format rules to use.\n   * When not supplied, uses the value of `LOCALE_ID`, which is `en-US` by default.\n   * See [Setting your app locale](guide/i18n/locale-id).\n   */\n  transform(\n    value: number | string,\n    currencyCode?: string,\n    display?: 'code' | 'symbol' | 'symbol-narrow' | string | boolean,\n    digitsInfo?: string,\n    locale?: string,\n  ): string | null;\n  transform(\n    value: null | undefined,\n    currencyCode?: string,\n    display?: 'code' | 'symbol' | 'symbol-narrow' | string | boolean,\n    digitsInfo?: string,\n    locale?: string,\n  ): null;\n  transform(\n    value: number | string | null | undefined,\n    currencyCode?: string,\n    display?: 'code' | 'symbol' | 'symbol-narrow' | string | boolean,\n    digitsInfo?: string,\n    locale?: string,\n  ): string | null;\n  transform(\n    value: number | string | null | undefined,\n    currencyCode: string = this._defaultCurrencyCode,\n    display: 'code' | 'symbol' | 'symbol-narrow' | string | boolean = 'symbol',\n    digitsInfo?: string,\n    locale?: string,\n  ): string | null {\n    if (!isValue(value)) return null;\n\n    locale ||= this._locale;\n\n    if (typeof display === 'boolean') {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        console.warn(\n          `Warning: the currency pipe has been changed in Angular v5. The symbolDisplay option (third parameter) is now a string instead of a boolean. The accepted values are \"code\", \"symbol\" or \"symbol-narrow\".`,\n        );\n      }\n      display = display ? 'symbol' : 'code';\n    }\n\n    let currency: string = currencyCode || this._defaultCurrencyCode;\n    if (display !== 'code') {\n      if (display === 'symbol' || display === 'symbol-narrow') {\n        currency = getCurrencySymbol(currency, display === 'symbol' ? 'wide' : 'narrow', locale);\n      } else {\n        currency = display;\n      }\n    }\n\n    try {\n      const num = strToNumber(value);\n      return formatCurrency(num, locale, currency, currencyCode, digitsInfo);\n    } catch (error) {\n      throw invalidPipeArgumentError(CurrencyPipe, (error as Error).message);\n    }\n  }\n}\n\nfunction isValue(value: number | string | null | undefined): value is number | string {\n  return !(value == null || value === '' || value !== value);\n}\n\n/**\n * Transforms a string into a number (if needed).\n */\nfunction strToNumber(value: number | string): number {\n  // Convert strings to numbers\n  if (typeof value === 'string' && !isNaN(Number(value) - parseFloat(value))) {\n    return Number(value);\n  }\n  if (typeof value !== 'number') {\n    throw new RuntimeError(\n      RuntimeErrorCode.VALUE_NOT_A_NUMBER,\n      ngDevMode && `${value} is not a number`,\n    );\n  }\n  return value;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Pipe, PipeTransform} from '@angular/core';\n\nimport {invalidPipeArgumentError} from './invalid_pipe_argument_error';\n\n/**\n * @ngModule CommonModule\n * @description\n *\n * Creates a new `Array` or `String` containing a subset (slice) of the elements.\n *\n * @usageNotes\n *\n * All behavior is based on the expected behavior of the JavaScript API `Array.prototype.slice()`\n * and `String.prototype.slice()`.\n *\n * When operating on an `Array`, the returned `Array` is always a copy even when all\n * the elements are being returned.\n *\n * When operating on a blank value, the pipe returns the blank value.\n *\n * ### List Example\n *\n * This `ngFor` example:\n *\n * {@example common/pipes/ts/slice_pipe.ts region='SlicePipe_list'}\n *\n * produces the following:\n *\n * ```html\n * <li>b</li>\n * <li>c</li>\n * ```\n *\n * ### String Examples\n *\n * {@example common/pipes/ts/slice_pipe.ts region='SlicePipe_string'}\n *\n * @publicApi\n */\n@Pipe({\n  name: 'slice',\n  pure: false,\n})\nexport class SlicePipe implements PipeTransform {\n  /**\n   * @param value a list or a string to be sliced.\n   * @param start the starting index of the subset to return:\n   *   - **a positive integer**: return the item at `start` index and all items after\n   *     in the list or string expression.\n   *   - **a negative integer**: return the item at `start` index from the end and all items after\n   *     in the list or string expression.\n   *   - **if positive and greater than the size of the expression**: return an empty list or\n   * string.\n   *   - **if negative and greater than the size of the expression**: return entire list or string.\n   * @param end the ending index of the subset to return:\n   *   - **omitted**: return all items until the end.\n   *   - **if positive**: return all items before `end` index of the list or string.\n   *   - **if negative**: return all items before `end` index from the end of the list or string.\n   */\n  transform<T>(value: ReadonlyArray<T>, start: number, end?: number): Array<T>;\n  transform(value: null | undefined, start: number, end?: number): null;\n  transform<T>(\n    value: ReadonlyArray<T> | null | undefined,\n    start: number,\n    end?: number,\n  ): Array<T> | null;\n  transform(value: string, start: number, end?: number): string;\n  transform(value: string | null | undefined, start: number, end?: number): string | null;\n  transform<T>(\n    value: ReadonlyArray<T> | string | null | undefined,\n    start: number,\n    end?: number,\n  ): Array<T> | string | null {\n    if (value == null) return null;\n\n    const supports = typeof value === 'string' || Array.isArray(value);\n\n    if (!supports) {\n      throw invalidPipeArgumentError(SlicePipe, value);\n    }\n\n    return value.slice(start, end);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * @module\n * @description\n * This module provides a set of common Pipes.\n */\nimport {AsyncPipe} from './async_pipe';\nimport {LowerCasePipe, TitleCasePipe, UpperCasePipe} from './case_conversion_pipes';\nimport {DATE_PIPE_DEFAULT_OPTIONS, DATE_PIPE_DEFAULT_TIMEZONE, DatePipe} from './date_pipe';\nimport {DatePipeConfig} from './date_pipe_config';\nimport {I18nPluralPipe} from './i18n_plural_pipe';\nimport {I18nSelectPipe} from './i18n_select_pipe';\nimport {JsonPipe} from './json_pipe';\nimport {KeyValue, KeyValuePipe} from './keyvalue_pipe';\nimport {<PERSON>urrencyPipe, DecimalPipe, PercentPipe} from './number_pipe';\nimport {SlicePipe} from './slice_pipe';\n\nexport {\n  AsyncPipe,\n  CurrencyPipe,\n  DATE_PIPE_DEFAULT_OPTIONS,\n  DATE_PIPE_DEFAULT_TIMEZONE,\n  DatePipe,\n  DatePipeConfig,\n  DecimalPipe,\n  I18nPluralPipe,\n  I18nSelectPipe,\n  JsonPipe,\n  KeyValue,\n  KeyValuePipe,\n  LowerCasePipe,\n  PercentPipe,\n  SlicePipe,\n  TitleCasePipe,\n  UpperCasePipe,\n};\n\n/**\n * A collection of Angular pipes that are likely to be used in each and every application.\n */\nexport const COMMON_PIPES = [\n  AsyncPipe,\n  UpperCasePipe,\n  LowerCasePipe,\n  JsonPipe,\n  SlicePipe,\n  DecimalPipe,\n  PercentPipe,\n  TitleCasePipe,\n  CurrencyPipe,\n  DatePipe,\n  I18nPluralPipe,\n  I18nSelectPipe,\n  KeyValuePipe,\n];\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\n\nimport {COMMON_DIRECTIVES} from './directives/index';\nimport {COMMON_PIPES} from './pipes/index';\n\n// Note: This does not contain the location providers,\n// as they need some platform specific implementations to work.\n/**\n * Exports all the basic Angular directives and pipes,\n * such as `NgIf`, `NgForOf`, `DecimalPipe`, and so on.\n * Re-exported by `BrowserModule`, which is included automatically in the root\n * `AppModule` when you create a new app with the CLI `new` command.\n *\n * @publicApi\n */\n@NgModule({\n  imports: [COMMON_DIRECTIVES, COMMON_PIPES],\n  exports: [COMMON_DIRECTIVES, COMMON_PIPES],\n})\nexport class CommonModule {}\n"], "names": ["ɵfindLocaleData", "ɵLocaleDataIndex", "ɵgetLocaleCurrencyCode", "ɵgetLocalePluralCase", "RuntimeError", "formatRuntimeError", "stringify", "i1.NgLocalization", "INTERNAL_APPLICATION_ERROR_HANDLER", "ɵisPromise", "ɵisSubscribable", "i1.<PERSON><PERSON><PERSON>", "i2.NgComponentOutlet", "i3.<PERSON><PERSON><PERSON><PERSON><PERSON>", "i4.Ng<PERSON>f", "i5.NgTemplateOutlet", "i6.<PERSON><PERSON><PERSON><PERSON>", "i7.Ng<PERSON><PERSON>", "i7.NgSwitchCase", "i7.NgSwitchDefault", "i8.<PERSON><PERSON><PERSON><PERSON>", "i8.NgPluralCase", "i9.AsyncPipe", "i10.UpperCasePipe", "i10.LowerCasePipe", "i11.JsonPipe", "i12.SlicePipe", "i13.DecimalPipe", "i13.Percent<PERSON><PERSON><PERSON>", "i10.TitleCasePipe", "i13.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i14.DatePipe", "i15.I18nPluralPipe", "i16.I18nSelectPipe", "i17.KeyValuePipe"], "mappings": ";;;;;;;;;;AAcA;;;;;;;;;;;;;;;;;AAiBG;AAEG,MAAO,oBAAqB,SAAQ,gBAAgB,CAAA;AAK9C,IAAA,iBAAA;IAJF,SAAS,GAAW,EAAE;IACtB,kBAAkB,GAAmB,EAAE;IAE/C,WACU,CAAA,iBAAmC,EACR,SAAkB,EAAA;AAErD,QAAA,KAAK,EAAE;QAHC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;AAIzB,QAAA,IAAI,SAAS,IAAI,IAAI,EAAE;AACrB,YAAA,IAAI,CAAC,SAAS,GAAG,SAAS;;;;IAK9B,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE;AACrC,YAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAG,EAAE;;;AAI3B,IAAA,UAAU,CAAC,EAA0B,EAAA;QAC5C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAC1B,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC,EACrC,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAE,CAAC,CACxC;;IAGM,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,SAAS;;IAGd,IAAI,CAAC,cAAuB,KAAK,EAAA;;;QAGxC,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,IAAI,GAAG;AAE/C,QAAA,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;;AAG1C,IAAA,kBAAkB,CAAC,QAAgB,EAAA;QAC1C,MAAM,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC;AACnD,QAAA,OAAO,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;;AAGhC,IAAA,SAAS,CAAC,KAAU,EAAE,KAAa,EAAE,IAAY,EAAE,WAAmB,EAAA;AAC7E,QAAA,MAAM,GAAG,GACP,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,oBAAoB,CAAC,WAAW,CAAC,CAAC;AACjE,YAAA,IAAI,CAAC,iBAAiB,CAAC,QAAQ;QACjC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC;;AAG5C,IAAA,YAAY,CAAC,KAAU,EAAE,KAAa,EAAE,IAAY,EAAE,WAAmB,EAAA;AAChF,QAAA,MAAM,GAAG,GACP,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,oBAAoB,CAAC,WAAW,CAAC,CAAC;AACjE,YAAA,IAAI,CAAC,iBAAiB,CAAC,QAAQ;QACjC,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC;;IAG/C,OAAO,GAAA;AACd,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE;;IAGzB,IAAI,GAAA;AACX,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE;;IAGtB,QAAQ,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE;;IAGjC,SAAS,CAAC,mBAA2B,CAAC,EAAA;QAC7C,IAAI,CAAC,iBAAiB,CAAC,SAAS,GAAG,gBAAgB,CAAC;;AAxE3C,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,+CAMT,aAAa,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHANxB,oBAAoB,EAAA,CAAA;;sGAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBADhC;;0BAOI;;0BAAY,MAAM;2BAAC,aAAa;;;AC5BrC;AACO,MAAM,aAAa,GAA2F,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,IAAI,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,MAAM,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,OAAO,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,MAAM,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC;;ACSl6G;;;;;;;;AAQG;IACS;AAAZ,CAAA,UAAY,iBAAiB,EAAA;AAC3B,IAAA,iBAAA,CAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP,IAAA,iBAAA,CAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP,IAAA,iBAAA,CAAA,iBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACR,IAAA,iBAAA,CAAA,iBAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU;AACZ,CAAC,EALW,iBAAiB,KAAjB,iBAAiB,GAK5B,EAAA,CAAA,CAAA;AAED;;;;;;;;;;AAUG;IACS;AAAZ,CAAA,UAAY,MAAM,EAAA;AAChB,IAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ;AACR,IAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO;AACP,IAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO;AACP,IAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO;AACP,IAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ;AACR,IAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS;AACX,CAAC,EAPW,MAAM,KAAN,MAAM,GAOjB,EAAA,CAAA,CAAA;AAED;;;;;;;;;;AAUG;IACS;AAAZ,CAAA,UAAY,SAAS,EAAA;AACnB,IAAA,SAAA,CAAA,SAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACN,IAAA,SAAA,CAAA,SAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU;AACZ,CAAC,EAHW,SAAS,KAAT,SAAS,GAGpB,EAAA,CAAA,CAAA;AAED;;;;;;;;AAQG;IACS;AAAZ,CAAA,UAAY,gBAAgB,EAAA;;AAE1B,IAAA,gBAAA,CAAA,gBAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;;AAEN,IAAA,gBAAA,CAAA,gBAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAW;;AAEX,IAAA,gBAAA,CAAA,gBAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;;AAEJ,IAAA,gBAAA,CAAA,gBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACP,CAAC,EATW,gBAAgB,KAAhB,gBAAgB,GAS3B,EAAA,CAAA,CAAA;AAED;;;;;;;;;;;;;AAaG;IACS;AAAZ,CAAA,UAAY,WAAW,EAAA;AACrB;;;AAGG;AACH,IAAA,WAAA,CAAA,WAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACL;;;AAGG;AACH,IAAA,WAAA,CAAA,WAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACN;;;AAGG;AACH,IAAA,WAAA,CAAA,WAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ;;;AAGG;AACH,IAAA,WAAA,CAAA,WAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACN,CAAC,EArBW,WAAW,KAAX,WAAW,GAqBtB,EAAA,CAAA,CAAA;AAED;AACA;AACA;;;;;;;;;;;;AAYG;AACU,MAAA,YAAY,GAAG;AAC1B;;;;AAIG;AACH,IAAA,OAAO,EAAE,CAAC;AACV;;;;AAIG;AACH,IAAA,KAAK,EAAE,CAAC;AACR;;;AAGG;AACH,IAAA,IAAI,EAAE,CAAC;AACP;;;AAGG;AACH,IAAA,WAAW,EAAE,CAAC;AACd;;;AAGG;AACH,IAAA,QAAQ,EAAE,CAAC;AACX;;;AAGG;AACH,IAAA,SAAS,EAAE,CAAC;AACZ;;;AAGG;AACH,IAAA,WAAW,EAAE,CAAC;AACd;;;AAGG;AACH,IAAA,sBAAsB,EAAE,CAAC;AACzB;;;AAGG;AACH,IAAA,QAAQ,EAAE,CAAC;AACX;;;AAGG;AACH,IAAA,QAAQ,EAAE,CAAC;AACX;;;AAGG;AACH,IAAA,GAAG,EAAE,EAAE;AACP;;;AAGG;AACH,IAAA,aAAa,EAAE,EAAE;AACjB;;;AAGG;AACH,IAAA,eAAe,EAAE,EAAE;AACnB;;;AAGG;AACH,IAAA,aAAa,EAAE,EAAE;;AAKnB;;;;;;AAMG;IACS;AAAZ,CAAA,UAAY,OAAO,EAAA;AACjB,IAAA,OAAA,CAAA,OAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;AACV,IAAA,OAAA,CAAA,OAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACN,IAAA,OAAA,CAAA,OAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP,IAAA,OAAA,CAAA,OAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS;AACT,IAAA,OAAA,CAAA,OAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACR,IAAA,OAAA,CAAA,OAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACN,IAAA,OAAA,CAAA,OAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACV,CAAC,EARW,OAAO,KAAP,OAAO,GAQlB,EAAA,CAAA,CAAA;AAED;;;;;;;;;;;AAWG;AACG,SAAU,WAAW,CAAC,MAAc,EAAA;IACxC,OAAOA,eAAe,CAAC,MAAM,CAAC,CAACC,gBAAgB,CAAC,QAAQ,CAAC;AAC3D;AAEA;;;;;;;;;;;;;AAaG;SACa,mBAAmB,CACjC,MAAc,EACd,SAAoB,EACpB,KAAuB,EAAA;AAEvB,IAAA,MAAM,IAAI,GAAGD,eAAe,CAAC,MAAM,CAAC;AACpC,IAAA,MAAM,QAAQ,GAAyB;AACrC,QAAA,IAAI,CAACC,gBAAgB,CAAC,gBAAgB,CAAC;AACvC,QAAA,IAAI,CAACA,gBAAgB,CAAC,oBAAoB,CAAC;KAC5C;IACD,MAAM,IAAI,GAAG,mBAAmB,CAAC,QAAQ,EAAE,SAAS,CAAC;AACrD,IAAA,OAAO,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC;AACzC;AAEA;;;;;;;;;;;;;;AAcG;SACa,iBAAiB,CAC/B,MAAc,EACd,SAAoB,EACpB,KAAuB,EAAA;AAEvB,IAAA,MAAM,IAAI,GAAGD,eAAe,CAAC,MAAM,CAAC;AACpC,IAAA,MAAM,QAAQ,GAAiB;AAC7B,QAAA,IAAI,CAACC,gBAAgB,CAAC,UAAU,CAAC;AACjC,QAAA,IAAI,CAACA,gBAAgB,CAAC,cAAc,CAAC;KACtC;IACD,MAAM,IAAI,GAAG,mBAAmB,CAAC,QAAQ,EAAE,SAAS,CAAC;AACrD,IAAA,OAAO,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC;AACzC;AAEA;;;;;;;;;;;;;;AAcG;SACa,mBAAmB,CACjC,MAAc,EACd,SAAoB,EACpB,KAAuB,EAAA;AAEvB,IAAA,MAAM,IAAI,GAAGD,eAAe,CAAC,MAAM,CAAC;AACpC,IAAA,MAAM,UAAU,GAAiB;AAC/B,QAAA,IAAI,CAACC,gBAAgB,CAAC,YAAY,CAAC;AACnC,QAAA,IAAI,CAACA,gBAAgB,CAAC,gBAAgB,CAAC;KACxC;IACD,MAAM,MAAM,GAAG,mBAAmB,CAAC,UAAU,EAAE,SAAS,CAAC;AACzD,IAAA,OAAO,mBAAmB,CAAC,MAAM,EAAE,KAAK,CAAC;AAC3C;AAEA;;;;;;;;;;;;;AAaG;AACa,SAAA,iBAAiB,CAC/B,MAAc,EACd,KAAuB,EAAA;AAEvB,IAAA,MAAM,IAAI,GAAGD,eAAe,CAAC,MAAM,CAAC;IACpC,MAAM,QAAQ,GAAuB,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAAC;AAChE,IAAA,OAAO,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC;AAC7C;AAEA;;;;;;;;;;;;;;;;AAgBG;AACG,SAAU,uBAAuB,CAAC,MAAc,EAAA;AACpD,IAAA,MAAM,IAAI,GAAGD,eAAe,CAAC,MAAM,CAAC;AACpC,IAAA,OAAO,IAAI,CAACC,gBAAgB,CAAC,cAAc,CAAC;AAC9C;AAEA;;;;;;;;;;;;AAYG;AACG,SAAU,qBAAqB,CAAC,MAAc,EAAA;AAClD,IAAA,MAAM,IAAI,GAAGD,eAAe,CAAC,MAAM,CAAC;AACpC,IAAA,OAAO,IAAI,CAACC,gBAAgB,CAAC,YAAY,CAAC;AAC5C;AAEA;;;;;;;;;;;;;AAaG;AACa,SAAA,mBAAmB,CAAC,MAAc,EAAE,KAAkB,EAAA;AACpE,IAAA,MAAM,IAAI,GAAGD,eAAe,CAAC,MAAM,CAAC;IACpC,OAAO,mBAAmB,CAAC,IAAI,CAACC,gBAAgB,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC;AACtE;AAEA;;;;;;;;;;;;AAYG;AACa,SAAA,mBAAmB,CAAC,MAAc,EAAE,KAAkB,EAAA;AACpE,IAAA,MAAM,IAAI,GAAGD,eAAe,CAAC,MAAM,CAAC;IACpC,OAAO,mBAAmB,CAAC,IAAI,CAACC,gBAAgB,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC;AACtE;AAEA;;;;;;;;;;;;;AAaG;AACa,SAAA,uBAAuB,CAAC,MAAc,EAAE,KAAkB,EAAA;AACxE,IAAA,MAAM,IAAI,GAAGD,eAAe,CAAC,MAAM,CAAC;IACpC,MAAM,kBAAkB,GAAa,IAAI,CAACC,gBAAgB,CAAC,cAAc,CAAC;AAC1E,IAAA,OAAO,mBAAmB,CAAC,kBAAkB,EAAE,KAAK,CAAC;AACvD;AAEA;;;;;;;;;;;;AAYG;AACa,SAAA,qBAAqB,CAAC,MAAc,EAAE,MAAoB,EAAA;AACxE,IAAA,MAAM,IAAI,GAAGD,eAAe,CAAC,MAAM,CAAC;IACpC,MAAM,GAAG,GAAG,IAAI,CAACC,gBAAgB,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC;AACxD,IAAA,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;AAC9B,QAAA,IAAI,MAAM,KAAK,YAAY,CAAC,eAAe,EAAE;YAC3C,OAAO,IAAI,CAACA,gBAAgB,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC;;AAC5D,aAAA,IAAI,MAAM,KAAK,YAAY,CAAC,aAAa,EAAE;YAChD,OAAO,IAAI,CAACA,gBAAgB,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC;;;AAGnE,IAAA,OAAO,GAAG;AACZ;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCG;AACa,SAAA,qBAAqB,CAAC,MAAc,EAAE,IAAuB,EAAA;AAC3E,IAAA,MAAM,IAAI,GAAGD,eAAe,CAAC,MAAM,CAAC;IACpC,OAAO,IAAI,CAACC,gBAAgB,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC;AACnD;AAEA;;;;;;;;;;;;AAYG;AACG,SAAU,uBAAuB,CAAC,MAAc,EAAA;AACpD,IAAA,MAAM,IAAI,GAAGD,eAAe,CAAC,MAAM,CAAC;IACpC,OAAO,IAAI,CAACC,gBAAgB,CAAC,cAAc,CAAC,IAAI,IAAI;AACtD;AAEA;;;;;;;;;;;AAWG;AACG,SAAU,qBAAqB,CAAC,MAAc,EAAA;AAClD,IAAA,MAAM,IAAI,GAAGD,eAAe,CAAC,MAAM,CAAC;IACpC,OAAO,IAAI,CAACC,gBAAgB,CAAC,YAAY,CAAC,IAAI,IAAI;AACpD;AAEA;;;;;;;;;;;;AAYG;AACG,SAAU,qBAAqB,CAAC,MAAc,EAAA;AAClD,IAAA,OAAOC,sBAAsB,CAAC,MAAM,CAAC;AACvC;AAEA;;;;;AAKG;AACH,SAAS,mBAAmB,CAAC,MAAc,EAAA;AACzC,IAAA,MAAM,IAAI,GAAGF,eAAe,CAAC,MAAM,CAAC;AACpC,IAAA,OAAO,IAAI,CAACC,gBAAgB,CAAC,UAAU,CAAC;AAC1C;AAEA;;;;;AAKG;AACI,MAAM,mBAAmB,GAC9BE;AAEF,SAAS,aAAa,CAAC,IAAS,EAAA;IAC9B,IAAI,CAAC,IAAI,CAACF,gBAAgB,CAAC,SAAS,CAAC,EAAE;QACrC,MAAM,IAAIG,aAAY,CAAA,IAAA,8DAEpB,SAAS;YACP,CACE,0CAAA,EAAA,IAAI,CAACH,gBAAgB,CAAC,QAAQ,CAChC,CAAA,8FAAA,CAAgG,CACnG;;AAEL;AAEA;;;;;;;;;;;;;;;;;;;;;;;;AAwBG;AACG,SAAU,4BAA4B,CAAC,MAAc,EAAA;AACzD,IAAA,MAAM,IAAI,GAAGD,eAAe,CAAC,MAAM,CAAC;IACpC,aAAa,CAAC,IAAI,CAAC;IACnB,MAAM,KAAK,GAAG,IAAI,CAACC,gBAAgB,CAAC,SAAS,CAAC,CAAA,CAAA,kDAA4C,IAAI,EAAE;AAChG,IAAA,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAA+B,KAAI;AACnD,QAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC5B,YAAA,OAAO,WAAW,CAAC,IAAI,CAAC;;AAE1B,QAAA,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,KAAC,CAAC;AACJ;AAEA;;;;;;;;;;;;;;;;;;;AAmBG;SACa,wBAAwB,CACtC,MAAc,EACd,SAAoB,EACpB,KAAuB,EAAA;AAEvB,IAAA,MAAM,IAAI,GAAGD,eAAe,CAAC,MAAM,CAAC;IACpC,aAAa,CAAC,IAAI,CAAC;AACnB,IAAA,MAAM,cAAc,GAAiB;AACnC,QAAA,IAAI,CAACC,gBAAgB,CAAC,SAAS,CAAC,CAA6C,CAAA,mDAAA;AAC7E,QAAA,IAAI,CAACA,gBAAgB,CAAC,SAAS,CAAC,CAAgD,CAAA,sDAAA;KACjF;IACD,MAAM,UAAU,GAAG,mBAAmB,CAAC,cAAc,EAAE,SAAS,CAAC,IAAI,EAAE;IACvE,OAAO,mBAAmB,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,EAAE;AACrD;AAEA;;;;;;;;;;;;AAYG;AACG,SAAU,kBAAkB,CAAC,MAAc,EAAA;AAC/C,IAAA,MAAM,IAAI,GAAGD,eAAe,CAAC,MAAM,CAAC;AACpC,IAAA,OAAO,IAAI,CAACC,gBAAgB,CAAC,cAAc,CAAC;AAC9C;AAEA;;;;;;;;;;;AAWG;AACH,SAAS,mBAAmB,CAAI,IAAS,EAAE,KAAa,EAAA;AACtD,IAAA,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAC/B,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;AAClC,YAAA,OAAO,IAAI,CAAC,CAAC,CAAC;;;AAGlB,IAAA,MAAM,IAAIG,aAAY,CAAA,IAAA,+CAEpB,SAAS,IAAI,wCAAwC,CACtD;AACH;AAcA;;AAEG;AACH,SAAS,WAAW,CAAC,IAAY,EAAA;AAC/B,IAAA,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;IAC9B,OAAO,EAAC,KAAK,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,EAAC;AACjC;AAEA;;;;;;;;;;;;;;;;;;;;AAoBG;AACG,SAAU,iBAAiB,CAAC,IAAY,EAAE,MAAyB,EAAE,MAAM,GAAG,IAAI,EAAA;AACtF,IAAA,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE;AAC/E,IAAA,MAAM,YAAY,GAAG,QAAQ,CAAA,CAAA,mCAA6B;IAE1D,IAAI,MAAM,KAAK,QAAQ,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;AAC3D,QAAA,OAAO,YAAY;;AAGrB,IAAA,OAAO,QAAQ,CAAA,CAAA,6BAAuB,IAAI,IAAI;AAChD;AAEA;AACA,MAAM,6BAA6B,GAAG,CAAC;AAEvC;;;;;;;;;;;;AAYG;AACG,SAAU,yBAAyB,CAAC,IAAY,EAAA;AACpD,IAAA,IAAI,MAAM;AACV,IAAA,MAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC;IACpC,IAAI,QAAQ,EAAE;QACZ,MAAM,GAAG,QAAQ,CAAA,CAAA,iCAA2B;;AAE9C,IAAA,OAAO,OAAO,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,6BAA6B;AAC5E;;ACnwBO,MAAM,kBAAkB,GAC7B,uGAAuG;AACzG;AACA,MAAM,aAAa,GAAqD,EAAE;AAC1E,MAAM,kBAAkB,GACtB,mNAAmN;AA2BrN;;;;;;;;;;;;;;;;;;;AAmBG;AACG,SAAU,UAAU,CACxB,KAA6B,EAC7B,MAAc,EACd,MAAc,EACd,QAAiB,EAAA;AAEjB,IAAA,IAAI,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC;IACxB,MAAM,WAAW,GAAG,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC;AAClD,IAAA,MAAM,GAAG,WAAW,IAAI,MAAM;IAE9B,IAAI,KAAK,GAAa,EAAE;AACxB,IAAA,IAAI,KAAK;IACT,OAAO,MAAM,EAAE;AACb,QAAA,KAAK,GAAG,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC;QACvC,IAAI,KAAK,EAAE;AACT,YAAA,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACpC,YAAA,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE;YACxB,IAAI,CAAC,IAAI,EAAE;gBACT;;YAEF,MAAM,GAAG,IAAI;;aACR;AACL,YAAA,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;YAClB;;;AAIJ,IAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;QACjD,qBAAqB,CAAC,KAAK,CAAC;;AAG9B,IAAA,IAAI,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,EAAE;IACjD,IAAI,QAAQ,EAAE;AACZ,QAAA,kBAAkB,GAAG,gBAAgB,CAAC,QAAQ,EAAE,kBAAkB,CAAC;QACnE,IAAI,GAAG,sBAAsB,CAAC,IAAI,EAAE,QAAc,CAAC;;IAGrD,IAAI,IAAI,GAAG,EAAE;AACb,IAAA,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AACtB,QAAA,MAAM,aAAa,GAAG,gBAAgB,CAAC,KAAK,CAAC;AAC7C,QAAA,IAAI,IAAI;cACJ,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,kBAAkB;cAC9C,KAAK,KAAK;AACV,kBAAE;AACF,kBAAE,KAAK,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AACzD,KAAC,CAAC;AAEF,IAAA,OAAO,IAAI;AACb;AAEA;;;;AAIG;AACH,SAAS,qBAAqB,CAAC,KAAe,EAAA;AAC5C,IAAA,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;;;;;QAKvF,MAAM,OAAO,GAAG,CAAA,uDAAA,EAA0D,KAAK,CAAC,IAAI,CAClF,EAAE,CACH,CAAA,iDAAA,CAAmD;AACpD,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;;YAEtB,OAAO,CAAC,KAAK,CAACC,mBAAkB,qDAA0C,OAAO,CAAC,CAAC;;aAC9E;AACL,YAAA,MAAM,IAAID,aAAY,CAA0C,IAAA,gDAAA,OAAO,CAAC;;;AAG9E;AAEA;;;;;;;;;AASG;AACH,SAAS,UAAU,CAAC,IAAY,EAAE,KAAa,EAAE,IAAY,EAAA;;;;;AAK3D,IAAA,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;;;;;;IAQ3B,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;;;;IAItC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAEzB,IAAA,OAAO,OAAO;AAChB;AAEA,SAAS,cAAc,CAAC,MAAc,EAAE,MAAc,EAAA;AACpD,IAAA,MAAM,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC;AACpC,IAAA,aAAa,CAAC,QAAQ,CAAC,KAAK,EAAE;IAE9B,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE;AACnC,QAAA,OAAO,aAAa,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;;IAGxC,IAAI,WAAW,GAAG,EAAE;IACpB,QAAQ,MAAM;AACZ,QAAA,KAAK,WAAW;YACd,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,KAAK,CAAC;YAC5D;AACF,QAAA,KAAK,YAAY;YACf,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC;YAC7D;AACF,QAAA,KAAK,UAAU;YACb,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC;YAC3D;AACF,QAAA,KAAK,UAAU;YACb,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC;YAC3D;AACF,QAAA,KAAK,WAAW;YACd,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,KAAK,CAAC;YAC5D;AACF,QAAA,KAAK,YAAY;YACf,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC;YAC7D;AACF,QAAA,KAAK,UAAU;YACb,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC;YAC3D;AACF,QAAA,KAAK,UAAU;YACb,WAAW,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC;YAC3D;AACF,QAAA,KAAK,OAAO;YACV,MAAM,SAAS,GAAG,cAAc,CAAC,MAAM,EAAE,WAAW,CAAC;YACrD,MAAM,SAAS,GAAG,cAAc,CAAC,MAAM,EAAE,WAAW,CAAC;YACrD,WAAW,GAAG,cAAc,CAAC,uBAAuB,CAAC,MAAM,EAAE,WAAW,CAAC,KAAK,CAAC,EAAE;gBAC/E,SAAS;gBACT,SAAS;AACV,aAAA,CAAC;YACF;AACF,QAAA,KAAK,QAAQ;YACX,MAAM,UAAU,GAAG,cAAc,CAAC,MAAM,EAAE,YAAY,CAAC;YACvD,MAAM,UAAU,GAAG,cAAc,CAAC,MAAM,EAAE,YAAY,CAAC;YACvD,WAAW,GAAG,cAAc,CAAC,uBAAuB,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,EAAE;gBAChF,UAAU;gBACV,UAAU;AACX,aAAA,CAAC;YACF;AACF,QAAA,KAAK,MAAM;YACT,MAAM,QAAQ,GAAG,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC;YACnD,MAAM,QAAQ,GAAG,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC;YACnD,WAAW,GAAG,cAAc,CAAC,uBAAuB,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE;gBAC9E,QAAQ;gBACR,QAAQ;AACT,aAAA,CAAC;YACF;AACF,QAAA,KAAK,MAAM;YACT,MAAM,QAAQ,GAAG,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC;YACnD,MAAM,QAAQ,GAAG,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC;YACnD,WAAW,GAAG,cAAc,CAAC,uBAAuB,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE;gBAC9E,QAAQ;gBACR,QAAQ;AACT,aAAA,CAAC;YACF;;IAEJ,IAAI,WAAW,EAAE;QACf,aAAa,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,GAAG,WAAW;;AAE/C,IAAA,OAAO,WAAW;AACpB;AAEA,SAAS,cAAc,CAAC,GAAW,EAAE,UAAoB,EAAA;IACvD,IAAI,UAAU,EAAE;QACd,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,KAAK,EAAE,GAAG,EAAA;AACnD,YAAA,OAAO,UAAU,IAAI,IAAI,IAAI,GAAG,IAAI,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK;AAC1E,SAAC,CAAC;;AAEJ,IAAA,OAAO,GAAG;AACZ;AAEA,SAAS,SAAS,CAChB,GAAW,EACX,MAAc,EACd,SAAS,GAAG,GAAG,EACf,IAAc,EACd,OAAiB,EAAA;IAEjB,IAAI,GAAG,GAAG,EAAE;AACZ,IAAA,IAAI,GAAG,GAAG,CAAC,KAAK,OAAO,IAAI,GAAG,IAAI,CAAC,CAAC,EAAE;QACpC,IAAI,OAAO,EAAE;AACX,YAAA,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;;aACT;YACL,GAAG,GAAG,CAAC,GAAG;YACV,GAAG,GAAG,SAAS;;;AAGnB,IAAA,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC;AACxB,IAAA,OAAO,MAAM,CAAC,MAAM,GAAG,MAAM,EAAE;AAC7B,QAAA,MAAM,GAAG,GAAG,GAAG,MAAM;;IAEvB,IAAI,IAAI,EAAE;QACR,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;;IAE/C,OAAO,GAAG,GAAG,MAAM;AACrB;AAEA,SAAS,uBAAuB,CAAC,YAAoB,EAAE,MAAc,EAAA;IACnE,MAAM,KAAK,GAAG,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC;IACxC,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC;AACnC;AAEA;;AAEG;AACH,SAAS,UAAU,CACjB,IAAc,EACd,IAAY,EACZ,MAAA,GAAiB,CAAC,EAClB,IAAI,GAAG,KAAK,EACZ,OAAO,GAAG,KAAK,EAAA;IAEf,OAAO,UAAU,IAAU,EAAE,MAAc,EAAA;QACzC,IAAI,IAAI,GAAG,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC;QAClC,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,MAAM,EAAE;YAChC,IAAI,IAAI,MAAM;;QAGhB,IAAI,IAAI,KAAmB,CAAA,uBAAE;YAC3B,IAAI,IAAI,KAAK,CAAC,IAAI,MAAM,KAAK,CAAC,EAAE,EAAE;gBAChC,IAAI,GAAG,EAAE;;;aAEN,IAAI,IAAI,KAA+B,CAAA,mCAAE;AAC9C,YAAA,OAAO,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC;;QAG5C,MAAM,WAAW,GAAG,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC;AACzE,QAAA,OAAO,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC;AAC1D,KAAC;AACH;AAEA,SAAS,WAAW,CAAC,IAAc,EAAE,IAAU,EAAA;IAC7C,QAAQ,IAAI;AACV,QAAA,KAAA,CAAA;AACE,YAAA,OAAO,IAAI,CAAC,WAAW,EAAE;AAC3B,QAAA,KAAA,CAAA;AACE,YAAA,OAAO,IAAI,CAAC,QAAQ,EAAE;AACxB,QAAA,KAAA,CAAA;AACE,YAAA,OAAO,IAAI,CAAC,OAAO,EAAE;AACvB,QAAA,KAAA,CAAA;AACE,YAAA,OAAO,IAAI,CAAC,QAAQ,EAAE;AACxB,QAAA,KAAA,CAAA;AACE,YAAA,OAAO,IAAI,CAAC,UAAU,EAAE;AAC1B,QAAA,KAAA,CAAA;AACE,YAAA,OAAO,IAAI,CAAC,UAAU,EAAE;AAC1B,QAAA,KAAA,CAAA;AACE,YAAA,OAAO,IAAI,CAAC,eAAe,EAAE;AAC/B,QAAA,KAAA,CAAA;AACE,YAAA,OAAO,IAAI,CAAC,MAAM,EAAE;AACtB,QAAA;YACE,MAAM,IAAIA,aAAY,CAEpB,IAAA,iDAAA,SAAS,IAAI,CAA2B,wBAAA,EAAA,IAAI,CAAI,EAAA,CAAA,CACjD;;AAEP;AAEA;;AAEG;AACH,SAAS,aAAa,CACpB,IAAqB,EACrB,KAAuB,EACvB,IAAkB,GAAA,SAAS,CAAC,MAAM,EAClC,QAAQ,GAAG,KAAK,EAAA;IAEhB,OAAO,UAAU,IAAU,EAAE,MAAc,EAAA;AACzC,QAAA,OAAO,kBAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC;AACtE,KAAC;AACH;AAEA;;AAEG;AACH,SAAS,kBAAkB,CACzB,IAAU,EACV,MAAc,EACd,IAAqB,EACrB,KAAuB,EACvB,IAAe,EACf,QAAiB,EAAA;IAEjB,QAAQ,IAAI;AACV,QAAA,KAAA,CAAA;AACE,YAAA,OAAO,mBAAmB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;AAClE,QAAA,KAAA,CAAA;AACE,YAAA,OAAO,iBAAiB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC9D,QAAA,KAAA,CAAA;AACE,YAAA,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,EAAE;AACpC,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,EAAE;YACxC,IAAI,QAAQ,EAAE;AACZ,gBAAA,MAAM,KAAK,GAAG,4BAA4B,CAAC,MAAM,CAAC;gBAClD,MAAM,UAAU,GAAG,wBAAwB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC;gBAChE,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,KAAI;AACrC,oBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;;AAEvB,wBAAA,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,IAAI;AACvB,wBAAA,MAAM,SAAS,GAAG,YAAY,IAAI,IAAI,CAAC,KAAK,IAAI,cAAc,IAAI,IAAI,CAAC,OAAO;wBAC9E,MAAM,QAAQ,GACZ,YAAY,GAAG,EAAE,CAAC,KAAK,KAAK,YAAY,KAAK,EAAE,CAAC,KAAK,IAAI,cAAc,GAAG,EAAE,CAAC,OAAO,CAAC;;;;;;;;;;;wBAWvF,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE;AACzB,4BAAA,IAAI,SAAS,IAAI,QAAQ,EAAE;AACzB,gCAAA,OAAO,IAAI;;;AAER,6BAAA,IAAI,SAAS,IAAI,QAAQ,EAAE;AAChC,4BAAA,OAAO,IAAI;;;yBAER;;AAEL,wBAAA,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY,IAAI,IAAI,CAAC,OAAO,KAAK,cAAc,EAAE;AAClE,4BAAA,OAAO,IAAI;;;AAGf,oBAAA,OAAO,KAAK;AACd,iBAAC,CAAC;AACF,gBAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AAChB,oBAAA,OAAO,UAAU,CAAC,KAAK,CAAC;;;;YAI5B,OAAO,mBAAmB,CAAC,MAAM,EAAE,IAAI,EAAoB,KAAK,CAAC,CAAC,YAAY,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AAC9F,QAAA,KAAA,CAAA;YACE,OAAO,iBAAiB,CAAC,MAAM,EAAoB,KAAK,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5F,QAAA;;;;;YAKE,MAAM,UAAU,GAAU,IAAI;YAC9B,MAAM,IAAIA,aAAY,CAEpB,IAAA,qDAAA,SAAS,IAAI,CAA+B,4BAAA,EAAA,UAAU,CAAE,CAAA,CACzD;;AAEP;AAEA;;;;AAIG;AACH,SAAS,cAAc,CAAC,KAAgB,EAAA;AACtC,IAAA,OAAO,UAAU,IAAU,EAAE,MAAc,EAAE,MAAc,EAAA;AACzD,QAAA,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM;QACxB,MAAM,SAAS,GAAG,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC;QACvE,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACrE,QAAQ,KAAK;AACX,YAAA,KAAA,CAAA;AACE,gBAAA,QACE,CAAC,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE;AACrB,oBAAA,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC;AAC9B,oBAAA,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC;AAEhD,YAAA,KAAA,CAAA;gBACE,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC;AACxE,YAAA,KAAA,CAAA;AACE,gBAAA,QACE,KAAK;qBACJ,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;AACtB,oBAAA,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC;oBAC9B,GAAG;AACH,oBAAA,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC;AAEhD,YAAA,KAAA,CAAA;AACE,gBAAA,IAAI,MAAM,KAAK,CAAC,EAAE;AAChB,oBAAA,OAAO,GAAG;;qBACL;AACL,oBAAA,QACE,CAAC,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE;AACrB,wBAAA,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC;wBAC9B,GAAG;AACH,wBAAA,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC;;AAGlD,YAAA;gBACE,MAAM,IAAIA,aAAY,CAEpB,IAAA,4CAAA,SAAS,IAAI,CAAuB,oBAAA,EAAA,KAAK,CAAG,CAAA,CAAA,CAC7C;;AAEP,KAAC;AACH;AAEA,MAAM,OAAO,GAAG,CAAC;AACjB,MAAM,QAAQ,GAAG,CAAC;AAClB,SAAS,sBAAsB,CAAC,IAAY,EAAA;AAC1C,IAAA,MAAM,cAAc,GAAG,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE;IAC5D,OAAO,UAAU,CACf,IAAI,EACJ,CAAC,EACD,CAAC,IAAI,cAAc,IAAI,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,cAAc,CAC5E;AACH;AAEA;;AAEG;AACG,SAAU,sBAAsB,CAAC,QAAc,EAAA;;AAEnD,IAAA,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,EAAE;;AAGpC,IAAA,MAAM,eAAe,GAAG,UAAU,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,GAAG,UAAU;AAErE,IAAA,OAAO,UAAU,CACf,QAAQ,CAAC,WAAW,EAAE,EACtB,QAAQ,CAAC,QAAQ,EAAE,EACnB,QAAQ,CAAC,OAAO,EAAE,GAAG,eAAe,CACrC;AACH;AAEA,SAAS,UAAU,CAAC,IAAY,EAAE,UAAU,GAAG,KAAK,EAAA;IAClD,OAAO,UAAU,IAAU,EAAE,MAAc,EAAA;AACzC,QAAA,IAAI,MAAM;QACV,IAAI,UAAU,EAAE;YACd,MAAM,yBAAyB,GAC7B,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC;AAC/D,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE;AAC5B,YAAA,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,yBAAyB,IAAI,CAAC,CAAC;;aAC3D;AACL,YAAA,MAAM,SAAS,GAAG,sBAAsB,CAAC,IAAI,CAAC;;;YAG9C,MAAM,UAAU,GAAG,sBAAsB,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YAClE,MAAM,IAAI,GAAG,SAAS,CAAC,OAAO,EAAE,GAAG,UAAU,CAAC,OAAO,EAAE;AACvD,YAAA,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC;;AAG1C,QAAA,OAAO,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;AACvF,KAAC;AACH;AAEA;;AAEG;AACH,SAAS,uBAAuB,CAAC,IAAY,EAAE,IAAI,GAAG,KAAK,EAAA;IACzD,OAAO,UAAU,IAAU,EAAE,MAAc,EAAA;AACzC,QAAA,MAAM,SAAS,GAAG,sBAAsB,CAAC,IAAI,CAAC;AAC9C,QAAA,MAAM,iBAAiB,GAAG,SAAS,CAAC,WAAW,EAAE;AACjD,QAAA,OAAO,SAAS,CACd,iBAAiB,EACjB,IAAI,EACJ,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC,EACrD,IAAI,CACL;AACH,KAAC;AACH;AAIA,MAAM,YAAY,GAAsC,EAAE;AAE1D;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,MAAc,EAAA;AACtC,IAAA,IAAI,YAAY,CAAC,MAAM,CAAC,EAAE;AACxB,QAAA,OAAO,YAAY,CAAC,MAAM,CAAC;;AAE7B,IAAA,IAAI,SAAS;IACb,QAAQ,MAAM;;AAEZ,QAAA,KAAK,GAAG;AACR,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,KAAK;AACR,YAAA,SAAS,GAAG,aAAa,CAAA,CAAA,6BAAuB,gBAAgB,CAAC,WAAW,CAAC;YAC7E;AACF,QAAA,KAAK,MAAM;AACT,YAAA,SAAS,GAAG,aAAa,CAAA,CAAA,6BAAuB,gBAAgB,CAAC,IAAI,CAAC;YACtE;AACF,QAAA,KAAK,OAAO;AACV,YAAA,SAAS,GAAG,aAAa,CAAA,CAAA,6BAAuB,gBAAgB,CAAC,MAAM,CAAC;YACxE;;AAGF,QAAA,KAAK,GAAG;YACN,SAAS,GAAG,UAAU,CAAA,CAAA,0BAAoB,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;YAC5D;;AAEF,QAAA,KAAK,IAAI;YACP,SAAS,GAAG,UAAU,CAAA,CAAA,0BAAoB,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;YAC3D;;AAEF,QAAA,KAAK,KAAK;YACR,SAAS,GAAG,UAAU,CAAA,CAAA,0BAAoB,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;YAC5D;;AAEF,QAAA,KAAK,MAAM;YACT,SAAS,GAAG,UAAU,CAAA,CAAA,0BAAoB,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;YAC5D;;AAGF,QAAA,KAAK,GAAG;AACN,YAAA,SAAS,GAAG,uBAAuB,CAAC,CAAC,CAAC;YACtC;;;AAGF,QAAA,KAAK,IAAI;AACP,YAAA,SAAS,GAAG,uBAAuB,CAAC,CAAC,EAAE,IAAI,CAAC;YAC5C;;;AAGF,QAAA,KAAK,KAAK;AACR,YAAA,SAAS,GAAG,uBAAuB,CAAC,CAAC,CAAC;YACtC;;AAEF,QAAA,KAAK,MAAM;AACT,YAAA,SAAS,GAAG,uBAAuB,CAAC,CAAC,CAAC;YACtC;;AAGF,QAAA,KAAK,GAAG;AACR,QAAA,KAAK,GAAG;AACN,YAAA,SAAS,GAAG,UAAU,CAAA,CAAA,uBAAiB,CAAC,EAAE,CAAC,CAAC;YAC5C;AACF,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,IAAI;AACP,YAAA,SAAS,GAAG,UAAU,CAAA,CAAA,uBAAiB,CAAC,EAAE,CAAC,CAAC;YAC5C;;AAGF,QAAA,KAAK,KAAK;AACR,YAAA,SAAS,GAAG,aAAa,CAAA,CAAA,+BAAyB,gBAAgB,CAAC,WAAW,CAAC;YAC/E;AACF,QAAA,KAAK,MAAM;AACT,YAAA,SAAS,GAAG,aAAa,CAAA,CAAA,+BAAyB,gBAAgB,CAAC,IAAI,CAAC;YACxE;AACF,QAAA,KAAK,OAAO;AACV,YAAA,SAAS,GAAG,aAAa,CAAA,CAAA,+BAAyB,gBAAgB,CAAC,MAAM,CAAC;YAC1E;;AAGF,QAAA,KAAK,KAAK;YACR,SAAS,GAAG,aAAa,CAAA,CAAA,+BAEvB,gBAAgB,CAAC,WAAW,EAC5B,SAAS,CAAC,UAAU,CACrB;YACD;AACF,QAAA,KAAK,MAAM;YACT,SAAS,GAAG,aAAa,CAAA,CAAA,+BAEvB,gBAAgB,CAAC,IAAI,EACrB,SAAS,CAAC,UAAU,CACrB;YACD;AACF,QAAA,KAAK,OAAO;YACV,SAAS,GAAG,aAAa,CAAA,CAAA,+BAEvB,gBAAgB,CAAC,MAAM,EACvB,SAAS,CAAC,UAAU,CACrB;YACD;;AAGF,QAAA,KAAK,GAAG;AACN,YAAA,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC;YACzB;AACF,QAAA,KAAK,IAAI;AACP,YAAA,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC;YACzB;;AAGF,QAAA,KAAK,GAAG;AACN,YAAA,SAAS,GAAG,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC;YAC/B;;AAGF,QAAA,KAAK,GAAG;AACN,YAAA,SAAS,GAAG,UAAU,CAAgB,CAAA,sBAAA,CAAC,CAAC;YACxC;AACF,QAAA,KAAK,IAAI;AACP,YAAA,SAAS,GAAG,UAAU,CAAgB,CAAA,sBAAA,CAAC,CAAC;YACxC;;AAGF,QAAA,KAAK,GAAG;AACR,QAAA,KAAK,IAAI;AACP,YAAA,SAAS,GAAG,UAAU,CAAe,CAAA,qBAAA,CAAC,CAAC;YACvC;AACF,QAAA,KAAK,KAAK;YACR,SAAS,GAAG,aAAa,CAAA,CAAA,6BAEvB,gBAAgB,CAAC,WAAW,EAC5B,SAAS,CAAC,UAAU,CACrB;YACD;AACF,QAAA,KAAK,MAAM;YACT,SAAS,GAAG,aAAa,CAAA,CAAA,6BAAuB,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,UAAU,CAAC;YAC5F;AACF,QAAA,KAAK,OAAO;YACV,SAAS,GAAG,aAAa,CAAA,CAAA,6BAEvB,gBAAgB,CAAC,MAAM,EACvB,SAAS,CAAC,UAAU,CACrB;YACD;AACF,QAAA,KAAK,QAAQ;YACX,SAAS,GAAG,aAAa,CAAA,CAAA,6BAAuB,gBAAgB,CAAC,KAAK,EAAE,SAAS,CAAC,UAAU,CAAC;YAC7F;;AAGF,QAAA,KAAK,GAAG;AACR,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,KAAK;AACR,YAAA,SAAS,GAAG,aAAa,CAAA,CAAA,6BAAuB,gBAAgB,CAAC,WAAW,CAAC;YAC7E;AACF,QAAA,KAAK,MAAM;AACT,YAAA,SAAS,GAAG,aAAa,CAAA,CAAA,6BAAuB,gBAAgB,CAAC,IAAI,CAAC;YACtE;AACF,QAAA,KAAK,OAAO;AACV,YAAA,SAAS,GAAG,aAAa,CAAA,CAAA,6BAAuB,gBAAgB,CAAC,MAAM,CAAC;YACxE;AACF,QAAA,KAAK,QAAQ;AACX,YAAA,SAAS,GAAG,aAAa,CAAA,CAAA,6BAAuB,gBAAgB,CAAC,KAAK,CAAC;YACvE;;AAGF,QAAA,KAAK,GAAG;AACR,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,KAAK;AACR,YAAA,SAAS,GAAG,aAAa,CAAA,CAAA,mCAA6B,gBAAgB,CAAC,WAAW,CAAC;YACnF;AACF,QAAA,KAAK,MAAM;AACT,YAAA,SAAS,GAAG,aAAa,CAAA,CAAA,mCAA6B,gBAAgB,CAAC,IAAI,CAAC;YAC5E;AACF,QAAA,KAAK,OAAO;AACV,YAAA,SAAS,GAAG,aAAa,CAAA,CAAA,mCAA6B,gBAAgB,CAAC,MAAM,CAAC;YAC9E;;AAGF,QAAA,KAAK,GAAG;AACR,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,KAAK;AACR,YAAA,SAAS,GAAG,aAAa,CAEvB,CAAA,mCAAA,gBAAgB,CAAC,WAAW,EAC5B,SAAS,CAAC,UAAU,EACpB,IAAI,CACL;YACD;AACF,QAAA,KAAK,MAAM;AACT,YAAA,SAAS,GAAG,aAAa,CAEvB,CAAA,mCAAA,gBAAgB,CAAC,IAAI,EACrB,SAAS,CAAC,UAAU,EACpB,IAAI,CACL;YACD;AACF,QAAA,KAAK,OAAO;AACV,YAAA,SAAS,GAAG,aAAa,CAEvB,CAAA,mCAAA,gBAAgB,CAAC,MAAM,EACvB,SAAS,CAAC,UAAU,EACpB,IAAI,CACL;YACD;;AAGF,QAAA,KAAK,GAAG;AACR,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,KAAK;AACR,YAAA,SAAS,GAAG,aAAa,CAEvB,CAAA,mCAAA,gBAAgB,CAAC,WAAW,EAC5B,SAAS,CAAC,MAAM,EAChB,IAAI,CACL;YACD;AACF,QAAA,KAAK,MAAM;AACT,YAAA,SAAS,GAAG,aAAa,CAEvB,CAAA,mCAAA,gBAAgB,CAAC,IAAI,EACrB,SAAS,CAAC,MAAM,EAChB,IAAI,CACL;YACD;AACF,QAAA,KAAK,OAAO;AACV,YAAA,SAAS,GAAG,aAAa,CAEvB,CAAA,mCAAA,gBAAgB,CAAC,MAAM,EACvB,SAAS,CAAC,MAAM,EAChB,IAAI,CACL;YACD;;AAGF,QAAA,KAAK,GAAG;YACN,SAAS,GAAG,UAAU,CAAiB,CAAA,uBAAA,CAAC,EAAE,CAAC,EAAE,CAAC;YAC9C;AACF,QAAA,KAAK,IAAI;YACP,SAAS,GAAG,UAAU,CAAiB,CAAA,uBAAA,CAAC,EAAE,CAAC,EAAE,CAAC;YAC9C;;AAGF,QAAA,KAAK,GAAG;AACN,YAAA,SAAS,GAAG,UAAU,CAAiB,CAAA,uBAAA,CAAC,CAAC;YACzC;;AAEF,QAAA,KAAK,IAAI;AACP,YAAA,SAAS,GAAG,UAAU,CAAiB,CAAA,uBAAA,CAAC,CAAC;YACzC;;AAGF,QAAA,KAAK,GAAG;AACN,YAAA,SAAS,GAAG,UAAU,CAAmB,CAAA,yBAAA,CAAC,CAAC;YAC3C;AACF,QAAA,KAAK,IAAI;AACP,YAAA,SAAS,GAAG,UAAU,CAAmB,CAAA,yBAAA,CAAC,CAAC;YAC3C;;AAGF,QAAA,KAAK,GAAG;AACN,YAAA,SAAS,GAAG,UAAU,CAAmB,CAAA,yBAAA,CAAC,CAAC;YAC3C;AACF,QAAA,KAAK,IAAI;AACP,YAAA,SAAS,GAAG,UAAU,CAAmB,CAAA,yBAAA,CAAC,CAAC;YAC3C;;AAGF,QAAA,KAAK,GAAG;AACN,YAAA,SAAS,GAAG,UAAU,CAA6B,CAAA,mCAAA,CAAC,CAAC;YACrD;AACF,QAAA,KAAK,IAAI;AACP,YAAA,SAAS,GAAG,UAAU,CAA6B,CAAA,mCAAA,CAAC,CAAC;YACrD;AACF,QAAA,KAAK,KAAK;AACR,YAAA,SAAS,GAAG,UAAU,CAA6B,CAAA,mCAAA,CAAC,CAAC;YACrD;;AAGF,QAAA,KAAK,GAAG;AACR,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,KAAK;YACR,SAAS,GAAG,cAAc,CAAA,CAAA,uBAAiB;YAC3C;;AAEF,QAAA,KAAK,OAAO;YACV,SAAS,GAAG,cAAc,CAAA,CAAA,0BAAoB;YAC9C;;AAGF,QAAA,KAAK,GAAG;AACR,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,KAAK;;AAEV,QAAA,KAAK,GAAG;AACR,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,KAAK;YACR,SAAS,GAAG,cAAc,CAAA,CAAA,0BAAoB;YAC9C;;AAEF,QAAA,KAAK,MAAM;AACX,QAAA,KAAK,MAAM;;AAEX,QAAA,KAAK,MAAM;YACT,SAAS,GAAG,cAAc,CAAA,CAAA,sBAAgB;YAC1C;AACF,QAAA;AACE,YAAA,OAAO,IAAI;;AAEf,IAAA,YAAY,CAAC,MAAM,CAAC,GAAG,SAAS;AAChC,IAAA,OAAO,SAAS;AAClB;AAEA,SAAS,gBAAgB,CAAC,QAAgB,EAAE,QAAgB,EAAA;;;IAG1D,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;AACrC,IAAA,MAAM,uBAAuB,GAAG,IAAI,CAAC,KAAK,CAAC,wBAAwB,GAAG,QAAQ,CAAC,GAAG,KAAK;AACvF,IAAA,OAAO,KAAK,CAAC,uBAAuB,CAAC,GAAG,QAAQ,GAAG,uBAAuB;AAC5E;AAEA,SAAS,cAAc,CAAC,IAAU,EAAE,OAAe,EAAA;IACjD,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;IAC/B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,OAAO,CAAC;AAC5C,IAAA,OAAO,IAAI;AACb;AAEA,SAAS,sBAAsB,CAAC,IAAU,EAAE,QAAgB,EAAE,OAAgB,EAAA;AAC5E,IAAA,MAAM,YAAY,GAAa,CAAC,CAAC,CAAI;AACrC,IAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,EAAE;IACnD,MAAM,cAAc,GAAG,gBAAgB,CAAC,QAAQ,EAAE,kBAAkB,CAAC;AACrE,IAAA,OAAO,cAAc,CAAC,IAAI,EAAE,YAAY,IAAI,cAAc,GAAG,kBAAkB,CAAC,CAAC;AACnF;AAEA;;;;;;;;;;;AAWG;AACG,SAAU,MAAM,CAAC,KAA6B,EAAA;AAClD,IAAA,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE;AACjB,QAAA,OAAO,KAAK;;IAGd,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AAC9C,QAAA,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC;;AAGxB,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,QAAA,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE;AAEpB,QAAA,IAAI,iCAAiC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACjD;;;;;;AAM0D;AAC1D,YAAA,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAW,KAAK,CAAC,GAAG,CAAC;YACrE,OAAO,UAAU,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;;AAGhC,QAAA,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC;;QAGlC,IAAI,CAAC,KAAK,CAAE,KAAa,GAAG,QAAQ,CAAC,EAAE;AACrC,YAAA,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC;;AAG3B,QAAA,IAAI,KAA8B;QAClC,KAAK,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,kBAAkB,CAAC,GAAG;AAC7C,YAAA,OAAO,eAAe,CAAC,KAAK,CAAC;;;AAIjC,IAAA,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,KAAY,CAAC;AACnC,IAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;QACjB,MAAM,IAAIA,aAAY,CAEpB,IAAA,oDAAA,SAAS,IAAI,CAAsB,mBAAA,EAAA,KAAK,CAAe,aAAA,CAAA,CACxD;;AAEH,IAAA,OAAO,IAAI;AACb;AAEA;;;AAGG;AACG,SAAU,eAAe,CAAC,KAAuB,EAAA;AACrD,IAAA,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;IACxB,IAAI,MAAM,GAAG,CAAC;IACd,IAAI,KAAK,GAAG,CAAC;;AAGb,IAAA,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW;AACpE,IAAA,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ;;AAG9D,IAAA,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;AACZ,QAAA,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;AACrC,QAAA,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;;AAEtC,IAAA,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/E,IAAA,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM;AACxC,IAAA,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK;IACvC,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;;;;IAI/B,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAChE,IAAA,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;AAClC,IAAA,OAAO,IAAI;AACb;AAEM,SAAU,MAAM,CAAC,KAAU,EAAA;AAC/B,IAAA,OAAO,KAAK,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;AACzD;;ACz8BO,MAAM,oBAAoB,GAAG,6BAA6B;AACjE,MAAM,UAAU,GAAG,EAAE;AACrB,MAAM,WAAW,GAAG,GAAG;AACvB,MAAM,SAAS,GAAG,GAAG;AACrB,MAAM,WAAW,GAAG,GAAG;AACvB,MAAM,SAAS,GAAG,GAAG;AACrB,MAAM,UAAU,GAAG,GAAG;AACtB,MAAM,aAAa,GAAG,GAAG;AACzB,MAAM,YAAY,GAAG,GAAG;AAExB;;AAEG;AACH,SAAS,0BAA0B,CACjC,KAAa,EACb,OAA2B,EAC3B,MAAc,EACd,WAAyB,EACzB,aAA2B,EAC3B,UAAmB,EACnB,SAAS,GAAG,KAAK,EAAA;IAEjB,IAAI,aAAa,GAAG,EAAE;IACtB,IAAI,MAAM,GAAG,KAAK;AAElB,IAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACpB,aAAa,GAAG,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,QAAQ,CAAC;;SAC/D;AACL,QAAA,IAAI,YAAY,GAAG,WAAW,CAAC,KAAK,CAAC;QAErC,IAAI,SAAS,EAAE;AACb,YAAA,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;;AAGxC,QAAA,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM;AAC3B,QAAA,IAAI,WAAW,GAAG,OAAO,CAAC,OAAO;AACjC,QAAA,IAAI,WAAW,GAAG,OAAO,CAAC,OAAO;QAEjC,IAAI,UAAU,EAAE;YACd,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,oBAAoB,CAAC;AACpD,YAAA,IAAI,KAAK,KAAK,IAAI,EAAE;gBAClB,MAAM,IAAIA,aAAY,CAEpB,IAAA,4CAAA,SAAS,IAAI,CAAG,EAAA,UAAU,CAA4B,0BAAA,CAAA,CACvD;;AAEH,YAAA,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC;AAC3B,YAAA,MAAM,eAAe,GAAG,KAAK,CAAC,CAAC,CAAC;AAChC,YAAA,MAAM,eAAe,GAAG,KAAK,CAAC,CAAC,CAAC;AAChC,YAAA,IAAI,UAAU,IAAI,IAAI,EAAE;AACtB,gBAAA,MAAM,GAAG,iBAAiB,CAAC,UAAU,CAAC;;AAExC,YAAA,IAAI,eAAe,IAAI,IAAI,EAAE;AAC3B,gBAAA,WAAW,GAAG,iBAAiB,CAAC,eAAe,CAAC;;AAElD,YAAA,IAAI,eAAe,IAAI,IAAI,EAAE;AAC3B,gBAAA,WAAW,GAAG,iBAAiB,CAAC,eAAe,CAAC;;iBAC3C,IAAI,eAAe,IAAI,IAAI,IAAI,WAAW,GAAG,WAAW,EAAE;gBAC/D,WAAW,GAAG,WAAW;;;AAI7B,QAAA,WAAW,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,CAAC;AAEnD,QAAA,IAAI,MAAM,GAAG,YAAY,CAAC,MAAM;AAChC,QAAA,IAAI,UAAU,GAAG,YAAY,CAAC,UAAU;AACxC,QAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ;QACtC,IAAI,QAAQ,GAAG,EAAE;AACjB,QAAA,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;;AAGhC,QAAA,OAAO,UAAU,GAAG,MAAM,EAAE,UAAU,EAAE,EAAE;AACxC,YAAA,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;;;AAInB,QAAA,OAAO,UAAU,GAAG,CAAC,EAAE,UAAU,EAAE,EAAE;AACnC,YAAA,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;;;AAInB,QAAA,IAAI,UAAU,GAAG,CAAC,EAAE;YAClB,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC;;aAC9C;YACL,QAAQ,GAAG,MAAM;AACjB,YAAA,MAAM,GAAG,CAAC,CAAC,CAAC;;;QAId,MAAM,MAAM,GAAG,EAAE;QACjB,IAAI,MAAM,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,EAAE;YACnC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;;QAGxE,OAAO,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,EAAE;YACpC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;;AAGvE,QAAA,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;;AAGjC,QAAA,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;;AAGvE,QAAA,IAAI,QAAQ,CAAC,MAAM,EAAE;AACnB,YAAA,aAAa,IAAI,qBAAqB,CAAC,MAAM,EAAE,aAAa,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;;QAGnF,IAAI,QAAQ,EAAE;AACZ,YAAA,aAAa,IAAI,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,WAAW,CAAC,GAAG,GAAG,GAAG,QAAQ;;;AAI7F,IAAA,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE;QACxB,aAAa,GAAG,OAAO,CAAC,MAAM,GAAG,aAAa,GAAG,OAAO,CAAC,MAAM;;SAC1D;QACL,aAAa,GAAG,OAAO,CAAC,MAAM,GAAG,aAAa,GAAG,OAAO,CAAC,MAAM;;AAGjE,IAAA,OAAO,aAAa;AACtB;AAEA;;;;;;;;;;;;;;;;;;;;;;;;AAwBG;AACG,SAAU,cAAc,CAC5B,KAAa,EACb,MAAc,EACd,QAAgB,EAChB,YAAqB,EACrB,UAAmB,EAAA;IAEnB,MAAM,MAAM,GAAG,qBAAqB,CAAC,MAAM,EAAE,iBAAiB,CAAC,QAAQ,CAAC;AACxE,IAAA,MAAM,OAAO,GAAG,iBAAiB,CAAC,MAAM,EAAE,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;AAEhG,IAAA,OAAO,CAAC,OAAO,GAAG,yBAAyB,CAAC,YAAa,CAAC;AAC1D,IAAA,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO;IAEjC,MAAM,GAAG,GAAG,0BAA0B,CACpC,KAAK,EACL,OAAO,EACP,MAAM,EACN,YAAY,CAAC,aAAa,EAC1B,YAAY,CAAC,eAAe,EAC5B,UAAU,CACX;AACD,IAAA,QACE;AACG,SAAA,OAAO,CAAC,aAAa,EAAE,QAAQ;;AAE/B,SAAA,OAAO,CAAC,aAAa,EAAE,EAAE;;;;;SAKzB,IAAI,EAAE;AAEb;AAEA;;;;;;;;;;;;;;;;;;AAkBG;SACa,aAAa,CAAC,KAAa,EAAE,MAAc,EAAE,UAAmB,EAAA;IAC9E,MAAM,MAAM,GAAG,qBAAqB,CAAC,MAAM,EAAE,iBAAiB,CAAC,OAAO,CAAC;AACvE,IAAA,MAAM,OAAO,GAAG,iBAAiB,CAAC,MAAM,EAAE,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;IAChG,MAAM,GAAG,GAAG,0BAA0B,CACpC,KAAK,EACL,OAAO,EACP,MAAM,EACN,YAAY,CAAC,KAAK,EAClB,YAAY,CAAC,OAAO,EACpB,UAAU,EACV,IAAI,CACL;IACD,OAAO,GAAG,CAAC,OAAO,CAChB,IAAI,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC,EAC7B,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,WAAW,CAAC,CACxD;AACH;AAEA;;;;;;;;;;;;;;;;AAgBG;SACa,YAAY,CAAC,KAAa,EAAE,MAAc,EAAE,UAAmB,EAAA;IAC7E,MAAM,MAAM,GAAG,qBAAqB,CAAC,MAAM,EAAE,iBAAiB,CAAC,OAAO,CAAC;AACvE,IAAA,MAAM,OAAO,GAAG,iBAAiB,CAAC,MAAM,EAAE,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;AAChG,IAAA,OAAO,0BAA0B,CAC/B,KAAK,EACL,OAAO,EACP,MAAM,EACN,YAAY,CAAC,KAAK,EAClB,YAAY,CAAC,OAAO,EACpB,UAAU,CACX;AACH;AAsBA,SAAS,iBAAiB,CAAC,MAAc,EAAE,SAAS,GAAG,GAAG,EAAA;AACxD,IAAA,MAAM,CAAC,GAAG;AACR,QAAA,MAAM,EAAE,CAAC;AACT,QAAA,OAAO,EAAE,CAAC;AACV,QAAA,OAAO,EAAE,CAAC;AACV,QAAA,MAAM,EAAE,EAAE;AACV,QAAA,MAAM,EAAE,EAAE;AACV,QAAA,MAAM,EAAE,EAAE;AACV,QAAA,MAAM,EAAE,EAAE;AACV,QAAA,KAAK,EAAE,CAAC;AACR,QAAA,MAAM,EAAE,CAAC;KACV;IAED,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC;AAC9C,IAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC;AAChC,IAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC;IAEhC,MAAM,aAAa,GACf,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC;AACjC,UAAE,QAAQ,CAAC,KAAK,CAAC,WAAW;AAC5B,UAAE;AACE,YAAA,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAC1D,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AACxD,SAAA,EACP,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,EAC1B,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,IAAI,EAAE;AAEnC,IAAA,CAAC,CAAC,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAE5D,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACxC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AAC7B,QAAA,IAAI,EAAE,KAAK,SAAS,EAAE;YACpB,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC;;AACxB,aAAA,IAAI,EAAE,KAAK,UAAU,EAAE;AAC5B,YAAA,CAAC,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC;;aACZ;AACL,YAAA,CAAC,CAAC,MAAM,IAAI,EAAE;;;IAIlB,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;IACvC,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC;AAC1C,IAAA,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC;IAEvE,IAAI,QAAQ,EAAE;QACZ,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,EAClE,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC;AAEpC,QAAA,CAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;AACvD,QAAA,CAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,QAAQ,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;;SACtD;QACL,CAAC,CAAC,MAAM,GAAG,SAAS,GAAG,CAAC,CAAC,MAAM;AAC/B,QAAA,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM;;AAGrB,IAAA,OAAO,CAAC;AACV;AAWA;AACA,SAAS,SAAS,CAAC,YAA0B,EAAA;;IAE3C,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;AAChC,QAAA,OAAO,YAAY;;;IAIrB,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,UAAU;AACxE,IAAA,IAAI,YAAY,CAAC,QAAQ,EAAE;AACzB,QAAA,YAAY,CAAC,QAAQ,IAAI,CAAC;;SACrB;AACL,QAAA,IAAI,WAAW,KAAK,CAAC,EAAE;YACrB,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;;AACzB,aAAA,IAAI,WAAW,KAAK,CAAC,EAAE;AAC5B,YAAA,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;AAE7B,QAAA,YAAY,CAAC,UAAU,IAAI,CAAC;;AAG9B,IAAA,OAAO,YAAY;AACrB;AAEA;;;AAGG;AACH,SAAS,WAAW,CAAC,GAAW,EAAA;IAC9B,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE;AAC/B,IAAA,IAAI,QAAQ,GAAG,CAAC,EACd,MAAM,EACN,UAAU;AACZ,IAAA,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK;;AAGf,IAAA,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE;QACnD,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;;;AAI1C,IAAA,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;;QAEjC,IAAI,UAAU,GAAG,CAAC;YAAE,UAAU,GAAG,CAAC;QAClC,UAAU,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;QAClC,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;;AAC1B,SAAA,IAAI,UAAU,GAAG,CAAC,EAAE;;AAEzB,QAAA,UAAU,GAAG,MAAM,CAAC,MAAM;;;AAI5B,IAAA,KAAK,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC,EAAE,EAAE;;;IAIjD,IAAI,CAAC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE;;AAEjC,QAAA,MAAM,GAAG,CAAC,CAAC,CAAC;QACZ,UAAU,GAAG,CAAC;;SACT;;AAEL,QAAA,KAAK,EAAE;AACP,QAAA,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,SAAS;AAAE,YAAA,KAAK,EAAE;;QAGlD,UAAU,IAAI,CAAC;QACf,MAAM,GAAG,EAAE;;AAEX,QAAA,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;AAChC,YAAA,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;;;;AAKxC,IAAA,IAAI,UAAU,GAAG,UAAU,EAAE;QAC3B,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC;AACzC,QAAA,QAAQ,GAAG,UAAU,GAAG,CAAC;QACzB,UAAU,GAAG,CAAC;;AAGhB,IAAA,OAAO,EAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAC;AACvC;AAEA;;;AAGG;AACH,SAAS,WAAW,CAAC,YAA0B,EAAE,OAAe,EAAE,OAAe,EAAA;AAC/E,IAAA,IAAI,OAAO,GAAG,OAAO,EAAE;QACrB,MAAM,IAAIA,aAAY,CAAA,IAAA,iEAEpB,SAAS;AACP,YAAA,CAAA,6CAAA,EAAgD,OAAO,CAAA,8BAAA,EAAiC,OAAO,CAAA,EAAA,CAAI,CACtG;;AAGH,IAAA,IAAI,MAAM,GAAG,YAAY,CAAC,MAAM;IAChC,IAAI,WAAW,GAAG,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,UAAU;AACzD,IAAA,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,EAAE,OAAO,CAAC;;AAGtE,IAAA,IAAI,OAAO,GAAG,YAAY,GAAG,YAAY,CAAC,UAAU;AACpD,IAAA,IAAI,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC;AAE3B,IAAA,IAAI,OAAO,GAAG,CAAC,EAAE;;AAEf,QAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;;AAGzD,QAAA,KAAK,IAAI,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,YAAA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;;;SAEV;;QAEL,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC;AACtC,QAAA,YAAY,CAAC,UAAU,GAAG,CAAC;AAC3B,QAAA,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,GAAG,YAAY,GAAG,CAAC,EAAE;AACzD,QAAA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE;AAAE,YAAA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;;AAGjD,IAAA,IAAI,KAAK,IAAI,CAAC,EAAE;AACd,QAAA,IAAI,OAAO,GAAG,CAAC,GAAG,CAAC,EAAE;AACnB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;AAChC,gBAAA,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;gBACjB,YAAY,CAAC,UAAU,EAAE;;AAE3B,YAAA,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;YACjB,YAAY,CAAC,UAAU,EAAE;;aACpB;AACL,YAAA,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC,EAAE;;;;AAKzB,IAAA,OAAO,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,WAAW,EAAE;AAAE,QAAA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AAE7E,IAAA,IAAI,iBAAiB,GAAG,YAAY,KAAK,CAAC;;;AAG1C,IAAA,MAAM,MAAM,GAAG,OAAO,GAAG,YAAY,CAAC,UAAU;;AAEhD,IAAA,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAA;AAC5D,QAAA,CAAC,GAAG,CAAC,GAAG,KAAK;AACb,QAAA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;QAChC,IAAI,iBAAiB,EAAE;;YAErB,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE;gBAClC,MAAM,CAAC,GAAG,EAAE;;iBACP;gBACL,iBAAiB,GAAG,KAAK;;;AAG7B,QAAA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;KACxB,EAAE,CAAC,CAAC;IACL,IAAI,KAAK,EAAE;AACT,QAAA,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;QACrB,YAAY,CAAC,UAAU,EAAE;;AAE7B;AAEM,SAAU,iBAAiB,CAAC,IAAY,EAAA;AAC5C,IAAA,MAAM,MAAM,GAAW,QAAQ,CAAC,IAAI,CAAC;AACrC,IAAA,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;QACjB,MAAM,IAAIA,aAAY,CAEpB,IAAA,iDAAA,SAAS,IAAI,uCAAuC,GAAG,IAAI,CAC5D;;AAEH,IAAA,OAAO,MAAM;AACf;;AChgBA;;AAEG;MAMmB,cAAc,CAAA;kHAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAd,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,EAJtB,UAAA,EAAA,MAAM,EACN,UAAA,EAAA,CAAC,MAAc,KAAK,IAAI,oBAAoB,CAAC,MAAM,CAAC,kBACzD,SAAS,EAAA,CAAA,EAAA,CAAA;;sGAEI,cAAc,EAAA,UAAA,EAAA,CAAA;kBALnC,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA;AACV,oBAAA,UAAU,EAAE,MAAM;oBAClB,UAAU,EAAE,CAAC,MAAc,KAAK,IAAI,oBAAoB,CAAC,MAAM,CAAC;oBAChE,IAAI,EAAE,CAAC,SAAS,CAAC;AAClB,iBAAA;;AAKD;;;;AAIG;AACG,SAAU,iBAAiB,CAC/B,KAAa,EACb,KAAe,EACf,cAA8B,EAC9B,MAAe,EAAA;AAEf,IAAA,IAAI,GAAG,GAAG,CAAI,CAAA,EAAA,KAAK,EAAE;IAErB,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;AAC3B,QAAA,OAAO,GAAG;;IAGZ,GAAG,GAAG,cAAc,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC;IAErD,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;AAC3B,QAAA,OAAO,GAAG;;IAGZ,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;AAC/B,QAAA,OAAO,OAAO;;IAGhB,MAAM,IAAIA,aAAY,CAEpB,IAAA,iDAAA,SAAS,IAAI,CAAsC,mCAAA,EAAA,KAAK,CAAG,CAAA,CAAA,CAC5D;AACH;AAEA;;;;AAIG;AAEG,MAAO,oBAAqB,SAAQ,cAAc,CAAA;AACb,IAAA,MAAA;AAAzC,IAAA,WAAA,CAAyC,MAAc,EAAA;AACrD,QAAA,KAAK,EAAE;QADgC,IAAM,CAAA,MAAA,GAAN,MAAM;;IAItC,iBAAiB,CAAC,KAAU,EAAE,MAAe,EAAA;AACpD,QAAA,MAAM,MAAM,GAAG,mBAAmB,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC;QAEhE,QAAQ,MAAM;YACZ,KAAK,MAAM,CAAC,IAAI;AACd,gBAAA,OAAO,MAAM;YACf,KAAK,MAAM,CAAC,GAAG;AACb,gBAAA,OAAO,KAAK;YACd,KAAK,MAAM,CAAC,GAAG;AACb,gBAAA,OAAO,KAAK;YACd,KAAK,MAAM,CAAC,GAAG;AACb,gBAAA,OAAO,KAAK;YACd,KAAK,MAAM,CAAC,IAAI;AACd,gBAAA,OAAO,MAAM;AACf,YAAA;AACE,gBAAA,OAAO,OAAO;;;AApBT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,kBACX,SAAS,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHADlB,oBAAoB,EAAA,CAAA;;sGAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBADhC;;0BAEc,MAAM;2BAAC,SAAS;;;AC/C/B,MAAM,SAAS,GAAG,KAAK;AAEvB,MAAM,WAAW,GAAa,EAAE;AAkBhC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCG;MAIU,OAAO,CAAA;AAOR,IAAA,KAAA;AACA,IAAA,SAAA;IAPF,cAAc,GAAG,WAAW;AAC5B,IAAA,QAAQ;AAER,IAAA,QAAQ,GAAG,IAAI,GAAG,EAAyB;IAEnD,WACU,CAAA,KAAiB,EACjB,SAAoB,EAAA;QADpB,IAAK,CAAA,KAAA,GAAL,KAAK;QACL,IAAS,CAAA,SAAA,GAAT,SAAS;;IAGnB,IACI,KAAK,CAAC,KAAa,EAAA;QACrB,IAAI,CAAC,cAAc,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,WAAW;;IAGnF,IACI,OAAO,CAAC,KAAkF,EAAA;QAC5F,IAAI,CAAC,QAAQ,GAAG,OAAO,KAAK,KAAK,QAAQ,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,KAAK;;AAGnF;;;;;;;;;;;;;;;;;;;;;;;;AAwBG;IACH,SAAS,GAAA;;AAEP,QAAA,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;AACvC,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC;;;AAIhC,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ;QAC9B,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,QAAQ,YAAY,GAAG,EAAE;AACtD,YAAA,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE;AAC5B,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC;;;AAE3B,aAAA,IAAI,QAAQ,IAAI,IAAI,EAAE;YAC3B,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AACzC,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;;QAItD,IAAI,CAAC,eAAe,EAAE;;IAGhB,YAAY,CAAC,KAAa,EAAE,WAAoB,EAAA;QACtD,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC;AACtC,QAAA,IAAI,KAAK,KAAK,SAAS,EAAE;AACvB,YAAA,IAAI,KAAK,CAAC,OAAO,KAAK,WAAW,EAAE;AACjC,gBAAA,KAAK,CAAC,OAAO,GAAG,IAAI;AACpB,gBAAA,KAAK,CAAC,OAAO,GAAG,WAAW;;AAE7B,YAAA,KAAK,CAAC,OAAO,GAAG,IAAI;;aACf;YACL,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,EAAC,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;;;IAI1E,eAAe,GAAA;AACrB,QAAA,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,QAAQ,EAAE;AACtC,YAAA,MAAM,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC;AAC3B,YAAA,MAAM,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC;AAE3B,YAAA,IAAI,KAAK,CAAC,OAAO,EAAE;gBACjB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC;AACvC,gBAAA,KAAK,CAAC,OAAO,GAAG,KAAK;;AAChB,iBAAA,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;;;AAGzB,gBAAA,IAAI,KAAK,CAAC,OAAO,EAAE;AACjB,oBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC;;AAEjC,gBAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC;;AAG7B,YAAA,KAAK,CAAC,OAAO,GAAG,KAAK;;;IAIjB,YAAY,CAAC,KAAa,EAAE,OAAgB,EAAA;QAClD,IAAI,SAAS,EAAE;AACb,YAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,MAAM,IAAI,KAAK,CACb,CAAiE,8DAAA,EAAAE,UAAS,CAAC,KAAK,CAAC,CAAE,CAAA,CACpF;;;AAGL,QAAA,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE;AACpB,QAAA,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACpB,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;gBACvC,IAAI,OAAO,EAAE;AACX,oBAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC;;qBACnD;AACL,oBAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC;;AAE/D,aAAC,CAAC;;;kHArHK,OAAO,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;sGAAP,OAAO,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,CAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;sGAAP,OAAO,EAAA,UAAA,EAAA,CAAA;kBAHnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,WAAW;AACtB,iBAAA;uGAaK,KAAK,EAAA,CAAA;sBADR,KAAK;uBAAC,OAAO;gBAMV,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,SAAS;;;ACrElB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuEG;MAKU,iBAAiB,CAAA;AAmCR,IAAA,iBAAA;;;;IA/BX,iBAAiB,GAAqB,IAAI;AAE1C,IAAA,uBAAuB;AACvB,IAAA,yBAAyB;AACzB,IAAA,oCAAoC;AACpC,IAAA,wBAAwB;AAExB,IAAA,yBAAyB;AAClC;;AAEG;AACM,IAAA,gCAAgC;AAEjC,IAAA,aAAa;AACb,IAAA,UAAU;AAElB;;;;AAIG;AACK,IAAA,WAAW,GAAG,IAAI,GAAG,EAAmB;AAEhD;;;AAGG;AACH,IAAA,IAAI,iBAAiB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,aAAa,EAAE,QAAQ,IAAI,IAAI;;AAG7C,IAAA,WAAA,CAAoB,iBAAmC,EAAA;QAAnC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;;AAE7B,IAAA,+BAA+B,CAAC,OAAsB,EAAA;;;;AAI5D,QAAA,QACE,OAAO,CAAC,2BAA2B,CAAC,KAAK,SAAS;AAClD,YAAA,OAAO,CAAC,kCAAkC,CAAC,KAAK,SAAS;;AAIrD,IAAA,gCAAgC,CAAC,OAAsB,EAAA;;;;AAI7D,QAAA,QACE,OAAO,CAAC,mBAAmB,CAAC,KAAK,SAAS;AAC1C,YAAA,OAAO,CAAC,0BAA0B,CAAC,KAAK,SAAS;AACjD,YAAA,OAAO,CAAC,2BAA2B,CAAC,KAAK,SAAS;AAClD,YAAA,OAAO,CAAC,sCAAsC,CAAC,KAAK,SAAS;AAC7D,YAAA,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC;;;AAKjD,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,IAAI,CAAC,gCAAgC,CAAC,OAAO,CAAC,EAAE;AAClD,YAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE;AAC9B,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;AACxB,YAAA,IAAI,CAAC,aAAa,GAAG,SAAS;AAE9B,YAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC,iBAAiB,CAAC,cAAc;AAExF,gBAAA,IAAI,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,EAAE;AACjD,oBAAA,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE;AAE1B,oBAAA,IAAI,IAAI,CAAC,yBAAyB,EAAE;AAClC,wBAAA,IAAI,CAAC,UAAU,GAAG,cAAc,CAC9B,IAAI,CAAC,yBAAyB,EAC9B,iBAAiB,CAAC,QAAQ,CAAC,CAC5B;;AACI,yBAAA,IAAI,IAAI,CAAC,gCAAgC,EAAE;AAChD,wBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,gCAAgC,CAAC,MAAM,CAC5D,iBAAiB,CAAC,QAAQ,CAAC,CAC5B;;yBACI;AACL,wBAAA,IAAI,CAAC,UAAU,GAAG,SAAS;;;AAI/B,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,iBAAiB,EAAE;oBAClF,QAAQ;oBACR,WAAW,EAAE,IAAI,CAAC,UAAU;oBAC5B,gBAAgB,EAAE,IAAI,CAAC,wBAAwB;oBAC/C,mBAAmB,EAAE,IAAI,CAAC,oCAAoC;AAC/D,iBAAA,CAAC;;;;;IAMR,SAAS,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,IAAI,CAAC,uBAAuB,EAAE;AAChC,gBAAA,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,EAAE;oBACjE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC;;;AAIzC,YAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC;;;;IAKjD,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE;;AAGpB,IAAA,oBAAoB,CAAC,YAAmC,EAAA;QAC9D,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE;YACnD,IAAI,CAAC,OAAO,EAAE;;AAEZ,gBAAA,YAAY,CAAC,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC;AAC3C,gBAAA,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC;;iBAC7B;;AAEL,gBAAA,YAAY,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,uBAAwB,CAAC,SAAS,CAAC,CAAC;gBAC1E,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC;;;;kHA5HjC,iBAAiB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;sGAAjB,iBAAiB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,uBAAA,EAAA,yBAAA,EAAA,yBAAA,EAAA,2BAAA,EAAA,oCAAA,EAAA,sCAAA,EAAA,wBAAA,EAAA,0BAAA,EAAA,yBAAA,EAAA,2BAAA,EAAA,gCAAA,EAAA,kCAAA,EAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;sGAAjB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAJ7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,QAAQ,EAAE,mBAAmB;AAC9B,iBAAA;qFAKU,iBAAiB,EAAA,CAAA;sBAAzB;gBAEQ,uBAAuB,EAAA,CAAA;sBAA/B;gBACQ,yBAAyB,EAAA,CAAA;sBAAjC;gBACQ,oCAAoC,EAAA,CAAA;sBAA5C;gBACQ,wBAAwB,EAAA,CAAA;sBAAhC;gBAEQ,yBAAyB,EAAA,CAAA;sBAAjC;gBAIQ,gCAAgC,EAAA,CAAA;sBAAxC;;AAmHH;AACA,SAAS,iBAAiB,CAAC,QAAkB,EAAA;IAC3C,MAAM,cAAc,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC;IAChD,OAAO,cAAc,CAAC,QAAQ;AAChC;;ACjNA;;;;;AAKG;MACU,cAAc,CAAA;AAGhB,IAAA,SAAA;AAOA,IAAA,OAAA;AAGA,IAAA,KAAA;AAGA,IAAA,KAAA;AAfT,IAAA,WAAA;;IAES,SAAY;AAEnB;;;;AAIG;IACI,OAAU;;IAGV,KAAa;;IAGb,KAAa,EAAA;QAbb,IAAS,CAAA,SAAA,GAAT,SAAS;QAOT,IAAO,CAAA,OAAA,GAAP,OAAO;QAGP,IAAK,CAAA,KAAA,GAAL,KAAK;QAGL,IAAK,CAAA,KAAA,GAAL,KAAK;;;AAId,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,KAAK,KAAK,CAAC;;;AAIzB,IAAA,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC;;;AAItC,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC;;;AAI7B,IAAA,IAAI,GAAG,GAAA;AACL,QAAA,OAAO,CAAC,IAAI,CAAC,IAAI;;AAEpB;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoGG;MAIU,OAAO,CAAA;AAqDR,IAAA,cAAA;AACA,IAAA,SAAA;AACA,IAAA,QAAA;AAtDV;;;;AAIG;IACH,IACI,OAAO,CAAC,OAA+C,EAAA;AACzD,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO;AACvB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI;;AAE3B;;;;;;;;;;;;;;;;;;AAkBG;IACH,IACI,YAAY,CAAC,EAAsB,EAAA;AACrC,QAAA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,EAAE,IAAI,IAAI,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;YAC7F,OAAO,CAAC,IAAI,CACV,CAA4C,yCAAA,EAAA,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAI,EAAA,CAAA;AAChE,gBAAA,CAAA,mFAAA,CAAqF,CACxF;;AAEH,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;AAGtB,IAAA,IAAI,YAAY,GAAA;QACd,OAAO,IAAI,CAAC,UAAU;;IAGhB,QAAQ,GAAyB,IAAI;IACrC,aAAa,GAAY,IAAI;IAC7B,OAAO,GAA6B,IAAI;;;AAGxC,IAAA,UAAU;AAElB,IAAA,WAAA,CACU,cAAgC,EAChC,SAA4C,EAC5C,QAAyB,EAAA;QAFzB,IAAc,CAAA,cAAA,GAAd,cAAc;QACd,IAAS,CAAA,SAAA,GAAT,SAAS;QACT,IAAQ,CAAA,QAAA,GAAR,QAAQ;;AAGlB;;;;AAIG;IACH,IACI,aAAa,CAAC,KAAwC,EAAA;;;;QAIxD,IAAI,KAAK,EAAE;AACT,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;;AAI1B;;;AAGG;IACH,SAAS,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK;;AAE1B,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ;AAC3B,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,KAAK,EAAE;AAC1B,gBAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,oBAAA,IAAI;;;AAGF,wBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;;AAClE,oBAAA,MAAM;AACN,wBAAA,IAAI,YAAY,GACd,CAA2C,wCAAA,EAAA,KAAK,CAAa,WAAA,CAAA;AAC7D,4BAAA,CAAA,EAAG,WAAW,CAAC,KAAK,CAAC,8DAA8D;AACrF,wBAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;4BAC7B,YAAY,IAAI,yCAAyC;;AAE3D,wBAAA,MAAM,IAAIF,aAAY,CAAyC,CAAA,IAAA,+CAAA,YAAY,CAAC;;;qBAEzE;;;AAGL,oBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;;;;AAIxE,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;AAChD,YAAA,IAAI,OAAO;AAAE,gBAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;;;AAIpC,IAAA,aAAa,CAAC,OAA2B,EAAA;AAC/C,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc;QACzC,OAAO,CAAC,gBAAgB,CACtB,CACE,IAA6B,EAC7B,qBAAoC,EACpC,YAA2B,KACzB;AACF,YAAA,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE;;;;AAI9B,gBAAA,aAAa,CAAC,kBAAkB,CAC9B,IAAI,CAAC,SAAS,EACd,IAAI,cAAc,CAAO,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAC3D,YAAY,KAAK,IAAI,GAAG,SAAS,GAAG,YAAY,CACjD;;AACI,iBAAA,IAAI,YAAY,IAAI,IAAI,EAAE;AAC/B,gBAAA,aAAa,CAAC,MAAM,CAAC,qBAAqB,KAAK,IAAI,GAAG,SAAS,GAAG,qBAAqB,CAAC;;AACnF,iBAAA,IAAI,qBAAqB,KAAK,IAAI,EAAE;gBACzC,MAAM,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,qBAAqB,CAAE;AACtD,gBAAA,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC;AACtC,gBAAA,eAAe,CAAC,IAA6C,EAAE,IAAI,CAAC;;AAExE,SAAC,CACF;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;YAC1D,MAAM,OAAO,GAA0C,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3E,YAAA,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO;AAC/B,YAAA,OAAO,CAAC,KAAK,GAAG,CAAC;AACjB,YAAA,OAAO,CAAC,KAAK,GAAG,IAAI;AACpB,YAAA,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,QAAS;;AAGlC,QAAA,OAAO,CAAC,qBAAqB,CAAC,CAAC,MAAW,KAAI;YAC5C,MAAM,OAAO,GAA0C,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC;AAC7F,YAAA,eAAe,CAAC,OAAO,EAAE,MAAM,CAAC;AAClC,SAAC,CAAC;;AAGJ;;;;;AAKG;AACH,IAAA,OAAO,sBAAsB,CAC3B,GAAkB,EAClB,GAAQ,EAAA;AAER,QAAA,OAAO,IAAI;;kHAjKF,OAAO,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,WAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,eAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;sGAAP,OAAO,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,YAAA,EAAA,cAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;sGAAP,OAAO,EAAA,UAAA,EAAA,CAAA;kBAHnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kBAAkB;AAC7B,iBAAA;6IAQK,OAAO,EAAA,CAAA;sBADV;gBAyBG,YAAY,EAAA,CAAA;sBADf;gBAkCG,aAAa,EAAA,CAAA;sBADhB;;AA2GH,SAAS,eAAe,CACtB,IAAwC,EACxC,MAA+B,EAAA;IAE/B,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI;AACtC;AAEA,SAAS,WAAW,CAAC,IAAS,EAAA;AAC5B,IAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAI;AACpC;;AC/UA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6IG;MAIU,IAAI,CAAA;AAQL,IAAA,cAAA;AAPF,IAAA,QAAQ,GAAmB,IAAI,WAAW,EAAK;IAC/C,gBAAgB,GAAuC,IAAI;IAC3D,gBAAgB,GAAuC,IAAI;IAC3D,YAAY,GAA2C,IAAI;IAC3D,YAAY,GAA2C,IAAI;IAEnE,WACU,CAAA,cAAgC,EACxC,WAAwC,EAAA;QADhC,IAAc,CAAA,cAAA,GAAd,cAAc;AAGtB,QAAA,IAAI,CAAC,gBAAgB,GAAG,WAAW;;AAGrC;;;AAGG;IACH,IACI,IAAI,CAAC,SAAY,EAAA;AACnB,QAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,SAAS;QACxD,IAAI,CAAC,WAAW,EAAE;;AAGpB;;;AAGG;IACH,IACI,QAAQ,CAAC,WAA+C,EAAA;AAC1D,QAAA,cAAc,CAAC,WAAW,EAAE,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,UAAU,CAAC;AAC1F,QAAA,IAAI,CAAC,gBAAgB,GAAG,WAAW;AACnC,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,WAAW,EAAE;;AAGpB;;;AAGG;IACH,IACI,QAAQ,CAAC,WAA+C,EAAA;AAC1D,QAAA,cAAc,CAAC,WAAW,EAAE,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,UAAU,CAAC;AAC1F,QAAA,IAAI,CAAC,gBAAgB,GAAG,WAAW;AACnC,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,WAAW,EAAE;;IAGZ,WAAW,GAAA;AACjB,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;AAC3B,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;AAC3B,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AACxB,gBAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,kBAAkB,CACxD,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,QAAQ,CACd;;;;aAGA;AACL,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;AAC3B,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AACxB,gBAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,kBAAkB,CACxD,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,QAAQ,CACd;;;;;;IAOF,OAAO,kBAAkB;AAEhC;;;;;;;AAOG;IACH,OAAO,oBAAoB;AAE3B;;;;;AAKG;AACH,IAAA,OAAO,sBAAsB,CAC3B,GAAY,EACZ,GAAQ,EAAA;AAER,QAAA,OAAO,IAAI;;kHAjGF,IAAI,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,WAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;sGAAJ,IAAI,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;sGAAJ,IAAI,EAAA,UAAA,EAAA,CAAA;kBAHhB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,QAAQ;AACnB,iBAAA;+GAoBK,IAAI,EAAA,CAAA;sBADP;gBAWG,QAAQ,EAAA,CAAA;sBADX;gBAaG,QAAQ,EAAA,CAAA;sBADX;;AA6DH;;;;;AAKG;MACU,WAAW,CAAA;IACf,SAAS,GAAM,IAAK;IACpB,IAAI,GAAM,IAAK;AACvB;AAED,SAAS,cAAc,CACrB,WAAoC,EACpC,QAA+B,EAAA;AAE/B,IAAA,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE;QAClD,MAAM,IAAIA,aAAY,CAAA,IAAA,kDAEpB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;YAC5C,CAAG,EAAA,QAAQ,yCAAyCE,UAAS,CAAC,WAAW,CAAC,CAAA,EAAA,CAAI,CACjF;;AAEL;;MC3Qa,UAAU,CAAA;AAIX,IAAA,iBAAA;AACA,IAAA,YAAA;IAJF,QAAQ,GAAG,KAAK;IAExB,WACU,CAAA,iBAAmC,EACnC,YAAiC,EAAA;QADjC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;QACjB,IAAY,CAAA,YAAA,GAAZ,YAAY;;IAGtB,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;QACpB,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC;;IAG9D,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;AACrB,QAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE;;AAGhC,IAAA,YAAY,CAAC,OAAgB,EAAA;AAC3B,QAAA,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAC7B,IAAI,CAAC,MAAM,EAAE;;AACR,aAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;YACpC,IAAI,CAAC,OAAO,EAAE;;;AAGnB;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmEG;MAIU,QAAQ,CAAA;IACX,aAAa,GAAiB,EAAE;IAChC,YAAY,GAAG,KAAK;IACpB,UAAU,GAAG,CAAC;IACd,mBAAmB,GAAG,CAAC;IACvB,iBAAiB,GAAG,KAAK;AACzB,IAAA,SAAS;;IAGjB,IACI,QAAQ,CAAC,QAAa,EAAA;AACxB,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ;AACzB,QAAA,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE;AACzB,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;;;;IAKlC,QAAQ,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,UAAU,EAAE;;;AAI1B,IAAA,WAAW,CAAC,IAAgB,EAAA;AAC1B,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;;;AAI/B,IAAA,UAAU,CAAC,KAAU,EAAA;AACnB,QAAA,MAAM,OAAO,GAAG,KAAK,KAAK,IAAI,CAAC,SAAS;AACxC,QAAA,IAAI,CAAC,iBAAiB,KAAK,OAAO;QAClC,IAAI,CAAC,mBAAmB,EAAE;QAC1B,IAAI,IAAI,CAAC,mBAAmB,KAAK,IAAI,CAAC,UAAU,EAAE;YAChD,IAAI,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC;AACjD,YAAA,IAAI,CAAC,mBAAmB,GAAG,CAAC;AAC5B,YAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK;;AAEhC,QAAA,OAAO,OAAO;;AAGR,IAAA,mBAAmB,CAAC,UAAmB,EAAA;AAC7C,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,UAAU,KAAK,IAAI,CAAC,YAAY,EAAE;AACrE,YAAA,IAAI,CAAC,YAAY,GAAG,UAAU;AAC9B,YAAA,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,aAAa,EAAE;AAC5C,gBAAA,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC;;;;kHA5C/B,QAAQ,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;sGAAR,QAAQ,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;sGAAR,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAHpB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,YAAY;AACvB,iBAAA;8BAWK,QAAQ,EAAA,CAAA;sBADX;;AAyCH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCG;MAIU,YAAY,CAAA;AAWO,IAAA,QAAA;AAVtB,IAAA,KAAK;AACb;;;AAGG;AACM,IAAA,YAAY;AAErB,IAAA,WAAA,CACE,aAA+B,EAC/B,WAAgC,EACJ,QAAkB,EAAA;QAAlB,IAAQ,CAAA,QAAA,GAAR,QAAQ;AAEpC,QAAA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,CAAC,QAAQ,EAAE;AAChE,YAAA,kCAAkC,CAAC,cAAc,EAAE,cAAc,CAAC;;QAGpE,QAAQ,CAAC,QAAQ,EAAE;QACnB,IAAI,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC,aAAa,EAAE,WAAW,CAAC;;AAGzD;;;AAGG;IACH,SAAS,GAAA;AACP,QAAA,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;;kHA1B3D,YAAY,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,WAAA,EAAA,EAAA,EAAA,KAAA,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;sGAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;sGAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAHxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;AAC3B,iBAAA;;0BAYI;;0BAAY;yCALN,YAAY,EAAA,CAAA;sBAApB;;AAwBH;;;;;;;;;;;;;;;AAeG;MAIU,eAAe,CAAA;AAC1B,IAAA,WAAA,CACE,aAA+B,EAC/B,WAAgC,EACZ,QAAkB,EAAA;AAEtC,QAAA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,CAAC,QAAQ,EAAE;AAChE,YAAA,kCAAkC,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;;QAG1E,QAAQ,CAAC,WAAW,CAAC,IAAI,UAAU,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;;kHAVvD,eAAe,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,WAAA,EAAA,EAAA,EAAA,KAAA,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;sGAAf,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;sGAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAH3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;AAC9B,iBAAA;;0BAKI;;0BAAY;;AAUjB,SAAS,kCAAkC,CAAC,QAAgB,EAAE,aAAqB,EAAA;AACjF,IAAA,MAAM,IAAIF,aAAY,CAEpB,IAAA,oDAAA,CAAA,qBAAA,EAAwB,QAAQ,CAAc,YAAA,CAAA;AAC5C,QAAA,CAAA,eAAA,EAAkB,aAAa,CAA+E,6EAAA,CAAA;AAC9G,QAAA,CAAA,+BAAA,CAAiC,CACpC;AACH;;ACvQA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BG;MAIU,QAAQ,CAAA;AAIC,IAAA,aAAA;AAHZ,IAAA,WAAW;IACX,UAAU,GAA8B,EAAE;AAElD,IAAA,WAAA,CAAoB,aAA6B,EAAA;QAA7B,IAAa,CAAA,aAAA,GAAb,aAAa;;IAEjC,IACI,QAAQ,CAAC,KAAa,EAAA;AACxB,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;;IAGzB,OAAO,CAAC,KAAa,EAAE,UAAsB,EAAA;AAC3C,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,UAAU;;AAG7B,IAAA,WAAW,CAAC,WAAmB,EAAA;QACrC,IAAI,CAAC,WAAW,EAAE;QAElB,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;AAC1C,QAAA,MAAM,GAAG,GAAG,iBAAiB,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC;QACrE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;;IAGlC,WAAW,GAAA;QACjB,IAAI,IAAI,CAAC,WAAW;AAAE,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;;AAG1C,IAAA,aAAa,CAAC,IAAgB,EAAA;QACpC,IAAI,IAAI,EAAE;AACR,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI;AACvB,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;;;kHA9BlB,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAG,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;sGAAR,QAAQ,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;sGAAR,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAHpB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,YAAY;AACvB,iBAAA;gFAQK,QAAQ,EAAA,CAAA;sBADX;;AA6BH;;;;;;;;;;;;;;;;;;;AAmBG;MAIU,YAAY,CAAA;AAEa,IAAA,KAAA;AADpC,IAAA,WAAA,CACoC,KAAa,EAC/C,QAA6B,EAC7B,aAA+B,EACvB,QAAkB,EAAA;QAHQ,IAAK,CAAA,KAAA,GAAL,KAAK;QAKvC,MAAM,SAAS,GAAY,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAChD,QAAQ,CAAC,OAAO,CAAC,SAAS,GAAG,CAAI,CAAA,EAAA,KAAK,CAAE,CAAA,GAAG,KAAK,EAAE,IAAI,UAAU,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;;AARjF,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,kBAEV,cAAc,EAAA,SAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,WAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;sGAFhB,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;sGAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAHxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;AAC3B,iBAAA;;0BAGI,SAAS;2BAAC,cAAc;;0BAGxB;;;AC5FL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCG;MAIU,OAAO,CAAA;AAKR,IAAA,KAAA;AACA,IAAA,QAAA;AACA,IAAA,SAAA;IANF,QAAQ,GAA+C,IAAI;IAC3D,OAAO,GAAmD,IAAI;AAEtE,IAAA,WAAA,CACU,KAAiB,EACjB,QAAyB,EACzB,SAAoB,EAAA;QAFpB,IAAK,CAAA,KAAA,GAAL,KAAK;QACL,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAS,CAAA,SAAA,GAAT,SAAS;;IAGnB,IACI,OAAO,CAAC,MAAiD,EAAA;AAC3D,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM;AACtB,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,MAAM,EAAE;AAC3B,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE;;;IAItD,SAAS,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAS,CAAC;YACjD,IAAI,OAAO,EAAE;AACX,gBAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;;;;IAKzB,SAAS,CAAC,WAAmB,EAAE,KAAyC,EAAA;AAC9E,QAAA,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC;QAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,SAAS,GAAI,mBAAmB,CAAC,QAAmB;AAE7F,QAAA,IAAI,KAAK,IAAI,IAAI,EAAE;AACjB,YAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,CACrB,IAAI,CAAC,KAAK,CAAC,aAAa,EACxB,IAAI,EACJ,IAAI,GAAG,CAAA,EAAG,KAAK,CAAA,EAAG,IAAI,CAAA,CAAE,GAAG,KAAK,EAChC,KAAK,CACN;;aACI;AACL,YAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,KAAK,CAAC;;;AAI7D,IAAA,aAAa,CAAC,OAAiD,EAAA;AACrE,QAAA,OAAO,CAAC,kBAAkB,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACxE,OAAO,CAAC,gBAAgB,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;QACrF,OAAO,CAAC,kBAAkB,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;;kHA9C9E,OAAO,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;sGAAP,OAAO,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;sGAAP,OAAO,EAAA,UAAA,EAAA,CAAA;kBAHnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,WAAW;AACtB,iBAAA;qIAYK,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,SAAS;;;ACrDlB;;;;;;;;;;;;;;;;;;;;;;;AAuBG;MAIU,gBAAgB,CAAA;AAmBP,IAAA,iBAAA;IAlBZ,QAAQ,GAA8B,IAAI;AAElD;;;;;AAKG;IACa,uBAAuB,GAAyB,IAAI;AAEpE;;AAEG;IACa,gBAAgB,GAAsC,IAAI;;IAG1D,wBAAwB,GAAgC,IAAI;AAE5E,IAAA,WAAA,CAAoB,iBAAmC,EAAA;QAAnC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;;AAErC,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE;AACrC,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB;AAE/C,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,gBAAA,gBAAgB,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;;;AAIlE,YAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AAC1B,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;gBACpB;;;;AAKF,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,0BAA0B,EAAE;AACrD,YAAA,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,EAAE,WAAW,EAAE;AACtF,gBAAA,QAAQ,EAAE,IAAI,CAAC,wBAAwB,IAAI,SAAS;AACrD,aAAA,CAAC;;;AAIN;;;;AAIG;AACK,IAAA,mBAAmB,CAAC,OAAsB,EAAA;AAChD,QAAA,OAAO,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC;;AAG/E;;;;AAIG;IACK,0BAA0B,GAAA;AAChC,QAAA,OAAU,IAAI,KAAK,CACjB,EAAE,EACF;YACE,GAAG,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,KAAI;AAC/B,gBAAA,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;AACjC,oBAAA,OAAO,KAAK;;AAEd,gBAAA,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,uBAAuB,EAAE,IAAI,EAAE,QAAQ,CAAC;aACjE;YACD,GAAG,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,KAAI;AAC/B,gBAAA,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;AACjC,oBAAA,OAAO,SAAS;;AAElB,gBAAA,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,uBAAuB,EAAE,IAAI,EAAE,QAAQ,CAAC;aACjE;AACF,SAAA,CACF;;kHA3EQ,gBAAgB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;sGAAhB,gBAAgB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,EAAA,uBAAA,EAAA,yBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,wBAAA,EAAA,0BAAA,EAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;sGAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAH5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oBAAoB;AAC/B,iBAAA;qFAUiB,uBAAuB,EAAA,CAAA;sBAAtC;gBAKe,gBAAgB,EAAA,CAAA;sBAA/B;gBAGe,wBAAwB,EAAA,CAAA;sBAAvC;;;AC5BH;;;AAGG;AACI,MAAM,iBAAiB,GAAe;IAC3C,OAAO;IACP,iBAAiB;IACjB,OAAO;IACP,IAAI;IACJ,gBAAgB;IAChB,OAAO;IACP,QAAQ;IACR,YAAY;IACZ,eAAe;IACf,QAAQ;IACR,YAAY;CACb;;ACxCe,SAAA,wBAAwB,CAAC,IAAe,EAAE,KAAa,EAAA;AACrE,IAAA,OAAO,IAAIH,aAAY,CAErB,IAAA,+CAAA,SAAS,IAAI,CAAyB,sBAAA,EAAA,KAAK,CAAe,YAAA,EAAAE,UAAS,CAAC,IAAI,CAAC,CAAA,CAAA,CAAG,CAC7E;AACH;;ACgBA,MAAM,oBAAoB,CAAA;AACxB,IAAA,kBAAkB,CAChB,KAAwB,EACxB,iBAAsB,EACtB,OAA6B,EAAA;;;;;;;;QAS7B,OAAO,SAAS,CAAC,MACf,KAAK,CAAC,SAAS,CAAC;AACd,YAAA,IAAI,EAAE,iBAAiB;AACvB,YAAA,KAAK,EAAE,OAAO;AACf,SAAA,CAAC,CACH;;AAGH,IAAA,OAAO,CAAC,YAA4B,EAAA;;QAElC,SAAS,CAAC,MAAM,YAAY,CAAC,WAAW,EAAE,CAAC;;AAE9C;AAED,MAAM,eAAe,CAAA;AACnB,IAAA,kBAAkB,CAChB,KAAuB,EACvB,iBAA2C,EAC3C,OAAsC,EAAA;;;;;;;;;;;;;;;;;;;;;;AAuBtC,QAAA,KAAK,CAAC,IAAI;;;QAGR,CAAC,CAAC,KAAK,iBAAiB,GAAG,CAAC,CAAC,EAC7B,CAAC,CAAC,KAAK,OAAO,GAAG,CAAC,CAAC,CACpB;QACD,OAAO;YACL,WAAW,EAAE,MAAK;gBAChB,iBAAiB,GAAG,IAAI;gBACxB,OAAO,GAAG,IAAI;aACf;SACF;;AAGH,IAAA,OAAO,CAAC,YAA4B,EAAA;QAClC,YAAY,CAAC,WAAW,EAAE;;AAE7B;AAED,MAAM,gBAAgB,GAAG,IAAI,eAAe,EAAE;AAC9C,MAAM,qBAAqB,GAAG,IAAI,oBAAoB,EAAE;AAExD;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BG;MAKU,SAAS,CAAA;AACZ,IAAA,IAAI;IACJ,YAAY,GAAQ,IAAI;IACxB,yBAAyB,GAAG,IAAI;IAEhC,aAAa,GAA6C,IAAI;IAC9D,IAAI,GAAoE,IAAI;IAC5E,SAAS,GAAgC,IAAI;AACpC,IAAA,uBAAuB,GAAG,MAAM,CAACE,mCAAkC,CAAC;AAErF,IAAA,WAAA,CAAY,GAAsB,EAAA;;;AAGhC,QAAA,IAAI,CAAC,IAAI,GAAG,GAAG;;IAGjB,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,QAAQ,EAAE;;;;;;AAMjB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI;;AAUlB,IAAA,SAAS,CAAI,GAAwE,EAAA;AACnF,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACd,IAAI,GAAG,EAAE;AACP,gBAAA,IAAI;;;;AAIF,oBAAA,IAAI,CAAC,yBAAyB,GAAG,KAAK;AACtC,oBAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;;wBACZ;AACR,oBAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI;;;YAGzC,OAAO,IAAI,CAAC,YAAY;;AAG1B,QAAA,IAAI,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE;YACrB,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;;QAG5B,OAAO,IAAI,CAAC,YAAY;;AAGlB,IAAA,UAAU,CAAC,GAA6D,EAAA;AAC9E,QAAA,IAAI,CAAC,IAAI,GAAG,GAAG;QACf,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;AAC1C,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,CACpD,GAAG,EACH,CAAC,KAAa,KAAK,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,KAAK,CAAC,EACtD,CAAC,CAAC,KAAK,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CACvC;;AAGK,IAAA,eAAe,CACrB,GAA6D,EAAA;AAE7D,QAAA,IAAIC,UAAU,CAAC,GAAG,CAAC,EAAE;AACnB,YAAA,OAAO,gBAAgB;;AAGzB,QAAA,IAAIC,eAAe,CAAC,GAAG,CAAC,EAAE;AACxB,YAAA,OAAO,qBAAqB;;AAG9B,QAAA,MAAM,wBAAwB,CAAC,SAAS,EAAE,GAAG,CAAC;;IAGxC,QAAQ,GAAA;;;QAGd,IAAI,CAAC,SAAU,CAAC,OAAO,CAAC,IAAI,CAAC,aAAc,CAAC;AAC5C,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AACxB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI;AACzB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI;;IAGV,kBAAkB,CAAC,KAAU,EAAE,KAAa,EAAA;AAClD,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,EAAE;AACvB,YAAA,IAAI,CAAC,YAAY,GAAG,KAAK;AACzB,YAAA,IAAI,IAAI,CAAC,yBAAyB,EAAE;AAClC,gBAAA,IAAI,CAAC,IAAI,EAAE,YAAY,EAAE;;;;kHA/FpB,SAAS,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA;gHAAT,SAAS,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,KAAA,EAAA,CAAA;;sGAAT,SAAS,EAAA,UAAA,EAAA,CAAA;kBAJrB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,OAAO;AACb,oBAAA,IAAI,EAAE,KAAK;AACZ,iBAAA;;;AChID;;;;;;;;;;;;;;AAcG;MAIU,aAAa,CAAA;AAOxB,IAAA,SAAS,CAAC,KAAgC,EAAA;QACxC,IAAI,KAAK,IAAI,IAAI;AAAE,YAAA,OAAO,IAAI;AAC9B,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,YAAA,MAAM,wBAAwB,CAAC,aAAa,EAAE,KAAK,CAAC;;AAEtD,QAAA,OAAO,KAAK,CAAC,WAAW,EAAE;;kHAZjB,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA;gHAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,WAAA,EAAA,CAAA;;sGAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBAHzB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,WAAW;AAClB,iBAAA;;AAiBD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,MAAM,gBAAgB,GACpB,orPAAorP;AAEtrP;;;;;;;;;;;;;;;;AAgBG;MAIU,aAAa,CAAA;AAOxB,IAAA,SAAS,CAAC,KAAgC,EAAA;QACxC,IAAI,KAAK,IAAI,IAAI;AAAE,YAAA,OAAO,IAAI;AAC9B,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,YAAA,MAAM,wBAAwB,CAAC,aAAa,EAAE,KAAK,CAAC;;AAGtD,QAAA,OAAO,KAAK,CAAC,OAAO,CAClB,gBAAgB,EAChB,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAC3D;;kHAhBQ,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA;gHAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,WAAA,EAAA,CAAA;;sGAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBAHzB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,WAAW;AAClB,iBAAA;;AAqBD;;;;;;;AAOG;MAIU,aAAa,CAAA;AAOxB,IAAA,SAAS,CAAC,KAAgC,EAAA;QACxC,IAAI,KAAK,IAAI,IAAI;AAAE,YAAA,OAAO,IAAI;AAC9B,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,YAAA,MAAM,wBAAwB,CAAC,aAAa,EAAE,KAAK,CAAC;;AAEtD,QAAA,OAAO,KAAK,CAAC,WAAW,EAAE;;kHAZjB,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA;gHAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,WAAA,EAAA,CAAA;;sGAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBAHzB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,WAAW;AAClB,iBAAA;;;ACvFD;;;AAGG;AACI,MAAM,mBAAmB,GAAG,YAAY;;ACV/C;;;;;AAKG;AACU,MAAA,0BAA0B,GAAG,IAAI,cAAc,CAC1D,SAAS,GAAG,4BAA4B,GAAG,EAAE;AAG/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BG;AACU,MAAA,yBAAyB,GAAG,IAAI,cAAc,CACzD,SAAS,GAAG,2BAA2B,GAAG,EAAE;AAG9C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6JG;MAIU,QAAQ,CAAA;AAEU,IAAA,MAAA;AAC6B,IAAA,eAAA;AACD,IAAA,cAAA;AAHzD,IAAA,WAAA,CAC6B,MAAc,EACe,eAA+B,EAChC,cAAsC,EAAA;QAFlE,IAAM,CAAA,MAAA,GAAN,MAAM;QACuB,IAAe,CAAA,eAAA,GAAf,eAAe;QAChB,IAAc,CAAA,cAAA,GAAd,cAAc;;AAmCvE,IAAA,SAAS,CACP,KAAgD,EAChD,MAAe,EACf,QAAiB,EACjB,MAAe,EAAA;QAEf,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK;AAAE,YAAA,OAAO,IAAI;AAEjE,QAAA,IAAI;YACF,MAAM,OAAO,GAAG,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE,UAAU,IAAI,mBAAmB;AAChF,YAAA,MAAM,SAAS,GACb,QAAQ,IAAI,IAAI,CAAC,cAAc,EAAE,QAAQ,IAAI,IAAI,CAAC,eAAe,IAAI,SAAS;AAChF,YAAA,OAAO,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC;;QACnE,OAAO,KAAK,EAAE;YACd,MAAM,wBAAwB,CAAC,QAAQ,EAAG,KAAe,CAAC,OAAO,CAAC;;;AArD3D,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAQ,EAET,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,SAAS,EACT,EAAA,EAAA,KAAA,EAAA,0BAA0B,6BAC1B,yBAAyB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA;gHAJxB,QAAQ,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA;;sGAAR,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAHpB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,MAAM;AACb,iBAAA;;0BAGI,MAAM;2BAAC,SAAS;;0BAChB,MAAM;2BAAC,0BAA0B;;0BAAG;;0BACpC,MAAM;2BAAC,yBAAyB;;0BAAG;;;ACnNxC,MAAM,qBAAqB,GAAW,IAAI;AAE1C;;;;;;;;;;;;;AAaG;MAIU,cAAc,CAAA;AACL,IAAA,aAAA;AAApB,IAAA,WAAA,CAAoB,aAA6B,EAAA;QAA7B,IAAa,CAAA,aAAA,GAAb,aAAa;;AAEjC;;;;;;AAMG;AACH,IAAA,SAAS,CACP,KAAgC,EAChC,SAAoC,EACpC,MAAe,EAAA;QAEf,IAAI,KAAK,IAAI,IAAI;AAAE,YAAA,OAAO,EAAE;QAE5B,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,IAAI,EAAE;AACvD,YAAA,MAAM,wBAAwB,CAAC,cAAc,EAAE,SAAS,CAAC;;AAG3D,QAAA,MAAM,GAAG,GAAG,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC;AAExF,QAAA,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,qBAAqB,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC;;kHAvB7D,cAAc,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAH,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA;gHAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA;;sGAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAH1B,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,YAAY;AACnB,iBAAA;;;ACpBD;;;;;;;;;;;;;;;;AAgBG;MAIU,cAAc,CAAA;AACzB;;;;AAIG;IACH,SAAS,CAAC,KAAgC,EAAE,OAAgC,EAAA;QAC1E,IAAI,KAAK,IAAI,IAAI;AAAE,YAAA,OAAO,EAAE;QAE5B,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC5D,YAAA,MAAM,wBAAwB,CAAC,cAAc,EAAE,OAAO,CAAC;;AAGzD,QAAA,IAAI,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;AACjC,YAAA,OAAO,OAAO,CAAC,KAAK,CAAC;;AAGvB,QAAA,IAAI,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;AACnC,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC;;AAGzB,QAAA,OAAO,EAAE;;kHArBA,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA;gHAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA;;sGAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAH1B,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,YAAY;AACnB,iBAAA;;;ACrBD;;;;;;;;;;;;;;AAcG;MAKU,QAAQ,CAAA;AACnB;;AAEG;AACH,IAAA,SAAS,CAAC,KAAU,EAAA;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;;kHAL5B,QAAQ,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA;gHAAR,QAAQ,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,KAAA,EAAA,CAAA;;sGAAR,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAJpB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,IAAI,EAAE,KAAK;AACZ,iBAAA;;;ACXD,SAAS,gBAAgB,CAAO,GAAM,EAAE,KAAQ,EAAA;IAC9C,OAAO,EAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAC;AACjC;AAaA;;;;;;;;;;;;;;;;;;;;AAoBG;MAKU,YAAY,CAAA;AACM,IAAA,OAAA;AAA7B,IAAA,WAAA,CAA6B,OAAwB,EAAA;QAAxB,IAAO,CAAA,OAAA,GAAP,OAAO;;AAE5B,IAAA,MAAM;IACN,SAAS,GAA8B,EAAE;IACzC,SAAS,GACf,iBAAiB;AAmCnB,IAAA,SAAS,CACP,KAAkF,EAClF,SAAA,GAAuE,iBAAiB,EAAA;AAExF,QAAA,IAAI,CAAC,KAAK,KAAK,EAAE,KAAK,YAAY,GAAG,CAAC,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC,EAAE;AACpE,YAAA,OAAO,IAAI;;;AAIb,QAAA,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE;QAEjD,MAAM,aAAa,GAAiC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAY,CAAC;AAClF,QAAA,MAAM,gBAAgB,GAAG,SAAS,KAAK,IAAI,CAAC,SAAS;QAErD,IAAI,aAAa,EAAE;AACjB,YAAA,IAAI,CAAC,SAAS,GAAG,EAAE;AACnB,YAAA,aAAa,CAAC,WAAW,CAAC,CAAC,CAA6B,KAAI;AAC1D,gBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,YAAa,CAAC,CAAC;AAC/D,aAAC,CAAC;;AAEJ,QAAA,IAAI,aAAa,IAAI,gBAAgB,EAAE;YACrC,IAAI,SAAS,EAAE;AACb,gBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;;AAEhC,YAAA,IAAI,CAAC,SAAS,GAAG,SAAS;;QAE5B,OAAO,IAAI,CAAC,SAAS;;kHAnEZ,YAAY,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,eAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA;gHAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EAAA,KAAA,EAAA,CAAA;;sGAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,UAAU;AAChB,oBAAA,IAAI,EAAE,KAAK;AACZ,iBAAA;;AAwEe,SAAA,iBAAiB,CAC/B,SAAyB,EACzB,SAAyB,EAAA;AAEzB,IAAA,MAAM,CAAC,GAAG,SAAS,CAAC,GAAG;AACvB,IAAA,MAAM,CAAC,GAAG,SAAS,CAAC,GAAG;;IAEvB,IAAI,CAAC,KAAK,CAAC;AAAE,QAAA,OAAO,CAAC;;IAErB,IAAI,CAAC,IAAI,IAAI;QAAE,OAAO,CAAC,CAAC;IACxB,IAAI,CAAC,IAAI,IAAI;AAAE,QAAA,OAAO,CAAC,CAAC,CAAC;;IAEzB,IAAI,OAAO,CAAC,IAAI,QAAQ,IAAI,OAAO,CAAC,IAAI,QAAQ,EAAE;AAChD,QAAA,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;;;IAGvB,IAAI,OAAO,CAAC,IAAI,QAAQ,IAAI,OAAO,CAAC,IAAI,QAAQ,EAAE;QAChD,OAAO,CAAC,GAAG,CAAC;;;IAGd,IAAI,OAAO,CAAC,IAAI,SAAS,IAAI,OAAO,CAAC,IAAI,SAAS,EAAE;AAClD,QAAA,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;;;AAGvB,IAAA,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC;AACzB,IAAA,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC;;IAEzB,OAAO,OAAO,IAAI,OAAO,GAAG,CAAC,GAAG,OAAO,GAAG,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC;AAC5D;;ACrIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DG;MAIU,WAAW,CAAA;AACiB,IAAA,OAAA;AAAvC,IAAA,WAAA,CAAuC,OAAe,EAAA;QAAf,IAAO,CAAA,OAAA,GAAP,OAAO;;AAgB9C,IAAA,SAAS,CACP,KAAyC,EACzC,UAAmB,EACnB,MAAe,EAAA;AAEf,QAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AAAE,YAAA,OAAO,IAAI;AAEhC,QAAA,MAAM,KAAK,IAAI,CAAC,OAAO;AAEvB,QAAA,IAAI;AACF,YAAA,MAAM,GAAG,GAAG,WAAW,CAAC,KAAK,CAAC;YAC9B,OAAO,YAAY,CAAC,GAAG,EAAE,MAAM,EAAE,UAAU,CAAC;;QAC5C,OAAO,KAAK,EAAE;YACd,MAAM,wBAAwB,CAAC,WAAW,EAAG,KAAe,CAAC,OAAO,CAAC;;;AA9B9D,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,kBACF,SAAS,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA;gHADlB,WAAW,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,QAAA,EAAA,CAAA;;sGAAX,WAAW,EAAA,UAAA,EAAA,CAAA;kBAHvB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,QAAQ;AACf,iBAAA;;0BAEc,MAAM;2BAAC,SAAS;;AAkC/B;;;;;;;;;;;;;;;;;;;AAmBG;MAIU,WAAW,CAAA;AACiB,IAAA,OAAA;AAAvC,IAAA,WAAA,CAAuC,OAAe,EAAA;QAAf,IAAO,CAAA,OAAA,GAAP,OAAO;;AAS9C;;;;;;;;;;;;;;;AAeG;AACH,IAAA,SAAS,CACP,KAAyC,EACzC,UAAmB,EACnB,MAAe,EAAA;AAEf,QAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AAAE,YAAA,OAAO,IAAI;AAChC,QAAA,MAAM,KAAK,IAAI,CAAC,OAAO;AACvB,QAAA,IAAI;AACF,YAAA,MAAM,GAAG,GAAG,WAAW,CAAC,KAAK,CAAC;YAC9B,OAAO,aAAa,CAAC,GAAG,EAAE,MAAM,EAAE,UAAU,CAAC;;QAC7C,OAAO,KAAK,EAAE;YACd,MAAM,wBAAwB,CAAC,WAAW,EAAG,KAAe,CAAC,OAAO,CAAC;;;AArC9D,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,kBACF,SAAS,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA;gHADlB,WAAW,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA;;sGAAX,WAAW,EAAA,UAAA,EAAA,CAAA;kBAHvB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,SAAS;AAChB,iBAAA;;0BAEc,MAAM;2BAAC,SAAS;;AAyC/B;;;;;;;;;;;;;;;;;;;;AAoBG;MAIU,YAAY,CAAA;AAEM,IAAA,OAAA;AACY,IAAA,oBAAA;IAFzC,WAC6B,CAAA,OAAe,EACH,oBAAA,GAA+B,KAAK,EAAA;QADhD,IAAO,CAAA,OAAA,GAAP,OAAO;QACK,IAAoB,CAAA,oBAAA,GAApB,oBAAoB;;AAwD7D,IAAA,SAAS,CACP,KAAyC,EACzC,YAAA,GAAuB,IAAI,CAAC,oBAAoB,EAChD,OAAkE,GAAA,QAAQ,EAC1E,UAAmB,EACnB,MAAe,EAAA;AAEf,QAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AAAE,YAAA,OAAO,IAAI;AAEhC,QAAA,MAAM,KAAK,IAAI,CAAC,OAAO;AAEvB,QAAA,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE;AAChC,YAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,gBAAA,OAAO,CAAC,IAAI,CACV,CAAA,wMAAA,CAA0M,CAC3M;;YAEH,OAAO,GAAG,OAAO,GAAG,QAAQ,GAAG,MAAM;;AAGvC,QAAA,IAAI,QAAQ,GAAW,YAAY,IAAI,IAAI,CAAC,oBAAoB;AAChE,QAAA,IAAI,OAAO,KAAK,MAAM,EAAE;YACtB,IAAI,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,eAAe,EAAE;AACvD,gBAAA,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,EAAE,OAAO,KAAK,QAAQ,GAAG,MAAM,GAAG,QAAQ,EAAE,MAAM,CAAC;;iBACnF;gBACL,QAAQ,GAAG,OAAO;;;AAItB,QAAA,IAAI;AACF,YAAA,MAAM,GAAG,GAAG,WAAW,CAAC,KAAK,CAAC;AAC9B,YAAA,OAAO,cAAc,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,CAAC;;QACtE,OAAO,KAAK,EAAE;YACd,MAAM,wBAAwB,CAAC,YAAY,EAAG,KAAe,CAAC,OAAO,CAAC;;;kHA5F/D,YAAY,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAEb,SAAS,EAAA,EAAA,EAAA,KAAA,EACT,qBAAqB,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA;gHAHpB,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,UAAA,EAAA,CAAA;;sGAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAHxB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,UAAU;AACjB,iBAAA;;0BAGI,MAAM;2BAAC,SAAS;;0BAChB,MAAM;2BAAC,qBAAqB;;AA8FjC,SAAS,OAAO,CAAC,KAAyC,EAAA;AACxD,IAAA,OAAO,EAAE,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,CAAC;AAC5D;AAEA;;AAEG;AACH,SAAS,WAAW,CAAC,KAAsB,EAAA;;AAEzC,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;AAC1E,QAAA,OAAO,MAAM,CAAC,KAAK,CAAC;;AAEtB,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAM,IAAIH,aAAY,CAEpB,IAAA,4CAAA,SAAS,IAAI,CAAG,EAAA,KAAK,CAAkB,gBAAA,CAAA,CACxC;;AAEH,IAAA,OAAO,KAAK;AACd;;AC7TA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCG;MAKU,SAAS,CAAA;AAyBpB,IAAA,SAAS,CACP,KAAmD,EACnD,KAAa,EACb,GAAY,EAAA;QAEZ,IAAI,KAAK,IAAI,IAAI;AAAE,YAAA,OAAO,IAAI;AAE9B,QAAA,MAAM,QAAQ,GAAG,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QAElE,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,MAAM,wBAAwB,CAAC,SAAS,EAAE,KAAK,CAAC;;QAGlD,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC;;kHAtCrB,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA;gHAAT,SAAS,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,EAAA,KAAA,EAAA,CAAA;;sGAAT,SAAS,EAAA,UAAA,EAAA,CAAA;kBAJrB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,OAAO;AACb,oBAAA,IAAI,EAAE,KAAK;AACZ,iBAAA;;;AC1CD;;;;AAIG;AAgCH;;AAEG;AACI,MAAM,YAAY,GAAG;IAC1B,SAAS;IACT,aAAa;IACb,aAAa;IACb,QAAQ;IACR,SAAS;IACT,WAAW;IACX,WAAW;IACX,aAAa;IACb,YAAY;IACZ,QAAQ;IACR,cAAc;IACd,cAAc;IACd,YAAY;CACb;;AChDD;AACA;AACA;;;;;;;AAOG;MAKU,YAAY,CAAA;kHAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;mHAAZ,YAAY,EAAA,OAAA,EAAA,CAAAO,OAAA,EAAAC,iBAAA,EAAAC,OAAA,EAAAC,IAAA,EAAAC,gBAAA,EAAAC,OAAA,EAAAC,QAAA,EAAAC,YAAA,EAAAC,eAAA,EAAAC,QAAA,EAAAC,YAAA,EAAAC,SAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,QAAA,EAAAC,SAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,aAAA,EAAAC,YAAA,EAAAC,QAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,YAAA,CAAA,EAAA,OAAA,EAAA,CAAAvB,OAAA,EAAAC,iBAAA,EAAAC,OAAA,EAAAC,IAAA,EAAAC,gBAAA,EAAAC,OAAA,EAAAC,QAAA,EAAAC,YAAA,EAAAC,eAAA,EAAAC,QAAA,EAAAC,YAAA,EAAAC,SAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,QAAA,EAAAC,SAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,aAAA,EAAAC,YAAA,EAAAC,QAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,YAAA,CAAA,EAAA,CAAA;mHAAZ,YAAY,EAAA,CAAA;;sGAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,iBAAiB,EAAE,YAAY,CAAC;AAC1C,oBAAA,OAAO,EAAE,CAAC,iBAAiB,EAAE,YAAY,CAAC;AAC3C,iBAAA;;;;;"}