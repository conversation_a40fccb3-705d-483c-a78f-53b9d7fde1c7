{"version": 3, "file": "cy.js", "sourceRoot": "", "sources": ["cy.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,WAAW,EAAC,aAAa,EAAC,cAAc,EAAC,UAAU,EAAC,aAAa,EAAC,aAAa,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,WAAW,EAAC,aAAa,EAAC,cAAc,EAAC,UAAU,EAAC,aAAa,EAAC,aAAa,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,OAAO,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,QAAQ,EAAC,KAAK,EAAC,SAAS,EAAC,YAAY,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,UAAU,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,QAAQ,EAAC,KAAK,EAAC,SAAS,EAAC,YAAY,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,UAAU,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,WAAW,EAAC,WAAW,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,cAAc,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,cAAc,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 0)\n    return 0;\nif (n === 1)\n    return 1;\nif (n === 2)\n    return 2;\nif (n === 3)\n    return 3;\nif (n === 6)\n    return 4;\nreturn 5;\n}\n\nexport default [\"cy\",[[\"b\",\"h\"],[\"AM\",\"PM\"],[\"yb\",\"yh\"]],[[\"AM\",\"PM\"],u,u],[[\"S\",\"Ll\",\"M\",\"M\",\"I\",\"G\",\"S\"],[\"Sul\",\"Llun\",\"Maw\",\"Mer\",\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\"],[\"<PERSON>yd<PERSON>\",\"<PERSON>yd<PERSON>lun\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON>yd<PERSON>\"],[\"<PERSON>\",\"<PERSON>l\",\"<PERSON>\",\"<PERSON>\",\"Ia\",\"Gw\",\"<PERSON>\"]],[[\"S\",\"Ll\",\"<PERSON>\",\"M\",\"I\",\"G\",\"S\"],[\"Sul\",\"Llun\",\"Maw\",\"Mer\",\"Iau\",\"Gwe\",\"Sad\"],[\"Dydd Sul\",\"Dydd Llun\",\"<PERSON>ydd Mawrth\",\"Dydd Mercher\",\"Dydd Iau\",\"Dydd Gwener\",\"Dydd Sadwrn\"],[\"Su\",\"Ll\",\"Ma\",\"Me\",\"Ia\",\"Gw\",\"Sa\"]],[[\"I\",\"Ch\",\"M\",\"E\",\"M\",\"M\",\"G\",\"A\",\"M\",\"H\",\"T\",\"Rh\"],[\"Ion\",\"Chwef\",\"Maw\",\"Ebr\",\"Mai\",\"Meh\",\"Gorff\",\"Awst\",\"Medi\",\"Hyd\",\"Tach\",\"Rhag\"],[\"Ionawr\",\"Chwefror\",\"Mawrth\",\"Ebrill\",\"Mai\",\"Mehefin\",\"Gorffennaf\",\"Awst\",\"Medi\",\"Hydref\",\"Tachwedd\",\"Rhagfyr\"]],[[\"I\",\"Ch\",\"M\",\"E\",\"M\",\"M\",\"G\",\"A\",\"M\",\"H\",\"T\",\"Rh\"],[\"Ion\",\"Chw\",\"Maw\",\"Ebr\",\"Mai\",\"Meh\",\"Gor\",\"Awst\",\"Medi\",\"Hyd\",\"Tach\",\"Rhag\"],[\"Ionawr\",\"Chwefror\",\"Mawrth\",\"Ebrill\",\"Mai\",\"Mehefin\",\"Gorffennaf\",\"Awst\",\"Medi\",\"Hydref\",\"Tachwedd\",\"Rhagfyr\"]],[[\"C\",\"O\"],[\"CC\",\"OC\"],[\"Cyn Crist\",\"Oed Crist\"]],1,[6,0],[\"dd/MM/yy\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,\"{1} 'am' {0}\",u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"GBP\",\"£\",\"Punt Prydain\",{\"BDT\":[u,\"TK\"],\"BWP\":[],\"BYN\":[u,\"р.\"],\"HKD\":[\"HK$\"],\"JPY\":[\"JP¥\",\"¥\"],\"KRW\":[u,\"₩\"],\"PHP\":[u,\"₱\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"],\"USD\":[\"US$\",\"$\"],\"XXX\":[],\"ZAR\":[],\"ZMW\":[]},\"ltr\", plural];\n"]}