import { Injectable } from '@angular/core';
import { MatSnackBar, MatSnackBarConfig } from '@angular/material/snack-bar';
import { ToastrService } from 'ngx-toastr';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  
  constructor(
    private snackBar: MatSnackBar,
    private toastr: ToastrService
  ) {}

  /**
   * Affiche une notification de succès
   */
  showSuccess(message: string, title?: string, duration: number = 5000): void {
    if (title) {
      this.toastr.success(message, title, {
        timeOut: duration,
        progressBar: true,
        closeButton: true
      });
    } else {
      this.showSnackBar(message, 'success', duration);
    }
  }

  /**
   * Affiche une notification d'erreur
   */
  showError(message: string, title?: string, duration: number = 8000): void {
    if (title) {
      this.toastr.error(message, title, {
        timeOut: duration,
        progressBar: true,
        closeButton: true,
        disableTimeOut: false
      });
    } else {
      this.showSnackBar(message, 'error', duration);
    }
  }

  /**
   * Affiche une notification d'avertissement
   */
  showWarning(message: string, title?: string, duration: number = 6000): void {
    if (title) {
      this.toastr.warning(message, title, {
        timeOut: duration,
        progressBar: true,
        closeButton: true
      });
    } else {
      this.showSnackBar(message, 'warning', duration);
    }
  }

  /**
   * Affiche une notification d'information
   */
  showInfo(message: string, title?: string, duration: number = 5000): void {
    if (title) {
      this.toastr.info(message, title, {
        timeOut: duration,
        progressBar: true,
        closeButton: true
      });
    } else {
      this.showSnackBar(message, 'info', duration);
    }
  }

  /**
   * Affiche une notification avec action
   */
  showWithAction(
    message: string, 
    actionText: string, 
    action: () => void, 
    type: 'success' | 'error' | 'warning' | 'info' = 'info',
    duration: number = 8000
  ): void {
    const config: MatSnackBarConfig = {
      duration,
      panelClass: [`snackbar-${type}`],
      horizontalPosition: 'right',
      verticalPosition: 'top'
    };

    const snackBarRef = this.snackBar.open(message, actionText, config);
    
    snackBarRef.onAction().subscribe(() => {
      action();
    });
  }

  /**
   * Affiche une notification de chargement
   */
  showLoading(message: string = 'Chargement en cours...'): void {
    this.showSnackBar(message, 'info', 0); // Duration 0 = pas de fermeture automatique
  }

  /**
   * Ferme toutes les notifications
   */
  dismissAll(): void {
    this.snackBar.dismiss();
    this.toastr.clear();
  }

  /**
   * Affiche une notification de confirmation
   */
  showConfirmation(
    message: string,
    confirmText: string = 'Confirmer',
    cancelText: string = 'Annuler'
  ): Promise<boolean> {
    return new Promise((resolve) => {
      const config: MatSnackBarConfig = {
        duration: 10000,
        panelClass: ['snackbar-confirmation'],
        horizontalPosition: 'center',
        verticalPosition: 'top'
      };

      const snackBarRef = this.snackBar.open(
        message, 
        `${confirmText} | ${cancelText}`, 
        config
      );

      snackBarRef.onAction().subscribe(() => {
        // Simuler une logique de confirmation simple
        // Dans une vraie application, vous pourriez utiliser un dialog
        resolve(true);
      });

      snackBarRef.afterDismissed().subscribe(() => {
        resolve(false);
      });
    });
  }

  /**
   * Affiche une SnackBar Material Design
   */
  private showSnackBar(
    message: string, 
    type: 'success' | 'error' | 'warning' | 'info', 
    duration: number
  ): void {
    const config: MatSnackBarConfig = {
      duration: duration || 5000,
      panelClass: [`snackbar-${type}`],
      horizontalPosition: 'right',
      verticalPosition: 'top'
    };

    this.snackBar.open(message, 'Fermer', config);
  }

  /**
   * Affiche une notification de succès pour une opération de fichier
   */
  showFileSuccess(operation: string, fileName: string): void {
    this.showSuccess(`${operation} réussi pour "${fileName}"`, 'Fichier traité');
  }

  /**
   * Affiche une notification d'erreur pour une opération de fichier
   */
  showFileError(operation: string, fileName: string, error: string): void {
    this.showError(`${operation} échoué pour "${fileName}": ${error}`, 'Erreur de fichier');
  }

  /**
   * Affiche une notification pour une opération API
   */
  showApiSuccess(operation: string): void {
    this.showSuccess(`${operation} terminé avec succès`, 'Opération réussie');
  }

  /**
   * Affiche une notification d'erreur API
   */
  showApiError(operation: string, error: string): void {
    this.showError(`${operation} échoué: ${error}`, 'Erreur API');
  }
}
