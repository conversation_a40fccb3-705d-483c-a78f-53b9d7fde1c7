{"version": 3, "file": "pcm.js", "sourceRoot": "", "sources": ["pcm.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAE7C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QAClB,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,WAAW,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,WAAW,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,UAAU,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,WAAW,EAAC,QAAQ,EAAC,UAAU,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,UAAU,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,WAAW,EAAC,QAAQ,EAAC,UAAU,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,cAAc,EAAC,eAAe,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,WAAW,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,cAAc,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,gBAAgB,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, i = Math.floor(Math.abs(val));\n\nif (i === 0 || n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"pcm\",[[\"AM\",\"PM\"],u,[\"<PERSON><PERSON> mọ́nin\",\"<PERSON><PERSON> ívnin\"]],u,[[\"S\",\"M\",\"T\",\"W\",\"T\",\"F\",\"S\"],[\"<PERSON>ọ́n\",\"<PERSON><PERSON>́<PERSON>\",\"<PERSON>i<PERSON>\",\"Wẹ́n\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON>a<PERSON>\",\"<PERSON><PERSON><PERSON>\"],[\"Sọ́nd<PERSON>\",\"<PERSON><PERSON><PERSON>nd<PERSON>\",\"<PERSON>i<PERSON><PERSON><PERSON><PERSON>\",\"Wẹ́nẹ́zdè\",\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"],[\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>n\",\"<PERSON><PERSON><PERSON>\",\"Wẹ́n\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\"]],u,[[\"J\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"S\",\"O\",\"N\",\"D\"],[\"<PERSON><PERSON>\",\"Fẹ́b\",\"<PERSON>h\",\"<PERSON>pr\",\"<PERSON>e\",\"<PERSON>\",\"<PERSON>\",\"Ọgọ\",\"Sẹp\",\"Ọkt\",\"N<PERSON>v\",\"<PERSON>s\"],[\"<PERSON><PERSON><PERSON><PERSON>ri\",\"Fẹ́búári\",\"<PERSON>h\",\"<PERSON>prel\",\"<PERSON>e\",\"<PERSON>\",\"Julai\",\"Ọgọst\",\"Sẹptẹ́mba\",\"Ọktóba\",\"Nọvẹ́mba\",\"Disẹ́mba\"]],[[\"J\",\"F\",\"M\",\"A\",\"M\",\"J\",\"J\",\"A\",\"S\",\"O\",\"N\",\"D\"],[\"Jén\",\"Fẹ́b\",\"Mach\",\"Épr\",\"Mee\",\"Jun\",\"Jul\",\"Ọ́gọ\",\"Sẹp\",\"Ọkt\",\"Nọv\",\"Dis\"],[\"Jénúári\",\"Fẹ́búári\",\"Mach\",\"Éprel\",\"Mee\",\"Jun\",\"Julai\",\"Ọgọst\",\"Sẹptẹ́mba\",\"Ọktóba\",\"Nọvẹ́mba\",\"Disẹ́mba\"]],[[\"BK\",\"KIY\"],u,[\"Bifọ́ Kraist\",\"Kraist Im Yiẹ\"]],1,[6,0],[\"dd/MM/y\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"H:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,\"{1} 'fọ' {0}\",u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"NGN\",\"₦\",\"Naijíriá Naíra\",{\"BYN\":[u,\"p.\"],\"CAD\":[\"KA$\",\"$\"],\"JPY\":[\"JP¥\",\"¥\"],\"NGN\":[\"₦\"],\"PHP\":[u,\"₱\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}