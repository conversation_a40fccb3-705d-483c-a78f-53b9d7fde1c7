import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-footer',
  standalone: true,
  imports: [CommonModule, MatIconModule, MatButtonModule],
  template: `
    <footer class="footer">
      <div class="footer-container">
        <div class="footer-content">
          <!-- Section principale -->
          <div class="footer-main">
            <div class="footer-brand">
              <mat-icon class="brand-icon">smart_toy</mat-icon>
              <div class="brand-text">
                <h3>Leoni Agents</h3>
                <p>Intelligence Artificielle de nouvelle génération</p>
              </div>
            </div>
            
            <div class="footer-description">
              <p>
                Révolutionnez votre workflow avec nos agents IA spécialisés 
                pour l'analyse d'erreurs et la génération SQL automatisée.
              </p>
            </div>
          </div>

          <!-- Liens rapides -->
          <div class="footer-links">
            <div class="link-group">
              <h4>Services</h4>
              <ul>
                <li><a href="/error-analysis">Analyse d'Erreurs</a></li>
                <li><a href="/sql-generation">Génération SQL</a></li>
              </ul>
            </div>
            
            <div class="link-group">
              <h4>Fonctionnalités</h4>
              <ul>
                <li>Analyse automatique</li>
                <li>Solutions intelligentes</li>
                <li>Support multi-langages</li>
                <li>Interface intuitive</li>
              </ul>
            </div>
            
            <div class="link-group">
              <h4>Statistiques</h4>
              <ul>
                <li>99.9% de précision</li>
                <li>&lt;2s de réponse</li>
                <li>24/7 disponible</li>
                <li>IA de pointe</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Barre de copyright -->
        <div class="footer-bottom">
          <div class="copyright">
            <p>&copy; {{ currentYear }} Leoni Agents. Tous droits réservés.</p>
          </div>
          
          <div class="tech-info">
            <span class="tech-badge">
              <mat-icon>code</mat-icon>
              Angular + Flask
            </span>
            <span class="tech-badge">
              <mat-icon>psychology</mat-icon>
              Powered by OpenAI
            </span>
          </div>
        </div>
      </div>
    </footer>
  `,
  styles: [`
    .footer {
      background: linear-gradient(135deg, #002857 0%, #001a3d 100%);
      color: white;
      margin-top: auto;
    }

    .footer-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 3rem 1rem 1rem;
    }

    .footer-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 3rem;
      margin-bottom: 2rem;
    }

    .footer-main {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }

    .footer-brand {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .brand-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      color: #ff7514;
      filter: drop-shadow(0 2px 8px rgba(255, 117, 20, 0.3));
    }

    .brand-text h3 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 700;
      background: linear-gradient(45deg, #ffffff, #ff7514);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .brand-text p {
      margin: 0;
      font-size: 0.9rem;
      opacity: 0.8;
      font-weight: 300;
    }

    .footer-description p {
      line-height: 1.6;
      opacity: 0.9;
      font-size: 0.95rem;
    }

    .footer-links {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 2rem;
    }

    .link-group h4 {
      margin: 0 0 1rem 0;
      font-size: 1.1rem;
      font-weight: 600;
      color: #ff7514;
    }

    .link-group ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .link-group li {
      margin-bottom: 0.5rem;
    }

    .link-group a {
      color: rgba(255, 255, 255, 0.8);
      text-decoration: none;
      font-size: 0.9rem;
      transition: color 0.3s ease;
    }

    .link-group a:hover {
      color: #ff7514;
    }

    .link-group li:not(:has(a)) {
      color: rgba(255, 255, 255, 0.7);
      font-size: 0.9rem;
    }

    .footer-bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 2rem;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .copyright p {
      margin: 0;
      font-size: 0.85rem;
      opacity: 0.7;
    }

    .tech-info {
      display: flex;
      gap: 1rem;
    }

    .tech-badge {
      display: flex;
      align-items: center;
      gap: 0.3rem;
      background: rgba(255, 117, 20, 0.1);
      padding: 0.3rem 0.8rem;
      border-radius: 20px;
      font-size: 0.8rem;
      border: 1px solid rgba(255, 117, 20, 0.3);
    }

    .tech-badge mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      color: #ff7514;
    }

    /* Responsive */
    @media (max-width: 768px) {
      .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
      }

      .footer-links {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .footer-bottom {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
      }

      .tech-info {
        justify-content: center;
      }
    }

    @media (max-width: 480px) {
      .footer-container {
        padding: 2rem 0.5rem 1rem;
      }

      .footer-brand {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
      }

      .brand-icon {
        font-size: 2.5rem;
        width: 2.5rem;
        height: 2.5rem;
      }

      .tech-info {
        flex-direction: column;
        gap: 0.5rem;
      }
    }
  `]
})
export class FooterComponent {
  currentYear = new Date().getFullYear();
}
