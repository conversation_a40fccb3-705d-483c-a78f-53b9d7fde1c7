{"version": 3, "file": "smn.js", "sourceRoot": "", "sources": ["smn.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,YAAY,EAAC,YAAY,EAAC,YAAY,EAAC,SAAS,EAAC,WAAW,EAAC,cAAc,EAAC,UAAU,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,WAAW,EAAC,WAAW,EAAC,WAAW,EAAC,UAAU,EAAC,WAAW,EAAC,aAAa,EAAC,UAAU,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,EAAC,OAAO,EAAC,MAAM,EAAC,QAAQ,EAAC,OAAO,EAAC,OAAO,EAAC,SAAS,EAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,cAAc,EAAC,YAAY,EAAC,aAAa,EAAC,aAAa,EAAC,YAAY,EAAC,WAAW,EAAC,aAAa,EAAC,YAAY,EAAC,YAAY,EAAC,cAAc,EAAC,aAAa,EAAC,aAAa,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,sBAAsB,EAAC,qBAAqB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,CAAC,MAAM,EAAC,SAAS,EAAC,WAAW,EAAC,cAAc,CAAC,EAAC,CAAC,SAAS,EAAC,eAAe,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,SAAS,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,MAAM,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nif (n === 2)\n    return 2;\nreturn 5;\n}\n\nexport default [\"smn\",[[\"ip.\",\"ep.\"],u,u],u,[[\"p\",\"V\",\"M\",\"K\",\"T\",\"V\",\"L\"],[\"pas\",\"vuo\",\"maj\",\"kos\",\"tuo\",\"vás\",\"láv\"],[\"pasepeeivi\",\"vuossaargâ\",\"majebaargâ\",\"koskoho\",\"tuorâstuv\",\"vástuppeeivi\",\"lávurduv\"],[\"pa\",\"vu\",\"ma\",\"ko\",\"tu\",\"vá\",\"lá\"]],[[\"S\",\"M\",\"T\",\"W\",\"T\",\"F\",\"S\"],[\"pas\",\"vuo\",\"maj\",\"kos\",\"tuo\",\"vás\",\"láv\"],[\"pasepeivi\",\"vuossargâ\",\"majebargâ\",\"koskokko\",\"tuorâstâh\",\"vástuppeivi\",\"lávurdâh\"],[\"pa\",\"vu\",\"ma\",\"ko\",\"tu\",\"vá\",\"lá\"]],[[\"U\",\"K\",\"NJ\",\"C\",\"V\",\"K\",\"S\",\"P\",\"Č\",\"R\",\"S\",\"J\"],[\"uđiv\",\"kuovâ\",\"njuhčâ\",\"cuáŋui\",\"vyesi\",\"kesi\",\"syeini\",\"porge\",\"čohčâ\",\"roovvâd\",\"skammâ\",\"juovlâ\"],[\"uđđâivemáánu\",\"kuovâmáánu\",\"njuhčâmáánu\",\"cuáŋuimáánu\",\"vyesimáánu\",\"kesimáánu\",\"syeinimáánu\",\"porgemáánu\",\"čohčâmáánu\",\"roovvâdmáánu\",\"skammâmáánu\",\"juovlâmáánu\"]],u,[[\"oKr.\",\"mKr.\"],u,[\"Ovdil Kristus šoddâm\",\"maŋa Kristus šoddâm\"]],1,[6,0],[\"d.M.y\",\"MMM d. y\",\"MMMM d. y\",\"cccc, MMMM d. y\"],[\"H.mm\",\"H.mm.ss\",\"H.mm.ss z\",\"H.mm.ss zzzz\"],[\"{1} {0}\",\"{1} 'tme' {0}\",u,u],[\",\",\" \",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"epiloho\",\".\"],[\"#,##0.###\",\"#,##0 %\",\"#,##0.00 ¤\",\"#E0\"],\"EUR\",\"€\",\"euro\",{\"JPY\":[\"JP¥\",\"¥\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}