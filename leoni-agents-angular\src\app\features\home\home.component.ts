import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { trigger, transition, style, animate, stagger, query } from '@angular/animations';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule
  ],
  template: `
    <div class="home-container">
      <!-- Hero Section -->
      <section class="hero-section" [@fadeInUp]>
        <div class="hero-content">
          <div class="hero-badge">
            <mat-icon>auto_awesome</mat-icon>
            <span>Nouvelle Génération d'IA</span>
            <mat-icon>rocket_launch</mat-icon>
          </div>
          
          <h1 class="hero-title">
            <span class="gradient-text">Leoni Agents</span>
            <br>
            <span class="subtitle">Intelligence Artificielle</span>
          </h1>
          
          <p class="hero-description">
            Révolutionnez votre workflow avec nos agents IA de nouvelle génération.
            <strong class="highlight-orange">Analyse d'erreurs intelligente</strong> et
            <strong class="highlight-blue">génération SQL automatisée</strong>
            pour une productivité sans limites.
          </p>
          
          <div class="hero-actions">
            <a mat-raised-button 
               color="primary" 
               routerLink="/error-analysis"
               class="cta-button primary">
              <mat-icon>bug_report</mat-icon>
              Analyser les Erreurs
              <mat-icon>arrow_forward</mat-icon>
            </a>
            
            <a mat-stroked-button 
               routerLink="/sql-generation"
               class="cta-button secondary">
              <mat-icon>storage</mat-icon>
              Générer du SQL
              <mat-icon>arrow_forward</mat-icon>
            </a>
          </div>
          
          <!-- Stats -->
          <div class="stats-grid" [@staggerIn]>
            <div class="stat-item" *ngFor="let stat of stats">
              <div class="stat-value" [style.color]="stat.color">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </div>
        </div>
      </section>

      <!-- Features Section -->
      <section class="features-section">
        <div class="section-header">
          <h2>Agents <span class="highlight-orange">Spécialisés</span></h2>
          <p>Découvrez nos agents IA de pointe, conçus pour transformer votre façon de travailler</p>
        </div>
        
        <div class="features-grid" [@staggerIn]>
          <mat-card class="feature-card error-analysis" *ngFor="let feature of features">
            <div class="card-header">
              <div class="card-icon" [style.background]="feature.iconBg">
                <mat-icon [style.color]="feature.iconColor">{{ feature.icon }}</mat-icon>
              </div>
              <div class="card-badge">{{ feature.badge }}</div>
            </div>
            
            <mat-card-header>
              <mat-card-title>{{ feature.title }}</mat-card-title>
              <mat-card-subtitle>{{ feature.subtitle }}</mat-card-subtitle>
            </mat-card-header>
            
            <mat-card-content>
              <ul class="feature-list">
                <li *ngFor="let item of feature.features">
                  <mat-icon [style.color]="feature.iconColor">fiber_manual_record</mat-icon>
                  {{ item }}
                </li>
              </ul>
            </mat-card-content>
            
            <mat-card-actions>
              <a mat-raised-button 
                 [routerLink]="feature.route"
                 [style.background]="feature.buttonBg"
                 class="feature-button">
                <mat-icon>{{ feature.buttonIcon }}</mat-icon>
                {{ feature.buttonText }}
                <mat-icon>arrow_forward</mat-icon>
              </a>
            </mat-card-actions>
          </mat-card>
        </div>
      </section>

      <!-- Technology Section -->
      <section class="tech-section">
        <div class="section-header">
          <h2>Technologies <span class="highlight-blue">Modernes</span></h2>
          <p>Stack technologique de pointe pour des performances optimales</p>
        </div>
        
        <div class="tech-grid">
          <mat-chip-set>
            <mat-chip *ngFor="let tech of technologies" [color]="tech.color">
              <mat-icon matChipAvatar>{{ tech.icon }}</mat-icon>
              {{ tech.name }}
            </mat-chip>
          </mat-chip-set>
        </div>
      </section>
    </div>
  `,
  styles: [`
    .home-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
    }

    /* Hero Section */
    .hero-section {
      text-align: center;
      padding: 4rem 0;
      background: linear-gradient(135deg, rgba(0, 40, 87, 0.05) 0%, rgba(255, 117, 20, 0.05) 100%);
      border-radius: 20px;
      margin-bottom: 4rem;
    }

    .hero-badge {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      background: linear-gradient(45deg, rgba(0, 40, 87, 0.1), rgba(255, 117, 20, 0.1));
      padding: 0.75rem 1.5rem;
      border-radius: 50px;
      border: 1px solid rgba(255, 117, 20, 0.2);
      margin-bottom: 2rem;
      animation: bounce 2s infinite;
    }

    .hero-title {
      font-size: 4rem;
      font-weight: 900;
      margin: 0 0 1.5rem 0;
      line-height: 1.1;
    }

    .gradient-text {
      background: linear-gradient(45deg, #002857, #003d7a, #ff7514);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .subtitle {
      color: #002857;
      font-size: 3rem;
      font-weight: 700;
    }

    .hero-description {
      font-size: 1.25rem;
      color: #666;
      max-width: 800px;
      margin: 0 auto 2.5rem;
      line-height: 1.6;
    }

    .highlight-orange {
      color: #ff7514;
      font-weight: 600;
    }

    .highlight-blue {
      color: #002857;
      font-weight: 600;
    }

    .hero-actions {
      display: flex;
      gap: 1rem;
      justify-content: center;
      margin-bottom: 3rem;
    }

    .cta-button {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 1rem 2rem;
      border-radius: 12px;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .cta-button.primary {
      background: linear-gradient(45deg, #ff7514, #ff8c42);
      color: white;
      box-shadow: 0 4px 15px rgba(255, 117, 20, 0.3);
    }

    .cta-button.primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(255, 117, 20, 0.4);
    }

    .cta-button.secondary {
      border: 2px solid #002857;
      color: #002857;
    }

    .cta-button.secondary:hover {
      background: #002857;
      color: white;
      transform: translateY(-2px);
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 2rem;
      max-width: 600px;
      margin: 0 auto;
    }

    .stat-item {
      text-align: center;
    }

    .stat-value {
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
    }

    .stat-label {
      color: #666;
      font-size: 0.9rem;
    }

    /* Features Section */
    .features-section {
      margin-bottom: 4rem;
    }

    .section-header {
      text-align: center;
      margin-bottom: 3rem;
    }

    .section-header h2 {
      font-size: 2.5rem;
      font-weight: 700;
      color: #002857;
      margin-bottom: 1rem;
    }

    .section-header p {
      font-size: 1.1rem;
      color: #666;
      max-width: 600px;
      margin: 0 auto;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 2rem;
    }

    .feature-card {
      border-radius: 16px;
      overflow: hidden;
      transition: all 0.3s ease;
      border: none;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem 1.5rem 0;
    }

    .card-icon {
      width: 60px;
      height: 60px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .card-icon mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
    }

    .card-badge {
      background: rgba(255, 117, 20, 0.1);
      color: #e6650f;
      padding: 0.3rem 0.8rem;
      border-radius: 20px;
      font-size: 0.75rem;
      font-weight: 600;
    }

    .feature-list {
      list-style: none;
      padding: 0;
      margin: 1rem 0;
    }

    .feature-list li {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 0.75rem;
      color: #555;
    }

    .feature-list mat-icon {
      font-size: 0.5rem;
      width: 0.5rem;
      height: 0.5rem;
    }

    .feature-button {
      width: 100%;
      color: white;
      border-radius: 12px;
      padding: 0.75rem;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
    }

    /* Technology Section */
    .tech-section {
      text-align: center;
      padding: 3rem 0;
    }

    .tech-grid {
      display: flex;
      justify-content: center;
    }

    /* Animations */
    @keyframes bounce {
      0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
      }
      40% {
        transform: translateY(-10px);
      }
      60% {
        transform: translateY(-5px);
      }
    }

    /* Responsive */
    @media (max-width: 768px) {
      .hero-title {
        font-size: 2.5rem;
      }

      .subtitle {
        font-size: 2rem;
      }

      .hero-actions {
        flex-direction: column;
        align-items: center;
      }

      .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .features-grid {
        grid-template-columns: 1fr;
      }
    }
  `],
  animations: [
    trigger('fadeInUp', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(30px)' }),
        animate('0.6s ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ])
    ]),
    trigger('staggerIn', [
      transition('* => *', [
        query(':enter', [
          style({ opacity: 0, transform: 'translateY(20px)' }),
          stagger(100, [
            animate('0.4s ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
          ])
        ], { optional: true })
      ])
    ])
  ]
})
export class HomeComponent implements OnInit {
  stats = [
    { value: '99.9%', label: 'Précision d\'analyse', color: '#ff7514' },
    { value: '<2s', label: 'Temps de réponse', color: '#002857' },
    { value: '24/7', label: 'Disponibilité', color: '#ff8c42' }
  ];

  features = [
    {
      title: 'Agent d\'Analyse d\'Erreurs',
      subtitle: 'IA avancée pour la détection et résolution intelligente des erreurs',
      icon: 'bug_report',
      iconColor: '#ffffff',
      iconBg: 'linear-gradient(45deg, #ff7514, #ff8c42)',
      badge: 'EXPERT',
      features: [
        'Analyse automatique des fichiers d\'erreur',
        'Localisation précise avec numéros de ligne',
        'Solutions détaillées et contexte de code',
        'Support multi-langages de programmation'
      ],
      route: '/error-analysis',
      buttonText: 'Commencer l\'Analyse',
      buttonIcon: 'psychology',
      buttonBg: 'linear-gradient(45deg, #ff7514, #ff8c42)'
    },
    {
      title: 'Agent de Génération SQL',
      subtitle: 'Création automatique de scripts SQL optimisés à partir de spécifications',
      icon: 'storage',
      iconColor: '#ffffff',
      iconBg: 'linear-gradient(45deg, #002857, #003d7a)',
      badge: 'INNOVANT',
      features: [
        'Génération automatique de scripts SQL',
        'Support multi-bases de données',
        'Upload de fichiers de spécification',
        'Optimisation et bonnes pratiques'
      ],
      route: '/sql-generation',
      buttonText: 'Générer du SQL',
      buttonIcon: 'memory',
      buttonBg: 'linear-gradient(45deg, #002857, #003d7a)'
    }
  ];

  technologies = [
    { name: 'Angular 17', icon: 'web', color: 'primary' },
    { name: 'Flask', icon: 'api', color: 'accent' },
    { name: 'OpenAI GPT-4', icon: 'psychology', color: 'warn' },
    { name: 'Material Design', icon: 'design_services', color: 'primary' },
    { name: 'TypeScript', icon: 'code', color: 'accent' }
  ];

  ngOnInit(): void {
    // Initialisation si nécessaire
  }
}
