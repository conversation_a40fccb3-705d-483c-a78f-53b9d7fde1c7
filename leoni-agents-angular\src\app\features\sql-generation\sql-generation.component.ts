import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTabsModule } from '@angular/material/tabs';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatExpansionModule } from '@angular/material/expansion';

import { ApiService } from '../../core/services/api.service';
import { FileService } from '../../core/services/file.service';
import { NotificationService } from '../../core/services/notification.service';
import { 
  SQLGenerationRequest, 
  SQLGenerationResult, 
  DatabaseType,
  LoadingState 
} from '../../core/models/types';

@Component({
  selector: 'app-sql-generation',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    MatTabsModule,
    MatChipsModule,
    MatDividerModule,
    MatTooltipModule,
    MatExpansionModule
  ],
  template: `
    <div class="sql-generation-container">
      <!-- Header -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-icon">
            <mat-icon>storage</mat-icon>
          </div>
          <div class="header-text">
            <h1>Agent de Génération SQL</h1>
            <p>Générez des scripts SQL optimisés à partir de vos spécifications fonctionnelles</p>
          </div>
        </div>
      </div>

      <!-- Formulaire de génération -->
      <mat-card class="generation-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>edit_note</mat-icon>
            Spécification Fonctionnelle
          </mat-card-title>
          <mat-card-subtitle>
            Décrivez vos besoins en base de données et obtenez un script SQL personnalisé
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <form [formGroup]="generationForm" (ngSubmit)="onGenerate()">
            <!-- Spécification -->
            <div class="specification-section">
              <mat-tab-group>
                <mat-tab label="Saisie Manuelle">
                  <div class="tab-content">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Spécification fonctionnelle</mat-label>
                      <textarea matInput 
                                formControlName="specification"
                                rows="12"
                                placeholder="Décrivez votre besoin en base de données...&#10;&#10;Exemple:&#10;Créer une base de données pour un système de gestion de bibliothèque avec:&#10;- Table des livres (titre, auteur, ISBN, date de publication)&#10;- Table des utilisateurs (nom, email, date d'inscription)&#10;- Table des emprunts (livre, utilisateur, date d'emprunt, date de retour)"></textarea>
                      <mat-hint>Décrivez en détail la structure de données souhaitée</mat-hint>
                    </mat-form-field>
                  </div>
                </mat-tab>
                
                <mat-tab label="Upload Fichier">
                  <div class="tab-content">
                    <div class="file-upload-area" 
                         (dragover)="onDragOver($event)" 
                         (dragleave)="onDragLeave($event)"
                         (drop)="onDrop($event)"
                         [class.drag-over]="isDragOver">
                      <input type="file" 
                             #specFileInput 
                             (change)="onFileSelected($event)"
                             accept=".txt,.md,.doc,.docx,.pdf"
                             style="display: none;">
                      
                      <div class="upload-content" (click)="specFileInput.click()">
                        <mat-icon class="upload-icon">description</mat-icon>
                        <p>Cliquez ou glissez votre fichier de spécification ici</p>
                        <small>Formats supportés: .txt, .md, .doc, .docx, .pdf</small>
                      </div>
                    </div>
                    
                    <div *ngIf="uploadedFile" class="file-info">
                      <mat-icon>description</mat-icon>
                      <span>{{ uploadedFile.name }}</span>
                      <button mat-icon-button (click)="clearFile()" type="button">
                        <mat-icon>close</mat-icon>
                      </button>
                    </div>
                  </div>
                </mat-tab>
              </mat-tab-group>
            </div>

            <!-- Configuration -->
            <div class="config-section">
              <h3>
                <mat-icon>settings</mat-icon>
                Configuration
              </h3>
              
              <div class="config-grid">
                <mat-form-field appearance="outline">
                  <mat-label>Type de base de données</mat-label>
                  <mat-select formControlName="databaseType">
                    <mat-option *ngFor="let db of databaseTypes" [value]="db.value">
                      <mat-icon>{{ db.icon }}</mat-icon>
                      {{ db.label }}
                    </mat-option>
                  </mat-select>
                  <mat-hint>Choisissez le SGBD cible</mat-hint>
                </mat-form-field>
                
                <div class="checkbox-section">
                  <mat-checkbox formControlName="includeComments">
                    Inclure des commentaires explicatifs
                  </mat-checkbox>
                  <mat-checkbox formControlName="optimizePerformance">
                    Optimiser pour les performances
                  </mat-checkbox>
                  <mat-checkbox formControlName="includeIndexes">
                    Générer les index recommandés
                  </mat-checkbox>
                </div>
              </div>
            </div>

            <!-- Bouton de génération -->
            <div class="action-section">
              <button mat-raised-button 
                      color="primary" 
                      type="submit"
                      [disabled]="!canGenerate() || loadingState.isLoading"
                      class="generate-button">
                <mat-spinner *ngIf="loadingState.isLoading" diameter="20"></mat-spinner>
                <mat-icon *ngIf="!loadingState.isLoading">auto_fix_high</mat-icon>
                {{ loadingState.isLoading ? 'Génération en cours...' : 'Générer le Script SQL' }}
              </button>
              
              <p class="help-text" *ngIf="!canGenerate()">
                <mat-icon>info</mat-icon>
                Veuillez fournir une spécification pour commencer la génération
              </p>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

      <!-- Résultats de la génération -->
      <mat-card *ngIf="generationResult" class="results-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>code</mat-icon>
            Script SQL Généré
          </mat-card-title>
          <mat-card-subtitle>
            Généré le {{ generationResult.timestamp | date:'dd/MM/yyyy à HH:mm' }}
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <!-- Script SQL -->
          <div class="sql-section">
            <div class="section-header">
              <h3>
                <mat-icon>data_object</mat-icon>
                Script SQL
              </h3>
              <div class="header-actions">
                <button mat-icon-button (click)="copySql()" matTooltip="Copier le SQL">
                  <mat-icon>content_copy</mat-icon>
                </button>
                <button mat-icon-button (click)="downloadSql()" matTooltip="Télécharger le SQL">
                  <mat-icon>download</mat-icon>
                </button>
              </div>
            </div>

            <div class="sql-code-block">
              <pre><code>{{ generationResult.sql }}</code></pre>
            </div>
          </div>

          <mat-divider></mat-divider>

          <!-- Explication -->
          <div class="explanation-section">
            <h3>
              <mat-icon>help_outline</mat-icon>
              Explication du Script
            </h3>
            <p class="explanation-text">{{ generationResult.explanation }}</p>
          </div>

          <mat-divider></mat-divider>

          <!-- Métadonnées -->
          <div class="metadata-section">
            <div class="metadata-grid">
              <!-- Tables -->
              <div class="metadata-item">
                <h4>
                  <mat-icon>table_view</mat-icon>
                  Tables Créées ({{ generationResult.tables.length }})
                </h4>
                <mat-chip-set>
                  <mat-chip *ngFor="let table of generationResult.tables" color="primary">
                    {{ table }}
                  </mat-chip>
                </mat-chip-set>
              </div>

              <!-- Opérations -->
              <div class="metadata-item">
                <h4>
                  <mat-icon>build</mat-icon>
                  Opérations SQL ({{ generationResult.operations.length }})
                </h4>
                <mat-chip-set>
                  <mat-chip *ngFor="let operation of generationResult.operations" color="accent">
                    {{ operation }}
                  </mat-chip>
                </mat-chip-set>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="actions-section">
            <button mat-raised-button color="primary" (click)="downloadSql()">
              <mat-icon>download</mat-icon>
              Télécharger le Script
            </button>

            <button mat-stroked-button (click)="copySql()">
              <mat-icon>content_copy</mat-icon>
              Copier le Script
            </button>

            <button mat-stroked-button (click)="resetGeneration()">
              <mat-icon>refresh</mat-icon>
              Nouvelle Génération
            </button>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Exemples et aide -->
      <mat-card class="help-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>help</mat-icon>
            Exemples et Conseils
          </mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <mat-accordion>
            <mat-expansion-panel>
              <mat-expansion-panel-header>
                <mat-panel-title>
                  <mat-icon>lightbulb</mat-icon>
                  Conseils pour une bonne spécification
                </mat-panel-title>
              </mat-expansion-panel-header>

              <ul class="tips-list">
                <li>Décrivez clairement les entités et leurs relations</li>
                <li>Spécifiez les types de données souhaités</li>
                <li>Mentionnez les contraintes importantes (clés primaires, uniques, etc.)</li>
                <li>Indiquez les index nécessaires pour les performances</li>
                <li>Précisez les données d'exemple si nécessaire</li>
              </ul>
            </mat-expansion-panel>

            <mat-expansion-panel>
              <mat-expansion-panel-header>
                <mat-panel-title>
                  <mat-icon>code</mat-icon>
                  Exemple de spécification
                </mat-panel-title>
              </mat-expansion-panel-header>

              <div class="example-spec">
                <pre>{{ exampleSpecification }}</pre>
              </div>
            </mat-expansion-panel>

            <mat-expansion-panel>
              <mat-expansion-panel-header>
                <mat-panel-title>
                  <mat-icon>storage</mat-icon>
                  Bases de données supportées
                </mat-panel-title>
              </mat-expansion-panel-header>

              <div class="db-support">
                <div *ngFor="let db of databaseTypes" class="db-item">
                  <mat-icon>{{ db.icon }}</mat-icon>
                  <div>
                    <strong>{{ db.label }}</strong>
                    <p>{{ db.description }}</p>
                  </div>
                </div>
              </div>
            </mat-expansion-panel>
          </mat-accordion>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .sql-generation-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem 1rem;
    }

    .page-header {
      background: linear-gradient(135deg, #002857 0%, #003d7a 100%);
      color: white;
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: 0 4px 20px rgba(0, 40, 87, 0.3);
    }

    .header-content {
      display: flex;
      align-items: center;
      gap: 1.5rem;
    }

    .header-icon {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .header-icon mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
    }

    .header-text h1 {
      margin: 0 0 0.5rem 0;
      font-size: 2.5rem;
      font-weight: 700;
    }

    .header-text p {
      margin: 0;
      opacity: 0.9;
      font-size: 1.1rem;
    }

    .generation-card, .results-card, .help-card {
      margin-bottom: 2rem;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .specification-section {
      margin-bottom: 2rem;
    }

    .tab-content {
      padding: 1rem 0;
    }

    .file-upload-area {
      border: 2px dashed #ddd;
      border-radius: 12px;
      padding: 2rem;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      background: #fafafa;
    }

    .file-upload-area:hover,
    .file-upload-area.drag-over {
      border-color: #002857;
      background: rgba(0, 40, 87, 0.05);
    }

    .upload-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.5rem;
    }

    .upload-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      color: #002857;
      margin-bottom: 0.5rem;
    }

    .file-info {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      background: #e8f5e8;
      padding: 0.75rem;
      border-radius: 8px;
      margin-top: 1rem;
    }

    .config-section h3 {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #002857;
      margin-bottom: 1rem;
    }

    .config-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
      align-items: start;
    }

    .checkbox-section {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .full-width {
      width: 100%;
    }

    .action-section {
      text-align: center;
      padding: 1rem 0;
    }

    .generate-button {
      padding: 1rem 2rem;
      font-size: 1.1rem;
      border-radius: 12px;
      min-width: 200px;
    }

    .help-text {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      color: #666;
      margin-top: 1rem;
    }

    .sql-section {
      margin-bottom: 2rem;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .section-header h3 {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #002857;
      margin: 0;
    }

    .header-actions {
      display: flex;
      gap: 0.5rem;
    }

    .sql-code-block {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      overflow-x: auto;
      max-height: 400px;
      overflow-y: auto;
    }

    .sql-code-block pre {
      margin: 0;
      padding: 1rem;
      font-family: 'Courier New', monospace;
      font-size: 0.9rem;
      line-height: 1.4;
      white-space: pre-wrap;
      word-wrap: break-word;
    }

    .explanation-section h3 {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #002857;
      margin: 1.5rem 0 1rem 0;
    }

    .explanation-text {
      background: #f0f8ff;
      padding: 1rem;
      border-radius: 8px;
      border-left: 4px solid #002857;
      font-size: 1rem;
      line-height: 1.6;
    }

    .metadata-section {
      margin: 2rem 0;
    }

    .metadata-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
    }

    .metadata-item h4 {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #333;
      margin-bottom: 1rem;
    }

    .actions-section {
      display: flex;
      gap: 1rem;
      justify-content: center;
      margin-top: 2rem;
      padding-top: 1rem;
      border-top: 1px solid #eee;
    }

    .tips-list {
      margin: 0;
      padding-left: 1.5rem;
    }

    .tips-list li {
      margin-bottom: 0.5rem;
      line-height: 1.5;
    }

    .example-spec {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 1rem;
      font-family: 'Courier New', monospace;
      font-size: 0.9rem;
      white-space: pre-wrap;
    }

    .db-support {
      display: grid;
      gap: 1rem;
    }

    .db-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 0.5rem;
      border-radius: 8px;
      background: #f8f9fa;
    }

    .db-item mat-icon {
      color: #002857;
    }

    .db-item strong {
      display: block;
      margin-bottom: 0.25rem;
    }

    .db-item p {
      margin: 0;
      font-size: 0.9rem;
      color: #666;
    }

    /* Responsive */
    @media (max-width: 768px) {
      .config-grid,
      .metadata-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .header-content {
        flex-direction: column;
        text-align: center;
      }

      .header-text h1 {
        font-size: 2rem;
      }

      .actions-section {
        flex-direction: column;
        align-items: center;
      }

      .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
      }
    }
  `]
})
export class SqlGenerationComponent implements OnInit {
  generationForm: FormGroup;
  generationResult: SQLGenerationResult | null = null;
  loadingState: LoadingState = { isLoading: false };
  uploadedFile: File | null = null;
  isDragOver = false;

  databaseTypes = [
    {
      value: DatabaseType.MYSQL,
      label: 'MySQL',
      icon: 'storage',
      description: 'Base de données relationnelle populaire et performante'
    },
    {
      value: DatabaseType.POSTGRESQL,
      label: 'PostgreSQL',
      icon: 'account_tree',
      description: 'Base de données avancée avec support des fonctionnalités modernes'
    },
    {
      value: DatabaseType.SQLITE,
      label: 'SQLite',
      icon: 'folder',
      description: 'Base de données légère et embarquée'
    },
    {
      value: DatabaseType.SQLSERVER,
      label: 'SQL Server',
      icon: 'business',
      description: 'Solution Microsoft pour les entreprises'
    }
  ];

  exampleSpecification = `Système de gestion de bibliothèque

Tables nécessaires:
1. Livres
   - ID unique (clé primaire)
   - Titre (obligatoire)
   - Auteur (obligatoire)
   - ISBN (unique)
   - Date de publication
   - Nombre de pages
   - Catégorie

2. Utilisateurs
   - ID unique (clé primaire)
   - Nom complet (obligatoire)
   - Email (unique, obligatoire)
   - Date d'inscription
   - Statut (actif/inactif)

3. Emprunts
   - ID unique (clé primaire)
   - ID livre (clé étrangère)
   - ID utilisateur (clé étrangère)
   - Date d'emprunt
   - Date de retour prévue
   - Date de retour effective
   - Statut (en cours/terminé)

Relations:
- Un livre peut être emprunté plusieurs fois
- Un utilisateur peut emprunter plusieurs livres
- Contraintes: un livre ne peut être emprunté que s'il est disponible`;

  constructor(
    private fb: FormBuilder,
    private apiService: ApiService,
    private fileService: FileService,
    private notificationService: NotificationService
  ) {
    this.generationForm = this.fb.group({
      specification: ['', [Validators.required, Validators.minLength(10)]],
      databaseType: [DatabaseType.MYSQL, Validators.required],
      includeComments: [true],
      optimizePerformance: [false],
      includeIndexes: [false]
    });
  }

  ngOnInit(): void {
    // Initialisation si nécessaire
  }

  // Gestion du drag & drop
  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      this.processFile(files[0]);
    }
  }

  // Sélection de fichier
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.processFile(file);
    }
  }

  // Traitement du fichier
  private processFile(file: File): void {
    this.fileService.readFile(file).subscribe({
      next: (result) => {
        if (result.success) {
          this.uploadedFile = file;
          this.generationForm.patchValue({
            specification: result.content
          });
          this.notificationService.showFileSuccess('Chargement', file.name);
        } else {
          this.notificationService.showFileError('Chargement', file.name, result.error || 'Erreur inconnue');
        }
      },
      error: (error) => {
        this.notificationService.showFileError('Chargement', file.name, error.message);
      }
    });
  }

  // Effacer le fichier
  clearFile(): void {
    this.uploadedFile = null;
    this.generationForm.patchValue({
      specification: ''
    });
  }

  // Vérifier si la génération peut être lancée
  canGenerate(): boolean {
    return this.generationForm.valid &&
           this.generationForm.get('specification')?.value?.trim().length > 0;
  }

  // Lancer la génération
  onGenerate(): void {
    if (!this.canGenerate()) {
      this.notificationService.showWarning('Veuillez fournir une spécification valide');
      return;
    }

    this.loadingState = { isLoading: true, message: 'Génération en cours...' };

    const formValues = this.generationForm.value;
    const request: SQLGenerationRequest = {
      specification: formValues.specification.trim(),
      databaseType: formValues.databaseType,
      includeComments: formValues.includeComments
    };

    this.apiService.generateSQL(request).subscribe({
      next: (result) => {
        this.generationResult = result;
        this.loadingState = { isLoading: false };
        this.notificationService.showApiSuccess('Génération SQL');

        // Scroll vers les résultats
        setTimeout(() => {
          const resultsElement = document.querySelector('.results-card');
          if (resultsElement) {
            resultsElement.scrollIntoView({ behavior: 'smooth' });
          }
        }, 100);
      },
      error: (error) => {
        this.loadingState = { isLoading: false };
        this.notificationService.showApiError('Génération SQL', error.message);
      }
    });
  }

  // Copier le SQL
  async copySql(): Promise<void> {
    if (!this.generationResult) return;

    try {
      await this.fileService.copyToClipboard(this.generationResult.sql);
      this.notificationService.showSuccess('Script SQL copié dans le presse-papiers');
    } catch (error) {
      this.notificationService.showError('Erreur lors de la copie du script');
    }
  }

  // Télécharger le SQL
  downloadSql(): void {
    if (!this.generationResult) return;

    const formValues = this.generationForm.value;
    const dbType = formValues.databaseType || 'mysql';
    const fileName = `script-${dbType}-${new Date().toISOString().split('T')[0]}.sql`;

    this.fileService.downloadFile(this.generationResult.sql, fileName, 'text/sql');
    this.notificationService.showSuccess('Script SQL téléchargé avec succès');
  }

  // Réinitialiser la génération
  resetGeneration(): void {
    this.generationResult = null;
    this.uploadedFile = null;
    this.generationForm.reset({
      specification: '',
      databaseType: DatabaseType.MYSQL,
      includeComments: true,
      optimizePerformance: false,
      includeIndexes: false
    });
    this.loadingState = { isLoading: false };

    // Scroll vers le haut
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }
}
