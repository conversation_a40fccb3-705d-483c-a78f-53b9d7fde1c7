{"version": 3, "file": "bn.js", "sourceRoot": "", "sources": ["bn.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAE7C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QAClB,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,OAAO,EAAC,KAAK,EAAC,UAAU,EAAC,OAAO,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,aAAa,EAAC,UAAU,EAAC,QAAQ,CAAC,EAAC,CAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,CAAC,EAAC,CAAC,MAAM,EAAC,KAAK,EAAC,OAAO,EAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,YAAY,EAAC,SAAS,EAAC,SAAS,EAAC,UAAU,CAAC,EAAC,CAAC,WAAW,EAAC,aAAa,EAAC,OAAO,EAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,YAAY,EAAC,SAAS,EAAC,SAAS,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,CAAC,EAAC,CAAC,WAAW,EAAC,aAAa,EAAC,OAAO,EAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,YAAY,EAAC,SAAS,EAAC,SAAS,EAAC,UAAU,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,cAAc,EAAC,WAAW,CAAC,EAAC,CAAC,EAAC,CAAC,cAAc,EAAC,aAAa,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,cAAc,EAAC,WAAW,EAAC,cAAc,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,gBAAgB,EAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, i = Math.floor(Math.abs(val));\n\nif (i === 0 || n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"bn\",[[\"AM\",\"PM\"],u,u],u,[[\"র\",\"সো\",\"ম\",\"বু\",\"বৃ\",\"শু\",\"শ\"],[\"রবি\",\"সোম\",\"মঙ্গল\",\"বুধ\",\"বৃহস্পতি\",\"শুক্র\",\"শনি\"],[\"রবিবার\",\"সোমবার\",\"মঙ্গলবার\",\"বুধবার\",\"বৃহস্পতিবার\",\"শুক্রবার\",\"শনিবার\"],[\"রঃ\",\"সোঃ\",\"মঃ\",\"বুঃ\",\"বৃঃ\",\"শুঃ\",\"শনি\"]],u,[[\"জা\",\"ফে\",\"মা\",\"এ\",\"মে\",\"জুন\",\"জু\",\"আ\",\"সে\",\"অ\",\"ন\",\"ডি\"],[\"জানু\",\"ফেব\",\"মার্চ\",\"এপ্রিল\",\"মে\",\"জুন\",\"জুলাই\",\"আগস্ট\",\"সেপ্টেম্বর\",\"অক্টোবর\",\"নভেম্বর\",\"ডিসেম্বর\"],[\"জানুয়ারী\",\"ফেব্রুয়ারী\",\"মার্চ\",\"এপ্রিল\",\"মে\",\"জুন\",\"জুলাই\",\"আগস্ট\",\"সেপ্টেম্বর\",\"অক্টোবর\",\"নভেম্বর\",\"ডিসেম্বর\"]],[[\"জা\",\"ফে\",\"মা\",\"এ\",\"মে\",\"জুন\",\"জু\",\"আ\",\"সে\",\"অ\",\"ন\",\"ডি\"],[\"জানুয়ারী\",\"ফেব্রুয়ারী\",\"মার্চ\",\"এপ্রিল\",\"মে\",\"জুন\",\"জুলাই\",\"আগস্ট\",\"সেপ্টেম্বর\",\"অক্টোবর\",\"নভেম্বর\",\"ডিসেম্বর\"],u],[[\"খ্রিস্টপূর্ব\",\"খৃষ্টাব্দ\"],u,[\"খ্রিস্টপূর্ব\",\"খ্রীষ্টাব্দ\"]],0,[6,0],[\"d/M/yy\",\"d MMM, y\",\"d MMMM, y\",\"EEEE, d MMMM, y\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##,##0.###\",\"#,##,##0%\",\"#,##,##0.00¤\",\"#E0\"],\"BDT\",\"৳\",\"বাংলাদেশী টাকা\",{\"BDT\":[\"৳\"],\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"],\"PHP\":[u,\"₱\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}