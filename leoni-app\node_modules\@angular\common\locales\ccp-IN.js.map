{"version": 3, "file": "ccp-IN.js", "sourceRoot": "", "sources": ["ccp-IN.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,QAAQ,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,UAAU,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,UAAU,EAAC,UAAU,EAAC,gBAAgB,EAAC,UAAU,EAAC,kBAAkB,EAAC,kBAAkB,EAAC,UAAU,CAAC,EAAC,CAAC,gBAAgB,EAAC,gBAAgB,EAAC,sBAAsB,EAAC,gBAAgB,EAAC,wBAAwB,EAAC,wBAAwB,EAAC,gBAAgB,CAAC,EAAC,CAAC,UAAU,EAAC,UAAU,EAAC,gBAAgB,EAAC,UAAU,EAAC,kBAAkB,EAAC,kBAAkB,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,MAAM,EAAC,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,UAAU,EAAC,MAAM,EAAC,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,YAAY,EAAC,kBAAkB,EAAC,MAAM,EAAC,UAAU,EAAC,UAAU,EAAC,gBAAgB,EAAC,0BAA0B,EAAC,sBAAsB,EAAC,sBAAsB,EAAC,oBAAoB,CAAC,EAAC,CAAC,cAAc,EAAC,wBAAwB,EAAC,YAAY,EAAC,kBAAkB,EAAC,MAAM,EAAC,UAAU,EAAC,UAAU,EAAC,gBAAgB,EAAC,0BAA0B,EAAC,sBAAsB,EAAC,sBAAsB,EAAC,sBAAsB,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,MAAM,EAAC,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,UAAU,EAAC,MAAM,EAAC,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,cAAc,EAAC,wBAAwB,EAAC,YAAY,EAAC,kBAAkB,EAAC,MAAM,EAAC,UAAU,EAAC,UAAU,EAAC,gBAAgB,EAAC,0BAA0B,EAAC,sBAAsB,EAAC,sBAAsB,EAAC,sBAAsB,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,8BAA8B,EAAC,wBAAwB,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,cAAc,EAAC,WAAW,EAAC,cAAc,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,6BAA6B,EAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"ccp-IN\",[[\"AM\",\"PM\"],u,u],u,[[\"𑄢𑄧\",\"𑄥𑄧\",\"𑄟𑄧\",\"𑄝𑄪\",\"𑄝𑄳𑄢𑄨\",\"𑄥𑄪\",\"𑄥𑄧\"],[\"𑄢𑄧𑄝𑄨\",\"𑄥𑄧𑄟𑄴\",\"𑄟𑄧𑄁𑄉𑄧𑄣𑄴\",\"𑄝𑄪𑄖𑄴\",\"𑄝𑄳𑄢𑄨𑄥𑄪𑄛𑄴\",\"𑄥𑄪𑄇𑄴𑄇𑄮𑄢𑄴\",\"𑄥𑄧𑄚𑄨\"],[\"𑄢𑄧𑄝𑄨𑄝𑄢𑄴\",\"𑄥𑄧𑄟𑄴𑄝𑄢𑄴\",\"𑄟𑄧𑄁𑄉𑄧𑄣𑄴𑄝𑄢𑄴\",\"𑄝𑄪𑄖𑄴𑄝𑄢𑄴\",\"𑄝𑄳𑄢𑄨𑄥𑄪𑄛𑄴𑄝𑄢𑄴\",\"𑄥𑄪𑄇𑄴𑄇𑄮𑄢𑄴𑄝𑄢𑄴\",\"𑄥𑄧𑄚𑄨𑄝𑄢𑄴\"],[\"𑄢𑄧𑄝𑄨\",\"𑄥𑄧𑄟𑄴\",\"𑄟𑄧𑄁𑄉𑄧𑄣𑄴\",\"𑄝𑄪𑄖𑄴\",\"𑄝𑄳𑄢𑄨𑄥𑄪𑄛𑄴\",\"𑄥𑄪𑄇𑄴𑄇𑄮𑄢𑄴\",\"𑄥𑄧𑄚𑄨\"]],u,[[\"𑄎\",\"𑄜𑄬\",\"𑄟\",\"𑄃𑄬\",\"𑄟𑄬\",\"𑄎𑄪𑄚𑄴\",\"𑄎𑄪\",\"𑄃\",\"𑄥𑄬\",\"𑄃𑄧\",\"𑄚𑄧\",\"𑄓𑄨\"],[\"𑄎𑄚𑄪\",\"𑄜𑄬𑄛𑄴\",\"𑄟𑄢𑄴𑄌𑄧\",\"𑄃𑄬𑄛𑄳𑄢𑄨𑄣𑄴\",\"𑄟𑄬\",\"𑄎𑄪𑄚𑄴\",\"𑄎𑄪𑄣𑄭\",\"𑄃𑄉𑄧𑄌𑄴𑄑𑄴\",\"𑄥𑄬𑄛𑄴𑄑𑄬𑄟𑄴𑄝𑄧𑄢𑄴\",\"𑄃𑄧𑄇𑄴𑄑𑄮𑄝𑄧𑄢𑄴\",\"𑄚𑄧𑄞𑄬𑄟𑄴𑄝𑄧𑄢𑄴\",\"𑄓𑄨𑄥𑄬𑄟𑄴𑄝𑄢𑄴\"],[\"𑄎𑄚𑄪𑄠𑄢𑄨\",\"𑄜𑄬𑄛𑄴𑄝𑄳𑄢𑄪𑄠𑄢𑄨\",\"𑄟𑄢𑄴𑄌𑄧\",\"𑄃𑄬𑄛𑄳𑄢𑄨𑄣𑄴\",\"𑄟𑄬\",\"𑄎𑄪𑄚𑄴\",\"𑄎𑄪𑄣𑄭\",\"𑄃𑄉𑄧𑄌𑄴𑄑𑄴\",\"𑄥𑄬𑄛𑄴𑄑𑄬𑄟𑄴𑄝𑄧𑄢𑄴\",\"𑄃𑄧𑄇𑄴𑄑𑄬𑄝𑄧𑄢𑄴\",\"𑄚𑄧𑄞𑄬𑄟𑄴𑄝𑄧𑄢𑄴\",\"𑄓𑄨𑄥𑄬𑄟𑄴𑄝𑄧𑄢𑄴\"]],[[\"𑄎\",\"𑄜𑄬\",\"𑄟\",\"𑄃𑄬\",\"𑄟𑄬\",\"𑄎𑄪𑄚𑄴\",\"𑄎𑄪\",\"𑄃\",\"𑄥𑄬\",\"𑄃𑄧\",\"𑄚𑄧\",\"𑄓𑄨\"],[\"𑄎𑄚𑄪𑄠𑄢𑄨\",\"𑄜𑄬𑄛𑄴𑄝𑄳𑄢𑄪𑄠𑄢𑄨\",\"𑄟𑄢𑄴𑄌𑄧\",\"𑄃𑄬𑄛𑄳𑄢𑄨𑄣𑄴\",\"𑄟𑄬\",\"𑄎𑄪𑄚𑄴\",\"𑄎𑄪𑄣𑄭\",\"𑄃𑄉𑄧𑄌𑄴𑄑𑄴\",\"𑄥𑄬𑄛𑄴𑄑𑄬𑄟𑄴𑄝𑄧𑄢𑄴\",\"𑄃𑄧𑄇𑄴𑄑𑄮𑄝𑄧𑄢𑄴\",\"𑄚𑄧𑄞𑄬𑄟𑄴𑄝𑄧𑄢𑄴\",\"𑄓𑄨𑄥𑄬𑄟𑄴𑄝𑄧𑄢𑄴\"],u],[[\"𑄈𑄳𑄢𑄨𑄌𑄴𑄑𑄴𑄛𑄫𑄢𑄴𑄝𑄧\",\"𑄈𑄳𑄢𑄨𑄌𑄴𑄑𑄛𑄴𑄘𑄧\"],u,u],0,[0,0],[\"d/M/yy\",\"d MMM, y\",\"d MMMM, y\",\"EEEE, d MMMM, y\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##,##0.###\",\"#,##,##0%\",\"#,##,##0.00¤\",\"#E0\"],\"INR\",\"₹\",\"𑄃𑄨𑄚𑄴𑄘𑄨𑄠𑄚𑄴 𑄢𑄪𑄛𑄨\",{\"BDT\":[\"৳\"],\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"],\"PHP\":[u,\"₱\"],\"STD\":[u,\"Db\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}