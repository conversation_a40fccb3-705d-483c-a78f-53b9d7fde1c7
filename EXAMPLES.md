# 📚 Exemples d'Utilisation - Leoni Agents

Ce document présente des exemples concrets d'utilisation des agents I<PERSON> de Leon<PERSON>.

## 🐛 Agent d'Analyse d'Erreurs

### Exemple 1: Erreur de Syntaxe C

**Fichier Programme (main.c):**
```c
#include <stdio.h>

int main() {
    int x = 10
    printf("La valeur de x est: %d\n", x);
    return 0;
}
```

**Fichier d'Erreur (error.log):**
```
main.c:4:15: error: expected ';' before 'printf'
    int x = 10
               ^
               ;
    printf("La valeur de x est: %d\n", x);
    ~~~~~~
```

**Résultat de l'Analyse:**
- **Type d'erreur**: Erreur de syntaxe
- **Localisation**: Ligne 4, caractère 15
- **Cause**: Point-virgule manquant après la déclaration de variable
- **Solution**: Ajouter `;` après `int x = 10`

### Exemple 2: Erreur de Logique

**Fichier Programme (calcul.c):**
```c
#include <stdio.h>

int diviser(int a, int b) {
    return a / b;
}

int main() {
    int resultat = diviser(10, 0);
    printf("Résultat: %d\n", resultat);
    return 0;
}
```

**Fichier d'Erreur (runtime.log):**
```
Floating point exception (core dumped)
Program terminated with signal SIGFPE
Division by zero at line 4 in function diviser()
```

**Résultat de l'Analyse:**
- **Type d'erreur**: Division par zéro
- **Sévérité**: CRITICAL
- **Solutions**: 
  1. Ajouter une vérification `if (b != 0)` avant la division
  2. Retourner une valeur d'erreur ou lever une exception
  3. Utiliser une valeur par défaut

## 🗄️ Agent de Génération SQL

### Exemple 1: Système de Gestion de Bibliothèque

**Spécification:**
```
Créer une base de données pour un système de gestion de bibliothèque avec:

1. Table des livres:
   - ID unique (clé primaire)
   - Titre (obligatoire, max 200 caractères)
   - Auteur (obligatoire, max 100 caractères)
   - ISBN (unique, 13 caractères)
   - Date de publication
   - Nombre de pages
   - Catégorie (Fiction, Science, Histoire, etc.)

2. Table des utilisateurs:
   - ID unique (clé primaire)
   - Nom complet (obligatoire)
   - Email (unique, obligatoire)
   - Date d'inscription
   - Statut (actif/inactif)

3. Table des emprunts:
   - ID unique (clé primaire)
   - ID livre (clé étrangère vers livres)
   - ID utilisateur (clé étrangère vers utilisateurs)
   - Date d'emprunt
   - Date de retour prévue
   - Date de retour effective (nullable)
   - Statut (en_cours/terminé/en_retard)

Relations:
- Un livre peut être emprunté plusieurs fois (historique)
- Un utilisateur peut emprunter plusieurs livres
- Contrainte: un livre ne peut être emprunté que s'il n'est pas déjà en cours d'emprunt
```

**Script SQL Généré (MySQL):**
```sql
-- Base de données de gestion de bibliothèque
-- Généré le 2024-01-15

-- Table des livres
CREATE TABLE livres (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(200) NOT NULL,
    auteur VARCHAR(100) NOT NULL,
    isbn CHAR(13) UNIQUE NOT NULL,
    date_publication DATE,
    nombre_pages INT,
    categorie ENUM('Fiction', 'Science', 'Histoire', 'Biographie', 'Technique') DEFAULT 'Fiction',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_auteur (auteur),
    INDEX idx_categorie (categorie)
) ENGINE=InnoDB;

-- Table des utilisateurs
CREATE TABLE utilisateurs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom_complet VARCHAR(150) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    date_inscription DATE DEFAULT (CURRENT_DATE),
    statut ENUM('actif', 'inactif') DEFAULT 'actif',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_statut (statut)
) ENGINE=InnoDB;

-- Table des emprunts
CREATE TABLE emprunts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    livre_id INT NOT NULL,
    utilisateur_id INT NOT NULL,
    date_emprunt DATE DEFAULT (CURRENT_DATE),
    date_retour_prevue DATE NOT NULL,
    date_retour_effective DATE NULL,
    statut ENUM('en_cours', 'terminé', 'en_retard') DEFAULT 'en_cours',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (livre_id) REFERENCES livres(id) ON DELETE RESTRICT,
    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id) ON DELETE RESTRICT,
    
    INDEX idx_livre_statut (livre_id, statut),
    INDEX idx_utilisateur (utilisateur_id),
    INDEX idx_dates (date_emprunt, date_retour_prevue)
) ENGINE=InnoDB;

-- Vue pour les livres disponibles
CREATE VIEW livres_disponibles AS
SELECT l.*
FROM livres l
WHERE l.id NOT IN (
    SELECT e.livre_id 
    FROM emprunts e 
    WHERE e.statut = 'en_cours'
);

-- Procédure pour emprunter un livre
DELIMITER //
CREATE PROCEDURE EmprunterLivre(
    IN p_livre_id INT,
    IN p_utilisateur_id INT,
    IN p_duree_jours INT DEFAULT 14
)
BEGIN
    DECLARE livre_disponible INT DEFAULT 0;
    DECLARE utilisateur_actif INT DEFAULT 0;
    
    -- Vérifier si le livre est disponible
    SELECT COUNT(*) INTO livre_disponible
    FROM livres_disponibles
    WHERE id = p_livre_id;
    
    -- Vérifier si l'utilisateur est actif
    SELECT COUNT(*) INTO utilisateur_actif
    FROM utilisateurs
    WHERE id = p_utilisateur_id AND statut = 'actif';
    
    IF livre_disponible > 0 AND utilisateur_actif > 0 THEN
        INSERT INTO emprunts (livre_id, utilisateur_id, date_retour_prevue)
        VALUES (p_livre_id, p_utilisateur_id, DATE_ADD(CURRENT_DATE, INTERVAL p_duree_jours DAY));
        
        SELECT 'Emprunt enregistré avec succès' AS message;
    ELSE
        SELECT 'Emprunt impossible: livre non disponible ou utilisateur inactif' AS message;
    END IF;
END //
DELIMITER ;

-- Données d'exemple
INSERT INTO livres (titre, auteur, isbn, date_publication, nombre_pages, categorie) VALUES
('Le Petit Prince', 'Antoine de Saint-Exupéry', '9782070408504', '1943-04-06', 96, 'Fiction'),
('1984', 'George Orwell', '9780451524935', '1949-06-08', 328, 'Fiction'),
('Une brève histoire du temps', 'Stephen Hawking', '9780553380163', '1988-04-01', 256, 'Science');

INSERT INTO utilisateurs (nom_complet, email) VALUES
('Jean Dupont', '<EMAIL>'),
('Marie Martin', '<EMAIL>'),
('Pierre Durand', '<EMAIL>');
```

### Exemple 2: E-commerce Simple

**Spécification:**
```
Base de données e-commerce basique:
- Produits (nom, prix, stock, catégorie)
- Clients (nom, email, adresse)
- Commandes (client, date, statut)
- Détails commandes (produit, quantité, prix unitaire)
```

**Script SQL Généré (PostgreSQL):**
```sql
-- Base de données e-commerce
-- PostgreSQL

-- Table des catégories
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des produits
CREATE TABLE produits (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(200) NOT NULL,
    description TEXT,
    prix DECIMAL(10,2) NOT NULL CHECK (prix > 0),
    stock INTEGER NOT NULL DEFAULT 0 CHECK (stock >= 0),
    categorie_id INTEGER REFERENCES categories(id),
    actif BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des clients
CREATE TABLE clients (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(150) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    adresse TEXT,
    telephone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des commandes
CREATE TABLE commandes (
    id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL REFERENCES clients(id),
    date_commande TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    statut VARCHAR(20) DEFAULT 'en_attente' 
        CHECK (statut IN ('en_attente', 'confirmée', 'expédiée', 'livrée', 'annulée')),
    total DECIMAL(10,2) DEFAULT 0,
    adresse_livraison TEXT
);

-- Table des détails de commande
CREATE TABLE details_commande (
    id SERIAL PRIMARY KEY,
    commande_id INTEGER NOT NULL REFERENCES commandes(id) ON DELETE CASCADE,
    produit_id INTEGER NOT NULL REFERENCES produits(id),
    quantite INTEGER NOT NULL CHECK (quantite > 0),
    prix_unitaire DECIMAL(10,2) NOT NULL,
    UNIQUE(commande_id, produit_id)
);

-- Index pour les performances
CREATE INDEX idx_produits_categorie ON produits(categorie_id);
CREATE INDEX idx_produits_actif ON produits(actif);
CREATE INDEX idx_commandes_client ON commandes(client_id);
CREATE INDEX idx_commandes_date ON commandes(date_commande);
CREATE INDEX idx_details_commande ON details_commande(commande_id);

-- Fonction pour calculer le total d'une commande
CREATE OR REPLACE FUNCTION calculer_total_commande(commande_id_param INTEGER)
RETURNS DECIMAL(10,2) AS $$
DECLARE
    total DECIMAL(10,2);
BEGIN
    SELECT COALESCE(SUM(quantite * prix_unitaire), 0)
    INTO total
    FROM details_commande
    WHERE commande_id = commande_id_param;
    
    RETURN total;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour mettre à jour le total automatiquement
CREATE OR REPLACE FUNCTION update_commande_total()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE commandes 
    SET total = calculer_total_commande(
        CASE 
            WHEN TG_OP = 'DELETE' THEN OLD.commande_id
            ELSE NEW.commande_id
        END
    )
    WHERE id = CASE 
        WHEN TG_OP = 'DELETE' THEN OLD.commande_id
        ELSE NEW.commande_id
    END;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_total
    AFTER INSERT OR UPDATE OR DELETE ON details_commande
    FOR EACH ROW EXECUTE FUNCTION update_commande_total();
```

## 🔧 Conseils d'Utilisation

### Pour l'Analyse d'Erreurs
1. **Fichiers clairs**: Utilisez des noms de fichiers explicites
2. **Logs complets**: Incluez tous les messages d'erreur
3. **Contexte**: Ajoutez des commentaires dans le code si nécessaire
4. **Format**: Respectez les formats de logs standards

### Pour la Génération SQL
1. **Spécifications détaillées**: Plus c'est précis, mieux c'est
2. **Relations claires**: Décrivez les liens entre les tables
3. **Contraintes**: Mentionnez les règles métier importantes
4. **Performance**: Indiquez les besoins de performance si spécifiques

### Bonnes Pratiques
- Testez toujours les scripts générés
- Sauvegardez vos données avant modification
- Utilisez des environnements de test
- Documentez vos modifications
