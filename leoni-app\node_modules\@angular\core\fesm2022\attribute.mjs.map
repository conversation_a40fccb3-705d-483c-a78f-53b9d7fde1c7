{"version": 3, "file": "attribute.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/primitives/event-dispatch/src/attribute.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nexport const Attribute = {\n  /**\n   * The jsaction attribute defines a mapping of a DOM event to a\n   * generic event (aka jsaction), to which the actual event handlers\n   * that implement the behavior of the application are bound. The\n   * value is a semicolon separated list of colon separated pairs of\n   * an optional DOM event name and a jsaction name. If the optional\n   * DOM event name is omitted, 'click' is assumed. The jsaction names\n   * are dot separated pairs of a namespace and a simple jsaction\n   * name.\n   *\n   * See grammar in README.md for expected syntax in the attribute value.\n   */\n  JSACTION: 'jsaction' as const,\n};\n"], "names": [], "mappings": ";;;;;;AAQa,MAAA,SAAS,GAAG;AACvB;;;;;;;;;;;AAWG;AACH,IAAA,QAAQ,EAAE,UAAmB;;;;;"}