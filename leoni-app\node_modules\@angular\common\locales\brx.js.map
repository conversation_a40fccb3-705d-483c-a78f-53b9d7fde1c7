{"version": 3, "file": "brx.js", "sourceRoot": "", "sources": ["brx.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,KAAK,EAAC,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,MAAM,EAAC,KAAK,EAAC,QAAQ,EAAC,OAAO,EAAC,MAAM,CAAC,EAAC,CAAC,QAAQ,EAAC,OAAO,EAAC,SAAS,EAAC,QAAQ,EAAC,WAAW,EAAC,UAAU,EAAC,SAAS,CAAC,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,MAAM,EAAC,KAAK,EAAC,QAAQ,EAAC,OAAO,EAAC,MAAM,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,CAAC,EAAC,CAAC,UAAU,EAAC,YAAY,EAAC,OAAO,EAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,YAAY,EAAC,SAAS,EAAC,SAAS,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,UAAU,EAAC,WAAW,EAAC,gBAAgB,CAAC,EAAC,CAAC,WAAW,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,gBAAgB,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,cAAc,EAAC,WAAW,EAAC,eAAe,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,aAAa,EAAC,EAAC,KAAK,EAAC,CAAC,OAAO,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"brx\",[[\"फुं\",\"बेलासे\"],u,u],u,[[\"र\",\"स\",\"मं\",\"बु\",\"बि\",\"सु\",\"सु\"],[\"रबि\",\"सम\",\"मंगल\",\"बुध\",\"बिस्थि\",\"सुखुर\",\"सुनि\"],[\"रबिबार\",\"समबार\",\"मंगलबार\",\"बुधबार\",\"बिस्थिबार\",\"सुखुरबार\",\"सुनिबार\"],[\"रबि\",\"सम\",\"मंगल\",\"बुध\",\"बिस्थि\",\"सुखुर\",\"सुनि\"]],u,[[\"ज\",\"फ\",\"म\",\"ए\",\"म\",\"ज\",\"ज\",\"आ\",\"स\",\"अ\",\"न\",\"ड\"],[\"जान\",\"फेब\",\"मार्च\",\"एप्रि\",\"मे\",\"जुन\",\"जुल\",\"आग\",\"सेप\",\"अक्ट’\",\"नवे\",\"डिसे\"],[\"जानुवारी\",\"फेब्रूवारी\",\"मार्च\",\"एप्रिल\",\"मे\",\"जुन\",\"जुलाई\",\"आगष्ट\",\"सेप्थेम्बर\",\"अक्ट’बर\",\"नवेम्बर\",\"डिसेम्बर\"]],u,[[\"बि.सि.\",\"ए.दि\"],u,u],0,[0,0],[\"y-MM-dd\",\"MMM d, y\",\"MMMM d, y\",\"y MMMM d, EEEE\"],[\"a नि h:mm\",\"a h:mm:ss\",\"a h:mm:ss z\",\"a h:mm:ss zzzz\"],[\"{1}, {0}\",u,\"{1} नि {0} याव\",u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##,##0.###\",\"#,##,##0%\",\"¤ #,##,##0.00\",\"#E0\"],\"INR\",\"₹\",\"भारतनि रुपी\",{\"CNY\":[\"सिएन¥\",\"¥\"],\"JPY\":[\"JP¥\",\"¥\"],\"RUB\":[\"रूब\",\"₽\"]},\"ltr\", plural];\n"]}