{"version": 3, "file": "core.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/authoring/input/input_signal_node.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/authoring/input/input_signal.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/compiler/compiler_facade_interface.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/di/host_attribute_token.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/di/host_tag_name_token.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/authoring/output/output.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/authoring/input/input.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/authoring/queries.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/authoring/model/model_signal.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/authoring/model/model.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/metadata/di.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/version.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/application/application_ngmodule_factory_compiler.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/image_performance_warning.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/platform/platform_destroy_listeners.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/platform/bootstrap.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/platform/platform_ref.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/platform/platform.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/change_detection/scheduling/exhaustive_check_no_changes.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/change_detection/provide_check_no_changes_config.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/util/is_dev_mode.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/linker/ng_module_factory_loader.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/change_detection/change_detector_ref.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/linker/view_ref.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/change_detection/differs/default_iterable_differ.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/change_detection/differs/default_keyvalue_differ.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/change_detection/differs/iterable_differs.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/change_detection/differs/keyvalue_differs.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/change_detection/change_detection.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/platform/platform_core_providers.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/application/application_module.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/application/create_application.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/hydration/event_replay.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/hydration/annotate.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/hydration/api.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/util/coercion.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/profiler.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/internal/get_closest_component_name.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/type_checking.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/jit/partial.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/util/transfer_state_utils.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/reactivity/after_render_effect.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/component.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/application/application_config.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/application/platform_tokens.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {SIGNAL_NODE, SignalNode, signalSetFn} from '../../../primitives/signals';\n\nexport const REQUIRED_UNSET_VALUE: unique symbol = /* @__PURE__ */ Symbol('InputSignalNode#UNSET');\n\n/**\n * Reactive node type for an input signal. An input signal extends a signal.\n * There are special properties to enable transforms and required inputs.\n */\nexport interface InputSignalNode<T, TransformT> extends SignalNode<T> {\n  /**\n   * User-configured transform that will run whenever a new value is applied\n   * to the input signal node.\n   */\n  transformFn: ((value: TransformT) => T) | undefined;\n\n  /**\n   * Applies a new value to the input signal. Expects transforms to be run\n   * manually before.\n   *\n   * This function is called by the framework runtime code whenever a binding\n   * changes. The value can in practice be anything at runtime, but for typing\n   * purposes we assume it's a valid `T` value. Type-checking will enforce that.\n   */\n  applyValueToInputSignal<T, TransformT>(node: InputSignalNode<T, TransformT>, value: T): void;\n\n  /**\n   * A debug name for the input signal. Used in Angular DevTools to identify the signal.\n   */\n  debugName?: string;\n}\n\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nexport const INPUT_SIGNAL_NODE: InputSignalNode<unknown, unknown> = /* @__PURE__ */ (() => {\n  return {\n    ...SIGNAL_NODE,\n    transformFn: undefined,\n\n    applyValueToInputSignal<T, TransformT>(node: InputSignalNode<T, TransformT>, value: T) {\n      signalSetFn(node, value);\n    },\n  };\n})();\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {producerAccessed, SIGNAL} from '../../../primitives/signals';\n\nimport {RuntimeError, RuntimeErrorCode} from '../../errors';\nimport {Signal} from '../../render3/reactivity/api';\n\nimport {INPUT_SIGNAL_NODE, InputSignalNode, REQUIRED_UNSET_VALUE} from './input_signal_node';\n\n/**\n * @publicAPI\n *\n * Options for signal inputs.\n */\nexport interface InputOptions<T, TransformT> {\n  /** Optional public name for the input. By default, the class field name is used. */\n  alias?: string;\n  /**\n   * Optional transform that runs whenever a new value is bound. Can be used to\n   * transform the input value before the input is updated.\n   *\n   * The transform function can widen the type of the input. For example, consider\n   * an input for `disabled`. In practice, as the component author, you want to only\n   * deal with a boolean, but users may want to bind a string if they just use the\n   * attribute form to bind to the input via `<my-dir input>`. A transform can then\n   * handle such string values and convert them to `boolean`. See: {@link booleanAttribute}.\n   */\n  transform?: (v: TransformT) => T;\n\n  /**\n   * A debug name for the input signal. Used in Angular DevTools to identify the signal.\n   */\n  debugName?: string;\n}\n\n/**\n * Signal input options without the transform option.\n *\n * @publicApi 19.0\n */\nexport type InputOptionsWithoutTransform<T> =\n  // Note: We still keep a notion of `transform` for auto-completion.\n  Omit<InputOptions<T, T>, 'transform'> & {transform?: undefined};\n/**\n * Signal input options with the transform option required.\n *\n * @publicAPI\n */\nexport type InputOptionsWithTransform<T, TransformT> = Required<\n  Pick<InputOptions<T, TransformT>, 'transform'>\n> &\n  InputOptions<T, TransformT>;\n\nexport const ɵINPUT_SIGNAL_BRAND_READ_TYPE: unique symbol = /* @__PURE__ */ Symbol();\nexport const ɵINPUT_SIGNAL_BRAND_WRITE_TYPE: unique symbol = /* @__PURE__ */ Symbol();\n\n/**\n * `InputSignalWithTransform` represents a special `Signal` for a\n * directive/component input with a `transform` function.\n *\n * Signal inputs with transforms capture an extra generic for their transform write\n * type. Transforms can expand the accepted bound values for an input while ensuring\n * value retrievals of the signal input are still matching the generic input type.\n *\n * ```ts\n * class MyDir {\n *   disabled = input(false, {\n *     transform: (v: string|boolean) => convertToBoolean(v),\n *   }); // InputSignalWithTransform<boolean, string|boolean>\n *\n *   click() {\n *     this.disabled() // always returns a `boolean`.\n *   }\n * }\n * ```\n *\n * @see {@link InputSignal} for additional information.\n *\n * @publicApi 19.0\n */\nexport interface InputSignalWithTransform<T, TransformT> extends Signal<T> {\n  [SIGNAL]: InputSignalNode<T, TransformT>;\n  [ɵINPUT_SIGNAL_BRAND_READ_TYPE]: T;\n  [ɵINPUT_SIGNAL_BRAND_WRITE_TYPE]: TransformT;\n}\n\n/**\n * `InputSignal` represents a special `Signal` for a directive/component input.\n *\n * An input signal is similar to a non-writable signal except that it also\n * carries additional type-information for transforms, and that Angular internally\n * updates the signal whenever a new value is bound.\n *\n * @see {@link InputOptionsWithTransform} for inputs with transforms.\n *\n * @publicApi 19.0\n */\nexport interface InputSignal<T> extends InputSignalWithTransform<T, T> {}\n\n/**\n * Creates an input signal.\n *\n * @param initialValue The initial value.\n *   Can be set to {@link REQUIRED_UNSET_VALUE} for required inputs.\n * @param options Additional options for the input. e.g. a transform, or an alias.\n */\nexport function createInputSignal<T, TransformT>(\n  initialValue: T,\n  options?: InputOptions<T, TransformT>,\n): InputSignalWithTransform<T, TransformT> {\n  const node: InputSignalNode<T, TransformT> = Object.create(INPUT_SIGNAL_NODE);\n\n  node.value = initialValue;\n\n  // Perf note: Always set `transformFn` here to ensure that `node` always\n  // has the same v8 class shape, allowing monomorphic reads on input signals.\n  node.transformFn = options?.transform;\n\n  function inputValueFn() {\n    // Record that someone looked at this signal.\n    producerAccessed(node);\n\n    if (node.value === REQUIRED_UNSET_VALUE) {\n      let message: string | null = null;\n      if (ngDevMode) {\n        const name = options?.debugName ?? options?.alias;\n        message = `Input${name ? ` \"${name}\"` : ''} is required but no value is available yet.`;\n      }\n      throw new RuntimeError(RuntimeErrorCode.REQUIRED_INPUT_NO_VALUE, message);\n    }\n\n    return node.value;\n  }\n\n  (inputValueFn as any)[SIGNAL] = node;\n\n  if (ngDevMode) {\n    inputValueFn.toString = () => `[Input Signal: ${inputValueFn()}]`;\n    node.debugName = options?.debugName;\n  }\n\n  return inputValueFn as InputSignalWithTransform<T, TransformT>;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * A set of interfaces which are shared between `@angular/core` and `@angular/compiler` to allow\n * for late binding of `@angular/compiler` for JIT purposes.\n *\n * This file has two copies. Please ensure that they are in sync:\n *  - packages/compiler/src/compiler_facade_interface.ts          (main)\n *  - packages/core/src/compiler/compiler_facade_interface.ts     (replica)\n *\n * Please ensure that the two files are in sync using this command:\n * ```shell\n * cp packages/compiler/src/compiler_facade_interface.ts \\\n *    packages/core/src/compiler/compiler_facade_interface.ts\n * ```\n */\n\nexport interface ExportedCompilerFacade {\n  ɵcompilerFacade: CompilerFacade;\n}\n\nexport interface CompilerFacade {\n  compilePipe(\n    angularCoreEnv: CoreEnvironment,\n    sourceMapUrl: string,\n    meta: R3PipeMetadataFacade,\n  ): any;\n  compilePipeDeclaration(\n    angularCoreEnv: CoreEnvironment,\n    sourceMapUrl: string,\n    declaration: R3DeclarePipeFacade,\n  ): any;\n  compileInjectable(\n    angularCoreEnv: CoreEnvironment,\n    sourceMapUrl: string,\n    meta: R3InjectableMetadataFacade,\n  ): any;\n  compileInjectableDeclaration(\n    angularCoreEnv: CoreEnvironment,\n    sourceMapUrl: string,\n    meta: R3DeclareInjectableFacade,\n  ): any;\n  compileInjector(\n    angularCoreEnv: CoreEnvironment,\n    sourceMapUrl: string,\n    meta: R3InjectorMetadataFacade,\n  ): any;\n  compileInjectorDeclaration(\n    angularCoreEnv: CoreEnvironment,\n    sourceMapUrl: string,\n    declaration: R3DeclareInjectorFacade,\n  ): any;\n  compileNgModule(\n    angularCoreEnv: CoreEnvironment,\n    sourceMapUrl: string,\n    meta: R3NgModuleMetadataFacade,\n  ): any;\n  compileNgModuleDeclaration(\n    angularCoreEnv: CoreEnvironment,\n    sourceMapUrl: string,\n    declaration: R3DeclareNgModuleFacade,\n  ): any;\n  compileDirective(\n    angularCoreEnv: CoreEnvironment,\n    sourceMapUrl: string,\n    meta: R3DirectiveMetadataFacade,\n  ): any;\n  compileDirectiveDeclaration(\n    angularCoreEnv: CoreEnvironment,\n    sourceMapUrl: string,\n    declaration: R3DeclareDirectiveFacade,\n  ): any;\n  compileComponent(\n    angularCoreEnv: CoreEnvironment,\n    sourceMapUrl: string,\n    meta: R3ComponentMetadataFacade,\n  ): any;\n  compileComponentDeclaration(\n    angularCoreEnv: CoreEnvironment,\n    sourceMapUrl: string,\n    declaration: R3DeclareComponentFacade,\n  ): any;\n  compileFactory(\n    angularCoreEnv: CoreEnvironment,\n    sourceMapUrl: string,\n    meta: R3FactoryDefMetadataFacade,\n  ): any;\n  compileFactoryDeclaration(\n    angularCoreEnv: CoreEnvironment,\n    sourceMapUrl: string,\n    meta: R3DeclareFactoryFacade,\n  ): any;\n\n  createParseSourceSpan(kind: string, typeName: string, sourceUrl: string): ParseSourceSpan;\n\n  FactoryTarget: typeof FactoryTarget;\n  // Note that we do not use `{new(): ResourceLoader}` here because\n  // the resource loader class is abstract and not constructable.\n  ResourceLoader: Function & {prototype: ResourceLoader};\n}\n\nexport interface CoreEnvironment {\n  [name: string]: unknown;\n}\n\nexport type ResourceLoader = {\n  get(url: string): Promise<string> | string;\n};\n\nexport type Provider = unknown;\nexport type Type = Function;\nexport type OpaqueValue = unknown;\n\nexport enum FactoryTarget {\n  Directive = 0,\n  Component = 1,\n  Injectable = 2,\n  Pipe = 3,\n  NgModule = 4,\n}\n\nexport interface R3DependencyMetadataFacade {\n  token: OpaqueValue;\n  attribute: string | null;\n  host: boolean;\n  optional: boolean;\n  self: boolean;\n  skipSelf: boolean;\n}\n\nexport interface R3DeclareDependencyMetadataFacade {\n  token: OpaqueValue;\n  attribute?: boolean;\n  host?: boolean;\n  optional?: boolean;\n  self?: boolean;\n  skipSelf?: boolean;\n}\n\nexport interface R3PipeMetadataFacade {\n  name: string;\n  type: Type;\n  pipeName: string | null;\n  pure: boolean;\n  isStandalone: boolean;\n}\n\nexport interface R3InjectableMetadataFacade {\n  name: string;\n  type: Type;\n  typeArgumentCount: number;\n  providedIn?: Type | 'root' | 'platform' | 'any' | null;\n  useClass?: OpaqueValue;\n  useFactory?: OpaqueValue;\n  useExisting?: OpaqueValue;\n  useValue?: OpaqueValue;\n  deps?: R3DependencyMetadataFacade[];\n}\n\nexport interface R3NgModuleMetadataFacade {\n  type: Type;\n  bootstrap: Function[];\n  declarations: Function[];\n  imports: Function[];\n  exports: Function[];\n  schemas: {name: string}[] | null;\n  id: string | null;\n}\n\nexport interface R3InjectorMetadataFacade {\n  name: string;\n  type: Type;\n  providers: Provider[];\n  imports: OpaqueValue[];\n}\n\nexport interface R3HostDirectiveMetadataFacade {\n  directive: Type;\n  inputs?: string[];\n  outputs?: string[];\n}\n\nexport interface R3DirectiveMetadataFacade {\n  name: string;\n  type: Type;\n  typeSourceSpan: ParseSourceSpan;\n  selector: string | null;\n  queries: R3QueryMetadataFacade[];\n  host: {[key: string]: string};\n  propMetadata: {[key: string]: OpaqueValue[]};\n  lifecycle: {usesOnChanges: boolean};\n  inputs: (string | {name: string; alias?: string; required?: boolean})[];\n  outputs: string[];\n  usesInheritance: boolean;\n  exportAs: string[] | null;\n  providers: Provider[] | null;\n  viewQueries: R3QueryMetadataFacade[];\n  isStandalone: boolean;\n  isSignal: boolean;\n  hostDirectives: R3HostDirectiveMetadataFacade[] | null;\n}\n\nexport interface R3ComponentMetadataFacade extends R3DirectiveMetadataFacade {\n  template: string;\n  preserveWhitespaces: boolean;\n  animations: OpaqueValue[] | undefined;\n  declarations: R3TemplateDependencyFacade[];\n  styles: string[];\n  encapsulation: ViewEncapsulation;\n  viewProviders: Provider[] | null;\n  interpolation?: [string, string];\n  changeDetection?: ChangeDetectionStrategy;\n  hasDirectiveDependencies: boolean;\n}\n\n// TODO(legacy-partial-output-inputs): Remove in v18.\n// https://github.com/angular/angular/blob/d4b423690210872b5c32a322a6090beda30b05a3/packages/core/src/compiler/compiler_facade_interface.ts#L197-L199\nexport type LegacyInputPartialMapping =\n  | string\n  | [bindingPropertyName: string, classPropertyName: string, transformFunction?: Function];\n\nexport interface R3DeclareDirectiveFacade {\n  selector?: string;\n  type: Type;\n  version: string;\n  inputs?: {\n    [fieldName: string]:\n      | {\n          classPropertyName: string;\n          publicName: string;\n          isSignal: boolean;\n          isRequired: boolean;\n          transformFunction: Function | null;\n        }\n      | LegacyInputPartialMapping;\n  };\n  outputs?: {[classPropertyName: string]: string};\n  host?: {\n    attributes?: {[key: string]: OpaqueValue};\n    listeners?: {[key: string]: string};\n    properties?: {[key: string]: string};\n    classAttribute?: string;\n    styleAttribute?: string;\n  };\n  queries?: R3DeclareQueryMetadataFacade[];\n  viewQueries?: R3DeclareQueryMetadataFacade[];\n  providers?: OpaqueValue;\n  exportAs?: string[];\n  usesInheritance?: boolean;\n  usesOnChanges?: boolean;\n  isStandalone?: boolean;\n  hostDirectives?: R3HostDirectiveMetadataFacade[] | null;\n  isSignal?: boolean;\n}\n\nexport interface R3DeclareComponentFacade extends R3DeclareDirectiveFacade {\n  template: string;\n  isInline?: boolean;\n  styles?: string[];\n\n  // Post-standalone libraries use a unified dependencies field.\n  dependencies?: R3DeclareTemplateDependencyFacade[];\n\n  // Pre-standalone libraries have separate component/directive/pipe fields:\n  components?: R3DeclareDirectiveDependencyFacade[];\n  directives?: R3DeclareDirectiveDependencyFacade[];\n  pipes?: {[pipeName: string]: OpaqueValue | (() => OpaqueValue)};\n\n  deferBlockDependencies?: (() => Promise<Type> | null)[];\n  viewProviders?: OpaqueValue;\n  animations?: OpaqueValue;\n  changeDetection?: ChangeDetectionStrategy;\n  encapsulation?: ViewEncapsulation;\n  interpolation?: [string, string];\n  preserveWhitespaces?: boolean;\n}\n\nexport type R3DeclareTemplateDependencyFacade = {\n  kind: string;\n} & (\n  | R3DeclareDirectiveDependencyFacade\n  | R3DeclarePipeDependencyFacade\n  | R3DeclareNgModuleDependencyFacade\n);\n\nexport interface R3DeclareDirectiveDependencyFacade {\n  kind?: 'directive' | 'component';\n  selector: string;\n  type: OpaqueValue | (() => OpaqueValue);\n  inputs?: string[];\n  outputs?: string[];\n  exportAs?: string[];\n}\n\nexport interface R3DeclarePipeDependencyFacade {\n  kind?: 'pipe';\n  name: string;\n  type: OpaqueValue | (() => OpaqueValue);\n}\n\nexport interface R3DeclareNgModuleDependencyFacade {\n  kind: 'ngmodule';\n  type: OpaqueValue | (() => OpaqueValue);\n}\n\nexport enum R3TemplateDependencyKind {\n  Directive = 0,\n  Pipe = 1,\n  NgModule = 2,\n}\n\nexport interface R3TemplateDependencyFacade {\n  kind: R3TemplateDependencyKind;\n  type: OpaqueValue | (() => OpaqueValue);\n}\nexport interface R3FactoryDefMetadataFacade {\n  name: string;\n  type: Type;\n  typeArgumentCount: number;\n  deps: R3DependencyMetadataFacade[] | null;\n  target: FactoryTarget;\n}\n\nexport interface R3DeclareFactoryFacade {\n  type: Type;\n  deps: R3DeclareDependencyMetadataFacade[] | 'invalid' | null;\n  target: FactoryTarget;\n}\n\nexport interface R3DeclareInjectableFacade {\n  type: Type;\n  providedIn?: Type | 'root' | 'platform' | 'any' | null;\n  useClass?: OpaqueValue;\n  useFactory?: OpaqueValue;\n  useExisting?: OpaqueValue;\n  useValue?: OpaqueValue;\n  deps?: R3DeclareDependencyMetadataFacade[];\n}\n\nexport enum ViewEncapsulation {\n  Emulated = 0,\n  // Historically the 1 value was for `Native` encapsulation which has been removed as of v11.\n  None = 2,\n  ShadowDom = 3,\n}\n\nexport type ChangeDetectionStrategy = number;\n\nexport interface R3QueryMetadataFacade {\n  propertyName: string;\n  first: boolean;\n  predicate: OpaqueValue | string[];\n  descendants: boolean;\n  emitDistinctChangesOnly: boolean;\n  read: OpaqueValue | null;\n  static: boolean;\n  isSignal: boolean;\n}\n\nexport interface R3DeclareQueryMetadataFacade {\n  propertyName: string;\n  first?: boolean;\n  predicate: OpaqueValue | string[];\n  descendants?: boolean;\n  read?: OpaqueValue;\n  static?: boolean;\n  emitDistinctChangesOnly?: boolean;\n  isSignal?: boolean;\n}\n\nexport interface R3DeclareInjectorFacade {\n  type: Type;\n  imports?: OpaqueValue[];\n  providers?: OpaqueValue[];\n}\n\nexport interface R3DeclareNgModuleFacade {\n  type: Type;\n  bootstrap?: OpaqueValue[] | (() => OpaqueValue[]);\n  declarations?: OpaqueValue[] | (() => OpaqueValue[]);\n  imports?: OpaqueValue[] | (() => OpaqueValue[]);\n  exports?: OpaqueValue[] | (() => OpaqueValue[]);\n  schemas?: OpaqueValue[];\n  id?: OpaqueValue;\n}\n\nexport interface R3DeclarePipeFacade {\n  type: Type;\n  name: string;\n  version: string;\n  pure?: boolean;\n  isStandalone?: boolean;\n}\n\nexport interface ParseSourceSpan {\n  start: any;\n  end: any;\n  details: any;\n  fullStart: any;\n}\n", "/*!\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ɵɵinjectAttribute} from '../render3/instructions/di_attr';\n\n/**\n * Creates a token that can be used to inject static attributes of the host node.\n *\n * @usageNotes\n * ### Injecting an attribute that is known to exist\n * ```ts\n * @Directive()\n * class MyDir {\n *   attr: string = inject(new HostAttributeToken('some-attr'));\n * }\n * ```\n *\n * ### Optionally injecting an attribute\n * ```ts\n * @Directive()\n * class MyDir {\n *   attr: string | null = inject(new HostAttributeToken('some-attr'), {optional: true});\n * }\n * ```\n * @publicApi\n */\nexport class HostAttributeToken {\n  constructor(private attributeName: string) {}\n\n  /** @internal */\n  __NG_ELEMENT_ID__ = () => ɵɵinjectAttribute(this.attributeName);\n\n  toString(): string {\n    return `HostAttributeToken ${this.attributeName}`;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {RuntimeError, RuntimeErrorCode} from '../errors';\nimport {TNode, TNodeType} from '../render3/interfaces/node';\nimport {getCurrentTNode} from '../render3/state';\n\nimport {InjectionToken} from './injection_token';\nimport {InternalInjectFlags} from './interface/injector';\n\n/**\n * A token that can be used to inject the tag name of the host node.\n *\n * @usageNotes\n * ### Injecting a tag name that is known to exist\n * ```ts\n * @Directive()\n * class MyDir {\n *   tagName: string = inject(HOST_TAG_NAME);\n * }\n * ```\n *\n * ### Optionally injecting a tag name\n * ```ts\n * @Directive()\n * class MyDir {\n *   tagName: string | null = inject(HOST_TAG_NAME, {optional: true});\n * }\n * ```\n * @publicApi\n */\nexport const HOST_TAG_NAME = new InjectionToken<string>(ngDevMode ? 'HOST_TAG_NAME' : '');\n\n// HOST_TAG_NAME should be resolved at the current node, similar to e.g. ElementRef,\n// so we manually specify __NG_ELEMENT_ID__ here, instead of using a factory.\n// tslint:disable-next-line:no-toplevel-property-access\n(HOST_TAG_NAME as any).__NG_ELEMENT_ID__ = (flags: InternalInjectFlags) => {\n  const tNode = getCurrentTNode();\n  if (tNode === null) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INJECTION_TOKEN,\n      ngDevMode &&\n        'HOST_TAG_NAME can only be injected in directives and components ' +\n          'during construction time (in a class constructor or as a class field initializer)',\n    );\n  }\n  if (tNode.type & TNodeType.Element) {\n    return tNode.value;\n  }\n  if (flags & InternalInjectFlags.Optional) {\n    return null;\n  }\n  throw new RuntimeError(\n    RuntimeErrorCode.INVALID_INJECTION_TOKEN,\n    ngDevMode &&\n      `HOST_TAG_NAME was used on ${getDevModeNodeName(\n        tNode,\n      )} which doesn't have an underlying element in the DOM. ` +\n        `This is invalid, and so the dependency should be marked as optional.`,\n  );\n};\n\nfunction getDevModeNodeName(tNode: TNode) {\n  if (tNode.type & TNodeType.ElementContainer) {\n    return 'an <ng-container>';\n  } else if (tNode.type & TNodeType.Container) {\n    return 'an <ng-template>';\n  } else if (tNode.type & TNodeType.LetDeclaration) {\n    return 'an @let declaration';\n  } else {\n    return 'a node';\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {assertInInjectionContext} from '../../di';\n\nimport {OutputEmitterRef} from './output_emitter_ref';\n\n/**\n * Options for declaring an output.\n *\n * @publicApi 19.0\n */\nexport interface OutputOptions {\n  alias?: string;\n}\n\n/**\n * The `output` function allows declaration of Angular outputs in\n * directives and components.\n *\n * You can use outputs to emit values to parent directives and component.\n * Parents can subscribe to changes via:\n *\n * - template event bindings. For example, `(myOutput)=\"doSomething($event)\"`\n * - programmatic subscription by using `OutputRef#subscribe`.\n *\n * @usageNotes\n *\n * To use `output()`, import the function from `@angular/core`.\n *\n * ```ts\n * import {output} from '@angular/core';\n * ```\n *\n * Inside your component, introduce a new class member and initialize\n * it with a call to `output`.\n *\n * ```ts\n * @Directive({\n *   ...\n * })\n * export class MyDir {\n *   nameChange = output<string>();    // OutputEmitterRef<string>\n *   onClick    = output();            // OutputEmitterRef<void>\n * }\n * ```\n *\n * You can emit values to consumers of your directive, by using\n * the `emit` method from `OutputEmitterRef`.\n *\n * ```ts\n * updateName(newName: string): void {\n *   this.nameChange.emit(newName);\n * }\n * ```\n * @initializerApiFunction {\"showTypesInSignaturePreview\": true}\n * @publicApi 19.0\n */\nexport function output<T = void>(opts?: OutputOptions): OutputEmitterRef<T> {\n  ngDevMode && assertInInjectionContext(output);\n  return new OutputEmitterRef<T>();\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {assertInInjectionContext} from '../../di';\n\nimport {\n  createInputSignal,\n  InputOptions,\n  InputOptionsWithoutTransform,\n  InputOptionsWithTransform,\n  InputSignal,\n  InputSignalWithTransform,\n} from './input_signal';\nimport {REQUIRED_UNSET_VALUE} from './input_signal_node';\n\nexport function inputFunction<ReadT, WriteT>(\n  initialValue?: ReadT,\n  opts?: InputOptions<ReadT, WriteT>,\n): InputSignalWithTransform<ReadT | undefined, WriteT> {\n  ngDevMode && assertInInjectionContext(input);\n  return createInputSignal(initialValue, opts);\n}\n\nexport function inputRequiredFunction<ReadT, WriteT = ReadT>(\n  opts?: InputOptions<ReadT, WriteT>,\n): InputSignalWithTransform<ReadT, WriteT> {\n  ngDevMode && assertInInjectionContext(input);\n  return createInputSignal(REQUIRED_UNSET_VALUE as never, opts);\n}\n\n/**\n * The `input` function allows declaration of inputs in directives and\n * components.\n *\n * The function exposes an API for also declaring required inputs via the\n * `input.required` function.\n *\n * @publicAPI\n * @docsPrivate Ignored because `input` is the canonical API entry.\n */\nexport interface InputFunction {\n  /**\n   * Initializes an input of type `T` with an initial value of `undefined`.\n   * Angular will implicitly use `undefined` as initial value.\n   */\n  <T>(): InputSignal<T | undefined>;\n  /** Declares an input of type `T` with an explicit initial value. */\n  <T>(initialValue: T, opts?: InputOptionsWithoutTransform<T>): InputSignal<T>;\n  /** Declares an input of type `T|undefined` without an initial value, but with input options */\n  <T>(initialValue: undefined, opts: InputOptionsWithoutTransform<T>): InputSignal<T | undefined>;\n  /**\n   * Declares an input of type `T` with an initial value and a transform\n   * function.\n   *\n   * The input accepts values of type `TransformT` and the given\n   * transform function will transform the value to type `T`.\n   */\n  <T, TransformT>(\n    initialValue: T,\n    opts: InputOptionsWithTransform<T, TransformT>,\n  ): InputSignalWithTransform<T, TransformT>;\n  /**\n   * Declares an input of type `T|undefined` without an initial value and with a transform\n   * function.\n   *\n   * The input accepts values of type `TransformT` and the given\n   * transform function will transform the value to type `T|undefined`.\n   */ <T, TransformT>(\n    initialValue: undefined,\n    opts: InputOptionsWithTransform<T | undefined, TransformT>,\n  ): InputSignalWithTransform<T | undefined, TransformT>;\n\n  /**\n   * Initializes a required input.\n   *\n   * Consumers of your directive/component need to bind to this\n   * input. If unset, a compile time error will be reported.\n   *\n   * @publicAPI\n   */\n  required: {\n    /** Declares a required input of type `T`. */\n    <T>(opts?: InputOptionsWithoutTransform<T>): InputSignal<T>;\n    /**\n     * Declares a required input of type `T` with a transform function.\n     *\n     * The input accepts values of type `TransformT` and the given\n     * transform function will transform the value to type `T`.\n     */\n    <T, TransformT>(\n      opts: InputOptionsWithTransform<T, TransformT>,\n    ): InputSignalWithTransform<T, TransformT>;\n  };\n}\n\n/**\n * The `input` function allows declaration of Angular inputs in directives\n * and components.\n *\n * There are two variants of inputs that can be declared:\n *\n *   1. **Optional inputs** with an initial value.\n *   2. **Required inputs** that consumers need to set.\n *\n * By default, the `input` function will declare optional inputs that\n * always have an initial value. Required inputs can be declared\n * using the `input.required()` function.\n *\n * Inputs are signals. The values of an input are exposed as a `Signal`.\n * The signal always holds the latest value of the input that is bound\n * from the parent.\n *\n * @usageNotes\n * To use signal-based inputs, import `input` from `@angular/core`.\n *\n * ```ts\n * import {input} from '@angular/core`;\n * ```\n *\n * Inside your component, introduce a new class member and initialize\n * it with a call to `input` or `input.required`.\n *\n * ```ts\n * @Component({\n *   ...\n * })\n * export class UserProfileComponent {\n *   firstName = input<string>();             // Signal<string|undefined>\n *   lastName  = input.required<string>();    // Signal<string>\n *   age       = input(0)                     // Signal<number>\n * }\n * ```\n *\n * Inside your component template, you can display values of the inputs\n * by calling the signal.\n *\n * ```html\n * <span>{{firstName()}}</span>\n * ```\n *\n * @publicAPI\n * @initializerApiFunction\n */\nexport const input: InputFunction = (() => {\n  // Note: This may be considered a side-effect, but nothing will depend on\n  // this assignment, unless this `input` constant export is accessed. It's a\n  // self-contained side effect that is local to the user facing`input` export.\n  (inputFunction as any).required = inputRequiredFunction;\n  return inputFunction as typeof inputFunction & {required: typeof inputRequiredFunction};\n})();\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {assertInInjectionContext} from '../di';\nimport {ProviderToken} from '../di/provider_token';\nimport {\n  createMultiResultQuerySignalFn,\n  createSingleResultOptionalQuerySignalFn,\n  createSingleResultRequiredQuerySignalFn,\n} from '../render3/queries/query_reactive';\nimport {Signal} from '../render3/reactivity/api';\n\nfunction viewChildFn<LocatorT, ReadT>(\n  locator: ProviderToken<LocatorT> | string,\n  opts?: {read?: ProviderToken<ReadT>; debugName?: string},\n): Signal<ReadT | undefined> {\n  ngDevMode && assertInInjectionContext(viewChild);\n  return createSingleResultOptionalQuerySignalFn<ReadT>(opts);\n}\n\nfunction viewChildRequiredFn<LocatorT, ReadT>(\n  locator: ProviderToken<LocatorT> | string,\n  opts?: {read?: ProviderToken<ReadT>; debugName?: string},\n): Signal<ReadT> {\n  ngDevMode && assertInInjectionContext(viewChild);\n  return createSingleResultRequiredQuerySignalFn<ReadT>(opts);\n}\n\n/**\n * Type of the `viewChild` function. The viewChild function creates a singular view query.\n *\n * It is a special function that also provides access to required query results via the `.required`\n * property.\n *\n * @publicApi\n * @docsPrivate Ignored because `viewChild` is the canonical API entry.\n */\nexport interface ViewChildFunction {\n  /**\n   * Initializes a view child query. Consider using `viewChild.required` for queries that should\n   * always match.\n   *\n   * @publicAPI\n   */\n\n  <LocatorT, ReadT>(\n    locator: ProviderToken<LocatorT> | string,\n    opts: {\n      read: ProviderToken<ReadT>;\n      debugName?: string;\n    },\n  ): Signal<ReadT | undefined>;\n\n  <LocatorT>(\n    locator: ProviderToken<LocatorT> | string,\n    opts?: {\n      debugName?: string;\n    },\n  ): Signal<LocatorT | undefined>;\n\n  /**\n   * Initializes a view child query that is expected to always match an element.\n   *\n   * @publicAPI\n   */\n  required: {\n    <LocatorT>(\n      locator: ProviderToken<LocatorT> | string,\n      opts?: {\n        debugName?: string;\n      },\n    ): Signal<LocatorT>;\n\n    <LocatorT, ReadT>(\n      locator: ProviderToken<LocatorT> | string,\n      opts: {\n        read: ProviderToken<ReadT>;\n        debugName?: string;\n      },\n    ): Signal<ReadT>;\n  };\n}\n\n/**\n * Initializes a view child query.\n *\n * Consider using `viewChild.required` for queries that should always match.\n *\n * @usageNotes\n * Create a child query in your component by declaring a\n * class field and initializing it with the `viewChild()` function.\n *\n * ```angular-ts\n * @Component({template: '<div #el></div><my-component #cmp />'})\n * export class TestComponent {\n *   divEl = viewChild<ElementRef>('el');                   // Signal<ElementRef|undefined>\n *   divElRequired = viewChild.required<ElementRef>('el');  // Signal<ElementRef>\n *   cmp = viewChild(MyComponent);                          // Signal<MyComponent|undefined>\n *   cmpRequired = viewChild.required(MyComponent);         // Signal<MyComponent>\n * }\n * ```\n *\n * @publicApi 19.0\n * @initializerApiFunction\n */\nexport const viewChild: ViewChildFunction = (() => {\n  // Note: This may be considered a side-effect, but nothing will depend on\n  // this assignment, unless this `viewChild` constant export is accessed. It's a\n  // self-contained side effect that is local to the user facing `viewChild` export.\n  (viewChildFn as any).required = viewChildRequiredFn;\n  return viewChildFn as typeof viewChildFn & {required: typeof viewChildRequiredFn};\n})();\n\nexport function viewChildren<LocatorT>(\n  locator: ProviderToken<LocatorT> | string,\n  opts?: {debugName?: string},\n): Signal<ReadonlyArray<LocatorT>>;\nexport function viewChildren<LocatorT, ReadT>(\n  locator: ProviderToken<LocatorT> | string,\n  opts: {\n    read: ProviderToken<ReadT>;\n    debugName?: string;\n  },\n): Signal<ReadonlyArray<ReadT>>;\n\n/**\n * Initializes a view children query.\n *\n * Query results are represented as a signal of a read-only collection containing all matched\n * elements.\n *\n * @usageNotes\n * Create a children query in your component by declaring a\n * class field and initializing it with the `viewChildren()` function.\n *\n * ```ts\n * @Component({...})\n * export class TestComponent {\n *   divEls = viewChildren<ElementRef>('el');   // Signal<ReadonlyArray<ElementRef>>\n * }\n * ```\n *\n * @initializerApiFunction\n * @publicApi 19.0\n */\nexport function viewChildren<LocatorT, ReadT>(\n  locator: ProviderToken<LocatorT> | string,\n  opts?: {\n    read?: ProviderToken<ReadT>;\n    debugName?: string;\n  },\n): Signal<ReadonlyArray<ReadT>> {\n  ngDevMode && assertInInjectionContext(viewChildren);\n  return createMultiResultQuerySignalFn<ReadT>(opts);\n}\n\nexport function contentChildFn<LocatorT, ReadT>(\n  locator: ProviderToken<LocatorT> | string,\n  opts?: {\n    descendants?: boolean;\n    read?: ProviderToken<ReadT>;\n    debugName?: string;\n  },\n): Signal<ReadT | undefined> {\n  ngDevMode && assertInInjectionContext(contentChild);\n  return createSingleResultOptionalQuerySignalFn<ReadT>(opts);\n}\n\nfunction contentChildRequiredFn<LocatorT, ReadT>(\n  locator: ProviderToken<LocatorT> | string,\n  opts?: {\n    descendants?: boolean;\n    read?: ProviderToken<ReadT>;\n    debugName?: string;\n  },\n): Signal<ReadT> {\n  ngDevMode && assertInInjectionContext(contentChildren);\n  return createSingleResultRequiredQuerySignalFn<ReadT>(opts);\n}\n\n/**\n * Type of the `contentChild` function.\n *\n * The contentChild function creates a singular content query. It is a special function that also\n * provides access to required query results via the `.required` property.\n *\n * @publicApi 19.0\n * @docsPrivate Ignored because `contentChild` is the canonical API entry.\n */\nexport interface ContentChildFunction {\n  /**\n   * Initializes a content child query.\n   *\n   * Consider using `contentChild.required` for queries that should always match.\n   * @publicAPI\n   */\n  <LocatorT>(\n    locator: ProviderToken<LocatorT> | string,\n    opts?: {\n      descendants?: boolean;\n      read?: undefined;\n      debugName?: string;\n    },\n  ): Signal<LocatorT | undefined>;\n\n  <LocatorT, ReadT>(\n    locator: ProviderToken<LocatorT> | string,\n    opts: {\n      descendants?: boolean;\n      read: ProviderToken<ReadT>;\n      debugName?: string;\n    },\n  ): Signal<ReadT | undefined>;\n\n  /**\n   * Initializes a content child query that is always expected to match.\n   */\n  required: {\n    <LocatorT>(\n      locator: ProviderToken<LocatorT> | string,\n      opts?: {\n        descendants?: boolean;\n        read?: undefined;\n        debugName?: string;\n      },\n    ): Signal<LocatorT>;\n\n    <LocatorT, ReadT>(\n      locator: ProviderToken<LocatorT> | string,\n      opts: {\n        descendants?: boolean;\n        read: ProviderToken<ReadT>;\n        debugName?: string;\n      },\n    ): Signal<ReadT>;\n  };\n}\n\n/**\n * Initializes a content child query. Consider using `contentChild.required` for queries that should\n * always match.\n *\n * @usageNotes\n * Create a child query in your component by declaring a\n * class field and initializing it with the `contentChild()` function.\n *\n * ```ts\n * @Component({...})\n * export class TestComponent {\n *   headerEl = contentChild<ElementRef>('h');                    // Signal<ElementRef|undefined>\n *   headerElElRequired = contentChild.required<ElementRef>('h'); // Signal<ElementRef>\n *   header = contentChild(MyHeader);                             // Signal<MyHeader|undefined>\n *   headerRequired = contentChild.required(MyHeader);            // Signal<MyHeader>\n * }\n * ```\n *\n * Note: By default `descendants` is `true` which means the query will traverse all descendants in the same template.\n *\n * @initializerApiFunction\n * @publicApi 19.0\n */\nexport const contentChild: ContentChildFunction = (() => {\n  // Note: This may be considered a side-effect, but nothing will depend on\n  // this assignment, unless this `viewChild` constant export is accessed. It's a\n  // self-contained side effect that is local to the user facing `viewChild` export.\n  (contentChildFn as any).required = contentChildRequiredFn;\n  return contentChildFn as typeof contentChildFn & {required: typeof contentChildRequiredFn};\n})();\n\nexport function contentChildren<LocatorT>(\n  locator: ProviderToken<LocatorT> | string,\n  opts?: {\n    descendants?: boolean;\n    read?: undefined;\n    debugName?: string;\n  },\n): Signal<ReadonlyArray<LocatorT>>;\nexport function contentChildren<LocatorT, ReadT>(\n  locator: ProviderToken<LocatorT> | string,\n  opts: {\n    descendants?: boolean;\n    read: ProviderToken<ReadT>;\n    debugName?: string;\n  },\n): Signal<ReadonlyArray<ReadT>>;\n\n/**\n * Initializes a content children query.\n *\n * Query results are represented as a signal of a read-only collection containing all matched\n * elements.\n *\n * @usageNotes\n * Create a children query in your component by declaring a\n * class field and initializing it with the `contentChildren()` function.\n *\n * ```ts\n * @Component({...})\n * export class TestComponent {\n *   headerEl = contentChildren<ElementRef>('h');   // Signal<ReadonlyArray<ElementRef>>\n * }\n * ```\n *\n * Note: By default `descendants` is `false` which means the query will not traverse all descendants in the same template.\n *\n * @initializerApiFunction\n * @publicApi 19.0\n */\nexport function contentChildren<LocatorT, ReadT>(\n  locator: ProviderToken<LocatorT> | string,\n  opts?: {\n    descendants?: boolean;\n    read?: ProviderToken<ReadT>;\n    debugName?: string;\n  },\n): Signal<ReadonlyArray<ReadT>> {\n  return createMultiResultQuerySignalFn<ReadT>(opts);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {producerAccessed, SIGNAL, signalSetFn} from '../../../primitives/signals';\n\nimport {RuntimeError, RuntimeErrorCode} from '../../errors';\nimport {Signal} from '../../render3/reactivity/api';\nimport {\n  signalAsReadonlyFn,\n  WritableSignal,\n  ɵWRITABLE_SIGNAL,\n} from '../../render3/reactivity/signal';\nimport {\n  InputSignal,\n  ɵINPUT_SIGNAL_BRAND_READ_TYPE,\n  ɵINPUT_SIGNAL_BRAND_WRITE_TYPE,\n} from '../input/input_signal';\nimport {INPUT_SIGNAL_NODE, InputSignalNode, REQUIRED_UNSET_VALUE} from '../input/input_signal_node';\nimport {OutputEmitterRef} from '../output/output_emitter_ref';\nimport {OutputRef} from '../output/output_ref';\n\n/**\n * @publicAPI\n *\n * Options for model signals.\n */\nexport interface ModelOptions {\n  /**\n   * Optional public name of the input side of the model. The output side will have the same\n   * name as the input, but suffixed with `Change`. By default, the class field name is used.\n   */\n  alias?: string;\n\n  /**\n   * A debug name for the model signal. Used in Angular DevTools to identify the signal.\n   */\n  debugName?: string;\n}\n\n/**\n * `ModelSignal` represents a special `Signal` for a directive/component model field.\n *\n * A model signal is a writeable signal that can be exposed as an output.\n * Whenever its value is updated, it emits to the output.\n *\n * @publicAPI\n */\nexport interface ModelSignal<T> extends WritableSignal<T>, InputSignal<T>, OutputRef<T> {\n  [SIGNAL]: InputSignalNode<T, T>;\n}\n\n/**\n * Creates a model signal.\n *\n * @param initialValue The initial value.\n *   Can be set to {@link REQUIRED_UNSET_VALUE} for required model signals.\n * @param options Additional options for the model.\n */\nexport function createModelSignal<T>(initialValue: T, opts?: ModelOptions): ModelSignal<T> {\n  const node: InputSignalNode<T, T> = Object.create(INPUT_SIGNAL_NODE);\n  const emitterRef = new OutputEmitterRef<T>();\n\n  node.value = initialValue;\n\n  function getter(): T {\n    producerAccessed(node);\n    assertModelSet(node.value);\n    return node.value;\n  }\n\n  getter[SIGNAL] = node;\n  getter.asReadonly = signalAsReadonlyFn.bind(getter as any) as () => Signal<T>;\n\n  // TODO: Should we throw an error when updating a destroyed model?\n  getter.set = (newValue: T) => {\n    if (!node.equal(node.value, newValue)) {\n      signalSetFn(node, newValue);\n      emitterRef.emit(newValue);\n    }\n  };\n\n  getter.update = (updateFn: (value: T) => T) => {\n    assertModelSet(node.value);\n    getter.set(updateFn(node.value));\n  };\n\n  getter.subscribe = emitterRef.subscribe.bind(emitterRef);\n  getter.destroyRef = emitterRef.destroyRef;\n\n  if (ngDevMode) {\n    getter.toString = () => `[Model Signal: ${getter()}]`;\n    node.debugName = opts?.debugName;\n  }\n\n  return getter as typeof getter &\n    Pick<\n      ModelSignal<T>,\n      | typeof ɵINPUT_SIGNAL_BRAND_READ_TYPE\n      | typeof ɵINPUT_SIGNAL_BRAND_WRITE_TYPE\n      | typeof ɵWRITABLE_SIGNAL\n    >;\n}\n\n/** Asserts that a model's value is set. */\nfunction assertModelSet(value: unknown): void {\n  if (value === REQUIRED_UNSET_VALUE) {\n    throw new RuntimeError(\n      RuntimeErrorCode.REQUIRED_MODEL_NO_VALUE,\n      ngDevMode && 'Model is required but no value is available yet.',\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {assertInInjectionContext} from '../../di';\nimport {REQUIRED_UNSET_VALUE} from '../input/input_signal_node';\n\nimport {createModelSignal, ModelOptions, ModelSignal} from './model_signal';\n\nexport function modelFunction<T>(\n  initialValue?: T,\n  opts?: ModelOptions,\n): ModelSignal<T | undefined> {\n  ngDevMode && assertInInjectionContext(model);\n\n  return createModelSignal(initialValue, opts);\n}\n\nexport function modelRequiredFunction<T>(opts?: ModelOptions): ModelSignal<T> {\n  ngDevMode && assertInInjectionContext(model);\n\n  return createModelSignal(REQUIRED_UNSET_VALUE as T, opts);\n}\n\n/**\n * `model` declares a writeable signal that is exposed as an input/output pair on the containing\n * directive. The input name is taken either from the class member or from the `alias` option.\n * The output name is generated by taking the input name and appending `Change`.\n *\n * The function exposes an API for also declaring required models via the\n * `model.required` function.\n *\n * @publicAPI\n * @docsPrivate Ignored because `model` is the canonical API entry.\n */\nexport interface ModelFunction {\n  /**\n   * Initializes a model of type `T` with an initial value of `undefined`.\n   * Angular will implicitly use `undefined` as initial value.\n   */\n  <T>(): ModelSignal<T | undefined>;\n  /** Initializes a model of type `T` with the given initial value. */\n  <T>(initialValue: T, opts?: ModelOptions): ModelSignal<T>;\n\n  required: {\n    /**\n     * Initializes a required model.\n     *\n     * Users of your directive/component need to bind to the input side of the model.\n     * If unset, a compile time error will be reported.\n     */\n    <T>(opts?: ModelOptions): ModelSignal<T>;\n  };\n}\n\n/**\n * `model` declares a writeable signal that is exposed as an input/output\n * pair on the containing directive.\n *\n * The input name is taken either from the class member or from the `alias` option.\n * The output name is generated by taking the input name and appending `Change`.\n *\n * @usageNotes\n *\n * To use `model()`, import the function from `@angular/core`.\n *\n * ```ts\n * import {model} from '@angular/core`;\n * ```\n *\n * Inside your component, introduce a new class member and initialize\n * it with a call to `model` or `model.required`.\n *\n * ```ts\n * @Directive({\n *   ...\n * })\n * export class MyDir {\n *   firstName = model<string>();            // ModelSignal<string|undefined>\n *   lastName  = model.required<string>();   // ModelSignal<string>\n *   age       = model(0);                   // ModelSignal<number>\n * }\n * ```\n *\n * Inside your component template, you can display the value of a `model`\n * by calling the signal.\n *\n * ```html\n * <span>{{firstName()}}</span>\n * ```\n *\n * Updating the `model` is equivalent to updating a writable signal.\n *\n * ```ts\n * updateName(newFirstName: string): void {\n *   this.firstName.set(newFirstName);\n * }\n * ```\n *\n * @publicApi 19.0\n * @initializerApiFunction\n */\nexport const model: ModelFunction = (() => {\n  // Note: This may be considered a side-effect, but nothing will depend on\n  // this assignment, unless this `model` constant export is accessed. It's a\n  // self-contained side effect that is local to the user facing `model` export.\n  (modelFunction as any).required = modelRequiredFunction;\n  return modelFunction as typeof modelFunction & {required: typeof modelRequiredFunction};\n})();\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ProviderToken} from '../di/provider_token';\nimport {makePropDecorator} from '../util/decorators';\n\n/**\n * Type of the `Attribute` decorator / constructor function.\n *\n * @publicApi\n */\nexport interface AttributeDecorator {\n  /**\n   * Specifies that a constant attribute value should be injected.\n   *\n   * The directive can inject constant string literals of host element attributes.\n   *\n   * @usageNotes\n   *\n   * Suppose we have an `<input>` element and want to know its `type`.\n   *\n   * ```html\n   * <input type=\"text\">\n   * ```\n   *\n   * A decorator can inject string literal `text` as in the following example.\n   *\n   * {@example core/ts/metadata/metadata.ts region='attributeMetadata'}\n   *\n   * @publicApi\n   */\n  (name: string): any;\n  new (name: string): Attribute;\n}\n\n/**\n * Type of the Attribute metadata.\n *\n * @publicApi\n */\nexport interface Attribute {\n  /**\n   * The name of the attribute to be injected into the constructor.\n   */\n  attributeName?: string;\n}\n\n/**\n * Type of the Query metadata.\n *\n * @publicApi\n */\nexport interface Query {\n  descendants: boolean;\n  emitDistinctChangesOnly: boolean;\n  first: boolean;\n  read: any;\n  isViewQuery: boolean;\n  selector: any;\n  static?: boolean;\n\n  /**\n   * @internal\n   *\n   * Whether the query is a signal query.\n   *\n   * This option exists for JIT compatibility. Users are not expected to use this.\n   * Angular needs a way to capture queries from classes so that the internal query\n   * functions can be generated. This needs to happen before the component is instantiated.\n   * Due to this, for JIT compilation, signal queries need an additional decorator\n   * declaring the query. Angular provides a TS transformer to automatically handle this\n   * for JIT usage (e.g. in tests).\n   */\n  isSignal?: boolean;\n}\n\n// Stores the default value of `emitDistinctChangesOnly` when the `emitDistinctChangesOnly` is not\n// explicitly set.\nexport const emitDistinctChangesOnlyDefaultValue = true;\n\n/**\n * Base class for query metadata.\n *\n * @see {@link ContentChildren}\n * @see {@link ContentChild}\n * @see {@link ViewChildren}\n * @see {@link ViewChild}\n *\n * @publicApi\n */\nexport abstract class Query {}\n\n/**\n * Type of the ContentChildren decorator / constructor function.\n *\n * @see {@link ContentChildren}\n * @publicApi\n */\nexport interface ContentChildrenDecorator {\n  /**\n   * @description\n   * Property decorator that configures a content query.\n   *\n   * Use to get the `QueryList` of elements or directives from the content DOM.\n   * Any time a child element is added, removed, or moved, the query list will be\n   * updated, and the changes observable of the query list will emit a new value.\n   *\n   * Content queries are set before the `ngAfterContentInit` callback is called.\n   *\n   * Does not retrieve elements or directives that are in other components' templates,\n   * since a component's template is always a black box to its ancestors.\n   *\n   * **Metadata Properties**:\n   *\n   * * **selector** - The directive type or the name used for querying.\n   * * **descendants** - If `true` include all descendants of the element. If `false` then only\n   * query direct children of the element.\n   * * **emitDistinctChangesOnly** - The ` QueryList#changes` observable will emit new values only\n   *   if the QueryList result has changed. When `false` the `changes` observable might emit even\n   *   if the QueryList has not changed.\n   *   ** Note: *** This config option is **deprecated**, it will be permanently set to `true` and\n   *   removed in future versions of Angular.\n   * * **read** - Used to read a different token from the queried elements.\n   *\n   * The following selectors are supported.\n   *   * Any class with the `@Component` or `@Directive` decorator\n   *   * A template reference variable as a string (e.g. query `<my-component #cmp></my-component>`\n   * with `@ContentChildren('cmp')`)\n   *   * Any provider defined in the child component tree of the current component (e.g.\n   * `@ContentChildren(SomeService) someService: SomeService`)\n   *   * Any provider defined through a string token (e.g. `@ContentChildren('someToken')\n   * someTokenVal: any`)\n   *   * A `TemplateRef` (e.g. query `<ng-template></ng-template>` with\n   * `@ContentChildren(TemplateRef) template;`)\n   *\n   * In addition, multiple string selectors can be separated with a comma (e.g.\n   * `@ContentChildren('cmp1,cmp2')`)\n   *\n   * The following values are supported by `read`:\n   *   * Any class with the `@Component` or `@Directive` decorator\n   *   * Any provider defined on the injector of the component that is matched by the `selector` of\n   * this query\n   *   * Any provider defined through a string token (e.g. `{provide: 'token', useValue: 'val'}`)\n   *   * `TemplateRef`, `ElementRef`, and `ViewContainerRef`\n   *\n   * @usageNotes\n   *\n   * Here is a simple demonstration of how the `ContentChildren` decorator can be used.\n   *\n   * {@example core/di/ts/contentChildren/content_children_howto.ts region='HowTo'}\n   *\n   * ### Tab-pane example\n   *\n   * Here is a slightly more realistic example that shows how `ContentChildren` decorators\n   * can be used to implement a tab pane component.\n   *\n   * {@example core/di/ts/contentChildren/content_children_example.ts region='Component'}\n   *\n   * @Annotation\n   */\n  (\n    selector: ProviderToken<unknown> | Function | string,\n    opts?: {\n      descendants?: boolean;\n      emitDistinctChangesOnly?: boolean;\n      read?: any;\n    },\n  ): any;\n  new (\n    selector: ProviderToken<unknown> | Function | string,\n    opts?: {descendants?: boolean; emitDistinctChangesOnly?: boolean; read?: any},\n  ): Query;\n}\n\n/**\n * Type of the ContentChildren metadata.\n *\n *\n * @Annotation\n * @publicApi\n */\nexport type ContentChildren = Query;\n\n/**\n * ContentChildren decorator and metadata.\n *\n *\n * @Annotation\n * @publicApi\n */\nexport const ContentChildren: ContentChildrenDecorator = makePropDecorator(\n  'ContentChildren',\n  (selector?: any, opts: any = {}) => ({\n    selector,\n    first: false,\n    isViewQuery: false,\n    descendants: false,\n    emitDistinctChangesOnly: emitDistinctChangesOnlyDefaultValue,\n    ...opts,\n  }),\n  Query,\n);\n\n/**\n * Type of the ContentChild decorator / constructor function.\n *\n * @publicApi\n */\nexport interface ContentChildDecorator {\n  /**\n   * @description\n   * Property decorator that configures a content query.\n   *\n   * Use to get the first element or the directive matching the selector from the content DOM.\n   * If the content DOM changes, and a new child matches the selector,\n   * the property will be updated.\n   *\n   * Does not retrieve elements or directives that are in other components' templates,\n   * since a component's template is always a black box to its ancestors.\n   *\n   * **Metadata Properties**:\n   *\n   * * **selector** - The directive type or the name used for querying.\n   * * **descendants** - If `true` (default) include all descendants of the element. If `false` then\n   * only query direct children of the element.\n   * * **read** - Used to read a different token from the queried element.\n   * * **static** - True to resolve query results before change detection runs,\n   * false to resolve after change detection. Defaults to false.\n   *\n   * The following selectors are supported.\n   *   * Any class with the `@Component` or `@Directive` decorator\n   *   * A template reference variable as a string (e.g. query `<my-component #cmp></my-component>`\n   * with `@ContentChild('cmp')`)\n   *   * Any provider defined in the child component tree of the current component (e.g.\n   * `@ContentChild(SomeService) someService: SomeService`)\n   *   * Any provider defined through a string token (e.g. `@ContentChild('someToken') someTokenVal:\n   * any`)\n   *   * A `TemplateRef` (e.g. query `<ng-template></ng-template>` with `@ContentChild(TemplateRef)\n   * template;`)\n   *\n   * The following values are supported by `read`:\n   *   * Any class with the `@Component` or `@Directive` decorator\n   *   * Any provider defined on the injector of the component that is matched by the `selector` of\n   * this query\n   *   * Any provider defined through a string token (e.g. `{provide: 'token', useValue: 'val'}`)\n   *   * `TemplateRef`, `ElementRef`, and `ViewContainerRef`\n   *\n   * Difference between dynamic and static queries:\n   *\n   * | Queries                             | Details |\n   * |:---                                 |:---     |\n   * | Dynamic queries \\(`static: false`\\) | The query resolves before the `ngAfterContentInit()`\n   * callback is called. The result will be updated for changes to your view, such as changes to\n   * `ngIf` and `ngFor` blocks. | | Static queries \\(`static: true`\\)   | The query resolves once\n   * the view has been created, but before change detection runs (before the `ngOnInit()` callback\n   * is called). The result, though, will never be updated to reflect changes to your view, such as\n   * changes to `ngIf` and `ngFor` blocks.  |\n   *\n   * @usageNotes\n   *\n   * {@example core/di/ts/contentChild/content_child_howto.ts region='HowTo'}\n   *\n   * ### Example\n   *\n   * {@example core/di/ts/contentChild/content_child_example.ts region='Component'}\n   *\n   * @Annotation\n   */\n  (\n    selector: ProviderToken<unknown> | Function | string,\n    opts?: {descendants?: boolean; read?: any; static?: boolean},\n  ): any;\n  new (\n    selector: ProviderToken<unknown> | Function | string,\n    opts?: {descendants?: boolean; read?: any; static?: boolean},\n  ): ContentChild;\n}\n\n/**\n * Type of the ContentChild metadata.\n *\n * @publicApi\n */\nexport type ContentChild = Query;\n\n/**\n * ContentChild decorator and metadata.\n *\n *\n * @Annotation\n *\n * @publicApi\n */\nexport const ContentChild: ContentChildDecorator = makePropDecorator(\n  'ContentChild',\n  (selector?: any, opts: any = {}) => ({\n    selector,\n    first: true,\n    isViewQuery: false,\n    descendants: true,\n    ...opts,\n  }),\n  Query,\n);\n\n/**\n * Type of the ViewChildren decorator / constructor function.\n *\n * @see {@link ViewChildren}\n *\n * @publicApi\n */\nexport interface ViewChildrenDecorator {\n  /**\n   * @description\n   * Property decorator that configures a view query.\n   *\n   * Use to get the `QueryList` of elements or directives from the view DOM.\n   * Any time a child element is added, removed, or moved, the query list will be updated,\n   * and the changes observable of the query list will emit a new value.\n   *\n   * View queries are set before the `ngAfterViewInit` callback is called.\n   *\n   * **Metadata Properties**:\n   *\n   * * **selector** - The directive type or the name used for querying.\n   * * **read** - Used to read a different token from the queried elements.\n   * * **emitDistinctChangesOnly** - The ` QueryList#changes` observable will emit new values only\n   *   if the QueryList result has changed. When `false` the `changes` observable might emit even\n   *   if the QueryList has not changed.\n   *   ** Note: *** This config option is **deprecated**, it will be permanently set to `true` and\n   * removed in future versions of Angular.\n   *\n   * The following selectors are supported.\n   *   * Any class with the `@Component` or `@Directive` decorator\n   *   * A template reference variable as a string (e.g. query `<my-component #cmp></my-component>`\n   * with `@ViewChildren('cmp')`)\n   *   * Any provider defined in the child component tree of the current component (e.g.\n   * `@ViewChildren(SomeService) someService!: SomeService`)\n   *   * Any provider defined through a string token (e.g. `@ViewChildren('someToken')\n   * someTokenVal!: any`)\n   *   * A `TemplateRef` (e.g. query `<ng-template></ng-template>` with `@ViewChildren(TemplateRef)\n   * template;`)\n   *\n   * In addition, multiple string selectors can be separated with a comma (e.g.\n   * `@ViewChildren('cmp1,cmp2')`)\n   *\n   * The following values are supported by `read`:\n   *   * Any class with the `@Component` or `@Directive` decorator\n   *   * Any provider defined on the injector of the component that is matched by the `selector` of\n   * this query\n   *   * Any provider defined through a string token (e.g. `{provide: 'token', useValue: 'val'}`)\n   *   * `TemplateRef`, `ElementRef`, and `ViewContainerRef`\n   *\n   * @usageNotes\n   *\n   * {@example core/di/ts/viewChildren/view_children_howto.ts region='HowTo'}\n   *\n   * ### Another example\n   *\n   * {@example core/di/ts/viewChildren/view_children_example.ts region='Component'}\n   *\n   * @Annotation\n   */\n  (\n    selector: ProviderToken<unknown> | Function | string,\n    opts?: {read?: any; emitDistinctChangesOnly?: boolean},\n  ): any;\n  new (\n    selector: ProviderToken<unknown> | Function | string,\n    opts?: {read?: any; emitDistinctChangesOnly?: boolean},\n  ): ViewChildren;\n}\n\n/**\n * Type of the ViewChildren metadata.\n *\n * @publicApi\n */\nexport type ViewChildren = Query;\n\n/**\n * ViewChildren decorator and metadata.\n *\n * @Annotation\n * @publicApi\n */\nexport const ViewChildren: ViewChildrenDecorator = makePropDecorator(\n  'ViewChildren',\n  (selector?: any, opts: any = {}) => ({\n    selector,\n    first: false,\n    isViewQuery: true,\n    descendants: true,\n    emitDistinctChangesOnly: emitDistinctChangesOnlyDefaultValue,\n    ...opts,\n  }),\n  Query,\n);\n\n/**\n * Type of the ViewChild decorator / constructor function.\n *\n * @see {@link ViewChild}\n * @publicApi\n */\nexport interface ViewChildDecorator {\n  /**\n   * @description\n   * Property decorator that configures a view query.\n   * The change detector looks for the first element or the directive matching the selector\n   * in the view DOM. If the view DOM changes, and a new child matches the selector,\n   * the property is updated.\n   *\n   * **Metadata Properties**:\n   *\n   * * **selector** - The directive type or the name used for querying.\n   * * **read** - Used to read a different token from the queried elements.\n   * * **static** - `true` to resolve query results before change detection runs,\n   * `false` to resolve after change detection. Defaults to `false`.\n   *\n   *\n   * The following selectors are supported.\n   *   * Any class with the `@Component` or `@Directive` decorator\n   *   * A template reference variable as a string (e.g. query `<my-component #cmp></my-component>`\n   * with `@ViewChild('cmp')`)\n   *   * Any provider defined in the child component tree of the current component (e.g.\n   * `@ViewChild(SomeService) someService: SomeService`)\n   *   * Any provider defined through a string token (e.g. `@ViewChild('someToken') someTokenVal:\n   * any`)\n   *   * A `TemplateRef` (e.g. query `<ng-template></ng-template>` with `@ViewChild(TemplateRef)\n   * template;`)\n   *\n   * The following values are supported by `read`:\n   *   * Any class with the `@Component` or `@Directive` decorator\n   *   * Any provider defined on the injector of the component that is matched by the `selector` of\n   * this query\n   *   * Any provider defined through a string token (e.g. `{provide: 'token', useValue: 'val'}`)\n   *   * `TemplateRef`, `ElementRef`, and `ViewContainerRef`\n   *\n   * Difference between dynamic and static queries:\n   *   * Dynamic queries \\(`static: false`\\) - The query resolves before the `ngAfterViewInit()`\n   * callback is called. The result will be updated for changes to your view, such as changes to\n   * `ngIf` and `ngFor` blocks.\n   *   * Static queries \\(`static: true`\\) - The query resolves once\n   * the view has been created, but before change detection runs (before the `ngOnInit()` callback\n   * is called). The result, though, will never be updated to reflect changes to your view, such as\n   * changes to `ngIf` and `ngFor` blocks.\n   *\n   * @usageNotes\n   *\n   * ### Example 1\n   *\n   * {@example core/di/ts/viewChild/view_child_example.ts region='Component'}\n   *\n   * ### Example 2\n   *\n   * {@example core/di/ts/viewChild/view_child_howto.ts region='HowTo'}\n   *\n   * @Annotation\n   */\n  (\n    selector: ProviderToken<unknown> | Function | string,\n    opts?: {read?: any; static?: boolean},\n  ): any;\n  new (\n    selector: ProviderToken<unknown> | Function | string,\n    opts?: {read?: any; static?: boolean},\n  ): ViewChild;\n}\n\n/**\n * Type of the ViewChild metadata.\n *\n * @publicApi\n */\nexport type ViewChild = Query;\n\n/**\n * ViewChild decorator and metadata.\n *\n * @Annotation\n * @publicApi\n */\nexport const ViewChild: ViewChildDecorator = makePropDecorator(\n  'ViewChild',\n  (selector: any, opts: any) => ({\n    selector,\n    first: true,\n    isViewQuery: true,\n    descendants: true,\n    ...opts,\n  }),\n  Query,\n);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * @description Represents the version of Angular\n *\n * @publicApi\n */\nexport class Version {\n  public readonly major: string;\n  public readonly minor: string;\n  public readonly patch: string;\n\n  constructor(public full: string) {\n    const parts = full.split('.');\n    this.major = parts[0];\n    this.minor = parts[1];\n    this.patch = parts.slice(2).join('.');\n  }\n}\n\n/**\n * @publicApi\n */\nexport const VERSION = new Version('20.1.4');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {getCompilerFacade, JitCompilerUsage} from '../compiler/compiler_facade';\nimport {Injector} from '../di/injector';\nimport {Type} from '../interface/type';\nimport {COMPILER_OPTIONS, CompilerOptions} from '../linker/compiler';\nimport {NgModuleFactory} from '../linker/ng_module_factory';\nimport {\n  isComponentResourceResolutionQueueEmpty,\n  resolveComponentResources,\n} from '../metadata/resource_loading';\nimport {assertNgModuleType} from '../render3/assert';\nimport {setJitOptions} from '../render3/jit/jit_options';\nimport {NgModuleFactory as R3NgModuleFactory} from '../render3/ng_module_ref';\n\nexport function compileNgModuleFactory<M>(\n  injector: Injector,\n  options: CompilerOptions,\n  moduleType: Type<M>,\n): Promise<NgModuleFactory<M>> {\n  ngDevMode && assertNgModuleType(moduleType);\n\n  const moduleFactory = new R3NgModuleFactory(moduleType);\n\n  // All of the logic below is irrelevant for AOT-compiled code.\n  if (typeof ngJitMode !== 'undefined' && !ngJitMode) {\n    return Promise.resolve(moduleFactory);\n  }\n\n  const compilerOptions = injector.get(COMPILER_OPTIONS, []).concat(options);\n\n  // Configure the compiler to use the provided options. This call may fail when multiple modules\n  // are bootstrapped with incompatible options, as a component can only be compiled according to\n  // a single set of options.\n  setJitOptions({\n    defaultEncapsulation: _lastDefined(compilerOptions.map((opts) => opts.defaultEncapsulation)),\n    preserveWhitespaces: _lastDefined(compilerOptions.map((opts) => opts.preserveWhitespaces)),\n  });\n\n  if (isComponentResourceResolutionQueueEmpty()) {\n    return Promise.resolve(moduleFactory);\n  }\n\n  const compilerProviders = compilerOptions.flatMap((option) => option.providers ?? []);\n\n  // In case there are no compiler providers, we just return the module factory as\n  // there won't be any resource loader. This can happen with Ivy, because AOT compiled\n  // modules can be still passed through \"bootstrapModule\". In that case we shouldn't\n  // unnecessarily require the JIT compiler.\n  if (compilerProviders.length === 0) {\n    return Promise.resolve(moduleFactory);\n  }\n\n  const compiler = getCompilerFacade({\n    usage: JitCompilerUsage.Decorator,\n    kind: 'NgModule',\n    type: moduleType,\n  });\n  const compilerInjector = Injector.create({providers: compilerProviders});\n  const resourceLoader = compilerInjector.get(compiler.ResourceLoader);\n  // The resource loader can also return a string while the \"resolveComponentResources\"\n  // always expects a promise. Therefore we need to wrap the returned value in a promise.\n  return resolveComponentResources((url) => Promise.resolve(resourceLoader.get(url))).then(\n    () => moduleFactory,\n  );\n}\n\nfunction _lastDefined<T>(args: T[]): T | undefined {\n  for (let i = args.length - 1; i >= 0; i--) {\n    if (args[i] !== undefined) {\n      return args[i];\n    }\n  }\n  return undefined;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {IMAGE_CONFIG, ImageConfig} from './application/application_tokens';\nimport {Injectable} from './di';\nimport {inject} from './di/injector_compatibility';\nimport {formatRuntimeError, RuntimeErrorCode} from './errors';\nimport {OnDestroy} from './interface/lifecycle_hooks';\nimport {getDocument} from './render3/interfaces/document';\n\n// A delay in milliseconds before the scan is run after onLoad, to avoid any\n// potential race conditions with other LCP-related functions. This delay\n// happens outside of the main JavaScript execution and will only effect the timing\n// on when the warning becomes visible in the console.\nconst SCAN_DELAY = 200;\n\nconst OVERSIZED_IMAGE_TOLERANCE = 1200;\n\n@Injectable({providedIn: 'root'})\nexport class ImagePerformanceWarning implements OnDestroy {\n  // Map of full image URLs -> original `ngSrc` values.\n  private window: Window | null = null;\n  private observer: PerformanceObserver | null = null;\n  private options: ImageConfig = inject(IMAGE_CONFIG);\n  private lcpImageUrl?: string;\n\n  public start() {\n    if (\n      (typeof ngServerMode !== 'undefined' && ngServerMode) ||\n      typeof PerformanceObserver === 'undefined' ||\n      (this.options?.disableImageSizeWarning && this.options?.disableImageLazyLoadWarning)\n    ) {\n      return;\n    }\n    this.observer = this.initPerformanceObserver();\n    const doc = getDocument();\n    const win = doc.defaultView;\n    if (win) {\n      this.window = win;\n      // Wait to avoid race conditions where LCP image triggers\n      // load event before it's recorded by the performance observer\n      const waitToScan = () => {\n        setTimeout(this.scanImages.bind(this), SCAN_DELAY);\n      };\n      const setup = () => {\n        // Consider the case when the application is created and destroyed multiple times.\n        // Typically, applications are created instantly once the page is loaded, and the\n        // `window.load` listener is always triggered. However, the `window.load` event will never\n        // be fired if the page is loaded, and the application is created later. Checking for\n        // `readyState` is the easiest way to determine whether the page has been loaded or not.\n        if (doc.readyState === 'complete') {\n          waitToScan();\n        } else {\n          this.window?.addEventListener('load', waitToScan, {once: true});\n        }\n      };\n      // Angular doesn't have to run change detection whenever any asynchronous tasks are invoked in\n      // the scope of this functionality.\n      if (typeof Zone !== 'undefined') {\n        Zone.root.run(() => setup());\n      } else {\n        setup();\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    this.observer?.disconnect();\n  }\n\n  private initPerformanceObserver(): PerformanceObserver | null {\n    if (typeof PerformanceObserver === 'undefined') {\n      return null;\n    }\n    const observer = new PerformanceObserver((entryList) => {\n      const entries = entryList.getEntries();\n      if (entries.length === 0) return;\n      // We use the latest entry produced by the `PerformanceObserver` as the best\n      // signal on which element is actually an LCP one. As an example, the first image to load on\n      // a page, by virtue of being the only thing on the page so far, is often a LCP candidate\n      // and gets reported by PerformanceObserver, but isn't necessarily the LCP element.\n      const lcpElement = entries[entries.length - 1];\n\n      // Cast to `any` due to missing `element` on the `LargestContentfulPaint` type of entry.\n      // See https://developer.mozilla.org/en-US/docs/Web/API/LargestContentfulPaint\n      const imgSrc = (lcpElement as any).element?.src ?? '';\n\n      // Exclude `data:` and `blob:` URLs, since they are fetched resources.\n      if (imgSrc.startsWith('data:') || imgSrc.startsWith('blob:')) return;\n      this.lcpImageUrl = imgSrc;\n    });\n    observer.observe({type: 'largest-contentful-paint', buffered: true});\n    return observer;\n  }\n\n  private scanImages(): void {\n    const images = getDocument().querySelectorAll('img');\n    let lcpElementFound,\n      lcpElementLoadedCorrectly = false;\n    // Important: do not refactor this to use `images.forEach` or\n    // `for (const ... of ...)`, because images might be a custom internal\n    // data structure — such as a lazily evaluated query result in Domino.\n    // (This naturally would never be a case in any browser).\n    for (let index = 0; index < images.length; index++) {\n      const image = images[index];\n\n      if (!image) {\n        continue;\n      }\n\n      if (!this.options?.disableImageSizeWarning) {\n        // Image elements using the NgOptimizedImage directive are excluded,\n        // as that directive has its own version of this check.\n        if (!image.getAttribute('ng-img') && this.isOversized(image)) {\n          logOversizedImageWarning(image.src);\n        }\n      }\n      if (!this.options?.disableImageLazyLoadWarning && this.lcpImageUrl) {\n        if (image.src === this.lcpImageUrl) {\n          lcpElementFound = true;\n          if (image.loading !== 'lazy' || image.getAttribute('ng-img')) {\n            // This variable is set to true and never goes back to false to account\n            // for the case where multiple images have the same src url, and some\n            // have lazy loading while others don't.\n            // Also ignore NgOptimizedImage because there's a different warning for that.\n            lcpElementLoadedCorrectly = true;\n          }\n        }\n      }\n    }\n    if (\n      lcpElementFound &&\n      !lcpElementLoadedCorrectly &&\n      this.lcpImageUrl &&\n      !this.options?.disableImageLazyLoadWarning\n    ) {\n      logLazyLCPWarning(this.lcpImageUrl);\n    }\n  }\n\n  private isOversized(image: HTMLImageElement): boolean {\n    if (!this.window) {\n      return false;\n    }\n\n    // The `isOversized` check may not be applicable or may require adjustments\n    // for several types of image formats or scenarios. Currently, we specify only\n    // `svg`, but this may also include `gif` since their quality isn’t tied to\n    // dimensions in the same way as raster images.\n    const nonOversizedImageExtentions = [\n      // SVG images are vector-based, which means they can scale\n      // to any size without losing quality.\n      '.svg',\n    ];\n\n    // Convert it to lowercase because this may have uppercase\n    // extensions, such as `IMAGE.SVG`.\n    // We fallback to an empty string because `src` may be `undefined`\n    // if it is explicitly set to `null` by some third-party code\n    // (e.g., `image.src = null`).\n    const imageSource = (image.src || '').toLowerCase();\n\n    if (nonOversizedImageExtentions.some((extension) => imageSource.endsWith(extension))) {\n      return false;\n    }\n\n    const computedStyle = this.window.getComputedStyle(image);\n    let renderedWidth = parseFloat(computedStyle.getPropertyValue('width'));\n    let renderedHeight = parseFloat(computedStyle.getPropertyValue('height'));\n    const boxSizing = computedStyle.getPropertyValue('box-sizing');\n    const objectFit = computedStyle.getPropertyValue('object-fit');\n\n    if (objectFit === `cover`) {\n      // Object fit cover may indicate a use case such as a sprite sheet where\n      // this warning does not apply.\n      return false;\n    }\n\n    if (boxSizing === 'border-box') {\n      // If the image `box-sizing` is set to `border-box`, we adjust the rendered\n      // dimensions by subtracting padding values.\n      const paddingTop = computedStyle.getPropertyValue('padding-top');\n      const paddingRight = computedStyle.getPropertyValue('padding-right');\n      const paddingBottom = computedStyle.getPropertyValue('padding-bottom');\n      const paddingLeft = computedStyle.getPropertyValue('padding-left');\n      renderedWidth -= parseFloat(paddingRight) + parseFloat(paddingLeft);\n      renderedHeight -= parseFloat(paddingTop) + parseFloat(paddingBottom);\n    }\n\n    const intrinsicWidth = image.naturalWidth;\n    const intrinsicHeight = image.naturalHeight;\n\n    const recommendedWidth = this.window.devicePixelRatio * renderedWidth;\n    const recommendedHeight = this.window.devicePixelRatio * renderedHeight;\n    const oversizedWidth = intrinsicWidth - recommendedWidth >= OVERSIZED_IMAGE_TOLERANCE;\n    const oversizedHeight = intrinsicHeight - recommendedHeight >= OVERSIZED_IMAGE_TOLERANCE;\n    return oversizedWidth || oversizedHeight;\n  }\n}\n\nfunction logLazyLCPWarning(src: string) {\n  console.warn(\n    formatRuntimeError(\n      RuntimeErrorCode.IMAGE_PERFORMANCE_WARNING,\n      `An image with src ${src} is the Largest Contentful Paint (LCP) element ` +\n        `but was given a \"loading\" value of \"lazy\", which can negatively impact ` +\n        `application loading performance. This warning can be addressed by ` +\n        `changing the loading value of the LCP image to \"eager\", or by using the ` +\n        `NgOptimizedImage directive's prioritization utilities. For more ` +\n        `information about addressing or disabling this warning, see ` +\n        `https://angular.dev/errors/NG0913`,\n    ),\n  );\n}\n\nfunction logOversizedImageWarning(src: string) {\n  console.warn(\n    formatRuntimeError(\n      RuntimeErrorCode.IMAGE_PERFORMANCE_WARNING,\n      `An image with src ${src} has intrinsic file dimensions much larger than its ` +\n        `rendered size. This can negatively impact application loading performance. ` +\n        `For more information about addressing or disabling this warning, see ` +\n        `https://angular.dev/errors/NG0913`,\n    ),\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from '../di';\n\n/**\n * Internal token that allows to register extra callbacks that should be invoked during the\n * `PlatformRef.destroy` operation. This token is needed to avoid a direct reference to the\n * `PlatformRef` class (i.e. register the callback via `PlatformRef.onDestroy`), thus making the\n * entire class tree-shakeable.\n */\nexport const PLATFORM_DESTROY_LISTENERS = new InjectionToken<Set<VoidFunction>>(\n  ngDevMode ? 'PlatformDestroyListeners' : '',\n);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {Subscription} from 'rxjs';\n\nimport {PROVIDED_NG_ZONE} from '../change_detection/scheduling/ng_zone_scheduling';\nimport {R3Injector} from '../di/r3_injector';\nimport {INTERNAL_APPLICATION_ERROR_HANDLER} from '../error_handler';\nimport {RuntimeError, RuntimeErrorCode} from '../errors';\nimport {DEFAULT_LOCALE_ID} from '../i18n/localization';\nimport {LOCALE_ID} from '../i18n/tokens';\nimport {ImagePerformanceWarning} from '../image_performance_warning';\nimport {Type} from '../interface/type';\nimport {PLATFORM_DESTROY_LISTENERS} from './platform_destroy_listeners';\nimport {setLocaleId} from '../render3/i18n/i18n_locale_id';\nimport {NgZone} from '../zone/ng_zone';\n\nimport {ApplicationInitStatus} from '../application/application_init';\nimport {ApplicationRef, remove} from '../application/application_ref';\nimport {PROVIDED_ZONELESS} from '../change_detection/scheduling/zoneless_scheduling';\nimport {InjectionToken, Injector} from '../di';\nimport {InternalNgModuleRef, NgModuleRef} from '../linker/ng_module_factory';\nimport {stringify} from '../util/stringify';\nimport {isPromise} from '../util/lang';\nimport {PendingTasksInternal} from '../pending_tasks';\n\n/**\n * InjectionToken to control root component bootstrap behavior.\n *\n * This token is primarily used in Angular's server-side rendering (SSR) scenarios,\n * particularly by the `@angular/ssr` package, to manage whether the root component\n * should be bootstrapped during the application initialization process.\n *\n * ## Purpose:\n * During SSR route extraction, setting this token to `false` prevents Angular from\n * bootstrapping the root component. This avoids unnecessary component rendering,\n * enabling route extraction without requiring additional APIs or triggering\n * component logic.\n *\n * ## Behavior:\n * - **`false`**: Prevents the root component from being bootstrapped.\n * - **`true`** (default): Proceeds with the normal root component bootstrap process.\n *\n * This mechanism ensures SSR can efficiently separate route extraction logic\n * from component rendering.\n */\nexport const ENABLE_ROOT_COMPONENT_BOOTSTRAP = new InjectionToken<boolean>(\n  ngDevMode ? 'ENABLE_ROOT_COMPONENT_BOOTSTRAP' : '',\n);\n\nexport interface BootstrapConfig {\n  platformInjector: Injector;\n}\n\nexport interface ModuleBootstrapConfig<M> extends BootstrapConfig {\n  moduleRef: InternalNgModuleRef<M>;\n  allPlatformModules: NgModuleRef<unknown>[];\n}\n\nexport interface ApplicationBootstrapConfig extends BootstrapConfig {\n  r3Injector: R3Injector;\n  rootComponent: Type<unknown> | undefined;\n}\n\nfunction isApplicationBootstrapConfig(\n  config: ApplicationBootstrapConfig | ModuleBootstrapConfig<unknown>,\n): config is ApplicationBootstrapConfig {\n  return !(config as ModuleBootstrapConfig<unknown>).moduleRef;\n}\n\nexport function bootstrap<M>(\n  moduleBootstrapConfig: ModuleBootstrapConfig<M>,\n): Promise<NgModuleRef<M>>;\nexport function bootstrap(\n  applicationBootstrapConfig: ApplicationBootstrapConfig,\n): Promise<ApplicationRef>;\nexport function bootstrap<M>(\n  config: ModuleBootstrapConfig<M> | ApplicationBootstrapConfig,\n): Promise<ApplicationRef> | Promise<NgModuleRef<M>> {\n  const envInjector = isApplicationBootstrapConfig(config)\n    ? config.r3Injector\n    : config.moduleRef.injector;\n  const ngZone = envInjector.get(NgZone);\n  return ngZone.run(() => {\n    if (isApplicationBootstrapConfig(config)) {\n      config.r3Injector.resolveInjectorInitializers();\n    } else {\n      config.moduleRef.resolveInjectorInitializers();\n    }\n    const exceptionHandler = envInjector.get(INTERNAL_APPLICATION_ERROR_HANDLER);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (envInjector.get(PROVIDED_ZONELESS) && envInjector.get(PROVIDED_NG_ZONE)) {\n        throw new RuntimeError(\n          RuntimeErrorCode.PROVIDED_BOTH_ZONE_AND_ZONELESS,\n          'Invalid change detection configuration: ' +\n            'provideZoneChangeDetection and provideZonelessChangeDetection cannot be used together.',\n        );\n      }\n    }\n\n    let onErrorSubscription: Subscription;\n    ngZone.runOutsideAngular(() => {\n      onErrorSubscription = ngZone.onError.subscribe({\n        next: exceptionHandler,\n      });\n    });\n\n    // If the whole platform is destroyed, invoke the `destroy` method\n    // for all bootstrapped applications as well.\n    if (isApplicationBootstrapConfig(config)) {\n      const destroyListener = () => envInjector.destroy();\n      const onPlatformDestroyListeners = config.platformInjector.get(PLATFORM_DESTROY_LISTENERS);\n      onPlatformDestroyListeners.add(destroyListener);\n\n      envInjector.onDestroy(() => {\n        onErrorSubscription.unsubscribe();\n        onPlatformDestroyListeners.delete(destroyListener);\n      });\n    } else {\n      const destroyListener = () => config.moduleRef.destroy();\n      const onPlatformDestroyListeners = config.platformInjector.get(PLATFORM_DESTROY_LISTENERS);\n      onPlatformDestroyListeners.add(destroyListener);\n\n      config.moduleRef.onDestroy(() => {\n        remove(config.allPlatformModules, config.moduleRef);\n        onErrorSubscription.unsubscribe();\n        onPlatformDestroyListeners.delete(destroyListener);\n      });\n    }\n\n    return _callAndReportToErrorHandler(exceptionHandler, ngZone, () => {\n      const pendingTasks = envInjector.get(PendingTasksInternal);\n      const taskId = pendingTasks.add();\n      const initStatus = envInjector.get(ApplicationInitStatus);\n      initStatus.runInitializers();\n\n      return initStatus.donePromise\n        .then(() => {\n          // If the `LOCALE_ID` provider is defined at bootstrap then we set the value for ivy\n          const localeId = envInjector.get(LOCALE_ID, DEFAULT_LOCALE_ID);\n          setLocaleId(localeId || DEFAULT_LOCALE_ID);\n\n          const enableRootComponentBoostrap = envInjector.get(\n            ENABLE_ROOT_COMPONENT_BOOTSTRAP,\n            true,\n          );\n          if (!enableRootComponentBoostrap) {\n            if (isApplicationBootstrapConfig(config)) {\n              return envInjector.get(ApplicationRef);\n            }\n\n            config.allPlatformModules.push(config.moduleRef);\n            return config.moduleRef;\n          }\n\n          if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const imagePerformanceService = envInjector.get(ImagePerformanceWarning);\n            imagePerformanceService.start();\n          }\n\n          if (isApplicationBootstrapConfig(config)) {\n            const appRef = envInjector.get(ApplicationRef);\n            if (config.rootComponent !== undefined) {\n              appRef.bootstrap(config.rootComponent);\n            }\n            return appRef;\n          } else {\n            moduleBootstrapImpl?.(config.moduleRef, config.allPlatformModules);\n            return config.moduleRef;\n          }\n        })\n        .finally(() => void pendingTasks.remove(taskId));\n    });\n  });\n}\n\n/**\n * Having a separate symbol for the module boostrap implementation allows us to\n * tree shake the module based boostrap implementation in standalone apps.\n */\nlet moduleBootstrapImpl: undefined | typeof _moduleDoBootstrap;\n\n/**\n * Set the implementation of the module based bootstrap.\n */\nexport function setModuleBootstrapImpl() {\n  moduleBootstrapImpl = _moduleDoBootstrap;\n}\n\nfunction _moduleDoBootstrap(\n  moduleRef: InternalNgModuleRef<any>,\n  allPlatformModules: NgModuleRef<unknown>[],\n): void {\n  const appRef = moduleRef.injector.get(ApplicationRef);\n  if (moduleRef._bootstrapComponents.length > 0) {\n    moduleRef._bootstrapComponents.forEach((f) => appRef.bootstrap(f));\n  } else if (moduleRef.instance.ngDoBootstrap) {\n    moduleRef.instance.ngDoBootstrap(appRef);\n  } else {\n    throw new RuntimeError(\n      RuntimeErrorCode.BOOTSTRAP_COMPONENTS_NOT_FOUND,\n      ngDevMode &&\n        `The module ${stringify(moduleRef.instance.constructor)} was bootstrapped, ` +\n          `but it does not declare \"@NgModule.bootstrap\" components nor a \"ngDoBootstrap\" method. ` +\n          `Please define one of these.`,\n    );\n  }\n  allPlatformModules.push(moduleRef);\n}\n\nfunction _callAndReportToErrorHandler(\n  errorHandler: (e: unknown) => void,\n  ngZone: NgZone,\n  callback: () => any,\n): any {\n  try {\n    const result = callback();\n    if (isPromise(result)) {\n      return result.catch((e: any) => {\n        ngZone.runOutsideAngular(() => errorHandler(e));\n        // rethrow as the exception handler might not do it\n        throw e;\n      });\n    }\n\n    return result;\n  } catch (e) {\n    ngZone.runOutsideAngular(() => errorHandler(e));\n    // rethrow as the exception handler might not do it\n    throw e;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {compileNgModuleFactory} from '../application/application_ngmodule_factory_compiler';\nimport {BootstrapOptions, optionsReducer} from '../application/application_ref';\nimport {\n  getNgZoneOptions,\n  internalProvideZoneChangeDetection,\n} from '../change_detection/scheduling/ng_zone_scheduling';\nimport {ChangeDetectionScheduler} from '../change_detection/scheduling/zoneless_scheduling';\nimport {ChangeDetectionSchedulerImpl} from '../change_detection/scheduling/zoneless_scheduling_impl';\nimport {Injectable, Injector} from '../di';\nimport {errorHandlerEnvironmentInitializer} from '../error_handler';\nimport {RuntimeError, RuntimeErrorCode} from '../errors';\nimport {Type} from '../interface/type';\nimport {CompilerOptions} from '../linker';\nimport {NgModuleFactory, NgModuleRef} from '../linker/ng_module_factory';\nimport {createNgModuleRefWithProviders} from '../render3/ng_module_ref';\nimport {getNgZone} from '../zone/ng_zone';\nimport {bootstrap, setModuleBootstrapImpl} from './bootstrap';\nimport {PLATFORM_DESTROY_LISTENERS} from './platform_destroy_listeners';\n\n/**\n * The Angular platform is the entry point for Angular on a web page.\n * Each page has exactly one platform. Services (such as reflection) which are common\n * to every Angular application running on the page are bound in its scope.\n * A page's platform is initialized implicitly when a platform is created using a platform\n * factory such as `PlatformBrowser`, or explicitly by calling the `createPlatform()` function.\n *\n * @publicApi\n */\n@Injectable({providedIn: 'platform'})\nexport class PlatformRef {\n  private _modules: NgModuleRef<any>[] = [];\n  private _destroyListeners: Array<() => void> = [];\n  private _destroyed: boolean = false;\n\n  /** @internal */\n  constructor(private _injector: Injector) {}\n\n  /**\n   * Creates an instance of an `@NgModule` for the given platform.\n   *\n   * @deprecated Passing NgModule factories as the `PlatformRef.bootstrapModuleFactory` function\n   *     argument is deprecated. Use the `PlatformRef.bootstrapModule` API instead.\n   */\n  bootstrapModuleFactory<M>(\n    moduleFactory: NgModuleFactory<M>,\n    options?: BootstrapOptions,\n  ): Promise<NgModuleRef<M>> {\n    const scheduleInRootZone = (options as any)?.scheduleInRootZone;\n    const ngZoneFactory = () =>\n      getNgZone(options?.ngZone, {\n        ...getNgZoneOptions({\n          eventCoalescing: options?.ngZoneEventCoalescing,\n          runCoalescing: options?.ngZoneRunCoalescing,\n        }),\n        scheduleInRootZone,\n      });\n    const ignoreChangesOutsideZone = options?.ignoreChangesOutsideZone;\n    const allAppProviders = [\n      internalProvideZoneChangeDetection({\n        ngZoneFactory,\n        ignoreChangesOutsideZone,\n      }),\n      {provide: ChangeDetectionScheduler, useExisting: ChangeDetectionSchedulerImpl},\n      errorHandlerEnvironmentInitializer,\n    ];\n    const moduleRef = createNgModuleRefWithProviders(\n      moduleFactory.moduleType,\n      this.injector,\n      allAppProviders,\n    );\n\n    setModuleBootstrapImpl();\n    return bootstrap({\n      moduleRef,\n      allPlatformModules: this._modules,\n      platformInjector: this.injector,\n    });\n  }\n\n  /**\n   * Creates an instance of an `@NgModule` for a given platform.\n   *\n   * @usageNotes\n   * ### Simple Example\n   *\n   * ```ts\n   * @NgModule({\n   *   imports: [BrowserModule]\n   * })\n   * class MyModule {}\n   *\n   * let moduleRef = platformBrowser().bootstrapModule(MyModule);\n   * ```\n   *\n   */\n  bootstrapModule<M>(\n    moduleType: Type<M>,\n    compilerOptions:\n      | (CompilerOptions & BootstrapOptions)\n      | Array<CompilerOptions & BootstrapOptions> = [],\n  ): Promise<NgModuleRef<M>> {\n    const options = optionsReducer({}, compilerOptions);\n    setModuleBootstrapImpl();\n    return compileNgModuleFactory(this.injector, options, moduleType).then((moduleFactory) =>\n      this.bootstrapModuleFactory(moduleFactory, options),\n    );\n  }\n\n  /**\n   * Registers a listener to be called when the platform is destroyed.\n   */\n  onDestroy(callback: () => void): void {\n    this._destroyListeners.push(callback);\n  }\n\n  /**\n   * Retrieves the platform {@link Injector}, which is the parent injector for\n   * every Angular application on the page and provides singleton providers.\n   */\n  get injector(): Injector {\n    return this._injector;\n  }\n\n  /**\n   * Destroys the current Angular platform and all Angular applications on the page.\n   * Destroys all modules and listeners registered with the platform.\n   */\n  destroy() {\n    if (this._destroyed) {\n      throw new RuntimeError(\n        RuntimeErrorCode.PLATFORM_ALREADY_DESTROYED,\n        ngDevMode && 'The platform has already been destroyed!',\n      );\n    }\n    this._modules.slice().forEach((module) => module.destroy());\n    this._destroyListeners.forEach((listener) => listener());\n\n    const destroyListeners = this._injector.get(PLATFORM_DESTROY_LISTENERS, null);\n    if (destroyListeners) {\n      destroyListeners.forEach((listener) => listener());\n      destroyListeners.clear();\n    }\n\n    this._destroyed = true;\n  }\n\n  /**\n   * Indicates whether this instance was destroyed.\n   */\n  get destroyed() {\n    return this._destroyed;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  publishDefaultGlobalUtils,\n  publishSignalConfiguration,\n} from '../application/application_ref';\nimport {PLATFORM_INITIALIZER} from '../application/application_tokens';\nimport {\n  EnvironmentProviders,\n  InjectionToken,\n  Injector,\n  makeEnvironmentProviders,\n  runInInjectionContext,\n  StaticProvider,\n} from '../di';\nimport {INJECTOR_SCOPE} from '../di/scope';\nimport {RuntimeError, RuntimeErrorCode} from '../errors';\n\nimport {PlatformRef} from './platform_ref';\nimport {PLATFORM_DESTROY_LISTENERS} from './platform_destroy_listeners';\n\nlet _platformInjector: Injector | null = null;\n\n/**\n * Internal token to indicate whether having multiple bootstrapped platform should be allowed (only\n * one bootstrapped platform is allowed by default). This token helps to support SSR scenarios.\n */\nexport const ALLOW_MULTIPLE_PLATFORMS = new InjectionToken<boolean>(\n  ngDevMode ? 'AllowMultipleToken' : '',\n);\n\n/**\n * Creates a platform.\n * Platforms must be created on launch using this function.\n *\n * @publicApi\n */\nexport function createPlatform(injector: Injector): PlatformRef {\n  if (_platformInjector && !_platformInjector.get(ALLOW_MULTIPLE_PLATFORMS, false)) {\n    throw new RuntimeError(\n      RuntimeErrorCode.MULTIPLE_PLATFORMS,\n      ngDevMode && 'There can be only one platform. Destroy the previous one to create a new one.',\n    );\n  }\n  publishDefaultGlobalUtils();\n  publishSignalConfiguration();\n  _platformInjector = injector;\n  const platform = injector.get(PlatformRef);\n  runPlatformInitializers(injector);\n  return platform;\n}\n\n/**\n * Creates a factory for a platform. Can be used to provide or override `Providers` specific to\n * your application's runtime needs, such as `PLATFORM_INITIALIZER` and `PLATFORM_ID`.\n * @param parentPlatformFactory Another platform factory to modify. Allows you to compose factories\n * to build up configurations that might be required by different libraries or parts of the\n * application.\n * @param name Identifies the new platform factory.\n * @param providers A set of dependency providers for platforms created with the new factory.\n *\n * @publicApi\n */\nexport function createPlatformFactory(\n  parentPlatformFactory: ((extraProviders?: StaticProvider[]) => PlatformRef) | null,\n  name: string,\n  providers: StaticProvider[] = [],\n): (extraProviders?: StaticProvider[]) => PlatformRef {\n  const desc = `Platform: ${name}`;\n  const marker = new InjectionToken(desc);\n  return (extraProviders: StaticProvider[] = []) => {\n    let platform = getPlatform();\n    if (!platform || platform.injector.get(ALLOW_MULTIPLE_PLATFORMS, false)) {\n      const platformProviders: StaticProvider[] = [\n        ...providers,\n        ...extraProviders,\n        {provide: marker, useValue: true},\n      ];\n      if (parentPlatformFactory) {\n        parentPlatformFactory(platformProviders);\n      } else {\n        createPlatform(createPlatformInjector(platformProviders, desc));\n      }\n    }\n    return assertPlatform(marker);\n  };\n}\n\n/**\n * Helper function to create an instance of a platform injector (that maintains the 'platform'\n * scope).\n */\nfunction createPlatformInjector(providers: StaticProvider[] = [], name?: string): Injector {\n  return Injector.create({\n    name,\n    providers: [\n      {provide: INJECTOR_SCOPE, useValue: 'platform'},\n      {provide: PLATFORM_DESTROY_LISTENERS, useValue: new Set([() => (_platformInjector = null)])},\n      ...providers,\n    ],\n  });\n}\n\n/**\n * Checks that there is currently a platform that contains the given token as a provider.\n *\n * @publicApi\n */\nexport function assertPlatform(requiredToken: any): PlatformRef {\n  const platform = getPlatform();\n\n  if (!platform) {\n    throw new RuntimeError(RuntimeErrorCode.PLATFORM_NOT_FOUND, ngDevMode && 'No platform exists!');\n  }\n\n  if (\n    (typeof ngDevMode === 'undefined' || ngDevMode) &&\n    !platform.injector.get(requiredToken, null)\n  ) {\n    throw new RuntimeError(\n      RuntimeErrorCode.MULTIPLE_PLATFORMS,\n      'A platform with a different configuration has been created. Please destroy it first.',\n    );\n  }\n\n  return platform;\n}\n\n/**\n * Returns the current platform.\n *\n * @publicApi\n */\nexport function getPlatform(): PlatformRef | null {\n  return _platformInjector?.get(PlatformRef) ?? null;\n}\n\n/**\n * Destroys the current Angular platform and all Angular applications on the page.\n * Destroys all modules and listeners registered with the platform.\n *\n * @publicApi\n */\nexport function destroyPlatform(): void {\n  getPlatform()?.destroy();\n}\n\n/**\n * The goal of this function is to bootstrap a platform injector,\n * but avoid referencing `PlatformRef` class.\n * This function is needed for bootstrapping a Standalone Component.\n */\nexport function createOrReusePlatformInjector(providers: StaticProvider[] = []): Injector {\n  // If a platform injector already exists, it means that the platform\n  // is already bootstrapped and no additional actions are required.\n  if (_platformInjector) return _platformInjector;\n\n  publishDefaultGlobalUtils();\n  // Otherwise, setup a new platform injector and run platform initializers.\n  const injector = createPlatformInjector(providers);\n  _platformInjector = injector;\n  publishSignalConfiguration();\n  runPlatformInitializers(injector);\n  return injector;\n}\n\n/**\n * @description\n * This function is used to provide initialization functions that will be executed upon\n * initialization of the platform injector.\n *\n * Note that the provided initializer is run in the injection context.\n *\n * Previously, this was achieved using the `PLATFORM_INITIALIZER` token which is now deprecated.\n *\n * @see {@link PLATFORM_INITIALIZER}\n *\n * @publicApi\n */\nexport function providePlatformInitializer(initializerFn: () => void): EnvironmentProviders {\n  return makeEnvironmentProviders([\n    {\n      provide: PLATFORM_INITIALIZER,\n      useValue: initializerFn,\n      multi: true,\n    },\n  ]);\n}\n\nfunction runPlatformInitializers(injector: Injector): void {\n  const inits = injector.get(PLATFORM_INITIALIZER, null);\n  runInInjectionContext(injector, () => {\n    inits?.forEach((init) => init());\n  });\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ApplicationRef} from '../../application/application_ref';\nimport {ChangeDetectionSchedulerImpl} from './zoneless_scheduling_impl';\nimport {inject} from '../../di/injector_compatibility';\nimport {provideEnvironmentInitializer} from '../../di/provider_collection';\nimport {NgZone} from '../../zone/ng_zone';\n\nimport {ErrorHandler} from '../../error_handler';\nimport {checkNoChangesInternal} from '../../render3/instructions/change_detection';\n\nexport function exhaustiveCheckNoChangesInterval(interval: number) {\n  return provideEnvironmentInitializer(() => {\n    const applicationRef = inject(ApplicationRef);\n    const errorHandler = inject(ErrorHandler);\n    const scheduler = inject(ChangeDetectionSchedulerImpl);\n    const ngZone = inject(NgZone);\n\n    function scheduleCheckNoChanges() {\n      ngZone.runOutsideAngular(() => {\n        setTimeout(() => {\n          if (applicationRef.destroyed) {\n            return;\n          }\n          if (scheduler.pendingRenderTaskId || scheduler.runningTick) {\n            scheduleCheckNoChanges();\n            return;\n          }\n\n          for (const view of applicationRef.allViews) {\n            try {\n              checkNoChangesInternal(view._lView, true /** exhaustive */);\n            } catch (e) {\n              errorHandler.handleError(e);\n            }\n          }\n\n          scheduleCheckNoChanges();\n        }, interval);\n      });\n    }\n    scheduleCheckNoChanges();\n  });\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {EnvironmentProviders, makeEnvironmentProviders} from '../di';\nimport {UseExhaustiveCheckNoChanges} from './use_exhaustive_check_no_changes';\nimport {exhaustiveCheckNoChangesInterval} from './scheduling/exhaustive_check_no_changes';\n\n/**\n * Used to disable exhaustive checks when verifying no expressions changed after they were checked.\n *\n * This means that `OnPush` components that are not marked for check will not be checked.\n * This behavior is the current default behavior in Angular. When running change detection\n * on a view tree, views marked for check are refreshed and the flag to check it is removed.\n * When Angular checks views a second time to ensure nothing has changed, `OnPush` components\n * will no longer be marked and not be checked.\n *\n * @developerPreview 20.0\n */\nexport function provideCheckNoChangesConfig(options: {exhaustive: false}): EnvironmentProviders;\n/**\n * - `interval` will periodically run `checkNoChanges` on application views. This can be useful\n *   in zoneless applications to periodically ensure no changes have been made without notifying\n *   Angular that templates need to be refreshed.\n * - The exhaustive option will treat all application views as if they were `ChangeDetectionStrategy.Default` when verifying\n *   no expressions have changed. All views attached to `ApplicationRef` and all the descendants of\n *   those views will be checked for changes (excluding those subtrees which are detached via `ChangeDetectorRef.detach()`).\n *   This is useful because the check that runs after regular change detection does not work for components using `ChangeDetectionStrategy.OnPush`.\n *   This check is will surface any existing errors hidden by `OnPush` components.\n *\n * @developerPreview 20.0\n */\nexport function provideCheckNoChangesConfig(options: {\n  interval?: number;\n  exhaustive: true;\n}): EnvironmentProviders;\nexport function provideCheckNoChangesConfig(options: {\n  interval?: number;\n  exhaustive: boolean;\n}): EnvironmentProviders {\n  return makeEnvironmentProviders(\n    typeof ngDevMode === 'undefined' || ngDevMode\n      ? [\n          {\n            provide: UseExhaustiveCheckNoChanges,\n            useValue: options.exhaustive,\n          },\n          options?.interval !== undefined ? exhaustiveCheckNoChangesInterval(options.interval) : [],\n        ]\n      : [],\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {global} from './global';\n\n/**\n * Returns whether Angular is in development mode.\n *\n * By default, this is true, unless `enableProdMode` is invoked prior to calling this method or the\n * application is built using the Angular CLI with the `optimization` option.\n * @see {@link /cli/build ng build}\n *\n * @publicApi\n */\nexport function isDevMode(): boolean {\n  return typeof ngDevMode === 'undefined' || !!ngDevMode;\n}\n\n/**\n * Disable Angular's development mode, which turns off assertions and other\n * checks within the framework.\n *\n * One important assertion this disables verifies that a change detection pass\n * does not result in additional changes to any bindings (also known as\n * unidirectional data flow).\n *\n * Using this method is discouraged as the Angular CLI will set production mode when using the\n * `optimization` option.\n * @see {@link /cli/build ng build}\n *\n * @publicApi\n */\nexport function enableProdMode(): void {\n  // The below check is there so when ngDevMode is set via terser\n  // `global['ngDevMode'] = false;` is also dropped.\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    global['ngDevMode'] = false;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Type} from '../interface/type';\nimport {NgModuleFactory as R3NgModuleFactory} from '../render3/ng_module_ref';\n\nimport {NgModuleFactory} from './ng_module_factory';\nimport {getRegisteredNgModuleType} from './ng_module_registration';\n\n/**\n * Returns the NgModuleFactory with the given id (specified using [@NgModule.id\n * field](api/core/NgModule#id)), if it exists and has been loaded. Factories for NgModules that do\n * not specify an `id` cannot be retrieved. Throws if an NgModule cannot be found.\n * @publicApi\n * @deprecated Use `getNgModuleById` instead.\n */\nexport function getModuleFactory(id: string): NgModuleFactory<any> {\n  const type = getRegisteredNgModuleType(id);\n  if (!type) throw noModuleError(id);\n  return new R3NgModuleFactory(type);\n}\n\n/**\n * Returns the NgModule class with the given id (specified using [@NgModule.id\n * field](api/core/NgModule#id)), if it exists and has been loaded. Classes for NgModules that do\n * not specify an `id` cannot be retrieved. Throws if an NgModule cannot be found.\n * @publicApi\n */\nexport function getNgModuleById<T>(id: string): Type<T> {\n  const type = getRegisteredNgModuleType(id);\n  if (!type) throw noModuleError(id);\n  return type;\n}\n\nfunction noModuleError(id: string): Error {\n  return new Error(`No module with ID ${id} loaded`);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InternalInjectFlags} from '../di/interface/injector';\nimport {TNode, TNodeType} from '../render3/interfaces/node';\nimport {isComponentHost} from '../render3/interfaces/type_checks';\nimport {DECLARATION_COMPONENT_VIEW, LView} from '../render3/interfaces/view';\nimport {getCurrentTNode, getLView} from '../render3/state';\nimport {getComponentLViewByIndex} from '../render3/util/view_utils';\nimport {ViewRef} from '../render3/view_ref';\n\n/**\n * Base class that provides change detection functionality.\n * A change-detection tree collects all views that are to be checked for changes.\n * Use the methods to add and remove views from the tree, initiate change-detection,\n * and explicitly mark views as _dirty_, meaning that they have changed and need to be re-rendered.\n *\n * @see [Using change detection hooks](guide/components/lifecycle#using-change-detection-hooks)\n * @see [Defining custom change detection](guide/components/lifecycle#defining-custom-change-detection)\n *\n * @usageNotes\n *\n * The following examples demonstrate how to modify default change-detection behavior\n * to perform explicit detection when needed.\n *\n * ### Use `markForCheck()` with `CheckOnce` strategy\n *\n * The following example sets the `OnPush` change-detection strategy for a component\n * (`CheckOnce`, rather than the default `CheckAlways`), then forces a second check\n * after an interval.\n *\n * {@example core/ts/change_detect/change-detection.ts region='mark-for-check'}\n *\n * ### Detach change detector to limit how often check occurs\n *\n * The following example defines a component with a large list of read-only data\n * that is expected to change constantly, many times per second.\n * To improve performance, we want to check and update the list\n * less often than the changes actually occur. To do that, we detach\n * the component's change detector and perform an explicit local check every five seconds.\n *\n * {@example core/ts/change_detect/change-detection.ts region='detach'}\n *\n *\n * ### Reattaching a detached component\n *\n * The following example creates a component displaying live data.\n * The component detaches its change detector from the main change detector tree\n * when the `live` property is set to false, and reattaches it when the property\n * becomes true.\n *\n * {@example core/ts/change_detect/change-detection.ts region='reattach'}\n *\n * @publicApi\n */\nexport abstract class ChangeDetectorRef {\n  /**\n   * When a view uses the {@link ChangeDetectionStrategy#OnPush} (checkOnce)\n   * change detection strategy, explicitly marks the view as changed so that\n   * it can be checked again.\n   *\n   * Components are normally marked as dirty (in need of rerendering) when inputs\n   * have changed or events have fired in the view. Call this method to ensure that\n   * a component is checked even if these triggers have not occurred.\n   *\n   * <!-- TODO: Add a link to a chapter on OnPush components -->\n   *\n   */\n  abstract markForCheck(): void;\n\n  /**\n   * Detaches this view from the change-detection tree.\n   * A detached view is  not checked until it is reattached.\n   * Use in combination with `detectChanges()` to implement local change detection checks.\n   *\n   * Detached views are not checked during change detection runs until they are\n   * re-attached, even if they are marked as dirty.\n   *\n   * <!-- TODO: Add a link to a chapter on detach/reattach/local digest -->\n   * <!-- TODO: Add a live demo once ref.detectChanges is merged into master -->\n   *\n   */\n  abstract detach(): void;\n\n  /**\n   * Checks this view and its children. Use in combination with {@link ChangeDetectorRef#detach}\n   * to implement local change detection checks.\n   *\n   * <!-- TODO: Add a link to a chapter on detach/reattach/local digest -->\n   * <!-- TODO: Add a live demo once ref.detectChanges is merged into master -->\n   *\n   */\n  abstract detectChanges(): void;\n\n  /**\n   * Checks the change detector and its children, and throws if any changes are detected.\n   *\n   * Use in development mode to verify that running change detection doesn't introduce\n   * other changes. Calling it in production mode is a noop.\n   *\n   * @deprecated This is a test-only API that does not have a place in production interface.\n   * `checkNoChanges` is already part of an `ApplicationRef` tick when the app is running in dev\n   * mode. For more granular `checkNoChanges` validation, use `ComponentFixture`.\n   */\n  abstract checkNoChanges(): void;\n\n  /**\n   * Re-attaches the previously detached view to the change detection tree.\n   * Views are attached to the tree by default.\n   *\n   * <!-- TODO: Add a link to a chapter on detach/reattach/local digest -->\n   *\n   */\n  abstract reattach(): void;\n\n  /**\n   * @internal\n   * @nocollapse\n   */\n  static __NG_ELEMENT_ID__: (flags: InternalInjectFlags) => ChangeDetectorRef =\n    injectChangeDetectorRef;\n}\n\n/** Returns a ChangeDetectorRef (a.k.a. a ViewRef) */\nexport function injectChangeDetectorRef(flags: InternalInjectFlags): ChangeDetectorRef {\n  return createViewRef(\n    getCurrentTNode()!,\n    getLView(),\n    (flags & InternalInjectFlags.ForPipe) === InternalInjectFlags.ForPipe,\n  );\n}\n\n/**\n * Creates a ViewRef and stores it on the injector as ChangeDetectorRef (public alias).\n *\n * @param tNode The node that is requesting a ChangeDetectorRef\n * @param lView The view to which the node belongs\n * @param isPipe Whether the view is being injected into a pipe.\n * @returns The ChangeDetectorRef to use\n */\nfunction createViewRef(tNode: TNode, lView: LView, isPipe: boolean): ChangeDetectorRef {\n  if (isComponentHost(tNode) && !isPipe) {\n    // The LView represents the location where the component is declared.\n    // Instead we want the LView for the component View and so we need to look it up.\n    const componentView = getComponentLViewByIndex(tNode.index, lView); // look down\n    return new ViewRef(componentView, componentView);\n  } else if (\n    tNode.type &\n    (TNodeType.AnyRNode | TNodeType.AnyContainer | TNodeType.Icu | TNodeType.LetDeclaration)\n  ) {\n    // The LView represents the location where the injection is requested from.\n    // We need to locate the containing LView (in case where the `lView` is an embedded view)\n    const hostComponentView = lView[DECLARATION_COMPONENT_VIEW]; // look up\n    return new ViewRef(hostComponentView, lView);\n  }\n  return null!;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ChangeDetectorRef} from '../change_detection/change_detector_ref';\n\n/**\n * Represents an Angular view.\n *\n * @see {@link /api/core/ChangeDetectorRef?tab=usage-notes Change detection usage}\n *\n * @publicApi\n */\nexport abstract class ViewRef extends ChangeDetectorRef {\n  /**\n   * Destroys this view and all of the data structures associated with it.\n   */\n  abstract destroy(): void;\n\n  /**\n   * Reports whether this view has been destroyed.\n   * @returns True after the `destroy()` method has been called, false otherwise.\n   */\n  abstract get destroyed(): boolean;\n\n  /**\n   * A lifecycle hook that provides additional developer-defined cleanup\n   * functionality for views.\n   * @param callback A handler function that cleans up developer-defined data\n   * associated with a view. Called when the `destroy()` method is invoked.\n   */\n  abstract onDestroy(callback: Function): void;\n}\n\n/**\n * Represents an Angular view in a view container.\n * An embedded view can be referenced from a component\n * other than the hosting component whose template defines it, or it can be defined\n * independently by a `TemplateRef`.\n *\n * Properties of elements in a view can change, but the structure (number and order) of elements in\n * a view cannot. Change the structure of elements by inserting, moving, or\n * removing nested views in a view container.\n *\n * @see {@link ViewContainerRef}\n *\n * @usageNotes\n *\n * The following template breaks down into two separate `TemplateRef` instances,\n * an outer one and an inner one.\n *\n * ```html\n * Count: {{items.length}}\n * <ul>\n *   <li *ngFor=\"let  item of items\">{{item}}</li>\n * </ul>\n * ```\n *\n * This is the outer `TemplateRef`:\n *\n * ```html\n * Count: {{items.length}}\n * <ul>\n *   <ng-template ngFor let-item [ngForOf]=\"items\"></ng-template>\n * </ul>\n * ```\n *\n * This is the inner `TemplateRef`:\n *\n * ```html\n *   <li>{{item}}</li>\n * ```\n *\n * The outer and inner `TemplateRef` instances are assembled into views as follows:\n *\n * ```html\n * <!-- ViewRef: outer-0 -->\n * Count: 2\n * <ul>\n *   <ng-template view-container-ref></ng-template>\n *   <!-- ViewRef: inner-1 --><li>first</li><!-- /ViewRef: inner-1 -->\n *   <!-- ViewRef: inner-2 --><li>second</li><!-- /ViewRef: inner-2 -->\n * </ul>\n * <!-- /ViewRef: outer-0 -->\n * ```\n * @publicApi\n */\nexport abstract class EmbeddedViewRef<C> extends ViewRef {\n  /**\n   * The context for this view, inherited from the anchor element.\n   */\n  abstract context: C;\n\n  /**\n   * The root nodes for this embedded view.\n   */\n  abstract get rootNodes(): any[];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {RuntimeError, RuntimeErrorCode} from '../../errors';\nimport {Writable} from '../../interface/type';\nimport {isListLikeIterable, iterateListLike} from '../../util/iterable';\nimport {stringify} from '../../util/stringify';\n\nimport type {\n  IterableChangeRecord,\n  IterableChanges,\n  IterableDiffer,\n  IterableDifferFactory,\n  NgIterable,\n  TrackByFunction,\n} from './iterable_differs';\n\nexport class DefaultIterableDifferFactory implements IterableDifferFactory {\n  constructor() {}\n  supports(obj: Object | null | undefined): boolean {\n    return isListLikeIterable(obj);\n  }\n\n  create<V>(trackByFn?: TrackByFunction<V>): DefaultIterableDiffer<V> {\n    return new DefaultIterableDiffer<V>(trackByFn);\n  }\n}\n\nconst trackByIdentity = (index: number, item: any) => item;\n\n/**\n * @deprecated v4.0.0 - Should not be part of public API.\n * @publicApi\n */\nexport class DefaultIterableDiffer<V> implements IterableDiffer<V>, IterableChanges<V> {\n  public readonly length: number = 0;\n  // TODO: confirm the usage of `collection` as it's unused, readonly and on a non public API.\n  public readonly collection!: V[] | Iterable<V> | null;\n  // Keeps track of the used records at any point in time (during & across `_check()` calls)\n  private _linkedRecords: _DuplicateMap<V> | null = null;\n  // Keeps track of the removed records at any point in time during `_check()` calls.\n  private _unlinkedRecords: _DuplicateMap<V> | null = null;\n  private _previousItHead: IterableChangeRecord_<V> | null = null;\n  private _itHead: IterableChangeRecord_<V> | null = null;\n  private _itTail: IterableChangeRecord_<V> | null = null;\n  private _additionsHead: IterableChangeRecord_<V> | null = null;\n  private _additionsTail: IterableChangeRecord_<V> | null = null;\n  private _movesHead: IterableChangeRecord_<V> | null = null;\n  private _movesTail: IterableChangeRecord_<V> | null = null;\n  private _removalsHead: IterableChangeRecord_<V> | null = null;\n  private _removalsTail: IterableChangeRecord_<V> | null = null;\n  // Keeps track of records where custom track by is the same, but item identity has changed\n  private _identityChangesHead: IterableChangeRecord_<V> | null = null;\n  private _identityChangesTail: IterableChangeRecord_<V> | null = null;\n  private _trackByFn: TrackByFunction<V>;\n\n  constructor(trackByFn?: TrackByFunction<V>) {\n    this._trackByFn = trackByFn || trackByIdentity;\n  }\n\n  forEachItem(fn: (record: IterableChangeRecord_<V>) => void) {\n    let record: IterableChangeRecord_<V> | null;\n    for (record = this._itHead; record !== null; record = record._next) {\n      fn(record);\n    }\n  }\n\n  forEachOperation(\n    fn: (\n      item: IterableChangeRecord<V>,\n      previousIndex: number | null,\n      currentIndex: number | null,\n    ) => void,\n  ) {\n    let nextIt = this._itHead;\n    let nextRemove = this._removalsHead;\n    let addRemoveOffset = 0;\n    let moveOffsets: number[] | null = null;\n    while (nextIt || nextRemove) {\n      // Figure out which is the next record to process\n      // Order: remove, add, move\n      const record: IterableChangeRecord<V> =\n        !nextRemove ||\n        (nextIt &&\n          nextIt.currentIndex! < getPreviousIndex(nextRemove, addRemoveOffset, moveOffsets))\n          ? nextIt!\n          : nextRemove;\n      const adjPreviousIndex = getPreviousIndex(record, addRemoveOffset, moveOffsets);\n      const currentIndex = record.currentIndex;\n\n      // consume the item, and adjust the addRemoveOffset and update moveDistance if necessary\n      if (record === nextRemove) {\n        addRemoveOffset--;\n        nextRemove = nextRemove._nextRemoved;\n      } else {\n        nextIt = nextIt!._next;\n        if (record.previousIndex == null) {\n          addRemoveOffset++;\n        } else {\n          // INVARIANT:  currentIndex < previousIndex\n          if (!moveOffsets) moveOffsets = [];\n          const localMovePreviousIndex = adjPreviousIndex - addRemoveOffset;\n          const localCurrentIndex = currentIndex! - addRemoveOffset;\n          if (localMovePreviousIndex != localCurrentIndex) {\n            for (let i = 0; i < localMovePreviousIndex; i++) {\n              const offset = i < moveOffsets.length ? moveOffsets[i] : (moveOffsets[i] = 0);\n              const index = offset + i;\n              if (localCurrentIndex <= index && index < localMovePreviousIndex) {\n                moveOffsets[i] = offset + 1;\n              }\n            }\n            const previousIndex = record.previousIndex;\n            moveOffsets[previousIndex] = localCurrentIndex - localMovePreviousIndex;\n          }\n        }\n      }\n\n      if (adjPreviousIndex !== currentIndex) {\n        fn(record, adjPreviousIndex, currentIndex);\n      }\n    }\n  }\n\n  forEachPreviousItem(fn: (record: IterableChangeRecord_<V>) => void) {\n    let record: IterableChangeRecord_<V> | null;\n    for (record = this._previousItHead; record !== null; record = record._nextPrevious) {\n      fn(record);\n    }\n  }\n\n  forEachAddedItem(fn: (record: IterableChangeRecord_<V>) => void) {\n    let record: IterableChangeRecord_<V> | null;\n    for (record = this._additionsHead; record !== null; record = record._nextAdded) {\n      fn(record);\n    }\n  }\n\n  forEachMovedItem(fn: (record: IterableChangeRecord_<V>) => void) {\n    let record: IterableChangeRecord_<V> | null;\n    for (record = this._movesHead; record !== null; record = record._nextMoved) {\n      fn(record);\n    }\n  }\n\n  forEachRemovedItem(fn: (record: IterableChangeRecord_<V>) => void) {\n    let record: IterableChangeRecord_<V> | null;\n    for (record = this._removalsHead; record !== null; record = record._nextRemoved) {\n      fn(record);\n    }\n  }\n\n  forEachIdentityChange(fn: (record: IterableChangeRecord_<V>) => void) {\n    let record: IterableChangeRecord_<V> | null;\n    for (record = this._identityChangesHead; record !== null; record = record._nextIdentityChange) {\n      fn(record);\n    }\n  }\n\n  diff(collection: NgIterable<V> | null | undefined): DefaultIterableDiffer<V> | null {\n    if (collection == null) collection = [];\n    if (!isListLikeIterable(collection)) {\n      throw new RuntimeError(\n        RuntimeErrorCode.INVALID_DIFFER_INPUT,\n        ngDevMode &&\n          `Error trying to diff '${stringify(collection)}'. Only arrays and iterables are allowed`,\n      );\n    }\n\n    if (this.check(collection)) {\n      return this;\n    } else {\n      return null;\n    }\n  }\n\n  onDestroy() {}\n\n  check(collection: NgIterable<V>): boolean {\n    this._reset();\n\n    let record: IterableChangeRecord_<V> | null = this._itHead;\n    let mayBeDirty: boolean = false;\n    let index: number;\n    let item: V;\n    let itemTrackBy: any;\n    if (Array.isArray(collection)) {\n      (this as Writable<this>).length = collection.length;\n\n      for (let index = 0; index < this.length; index++) {\n        item = collection[index];\n        itemTrackBy = this._trackByFn(index, item);\n        if (record === null || !Object.is(record.trackById, itemTrackBy)) {\n          record = this._mismatch(record, item, itemTrackBy, index);\n          mayBeDirty = true;\n        } else {\n          if (mayBeDirty) {\n            // TODO(misko): can we limit this to duplicates only?\n            record = this._verifyReinsertion(record, item, itemTrackBy, index);\n          }\n          if (!Object.is(record.item, item)) this._addIdentityChange(record, item);\n        }\n\n        record = record._next;\n      }\n    } else {\n      index = 0;\n      iterateListLike(collection, (item: V) => {\n        itemTrackBy = this._trackByFn(index, item);\n        if (record === null || !Object.is(record.trackById, itemTrackBy)) {\n          record = this._mismatch(record, item, itemTrackBy, index);\n          mayBeDirty = true;\n        } else {\n          if (mayBeDirty) {\n            // TODO(misko): can we limit this to duplicates only?\n            record = this._verifyReinsertion(record, item, itemTrackBy, index);\n          }\n          if (!Object.is(record.item, item)) this._addIdentityChange(record, item);\n        }\n        record = record._next;\n        index++;\n      });\n      (this as Writable<this>).length = index;\n    }\n\n    this._truncate(record);\n    (this as Writable<this>).collection = collection;\n    return this.isDirty;\n  }\n\n  /* CollectionChanges is considered dirty if it has any additions, moves, removals, or identity\n   * changes.\n   */\n  get isDirty(): boolean {\n    return (\n      this._additionsHead !== null ||\n      this._movesHead !== null ||\n      this._removalsHead !== null ||\n      this._identityChangesHead !== null\n    );\n  }\n\n  /**\n   * Reset the state of the change objects to show no changes. This means set previousKey to\n   * currentKey, and clear all of the queues (additions, moves, removals).\n   * Set the previousIndexes of moved and added items to their currentIndexes\n   * Reset the list of additions, moves and removals\n   *\n   * @internal\n   */\n  _reset() {\n    if (this.isDirty) {\n      let record: IterableChangeRecord_<V> | null;\n\n      for (record = this._previousItHead = this._itHead; record !== null; record = record._next) {\n        record._nextPrevious = record._next;\n      }\n\n      for (record = this._additionsHead; record !== null; record = record._nextAdded) {\n        record.previousIndex = record.currentIndex;\n      }\n      this._additionsHead = this._additionsTail = null;\n\n      for (record = this._movesHead; record !== null; record = record._nextMoved) {\n        record.previousIndex = record.currentIndex;\n      }\n      this._movesHead = this._movesTail = null;\n      this._removalsHead = this._removalsTail = null;\n      this._identityChangesHead = this._identityChangesTail = null;\n\n      // TODO(vicb): when assert gets supported\n      // assert(!this.isDirty);\n    }\n  }\n\n  /**\n   * This is the core function which handles differences between collections.\n   *\n   * - `record` is the record which we saw at this position last time. If null then it is a new\n   *   item.\n   * - `item` is the current item in the collection\n   * - `index` is the position of the item in the collection\n   *\n   * @internal\n   */\n  _mismatch(\n    record: IterableChangeRecord_<V> | null,\n    item: V,\n    itemTrackBy: any,\n    index: number,\n  ): IterableChangeRecord_<V> {\n    // The previous record after which we will append the current one.\n    let previousRecord: IterableChangeRecord_<V> | null;\n\n    if (record === null) {\n      previousRecord = this._itTail;\n    } else {\n      previousRecord = record._prev;\n      // Remove the record from the collection since we know it does not match the item.\n      this._remove(record);\n    }\n\n    // See if we have evicted the item, which used to be at some anterior position of _itHead list.\n    record = this._unlinkedRecords === null ? null : this._unlinkedRecords.get(itemTrackBy, null);\n    if (record !== null) {\n      // It is an item which we have evicted earlier: reinsert it back into the list.\n      // But first we need to check if identity changed, so we can update in view if necessary.\n      if (!Object.is(record.item, item)) this._addIdentityChange(record, item);\n\n      this._reinsertAfter(record, previousRecord, index);\n    } else {\n      // Attempt to see if the item is at some posterior position of _itHead list.\n      record = this._linkedRecords === null ? null : this._linkedRecords.get(itemTrackBy, index);\n      if (record !== null) {\n        // We have the item in _itHead at/after `index` position. We need to move it forward in the\n        // collection.\n        // But first we need to check if identity changed, so we can update in view if necessary.\n        if (!Object.is(record.item, item)) this._addIdentityChange(record, item);\n\n        this._moveAfter(record, previousRecord, index);\n      } else {\n        // It is a new item: add it.\n        record = this._addAfter(\n          new IterableChangeRecord_<V>(item, itemTrackBy),\n          previousRecord,\n          index,\n        );\n      }\n    }\n    return record;\n  }\n\n  /**\n   * This check is only needed if an array contains duplicates. (Short circuit of nothing dirty)\n   *\n   * Use case: `[a, a]` => `[b, a, a]`\n   *\n   * If we did not have this check then the insertion of `b` would:\n   *   1) evict first `a`\n   *   2) insert `b` at `0` index.\n   *   3) leave `a` at index `1` as is. <-- this is wrong!\n   *   3) reinsert `a` at index 2. <-- this is wrong!\n   *\n   * The correct behavior is:\n   *   1) evict first `a`\n   *   2) insert `b` at `0` index.\n   *   3) reinsert `a` at index 1.\n   *   3) move `a` at from `1` to `2`.\n   *\n   *\n   * Double check that we have not evicted a duplicate item. We need to check if the item type may\n   * have already been removed:\n   * The insertion of b will evict the first 'a'. If we don't reinsert it now it will be reinserted\n   * at the end. Which will show up as the two 'a's switching position. This is incorrect, since a\n   * better way to think of it is as insert of 'b' rather then switch 'a' with 'b' and then add 'a'\n   * at the end.\n   *\n   * @internal\n   */\n  _verifyReinsertion(\n    record: IterableChangeRecord_<V>,\n    item: V,\n    itemTrackBy: any,\n    index: number,\n  ): IterableChangeRecord_<V> {\n    let reinsertRecord: IterableChangeRecord_<V> | null =\n      this._unlinkedRecords === null ? null : this._unlinkedRecords.get(itemTrackBy, null);\n    if (reinsertRecord !== null) {\n      record = this._reinsertAfter(reinsertRecord, record._prev!, index);\n    } else if (record.currentIndex != index) {\n      record.currentIndex = index;\n      this._addToMoves(record, index);\n    }\n    return record;\n  }\n\n  /**\n   * Get rid of any excess {@link IterableChangeRecord_}s from the previous collection\n   *\n   * - `record` The first excess {@link IterableChangeRecord_}.\n   *\n   * @internal\n   */\n  _truncate(record: IterableChangeRecord_<V> | null) {\n    // Anything after that needs to be removed;\n    while (record !== null) {\n      const nextRecord: IterableChangeRecord_<V> | null = record._next;\n      this._addToRemovals(this._unlink(record));\n      record = nextRecord;\n    }\n    if (this._unlinkedRecords !== null) {\n      this._unlinkedRecords.clear();\n    }\n\n    if (this._additionsTail !== null) {\n      this._additionsTail._nextAdded = null;\n    }\n    if (this._movesTail !== null) {\n      this._movesTail._nextMoved = null;\n    }\n    if (this._itTail !== null) {\n      this._itTail._next = null;\n    }\n    if (this._removalsTail !== null) {\n      this._removalsTail._nextRemoved = null;\n    }\n    if (this._identityChangesTail !== null) {\n      this._identityChangesTail._nextIdentityChange = null;\n    }\n  }\n\n  /** @internal */\n  _reinsertAfter(\n    record: IterableChangeRecord_<V>,\n    prevRecord: IterableChangeRecord_<V> | null,\n    index: number,\n  ): IterableChangeRecord_<V> {\n    if (this._unlinkedRecords !== null) {\n      this._unlinkedRecords.remove(record);\n    }\n    const prev = record._prevRemoved;\n    const next = record._nextRemoved;\n\n    if (prev === null) {\n      this._removalsHead = next;\n    } else {\n      prev._nextRemoved = next;\n    }\n    if (next === null) {\n      this._removalsTail = prev;\n    } else {\n      next._prevRemoved = prev;\n    }\n\n    this._insertAfter(record, prevRecord, index);\n    this._addToMoves(record, index);\n    return record;\n  }\n\n  /** @internal */\n  _moveAfter(\n    record: IterableChangeRecord_<V>,\n    prevRecord: IterableChangeRecord_<V> | null,\n    index: number,\n  ): IterableChangeRecord_<V> {\n    this._unlink(record);\n    this._insertAfter(record, prevRecord, index);\n    this._addToMoves(record, index);\n    return record;\n  }\n\n  /** @internal */\n  _addAfter(\n    record: IterableChangeRecord_<V>,\n    prevRecord: IterableChangeRecord_<V> | null,\n    index: number,\n  ): IterableChangeRecord_<V> {\n    this._insertAfter(record, prevRecord, index);\n\n    if (this._additionsTail === null) {\n      // TODO(vicb):\n      // assert(this._additionsHead === null);\n      this._additionsTail = this._additionsHead = record;\n    } else {\n      // TODO(vicb):\n      // assert(_additionsTail._nextAdded === null);\n      // assert(record._nextAdded === null);\n      this._additionsTail = this._additionsTail._nextAdded = record;\n    }\n    return record;\n  }\n\n  /** @internal */\n  _insertAfter(\n    record: IterableChangeRecord_<V>,\n    prevRecord: IterableChangeRecord_<V> | null,\n    index: number,\n  ): IterableChangeRecord_<V> {\n    // TODO(vicb):\n    // assert(record != prevRecord);\n    // assert(record._next === null);\n    // assert(record._prev === null);\n\n    const next: IterableChangeRecord_<V> | null =\n      prevRecord === null ? this._itHead : prevRecord._next;\n    // TODO(vicb):\n    // assert(next != record);\n    // assert(prevRecord != record);\n    record._next = next;\n    record._prev = prevRecord;\n    if (next === null) {\n      this._itTail = record;\n    } else {\n      next._prev = record;\n    }\n    if (prevRecord === null) {\n      this._itHead = record;\n    } else {\n      prevRecord._next = record;\n    }\n\n    if (this._linkedRecords === null) {\n      this._linkedRecords = new _DuplicateMap<V>();\n    }\n    this._linkedRecords.put(record);\n\n    record.currentIndex = index;\n    return record;\n  }\n\n  /** @internal */\n  _remove(record: IterableChangeRecord_<V>): IterableChangeRecord_<V> {\n    return this._addToRemovals(this._unlink(record));\n  }\n\n  /** @internal */\n  _unlink(record: IterableChangeRecord_<V>): IterableChangeRecord_<V> {\n    if (this._linkedRecords !== null) {\n      this._linkedRecords.remove(record);\n    }\n\n    const prev = record._prev;\n    const next = record._next;\n\n    // TODO(vicb):\n    // assert((record._prev = null) === null);\n    // assert((record._next = null) === null);\n\n    if (prev === null) {\n      this._itHead = next;\n    } else {\n      prev._next = next;\n    }\n    if (next === null) {\n      this._itTail = prev;\n    } else {\n      next._prev = prev;\n    }\n\n    return record;\n  }\n\n  /** @internal */\n  _addToMoves(record: IterableChangeRecord_<V>, toIndex: number): IterableChangeRecord_<V> {\n    // TODO(vicb):\n    // assert(record._nextMoved === null);\n\n    if (record.previousIndex === toIndex) {\n      return record;\n    }\n\n    if (this._movesTail === null) {\n      // TODO(vicb):\n      // assert(_movesHead === null);\n      this._movesTail = this._movesHead = record;\n    } else {\n      // TODO(vicb):\n      // assert(_movesTail._nextMoved === null);\n      this._movesTail = this._movesTail._nextMoved = record;\n    }\n\n    return record;\n  }\n\n  private _addToRemovals(record: IterableChangeRecord_<V>): IterableChangeRecord_<V> {\n    if (this._unlinkedRecords === null) {\n      this._unlinkedRecords = new _DuplicateMap<V>();\n    }\n    this._unlinkedRecords.put(record);\n    record.currentIndex = null;\n    record._nextRemoved = null;\n\n    if (this._removalsTail === null) {\n      // TODO(vicb):\n      // assert(_removalsHead === null);\n      this._removalsTail = this._removalsHead = record;\n      record._prevRemoved = null;\n    } else {\n      // TODO(vicb):\n      // assert(_removalsTail._nextRemoved === null);\n      // assert(record._nextRemoved === null);\n      record._prevRemoved = this._removalsTail;\n      this._removalsTail = this._removalsTail._nextRemoved = record;\n    }\n    return record;\n  }\n\n  /** @internal */\n  _addIdentityChange(record: IterableChangeRecord_<V>, item: V) {\n    record.item = item;\n    if (this._identityChangesTail === null) {\n      this._identityChangesTail = this._identityChangesHead = record;\n    } else {\n      this._identityChangesTail = this._identityChangesTail._nextIdentityChange = record;\n    }\n    return record;\n  }\n}\n\nexport class IterableChangeRecord_<V> implements IterableChangeRecord<V> {\n  currentIndex: number | null = null;\n  previousIndex: number | null = null;\n\n  /** @internal */\n  _nextPrevious: IterableChangeRecord_<V> | null = null;\n  /** @internal */\n  _prev: IterableChangeRecord_<V> | null = null;\n  /** @internal */\n  _next: IterableChangeRecord_<V> | null = null;\n  /** @internal */\n  _prevDup: IterableChangeRecord_<V> | null = null;\n  /** @internal */\n  _nextDup: IterableChangeRecord_<V> | null = null;\n  /** @internal */\n  _prevRemoved: IterableChangeRecord_<V> | null = null;\n  /** @internal */\n  _nextRemoved: IterableChangeRecord_<V> | null = null;\n  /** @internal */\n  _nextAdded: IterableChangeRecord_<V> | null = null;\n  /** @internal */\n  _nextMoved: IterableChangeRecord_<V> | null = null;\n  /** @internal */\n  _nextIdentityChange: IterableChangeRecord_<V> | null = null;\n\n  constructor(\n    public item: V,\n    public trackById: any,\n  ) {}\n}\n\n// A linked list of IterableChangeRecords with the same IterableChangeRecord_.item\nclass _DuplicateItemRecordList<V> {\n  /** @internal */\n  _head: IterableChangeRecord_<V> | null = null;\n  /** @internal */\n  _tail: IterableChangeRecord_<V> | null = null;\n\n  /**\n   * Append the record to the list of duplicates.\n   *\n   * Note: by design all records in the list of duplicates hold the same value in record.item.\n   */\n  add(record: IterableChangeRecord_<V>): void {\n    if (this._head === null) {\n      this._head = this._tail = record;\n      record._nextDup = null;\n      record._prevDup = null;\n    } else {\n      // TODO(vicb):\n      // assert(record.item ==  _head.item ||\n      //       record.item is num && record.item.isNaN && _head.item is num && _head.item.isNaN);\n      this._tail!._nextDup = record;\n      record._prevDup = this._tail;\n      record._nextDup = null;\n      this._tail = record;\n    }\n  }\n\n  // Returns a IterableChangeRecord_ having IterableChangeRecord_.trackById == trackById and\n  // IterableChangeRecord_.currentIndex >= atOrAfterIndex\n  get(trackById: any, atOrAfterIndex: number | null): IterableChangeRecord_<V> | null {\n    let record: IterableChangeRecord_<V> | null;\n    for (record = this._head; record !== null; record = record._nextDup) {\n      if (\n        (atOrAfterIndex === null || atOrAfterIndex <= record.currentIndex!) &&\n        Object.is(record.trackById, trackById)\n      ) {\n        return record;\n      }\n    }\n    return null;\n  }\n\n  /**\n   * Remove one {@link IterableChangeRecord_} from the list of duplicates.\n   *\n   * Returns whether the list of duplicates is empty.\n   */\n  remove(record: IterableChangeRecord_<V>): boolean {\n    // TODO(vicb):\n    // assert(() {\n    //  // verify that the record being removed is in the list.\n    //  for (IterableChangeRecord_ cursor = _head; cursor != null; cursor = cursor._nextDup) {\n    //    if (identical(cursor, record)) return true;\n    //  }\n    //  return false;\n    //});\n\n    const prev: IterableChangeRecord_<V> | null = record._prevDup;\n    const next: IterableChangeRecord_<V> | null = record._nextDup;\n    if (prev === null) {\n      this._head = next;\n    } else {\n      prev._nextDup = next;\n    }\n    if (next === null) {\n      this._tail = prev;\n    } else {\n      next._prevDup = prev;\n    }\n    return this._head === null;\n  }\n}\n\nclass _DuplicateMap<V> {\n  map = new Map<any, _DuplicateItemRecordList<V>>();\n\n  put(record: IterableChangeRecord_<V>) {\n    const key = record.trackById;\n\n    let duplicates = this.map.get(key);\n    if (!duplicates) {\n      duplicates = new _DuplicateItemRecordList<V>();\n      this.map.set(key, duplicates);\n    }\n    duplicates.add(record);\n  }\n\n  /**\n   * Retrieve the `value` using key. Because the IterableChangeRecord_ value may be one which we\n   * have already iterated over, we use the `atOrAfterIndex` to pretend it is not there.\n   *\n   * Use case: `[a, b, c, a, a]` if we are at index `3` which is the second `a` then asking if we\n   * have any more `a`s needs to return the second `a`.\n   */\n  get(trackById: any, atOrAfterIndex: number | null): IterableChangeRecord_<V> | null {\n    const key = trackById;\n    const recordList = this.map.get(key);\n    return recordList ? recordList.get(trackById, atOrAfterIndex) : null;\n  }\n\n  /**\n   * Removes a {@link IterableChangeRecord_} from the list of duplicates.\n   *\n   * The list of duplicates also is removed from the map if it gets empty.\n   */\n  remove(record: IterableChangeRecord_<V>): IterableChangeRecord_<V> {\n    const key = record.trackById;\n    const recordList: _DuplicateItemRecordList<V> = this.map.get(key)!;\n    // Remove the list of duplicates when it gets empty\n    if (recordList.remove(record)) {\n      this.map.delete(key);\n    }\n    return record;\n  }\n\n  get isEmpty(): boolean {\n    return this.map.size === 0;\n  }\n\n  clear() {\n    this.map.clear();\n  }\n}\n\nfunction getPreviousIndex(\n  item: any,\n  addRemoveOffset: number,\n  moveOffsets: number[] | null,\n): number {\n  const previousIndex = item.previousIndex;\n  if (previousIndex === null) return previousIndex;\n  let moveOffset = 0;\n  if (moveOffsets && previousIndex < moveOffsets.length) {\n    moveOffset = moveOffsets[previousIndex];\n  }\n  return previousIndex + addRemoveOffset + moveOffset;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {RuntimeError, RuntimeErrorCode} from '../../errors';\nimport {isJsObject} from '../../util/iterable';\nimport {stringify} from '../../util/stringify';\n\nimport type {\n  KeyValueChangeRecord,\n  KeyValueChanges,\n  KeyValueDiffer,\n  KeyValueDifferFactory,\n} from './keyvalue_differs';\n\nexport class DefaultKeyValueDifferFactory<K, V> implements KeyValueDifferFactory {\n  constructor() {}\n  supports(obj: any): boolean {\n    return obj instanceof Map || isJsObject(obj);\n  }\n\n  create<K, V>(): KeyValueDiffer<K, V> {\n    return new DefaultKeyValueDiffer<K, V>();\n  }\n}\n\nexport class DefaultKeyValueDiffer<K, V> implements KeyValueDiffer<K, V>, KeyValueChanges<K, V> {\n  private _records = new Map<K, KeyValueChangeRecord_<K, V>>();\n  private _mapHead: KeyValueChangeRecord_<K, V> | null = null;\n  // _appendAfter is used in the check loop\n  private _appendAfter: KeyValueChangeRecord_<K, V> | null = null;\n  private _previousMapHead: KeyValueChangeRecord_<K, V> | null = null;\n  private _changesHead: KeyValueChangeRecord_<K, V> | null = null;\n  private _changesTail: KeyValueChangeRecord_<K, V> | null = null;\n  private _additionsHead: KeyValueChangeRecord_<K, V> | null = null;\n  private _additionsTail: KeyValueChangeRecord_<K, V> | null = null;\n  private _removalsHead: KeyValueChangeRecord_<K, V> | null = null;\n  private _removalsTail: KeyValueChangeRecord_<K, V> | null = null;\n\n  get isDirty(): boolean {\n    return (\n      this._additionsHead !== null || this._changesHead !== null || this._removalsHead !== null\n    );\n  }\n\n  forEachItem(fn: (r: KeyValueChangeRecord<K, V>) => void) {\n    let record: KeyValueChangeRecord_<K, V> | null;\n    for (record = this._mapHead; record !== null; record = record._next) {\n      fn(record);\n    }\n  }\n\n  forEachPreviousItem(fn: (r: KeyValueChangeRecord<K, V>) => void) {\n    let record: KeyValueChangeRecord_<K, V> | null;\n    for (record = this._previousMapHead; record !== null; record = record._nextPrevious) {\n      fn(record);\n    }\n  }\n\n  forEachChangedItem(fn: (r: KeyValueChangeRecord<K, V>) => void) {\n    let record: KeyValueChangeRecord_<K, V> | null;\n    for (record = this._changesHead; record !== null; record = record._nextChanged) {\n      fn(record);\n    }\n  }\n\n  forEachAddedItem(fn: (r: KeyValueChangeRecord<K, V>) => void) {\n    let record: KeyValueChangeRecord_<K, V> | null;\n    for (record = this._additionsHead; record !== null; record = record._nextAdded) {\n      fn(record);\n    }\n  }\n\n  forEachRemovedItem(fn: (r: KeyValueChangeRecord<K, V>) => void) {\n    let record: KeyValueChangeRecord_<K, V> | null;\n    for (record = this._removalsHead; record !== null; record = record._nextRemoved) {\n      fn(record);\n    }\n  }\n\n  diff(map?: Map<any, any> | {[k: string]: any} | null): any {\n    if (!map) {\n      map = new Map();\n    } else if (!(map instanceof Map || isJsObject(map))) {\n      throw new RuntimeError(\n        RuntimeErrorCode.INVALID_DIFFER_INPUT,\n        ngDevMode && `Error trying to diff '${stringify(map)}'. Only maps and objects are allowed`,\n      );\n    }\n\n    return this.check(map) ? this : null;\n  }\n\n  onDestroy() {}\n\n  /**\n   * Check the current state of the map vs the previous.\n   * The algorithm is optimised for when the keys do no change.\n   */\n  check(map: Map<any, any> | {[k: string]: any}): boolean {\n    this._reset();\n\n    let insertBefore = this._mapHead;\n    this._appendAfter = null;\n\n    this._forEach(map, (value: any, key: any) => {\n      if (insertBefore && insertBefore.key === key) {\n        this._maybeAddToChanges(insertBefore, value);\n        this._appendAfter = insertBefore;\n        insertBefore = insertBefore._next;\n      } else {\n        const record = this._getOrCreateRecordForKey(key, value);\n        insertBefore = this._insertBeforeOrAppend(insertBefore, record);\n      }\n    });\n\n    // Items remaining at the end of the list have been deleted\n    if (insertBefore) {\n      if (insertBefore._prev) {\n        insertBefore._prev._next = null;\n      }\n\n      this._removalsHead = insertBefore;\n\n      for (\n        let record: KeyValueChangeRecord_<K, V> | null = insertBefore;\n        record !== null;\n        record = record._nextRemoved\n      ) {\n        if (record === this._mapHead) {\n          this._mapHead = null;\n        }\n        this._records.delete(record.key);\n        record._nextRemoved = record._next;\n        record.previousValue = record.currentValue;\n        record.currentValue = null;\n        record._prev = null;\n        record._next = null;\n      }\n    }\n\n    // Make sure tails have no next records from previous runs\n    if (this._changesTail) this._changesTail._nextChanged = null;\n    if (this._additionsTail) this._additionsTail._nextAdded = null;\n\n    return this.isDirty;\n  }\n\n  /**\n   * Inserts a record before `before` or append at the end of the list when `before` is null.\n   *\n   * Notes:\n   * - This method appends at `this._appendAfter`,\n   * - This method updates `this._appendAfter`,\n   * - The return value is the new value for the insertion pointer.\n   */\n  private _insertBeforeOrAppend(\n    before: KeyValueChangeRecord_<K, V> | null,\n    record: KeyValueChangeRecord_<K, V>,\n  ): KeyValueChangeRecord_<K, V> | null {\n    if (before) {\n      const prev = before._prev;\n      record._next = before;\n      record._prev = prev;\n      before._prev = record;\n      if (prev) {\n        prev._next = record;\n      }\n      if (before === this._mapHead) {\n        this._mapHead = record;\n      }\n\n      this._appendAfter = before;\n      return before;\n    }\n\n    if (this._appendAfter) {\n      this._appendAfter._next = record;\n      record._prev = this._appendAfter;\n    } else {\n      this._mapHead = record;\n    }\n\n    this._appendAfter = record;\n    return null;\n  }\n\n  private _getOrCreateRecordForKey(key: K, value: V): KeyValueChangeRecord_<K, V> {\n    if (this._records.has(key)) {\n      const record = this._records.get(key)!;\n      this._maybeAddToChanges(record, value);\n      const prev = record._prev;\n      const next = record._next;\n      if (prev) {\n        prev._next = next;\n      }\n      if (next) {\n        next._prev = prev;\n      }\n      record._next = null;\n      record._prev = null;\n\n      return record;\n    }\n\n    const record = new KeyValueChangeRecord_<K, V>(key);\n    this._records.set(key, record);\n    record.currentValue = value;\n    this._addToAdditions(record);\n    return record;\n  }\n\n  /** @internal */\n  _reset() {\n    if (this.isDirty) {\n      let record: KeyValueChangeRecord_<K, V> | null;\n      // let `_previousMapHead` contain the state of the map before the changes\n      this._previousMapHead = this._mapHead;\n      for (record = this._previousMapHead; record !== null; record = record._next) {\n        record._nextPrevious = record._next;\n      }\n\n      // Update `record.previousValue` with the value of the item before the changes\n      // We need to update all changed items (that's those which have been added and changed)\n      for (record = this._changesHead; record !== null; record = record._nextChanged) {\n        record.previousValue = record.currentValue;\n      }\n      for (record = this._additionsHead; record != null; record = record._nextAdded) {\n        record.previousValue = record.currentValue;\n      }\n\n      this._changesHead = this._changesTail = null;\n      this._additionsHead = this._additionsTail = null;\n      this._removalsHead = null;\n    }\n  }\n\n  // Add the record or a given key to the list of changes only when the value has actually changed\n  private _maybeAddToChanges(record: KeyValueChangeRecord_<K, V>, newValue: any): void {\n    if (!Object.is(newValue, record.currentValue)) {\n      record.previousValue = record.currentValue;\n      record.currentValue = newValue;\n      this._addToChanges(record);\n    }\n  }\n\n  private _addToAdditions(record: KeyValueChangeRecord_<K, V>) {\n    if (this._additionsHead === null) {\n      this._additionsHead = this._additionsTail = record;\n    } else {\n      this._additionsTail!._nextAdded = record;\n      this._additionsTail = record;\n    }\n  }\n\n  private _addToChanges(record: KeyValueChangeRecord_<K, V>) {\n    if (this._changesHead === null) {\n      this._changesHead = this._changesTail = record;\n    } else {\n      this._changesTail!._nextChanged = record;\n      this._changesTail = record;\n    }\n  }\n\n  /** @internal */\n  private _forEach<K, V>(obj: Map<K, V> | {[k: string]: V}, fn: (v: V, k: any) => void) {\n    if (obj instanceof Map) {\n      obj.forEach(fn);\n    } else {\n      Object.keys(obj).forEach((k) => fn(obj[k], k));\n    }\n  }\n}\n\nclass KeyValueChangeRecord_<K, V> implements KeyValueChangeRecord<K, V> {\n  previousValue: V | null = null;\n  currentValue: V | null = null;\n\n  /** @internal */\n  _nextPrevious: KeyValueChangeRecord_<K, V> | null = null;\n  /** @internal */\n  _next: KeyValueChangeRecord_<K, V> | null = null;\n  /** @internal */\n  _prev: KeyValueChangeRecord_<K, V> | null = null;\n  /** @internal */\n  _nextAdded: KeyValueChangeRecord_<K, V> | null = null;\n  /** @internal */\n  _nextRemoved: KeyValueChangeRecord_<K, V> | null = null;\n  /** @internal */\n  _nextChanged: KeyValueChangeRecord_<K, V> | null = null;\n\n  constructor(public key: K) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ɵɵdefineInjectable} from '../../di/interface/defs';\nimport {StaticProvider} from '../../di/interface/provider';\nimport {Optional, SkipSelf} from '../../di/metadata';\nimport {RuntimeError, RuntimeErrorCode} from '../../errors';\nimport {DefaultIterableDifferFactory} from '../differs/default_iterable_differ';\n\n/**\n * A type describing supported iterable types.\n *\n * @publicApi\n */\nexport type NgIterable<T> = Array<T> | Iterable<T>;\n\n/**\n * A strategy for tracking changes over time to an iterable. Used by {@link /api/common/NgForOf NgForOf} to\n * respond to changes in an iterable by effecting equivalent changes in the DOM.\n *\n * @publicApi\n */\nexport interface IterableDiffer<V> {\n  /**\n   * Compute a difference between the previous state and the new `object` state.\n   *\n   * @param object containing the new value.\n   * @returns an object describing the difference. The return value is only valid until the next\n   * `diff()` invocation.\n   */\n  diff(object: NgIterable<V> | undefined | null): IterableChanges<V> | null;\n}\n\n/**\n * An object describing the changes in the `Iterable` collection since last time\n * `IterableDiffer#diff()` was invoked.\n *\n * @publicApi\n */\nexport interface IterableChanges<V> {\n  /**\n   * Iterate over all changes. `IterableChangeRecord` will contain information about changes\n   * to each item.\n   */\n  forEachItem(fn: (record: IterableChangeRecord<V>) => void): void;\n\n  /**\n   * Iterate over a set of operations which when applied to the original `Iterable` will produce the\n   * new `Iterable`.\n   *\n   * NOTE: These are not necessarily the actual operations which were applied to the original\n   * `Iterable`, rather these are a set of computed operations which may not be the same as the\n   * ones applied.\n   *\n   * @param record A change which needs to be applied\n   * @param previousIndex The `IterableChangeRecord#previousIndex` of the `record` refers to the\n   *        original `Iterable` location, where as `previousIndex` refers to the transient location\n   *        of the item, after applying the operations up to this point.\n   * @param currentIndex The `IterableChangeRecord#currentIndex` of the `record` refers to the\n   *        original `Iterable` location, where as `currentIndex` refers to the transient location\n   *        of the item, after applying the operations up to this point.\n   */\n  forEachOperation(\n    fn: (\n      record: IterableChangeRecord<V>,\n      previousIndex: number | null,\n      currentIndex: number | null,\n    ) => void,\n  ): void;\n\n  /**\n   * Iterate over changes in the order of original `Iterable` showing where the original items\n   * have moved.\n   */\n  forEachPreviousItem(fn: (record: IterableChangeRecord<V>) => void): void;\n\n  /** Iterate over all added items. */\n  forEachAddedItem(fn: (record: IterableChangeRecord<V>) => void): void;\n\n  /** Iterate over all moved items. */\n  forEachMovedItem(fn: (record: IterableChangeRecord<V>) => void): void;\n\n  /** Iterate over all removed items. */\n  forEachRemovedItem(fn: (record: IterableChangeRecord<V>) => void): void;\n\n  /**\n   * Iterate over all items which had their identity (as computed by the `TrackByFunction`)\n   * changed.\n   */\n  forEachIdentityChange(fn: (record: IterableChangeRecord<V>) => void): void;\n}\n\n/**\n * Record representing the item change information.\n *\n * @publicApi\n */\nexport interface IterableChangeRecord<V> {\n  /** Current index of the item in `Iterable` or null if removed. */\n  readonly currentIndex: number | null;\n\n  /** Previous index of the item in `Iterable` or null if added. */\n  readonly previousIndex: number | null;\n\n  /** The item. */\n  readonly item: V;\n\n  /** Track by identity as computed by the `TrackByFunction`. */\n  readonly trackById: any;\n}\n\n/**\n * A function optionally passed into the `NgForOf` directive to customize how `NgForOf` uniquely\n * identifies items in an iterable.\n *\n * `NgForOf` needs to uniquely identify items in the iterable to correctly perform DOM updates\n * when items in the iterable are reordered, new items are added, or existing items are removed.\n *\n *\n * In all of these scenarios it is usually desirable to only update the DOM elements associated\n * with the items affected by the change. This behavior is important to:\n *\n * - preserve any DOM-specific UI state (like cursor position, focus, text selection) when the\n *   iterable is modified\n * - enable animation of item addition, removal, and iterable reordering\n * - preserve the value of the `<select>` element when nested `<option>` elements are dynamically\n *   populated using `NgForOf` and the bound iterable is updated\n *\n * A common use for custom `trackBy` functions is when the model that `NgForOf` iterates over\n * contains a property with a unique identifier. For example, given a model:\n *\n * ```ts\n * class User {\n *   id: number;\n *   name: string;\n *   ...\n * }\n * ```\n * a custom `trackBy` function could look like the following:\n * ```ts\n * function userTrackBy(index, user) {\n *   return user.id;\n * }\n * ```\n *\n * A custom `trackBy` function must have several properties:\n *\n * - be [idempotent](https://en.wikipedia.org/wiki/Idempotence) (be without side effects, and always\n * return the same value for a given input)\n * - return unique value for all unique inputs\n * - be fast\n *\n * @see [`NgForOf#ngForTrackBy`](api/common/NgForOf#ngForTrackBy)\n * @publicApi\n */\nexport interface TrackByFunction<T> {\n  // Note: the type parameter `U` enables more accurate template type checking in case a trackBy\n  // function is declared using a base type of the iterated type. The `U` type gives TypeScript\n  // additional freedom to infer a narrower type for the `item` parameter type, instead of imposing\n  // the trackBy's declared item type as the inferred type for `T`.\n  // See https://github.com/angular/angular/issues/40125\n\n  /**\n   * @param index The index of the item within the iterable.\n   * @param item The item in the iterable.\n   */\n  <U extends T>(index: number, item: T & U): any;\n}\n\n/**\n * Provides a factory for {@link IterableDiffer}.\n *\n * @publicApi\n */\nexport interface IterableDifferFactory {\n  supports(objects: any): boolean;\n  create<V>(trackByFn?: TrackByFunction<V>): IterableDiffer<V>;\n}\n\nexport function defaultIterableDiffersFactory() {\n  return new IterableDiffers([new DefaultIterableDifferFactory()]);\n}\n\n/**\n * A repository of different iterable diffing strategies used by NgFor, NgClass, and others.\n *\n * @publicApi\n */\nexport class IterableDiffers {\n  /** @nocollapse */\n  static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ ɵɵdefineInjectable({\n    token: IterableDiffers,\n    providedIn: 'root',\n    factory: defaultIterableDiffersFactory,\n  });\n\n  constructor(private factories: IterableDifferFactory[]) {}\n\n  static create(factories: IterableDifferFactory[], parent?: IterableDiffers): IterableDiffers {\n    if (parent != null) {\n      const copied = parent.factories.slice();\n      factories = factories.concat(copied);\n    }\n\n    return new IterableDiffers(factories);\n  }\n\n  /**\n   * Takes an array of {@link IterableDifferFactory} and returns a provider used to extend the\n   * inherited {@link IterableDiffers} instance with the provided factories and return a new\n   * {@link IterableDiffers} instance.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * The following example shows how to extend an existing list of factories,\n   * which will only be applied to the injector for this component and its children.\n   * This step is all that's required to make a new {@link IterableDiffer} available.\n   *\n   * ```ts\n   * @Component({\n   *   viewProviders: [\n   *     IterableDiffers.extend([new ImmutableListDiffer()])\n   *   ]\n   * })\n   * ```\n   */\n  static extend(factories: IterableDifferFactory[]): StaticProvider {\n    return {\n      provide: IterableDiffers,\n      useFactory: (parent: IterableDiffers | null) => {\n        // if parent is null, it means that we are in the root injector and we have just overridden\n        // the default injection mechanism for IterableDiffers, in such a case just assume\n        // `defaultIterableDiffersFactory`.\n        return IterableDiffers.create(factories, parent || defaultIterableDiffersFactory());\n      },\n      // Dependency technically isn't optional, but we can provide a better error message this way.\n      deps: [[IterableDiffers, new SkipSelf(), new Optional()]],\n    };\n  }\n\n  find(iterable: any): IterableDifferFactory {\n    const factory = this.factories.find((f) => f.supports(iterable));\n    if (factory != null) {\n      return factory;\n    } else {\n      throw new RuntimeError(\n        RuntimeErrorCode.NO_SUPPORTING_DIFFER_FACTORY,\n        ngDevMode &&\n          `Cannot find a differ supporting object '${iterable}' of type '${getTypeNameForDebugging(\n            iterable,\n          )}'`,\n      );\n    }\n  }\n}\n\nexport function getTypeNameForDebugging(type: any): string {\n  return type['name'] || typeof type;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Optional, SkipSelf, StaticProvider, ɵɵdefineInjectable} from '../../di';\nimport {RuntimeError, RuntimeErrorCode} from '../../errors';\n\nimport {DefaultKeyValueDifferFactory} from './default_keyvalue_differ';\n\n/**\n * A differ that tracks changes made to an object over time.\n *\n * @publicApi\n */\nexport interface KeyValueDiffer<K, V> {\n  /**\n   * Compute a difference between the previous state and the new `object` state.\n   *\n   * @param object containing the new value.\n   * @returns an object describing the difference. The return value is only valid until the next\n   * `diff()` invocation.\n   */\n  diff(object: Map<K, V>): KeyValueChanges<K, V> | null;\n\n  /**\n   * Compute a difference between the previous state and the new `object` state.\n   *\n   * @param object containing the new value.\n   * @returns an object describing the difference. The return value is only valid until the next\n   * `diff()` invocation.\n   */\n  diff(object: {[key: string]: V}): KeyValueChanges<string, V> | null;\n  // TODO(TS2.1): diff<KP extends string>(this: KeyValueDiffer<KP, V>, object: Record<KP, V>):\n  // KeyValueDiffer<KP, V>;\n}\n\n/**\n * An object describing the changes in the `Map` or `{[k:string]: string}` since last time\n * `KeyValueDiffer#diff()` was invoked.\n *\n * @publicApi\n */\nexport interface KeyValueChanges<K, V> {\n  /**\n   * Iterate over all changes. `KeyValueChangeRecord` will contain information about changes\n   * to each item.\n   */\n  forEachItem(fn: (r: KeyValueChangeRecord<K, V>) => void): void;\n\n  /**\n   * Iterate over changes in the order of original Map showing where the original items\n   * have moved.\n   */\n  forEachPreviousItem(fn: (r: KeyValueChangeRecord<K, V>) => void): void;\n\n  /**\n   * Iterate over all keys for which values have changed.\n   */\n  forEachChangedItem(fn: (r: KeyValueChangeRecord<K, V>) => void): void;\n\n  /**\n   * Iterate over all added items.\n   */\n  forEachAddedItem(fn: (r: KeyValueChangeRecord<K, V>) => void): void;\n\n  /**\n   * Iterate over all removed items.\n   */\n  forEachRemovedItem(fn: (r: KeyValueChangeRecord<K, V>) => void): void;\n}\n\n/**\n * Record representing the item change information.\n *\n * @publicApi\n */\nexport interface KeyValueChangeRecord<K, V> {\n  /**\n   * Current key in the Map.\n   */\n  readonly key: K;\n\n  /**\n   * Current value for the key or `null` if removed.\n   */\n  readonly currentValue: V | null;\n\n  /**\n   * Previous value for the key or `null` if added.\n   */\n  readonly previousValue: V | null;\n}\n\n/**\n * Provides a factory for {@link KeyValueDiffer}.\n *\n * @publicApi\n */\nexport interface KeyValueDifferFactory {\n  /**\n   * Test to see if the differ knows how to diff this kind of object.\n   */\n  supports(objects: any): boolean;\n\n  /**\n   * Create a `KeyValueDiffer`.\n   */\n  create<K, V>(): KeyValueDiffer<K, V>;\n}\n\nexport function defaultKeyValueDiffersFactory() {\n  return new KeyValueDiffers([new DefaultKeyValueDifferFactory()]);\n}\n\n/**\n * A repository of different Map diffing strategies used by NgClass, NgStyle, and others.\n *\n * @publicApi\n */\nexport class KeyValueDiffers {\n  /** @nocollapse */\n  static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ ɵɵdefineInjectable({\n    token: KeyValueDiffers,\n    providedIn: 'root',\n    factory: defaultKeyValueDiffersFactory,\n  });\n\n  private readonly factories: KeyValueDifferFactory[];\n\n  constructor(factories: KeyValueDifferFactory[]) {\n    this.factories = factories;\n  }\n\n  static create<S>(factories: KeyValueDifferFactory[], parent?: KeyValueDiffers): KeyValueDiffers {\n    if (parent) {\n      const copied = parent.factories.slice();\n      factories = factories.concat(copied);\n    }\n    return new KeyValueDiffers(factories);\n  }\n\n  /**\n   * Takes an array of {@link KeyValueDifferFactory} and returns a provider used to extend the\n   * inherited {@link KeyValueDiffers} instance with the provided factories and return a new\n   * {@link KeyValueDiffers} instance.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * The following example shows how to extend an existing list of factories,\n   * which will only be applied to the injector for this component and its children.\n   * This step is all that's required to make a new {@link KeyValueDiffer} available.\n   *\n   * ```ts\n   * @Component({\n   *   viewProviders: [\n   *     KeyValueDiffers.extend([new ImmutableMapDiffer()])\n   *   ]\n   * })\n   * ```\n   */\n  static extend<S>(factories: KeyValueDifferFactory[]): StaticProvider {\n    return {\n      provide: KeyValueDiffers,\n      useFactory: (parent: KeyValueDiffers) => {\n        // if parent is null, it means that we are in the root injector and we have just overridden\n        // the default injection mechanism for KeyValueDiffers, in such a case just assume\n        // `defaultKeyValueDiffersFactory`.\n        return KeyValueDiffers.create(factories, parent || defaultKeyValueDiffersFactory());\n      },\n      // Dependency technically isn't optional, but we can provide a better error message this way.\n      deps: [[KeyValueDiffers, new SkipSelf(), new Optional()]],\n    };\n  }\n\n  find(kv: any): KeyValueDifferFactory {\n    const factory = this.factories.find((f) => f.supports(kv));\n    if (factory) {\n      return factory;\n    }\n    throw new RuntimeError(\n      RuntimeErrorCode.NO_SUPPORTING_DIFFER_FACTORY,\n      ngDevMode && `Cannot find a differ supporting object '${kv}'`,\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DefaultIterableDifferFactory} from './differs/default_iterable_differ';\nimport {DefaultKeyValueDifferFactory} from './differs/default_keyvalue_differ';\nimport {IterableDifferFactory, IterableDiffers} from './differs/iterable_differs';\nimport {KeyValueDifferFactory, KeyValueDiffers} from './differs/keyvalue_differs';\n\nexport {SimpleChange, SimpleChanges} from '../interface/simple_change';\nexport {devModeEqual} from '../util/comparison';\nexport {ChangeDetectorRef} from './change_detector_ref';\nexport {ChangeDetectionStrategy} from './constants';\nexport {\n  DefaultIterableDiffer,\n  DefaultIterableDifferFactory,\n} from './differs/default_iterable_differ';\nexport {DefaultKeyValueDifferFactory} from './differs/default_keyvalue_differ';\nexport {\n  IterableChangeRecord,\n  IterableChanges,\n  IterableDiffer,\n  IterableDifferFactory,\n  IterableDiffers,\n  NgIterable,\n  TrackByFunction,\n} from './differs/iterable_differs';\nexport {\n  KeyValueChangeRecord,\n  KeyValueChanges,\n  KeyValueDiffer,\n  KeyValueDifferFactory,\n  KeyValueDiffers,\n} from './differs/keyvalue_differs';\n\nexport {PipeTransform} from './pipe_transform';\n\n/**\n * Structural diffing for `Object`s and `Map`s.\n */\nconst keyValDiff: KeyValueDifferFactory[] = [new DefaultKeyValueDifferFactory()];\n\n/**\n * Structural diffing for `Iterable` types such as `Array`s.\n */\nconst iterableDiff: IterableDifferFactory[] = [new DefaultIterableDifferFactory()];\n\nexport const defaultIterableDiffers = new IterableDiffers(iterableDiff);\n\nexport const defaultKeyValueDiffers = new KeyValueDiffers(keyValDiff);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {StaticProvider} from '../di';\n\nimport {createPlatformFactory} from './platform';\nimport {PlatformRef} from './platform_ref';\n\n/**\n * This platform has to be included in any other platform\n *\n * @publicApi\n */\nexport const platformCore: (extraProviders?: StaticProvider[] | undefined) => PlatformRef =\n  createPlatformFactory(null, 'core', []);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '../metadata';\n\nimport {ApplicationRef} from './application_ref';\n\n/**\n * Re-exported by `BrowserModule`, which is included automatically in the root\n * `AppModule` when you create a new app with the CLI `new` command. Eagerly injects\n * `ApplicationRef` to instantiate it.\n *\n * @publicApi\n */\n@NgModule()\nexport class ApplicationModule {\n  // Inject ApplicationRef to make it eager...\n  constructor(appRef: ApplicationRef) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {internalProvideZoneChangeDetection} from '../change_detection/scheduling/ng_zone_scheduling';\nimport {EnvironmentProviders, Provider, StaticProvider} from '../di/interface/provider';\nimport {EnvironmentInjector} from '../di/r3_injector';\nimport {Type} from '../interface/type';\nimport {createOrReusePlatformInjector} from '../platform/platform';\nimport {assertStandaloneComponentType} from '../render3/errors';\nimport {EnvironmentNgModuleRefAdapter} from '../render3/ng_module_ref';\n\nimport {ApplicationRef} from './application_ref';\nimport {ChangeDetectionScheduler} from '../change_detection/scheduling/zoneless_scheduling';\nimport {ChangeDetectionSchedulerImpl} from '../change_detection/scheduling/zoneless_scheduling_impl';\nimport {bootstrap} from '../platform/bootstrap';\nimport {profiler} from '../render3/profiler';\nimport {ProfilerEvent} from '../render3/profiler_types';\nimport {errorHandlerEnvironmentInitializer} from '../error_handler';\n\n/**\n * Internal create application API that implements the core application creation logic and optional\n * bootstrap logic.\n *\n * Platforms (such as `platform-browser`) may require different set of application and platform\n * providers for an application to function correctly. As a result, platforms may use this function\n * internally and supply the necessary providers during the bootstrap, while exposing\n * platform-specific APIs as a part of their public API.\n *\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n */\n\nexport function internalCreateApplication(config: {\n  rootComponent?: Type<unknown>;\n  appProviders?: Array<Provider | EnvironmentProviders>;\n  platformProviders?: Provider[];\n}): Promise<ApplicationRef> {\n  profiler(ProfilerEvent.BootstrapApplicationStart);\n  try {\n    const {rootComponent, appProviders, platformProviders} = config;\n\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && rootComponent !== undefined) {\n      assertStandaloneComponentType(rootComponent);\n    }\n\n    const platformInjector = createOrReusePlatformInjector(platformProviders as StaticProvider[]);\n\n    // Create root application injector based on a set of providers configured at the platform\n    // bootstrap level as well as providers passed to the bootstrap call by a user.\n    const allAppProviders = [\n      internalProvideZoneChangeDetection({}),\n      {provide: ChangeDetectionScheduler, useExisting: ChangeDetectionSchedulerImpl},\n      errorHandlerEnvironmentInitializer,\n      ...(appProviders || []),\n    ];\n    const adapter = new EnvironmentNgModuleRefAdapter({\n      providers: allAppProviders,\n      parent: platformInjector as EnvironmentInjector,\n      debugName: typeof ngDevMode === 'undefined' || ngDevMode ? 'Environment Injector' : '',\n      // We skip environment initializers because we need to run them inside the NgZone, which\n      // happens after we get the NgZone instance from the Injector.\n      runEnvironmentInitializers: false,\n    });\n\n    return bootstrap({\n      r3Injector: adapter.injector,\n      platformInjector,\n      rootComponent,\n    });\n  } catch (e) {\n    return Promise.reject(e);\n  } finally {\n    profiler(ProfilerEvent.BootstrapApplicationEnd);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  isEarlyEventType,\n  isCaptureEventType,\n  EventContractContainer,\n  EventContract,\n  EventDispatcher,\n  registerDispatcher,\n  getAppScopedQueuedEventInfos,\n  clearAppScopedEarlyEventContract,\n  EventPhase,\n} from '../../primitives/event-dispatch';\n\nimport {APP_BOOTSTRAP_LISTENER, ApplicationRef} from '../application/application_ref';\nimport {ENVIRONMENT_INITIALIZER, Injector} from '../di';\nimport {inject} from '../di/injector_compatibility';\nimport {Provider} from '../di/interface/provider';\nimport {RElement, RNode} from '../render3/interfaces/renderer_dom';\nimport {CLEANUP, LView, TView} from '../render3/interfaces/view';\nimport {unwrapRNode} from '../render3/util/view_utils';\n\nimport {\n  JSACTION_BLOCK_ELEMENT_MAP,\n  EVENT_REPLAY_ENABLED_DEFAULT,\n  IS_EVENT_REPLAY_ENABLED,\n} from './tokens';\nimport {\n  sharedStashFunction,\n  sharedMapFunction,\n  DEFER_BLOCK_SSR_ID_ATTRIBUTE,\n  EventContractDetails,\n  JSACTION_EVENT_CONTRACT,\n  invokeListeners,\n  removeListeners,\n  enableStashEventListenerImpl,\n  setStashFn,\n} from '../event_delegation_utils';\nimport {APP_ID} from '../application/application_tokens';\nimport {performanceMarkFeature} from '../util/performance';\nimport {triggerHydrationFromBlockName} from '../defer/triggering';\nimport {isIncrementalHydrationEnabled} from './utils';\n\n/** Apps in which we've enabled event replay.\n *  This is to prevent initializing event replay more than once per app.\n */\nconst appsWithEventReplay = new WeakSet<ApplicationRef>();\n\n/**\n * The key that represents all replayable elements that are not in defer blocks.\n */\nconst EAGER_CONTENT_LISTENERS_KEY = '';\n\n/**\n * A list of block events that need to be replayed\n */\nlet blockEventQueue: {event: Event; currentTarget: Element}[] = [];\n\n/**\n * Determines whether Event Replay feature should be activated on the client.\n */\nfunction shouldEnableEventReplay(injector: Injector) {\n  return injector.get(IS_EVENT_REPLAY_ENABLED, EVENT_REPLAY_ENABLED_DEFAULT);\n}\n\n/**\n * Returns a set of providers required to setup support for event replay.\n * Requires hydration to be enabled separately.\n */\nexport function withEventReplay(): Provider[] {\n  const providers: Provider[] = [\n    {\n      provide: IS_EVENT_REPLAY_ENABLED,\n      useFactory: () => {\n        let isEnabled = true;\n        if (typeof ngServerMode === 'undefined' || !ngServerMode) {\n          // Note: globalThis[CONTRACT_PROPERTY] may be undefined in case Event Replay feature\n          // is enabled, but there are no events configured in this application, in which case\n          // we don't activate this feature, since there are no events to replay.\n          const appId = inject(APP_ID);\n          isEnabled = !!window._ejsas?.[appId];\n        }\n        if (isEnabled) {\n          performanceMarkFeature('NgEventReplay');\n        }\n        return isEnabled;\n      },\n    },\n  ];\n\n  if (typeof ngServerMode === 'undefined' || !ngServerMode) {\n    providers.push(\n      {\n        provide: ENVIRONMENT_INITIALIZER,\n        useValue: () => {\n          const appRef = inject(ApplicationRef);\n          const {injector} = appRef;\n          // We have to check for the appRef here due to the possibility of multiple apps\n          // being present on the same page. We only want to enable event replay for the\n          // apps that actually want it.\n          if (!appsWithEventReplay.has(appRef)) {\n            const jsActionMap = inject(JSACTION_BLOCK_ELEMENT_MAP);\n            if (shouldEnableEventReplay(injector)) {\n              enableStashEventListenerImpl();\n              const appId = injector.get(APP_ID);\n              const clearStashFn = setStashFn(\n                appId,\n                (rEl: RNode, eventName: string, listenerFn: VoidFunction) => {\n                  // If a user binds to a ng-container and uses a directive that binds using a host listener,\n                  // this element could be a comment node. So we need to ensure we have an actual element\n                  // node before stashing anything.\n                  if ((rEl as Node).nodeType !== Node.ELEMENT_NODE) return;\n                  sharedStashFunction(rEl as RElement, eventName, listenerFn);\n                  sharedMapFunction(rEl as RElement, jsActionMap);\n                },\n              );\n              // Clean up the reference to the function set by the environment initializer,\n              // as the function closure may capture injected elements and prevent them\n              // from being properly garbage collected.\n              appRef.onDestroy(clearStashFn);\n            }\n          }\n        },\n        multi: true,\n      },\n      {\n        provide: APP_BOOTSTRAP_LISTENER,\n        useFactory: () => {\n          const appRef = inject(ApplicationRef);\n          const {injector} = appRef;\n\n          return () => {\n            // We have to check for the appRef here due to the possibility of multiple apps\n            // being present on the same page. We only want to enable event replay for the\n            // apps that actually want it.\n            if (!shouldEnableEventReplay(injector) || appsWithEventReplay.has(appRef)) {\n              return;\n            }\n\n            appsWithEventReplay.add(appRef);\n\n            const appId = injector.get(APP_ID);\n            appRef.onDestroy(() => {\n              appsWithEventReplay.delete(appRef);\n              // Ensure that we're always safe calling this in the browser.\n              if (typeof ngServerMode !== 'undefined' && !ngServerMode) {\n                // `_ejsa` should be deleted when the app is destroyed, ensuring that\n                // no elements are still captured in the global list and are not prevented\n                // from being garbage collected.\n                clearAppScopedEarlyEventContract(appId);\n              }\n            });\n\n            // Kick off event replay logic once hydration for the initial part\n            // of the application is completed. This timing is similar to the unclaimed\n            // dehydrated views cleanup timing.\n            appRef.whenStable().then(() => {\n              // Note: we have to check whether the application is destroyed before\n              // performing other operations with the `injector`.\n              // The application may be destroyed **before** it becomes stable, so when\n              // the `whenStable` resolves, the injector might already be in\n              // a destroyed state. Thus, calling `injector.get` would throw an error\n              // indicating that the injector has already been destroyed.\n              if (appRef.destroyed) {\n                return;\n              }\n\n              const eventContractDetails = injector.get(JSACTION_EVENT_CONTRACT);\n              initEventReplay(eventContractDetails, injector);\n              const jsActionMap = injector.get(JSACTION_BLOCK_ELEMENT_MAP);\n              jsActionMap.get(EAGER_CONTENT_LISTENERS_KEY)?.forEach(removeListeners);\n              jsActionMap.delete(EAGER_CONTENT_LISTENERS_KEY);\n\n              const eventContract = eventContractDetails.instance!;\n              // This removes event listeners registered through the container manager,\n              // as listeners registered on `document.body` might never be removed if we\n              // don't clean up the contract.\n              if (isIncrementalHydrationEnabled(injector)) {\n                // When incremental hydration is enabled, we cannot clean up the event\n                // contract immediately because we're unaware if there are any deferred\n                // blocks to hydrate. We can only schedule a contract cleanup when the\n                // app is destroyed.\n                appRef.onDestroy(() => eventContract.cleanUp());\n              } else {\n                eventContract.cleanUp();\n              }\n            });\n          };\n        },\n        multi: true,\n      },\n    );\n  }\n\n  return providers;\n}\n\nconst initEventReplay = (eventDelegation: EventContractDetails, injector: Injector) => {\n  const appId = injector.get(APP_ID);\n  // This is set in packages/platform-server/src/utils.ts\n  const earlyJsactionData = window._ejsas![appId]!;\n  const eventContract = (eventDelegation.instance = new EventContract(\n    new EventContractContainer(earlyJsactionData.c),\n  ));\n  for (const et of earlyJsactionData.et) {\n    eventContract.addEvent(et);\n  }\n  for (const et of earlyJsactionData.etc) {\n    eventContract.addEvent(et);\n  }\n  const eventInfos = getAppScopedQueuedEventInfos(appId);\n  eventContract.replayEarlyEventInfos(eventInfos);\n  clearAppScopedEarlyEventContract(appId);\n  const dispatcher = new EventDispatcher((event) => {\n    invokeRegisteredReplayListeners(injector, event, event.currentTarget as Element);\n  });\n  registerDispatcher(eventContract, dispatcher);\n};\n\n/**\n * Extracts information about all DOM events (added in a template) registered on elements in a give\n * LView. Maps collected events to a corresponding DOM element (an element is used as a key).\n */\nexport function collectDomEventsInfo(\n  tView: TView,\n  lView: LView,\n  eventTypesToReplay: {regular: Set<string>; capture: Set<string>},\n): Map<Element, string[]> {\n  const domEventsInfo = new Map<Element, string[]>();\n  const lCleanup = lView[CLEANUP];\n  const tCleanup = tView.cleanup;\n  if (!tCleanup || !lCleanup) {\n    return domEventsInfo;\n  }\n  for (let i = 0; i < tCleanup.length; ) {\n    const firstParam = tCleanup[i++];\n    const secondParam = tCleanup[i++];\n    if (typeof firstParam !== 'string') {\n      continue;\n    }\n    const eventType = firstParam;\n    if (!isEarlyEventType(eventType)) {\n      continue;\n    }\n    if (isCaptureEventType(eventType)) {\n      eventTypesToReplay.capture.add(eventType);\n    } else {\n      eventTypesToReplay.regular.add(eventType);\n    }\n    const listenerElement = unwrapRNode(lView[secondParam]) as any as Element;\n    i++; // move the cursor to the next position (location of the listener idx)\n    const useCaptureOrIndx = tCleanup[i++];\n    // if useCaptureOrIndx is boolean then report it as is.\n    // if useCaptureOrIndx is positive number then it in unsubscribe method\n    // if useCaptureOrIndx is negative number then it is a Subscription\n    const isDomEvent = typeof useCaptureOrIndx === 'boolean' || useCaptureOrIndx >= 0;\n    if (!isDomEvent) {\n      continue;\n    }\n    if (!domEventsInfo.has(listenerElement)) {\n      domEventsInfo.set(listenerElement, [eventType]);\n    } else {\n      domEventsInfo.get(listenerElement)!.push(eventType);\n    }\n  }\n  return domEventsInfo;\n}\n\nexport function invokeRegisteredReplayListeners(\n  injector: Injector,\n  event: Event,\n  currentTarget: Element | null,\n) {\n  const blockName =\n    (currentTarget && currentTarget.getAttribute(DEFER_BLOCK_SSR_ID_ATTRIBUTE)) ?? '';\n  if (/d\\d+/.test(blockName)) {\n    hydrateAndInvokeBlockListeners(blockName, injector, event, currentTarget!);\n  } else if (event.eventPhase === EventPhase.REPLAY) {\n    invokeListeners(event, currentTarget);\n  }\n}\n\nfunction hydrateAndInvokeBlockListeners(\n  blockName: string,\n  injector: Injector,\n  event: Event,\n  currentTarget: Element,\n) {\n  blockEventQueue.push({event, currentTarget});\n  triggerHydrationFromBlockName(injector, blockName, replayQueuedBlockEvents);\n}\n\nfunction replayQueuedBlockEvents(hydratedBlocks: string[]) {\n  // clone the queue\n  const queue = [...blockEventQueue];\n  const hydrated = new Set<string>(hydratedBlocks);\n  // empty it\n  blockEventQueue = [];\n  for (let {event, currentTarget} of queue) {\n    const blockName = currentTarget.getAttribute(DEFER_BLOCK_SSR_ID_ATTRIBUTE)!;\n    if (hydrated.has(blockName)) {\n      invokeListeners(event, currentTarget);\n    } else {\n      // requeue events that weren't yet hydrated\n      blockEventQueue.push({event, currentTarget});\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ApplicationRef} from '../application/application_ref';\nimport {APP_ID} from '../application/application_tokens';\nimport {\n  DEFER_BLOCK_STATE as CURRENT_DEFER_BLOCK_STATE,\n  DeferBlockTrigger,\n  HydrateTriggerDetails,\n  TDeferBlockDetails,\n} from '../defer/interfaces';\nimport {getLDeferBlockDetails, getTDeferBlockDetails, isDeferBlock} from '../defer/utils';\nimport {isDetachedByI18n} from '../i18n/utils';\nimport {ViewEncapsulation} from '../metadata';\nimport {Renderer2} from '../render';\nimport {assertTNode} from '../render3/assert';\nimport {collectNativeNodes, collectNativeNodesInLContainer} from '../render3/collect_native_nodes';\nimport {getComponentDef} from '../render3/def_getters';\nimport {CONTAINER_HEADER_OFFSET, LContainer} from '../render3/interfaces/container';\nimport {isLetDeclaration, isTNodeShape, TNode, TNodeType} from '../render3/interfaces/node';\nimport {RComment, RElement} from '../render3/interfaces/renderer_dom';\nimport {\n  hasI18n,\n  isComponentHost,\n  isLContainer,\n  isProjectionTNode,\n  isRootView,\n} from '../render3/interfaces/type_checks';\nimport {\n  CONTEXT,\n  HEADER_OFFSET,\n  HOST,\n  INJECTOR,\n  LView,\n  PARENT,\n  RENDERER,\n  TView,\n  TVIEW,\n  TViewType,\n} from '../render3/interfaces/view';\nimport {unwrapLView, unwrapRNode} from '../render3/util/view_utils';\nimport {TransferState} from '../transfer_state';\n\nimport {\n  unsupportedProjectionOfDomNodes,\n  validateMatchingNode,\n  validateNodeExists,\n} from './error_handling';\nimport {collectDomEventsInfo} from './event_replay';\nimport {setJSActionAttributes} from '../event_delegation_utils';\nimport {\n  getOrComputeI18nChildren,\n  isI18nHydrationEnabled,\n  isI18nHydrationSupportEnabled,\n  trySerializeI18nBlock,\n} from './i18n';\nimport {\n  CONTAINERS,\n  DEFER_BLOCK_ID,\n  DEFER_BLOCK_STATE,\n  DEFER_HYDRATE_TRIGGERS,\n  DEFER_PARENT_BLOCK_ID,\n  DISCONNECTED_NODES,\n  ELEMENT_CONTAINERS,\n  I18N_DATA,\n  MULTIPLIER,\n  NODES,\n  NUM_ROOT_NODES,\n  SerializedContainerView,\n  SerializedDeferBlock,\n  SerializedTriggerDetails,\n  SerializedView,\n  TEMPLATE_ID,\n  TEMPLATES,\n} from './interfaces';\nimport {calcPathForNode, isDisconnectedNode} from './node_lookup_utils';\nimport {isInSkipHydrationBlock, SKIP_HYDRATION_ATTR_NAME} from './skip_hydration';\nimport {EVENT_REPLAY_ENABLED_DEFAULT, IS_EVENT_REPLAY_ENABLED} from './tokens';\nimport {\n  convertHydrateTriggersToJsAction,\n  getLNodeForHydration,\n  isIncrementalHydrationEnabled,\n  NGH_ATTR_NAME,\n  NGH_DATA_KEY,\n  NGH_DEFER_BLOCKS_KEY,\n  processTextNodeBeforeSerialization,\n  TextNodeMarker,\n} from './utils';\nimport {Injector} from '../di';\n\n/**\n * A collection that tracks all serialized views (`ngh` DOM annotations)\n * to avoid duplication. An attempt to add a duplicate view results in the\n * collection returning the index of the previously collected serialized view.\n * This reduces the number of annotations needed for a given page.\n */\nclass SerializedViewCollection {\n  private views: SerializedView[] = [];\n  private indexByContent = new Map<string, number>();\n\n  add(serializedView: SerializedView): number {\n    const viewAsString = JSON.stringify(serializedView);\n    if (!this.indexByContent.has(viewAsString)) {\n      const index = this.views.length;\n      this.views.push(serializedView);\n      this.indexByContent.set(viewAsString, index);\n      return index;\n    }\n    return this.indexByContent.get(viewAsString)!;\n  }\n\n  getAll(): SerializedView[] {\n    return this.views;\n  }\n}\n\n/**\n * Global counter that is used to generate a unique id for TViews\n * during the serialization process.\n */\nlet tViewSsrId = 0;\n\n/**\n * Generates a unique id for a given TView and returns this id.\n * The id is also stored on this instance of a TView and reused in\n * subsequent calls.\n *\n * This id is needed to uniquely identify and pick up dehydrated views\n * at runtime.\n */\nfunction getSsrId(tView: TView): string {\n  if (!tView.ssrId) {\n    tView.ssrId = `t${tViewSsrId++}`;\n  }\n  return tView.ssrId;\n}\n\n/**\n * Describes a context available during the serialization\n * process. The context is used to share and collect information\n * during the serialization.\n */\nexport interface HydrationContext {\n  serializedViewCollection: SerializedViewCollection;\n  corruptedTextNodes: Map<HTMLElement, TextNodeMarker>;\n  isI18nHydrationEnabled: boolean;\n  isIncrementalHydrationEnabled: boolean;\n  i18nChildren: Map<TView, Set<number> | null>;\n  eventTypesToReplay: {regular: Set<string>; capture: Set<string>};\n  shouldReplayEvents: boolean;\n  appId: string; // the value of `APP_ID`\n  deferBlocks: Map<string /* defer block id, e.g. `d0` */, SerializedDeferBlock>;\n}\n\n/**\n * Computes the number of root nodes in a given view\n * (or child nodes in a given container if a tNode is provided).\n */\nfunction calcNumRootNodes(tView: TView, lView: LView, tNode: TNode | null): number {\n  const rootNodes: unknown[] = [];\n  collectNativeNodes(tView, lView, tNode, rootNodes);\n  return rootNodes.length;\n}\n\n/**\n * Computes the number of root nodes in all views in a given LContainer.\n */\nfunction calcNumRootNodesInLContainer(lContainer: LContainer): number {\n  const rootNodes: unknown[] = [];\n  collectNativeNodesInLContainer(lContainer, rootNodes);\n  return rootNodes.length;\n}\n\n/**\n * Annotates root level component's LView for hydration,\n * see `annotateHostElementForHydration` for additional information.\n */\nfunction annotateComponentLViewForHydration(\n  lView: LView,\n  context: HydrationContext,\n  injector: Injector,\n): number | null {\n  const hostElement = lView[HOST];\n  // Root elements might also be annotated with the `ngSkipHydration` attribute,\n  // check if it's present before starting the serialization process.\n  if (hostElement && !(hostElement as HTMLElement).hasAttribute(SKIP_HYDRATION_ATTR_NAME)) {\n    return annotateHostElementForHydration(hostElement as HTMLElement, lView, null, context);\n  }\n  return null;\n}\n\n/**\n * Annotates root level LContainer for hydration. This happens when a root component\n * injects ViewContainerRef, thus making the component an anchor for a view container.\n * This function serializes the component itself as well as all views from the view\n * container.\n */\nfunction annotateLContainerForHydration(\n  lContainer: LContainer,\n  context: HydrationContext,\n  injector: Injector,\n) {\n  const componentLView = unwrapLView(lContainer[HOST]) as LView<unknown>;\n\n  // Serialize the root component itself.\n  const componentLViewNghIndex = annotateComponentLViewForHydration(\n    componentLView,\n    context,\n    injector,\n  );\n\n  if (componentLViewNghIndex === null) {\n    // Component was not serialized (for example, if hydration was skipped by adding\n    // the `ngSkipHydration` attribute or this component uses i18n blocks in the template,\n    // but `withI18nSupport()` was not added), avoid annotating host element with the `ngh`\n    // attribute.\n    return;\n  }\n\n  const hostElement = unwrapRNode(componentLView[HOST]!) as HTMLElement;\n\n  // Serialize all views within this view container.\n  const rootLView = lContainer[PARENT];\n  const rootLViewNghIndex = annotateHostElementForHydration(hostElement, rootLView, null, context);\n\n  const renderer = componentLView[RENDERER] as Renderer2;\n\n  // For cases when a root component also acts as an anchor node for a ViewContainerRef\n  // (for example, when ViewContainerRef is injected in a root component), there is a need\n  // to serialize information about the component itself, as well as an LContainer that\n  // represents this ViewContainerRef. Effectively, we need to serialize 2 pieces of info:\n  // (1) hydration info for the root component itself and (2) hydration info for the\n  // ViewContainerRef instance (an LContainer). Each piece of information is included into\n  // the hydration data (in the TransferState object) separately, thus we end up with 2 ids.\n  // Since we only have 1 root element, we encode both bits of info into a single string:\n  // ids are separated by the `|` char (e.g. `10|25`, where `10` is the ngh for a component view\n  // and 25 is the `ngh` for a root view which holds LContainer).\n  const finalIndex = `${componentLViewNghIndex}|${rootLViewNghIndex}`;\n  renderer.setAttribute(hostElement, NGH_ATTR_NAME, finalIndex);\n}\n\n/**\n * Annotates all components bootstrapped in a given ApplicationRef\n * with info needed for hydration.\n *\n * @param appRef An instance of an ApplicationRef.\n * @param doc A reference to the current Document instance.\n * @return event types that need to be replayed\n */\nexport function annotateForHydration(appRef: ApplicationRef, doc: Document) {\n  const injector = appRef.injector;\n  const isI18nHydrationEnabledVal = isI18nHydrationEnabled(injector);\n  const isIncrementalHydrationEnabledVal = isIncrementalHydrationEnabled(injector);\n  const serializedViewCollection = new SerializedViewCollection();\n  const corruptedTextNodes = new Map<HTMLElement, TextNodeMarker>();\n  const viewRefs = appRef._views;\n  const shouldReplayEvents = injector.get(IS_EVENT_REPLAY_ENABLED, EVENT_REPLAY_ENABLED_DEFAULT);\n  const eventTypesToReplay = {\n    regular: new Set<string>(),\n    capture: new Set<string>(),\n  };\n  const deferBlocks = new Map<string, SerializedDeferBlock>();\n  const appId = appRef.injector.get(APP_ID);\n  for (const viewRef of viewRefs) {\n    const lNode = getLNodeForHydration(viewRef);\n\n    // An `lView` might be `null` if a `ViewRef` represents\n    // an embedded view (not a component view).\n    if (lNode !== null) {\n      const context: HydrationContext = {\n        serializedViewCollection,\n        corruptedTextNodes,\n        isI18nHydrationEnabled: isI18nHydrationEnabledVal,\n        isIncrementalHydrationEnabled: isIncrementalHydrationEnabledVal,\n        i18nChildren: new Map(),\n        eventTypesToReplay,\n        shouldReplayEvents,\n        appId,\n        deferBlocks,\n      };\n      if (isLContainer(lNode)) {\n        annotateLContainerForHydration(lNode, context, injector);\n      } else {\n        annotateComponentLViewForHydration(lNode, context, injector);\n      }\n      insertCorruptedTextNodeMarkers(corruptedTextNodes, doc);\n    }\n  }\n\n  // Note: we *always* include hydration info key and a corresponding value\n  // into the TransferState, even if the list of serialized views is empty.\n  // This is needed as a signal to the client that the server part of the\n  // hydration logic was setup and enabled correctly. Otherwise, if a client\n  // hydration doesn't find a key in the transfer state - an error is produced.\n  const serializedViews = serializedViewCollection.getAll();\n  const transferState = injector.get(TransferState);\n  transferState.set(NGH_DATA_KEY, serializedViews);\n\n  if (deferBlocks.size > 0) {\n    const blocks: {[key: string]: SerializedDeferBlock} = {};\n    for (const [id, info] of deferBlocks.entries()) {\n      blocks[id] = info;\n    }\n    transferState.set(NGH_DEFER_BLOCKS_KEY, blocks);\n  }\n\n  return eventTypesToReplay;\n}\n\n/**\n * Serializes the lContainer data into a list of SerializedView objects,\n * that represent views within this lContainer.\n *\n * @param lContainer the lContainer we are serializing\n * @param tNode the TNode that contains info about this LContainer\n * @param lView that hosts this LContainer\n * @param parentDeferBlockId the defer block id of the parent if it exists\n * @param context the hydration context\n * @returns an array of the `SerializedView` objects\n */\nfunction serializeLContainer(\n  lContainer: LContainer,\n  tNode: TNode,\n  lView: LView,\n  parentDeferBlockId: string | null,\n  context: HydrationContext,\n): SerializedContainerView[] {\n  const views: SerializedContainerView[] = [];\n  let lastViewAsString = '';\n\n  for (let i = CONTAINER_HEADER_OFFSET; i < lContainer.length; i++) {\n    let childLView = lContainer[i] as LView;\n\n    let template: string;\n    let numRootNodes: number;\n    let serializedView: SerializedContainerView | undefined;\n\n    if (isRootView(childLView)) {\n      // If this is a root view, get an LView for the underlying component,\n      // because it contains information about the view to serialize.\n      childLView = childLView[HEADER_OFFSET];\n\n      // If we have an LContainer at this position, this indicates that the\n      // host element was used as a ViewContainerRef anchor (e.g. a `ViewContainerRef`\n      // was injected within the component class). This case requires special handling.\n      if (isLContainer(childLView)) {\n        // Calculate the number of root nodes in all views in a given container\n        // and increment by one to account for an anchor node itself, i.e. in this\n        // scenario we'll have a layout that would look like this:\n        // `<app-root /><#VIEW1><#VIEW2>...<!--container-->`\n        // The `+1` is to capture the `<app-root />` element.\n        numRootNodes = calcNumRootNodesInLContainer(childLView) + 1;\n\n        annotateLContainerForHydration(childLView, context, lView[INJECTOR]);\n\n        const componentLView = unwrapLView(childLView[HOST]) as LView<unknown>;\n\n        serializedView = {\n          [TEMPLATE_ID]: componentLView[TVIEW].ssrId!,\n          [NUM_ROOT_NODES]: numRootNodes,\n        };\n      }\n    }\n\n    if (!serializedView) {\n      const childTView = childLView[TVIEW];\n\n      if (childTView.type === TViewType.Component) {\n        template = childTView.ssrId!;\n\n        // This is a component view, thus it has only 1 root node: the component\n        // host node itself (other nodes would be inside that host node).\n        numRootNodes = 1;\n      } else {\n        template = getSsrId(childTView);\n        numRootNodes = calcNumRootNodes(childTView, childLView, childTView.firstChild);\n      }\n\n      serializedView = {\n        [TEMPLATE_ID]: template,\n        [NUM_ROOT_NODES]: numRootNodes,\n      };\n\n      let isHydrateNeverBlock = false;\n\n      // If this is a defer block, serialize extra info.\n      if (isDeferBlock(lView[TVIEW], tNode)) {\n        const lDetails = getLDeferBlockDetails(lView, tNode);\n        const tDetails = getTDeferBlockDetails(lView[TVIEW], tNode);\n\n        if (context.isIncrementalHydrationEnabled && tDetails.hydrateTriggers !== null) {\n          const deferBlockId = `d${context.deferBlocks.size}`;\n\n          if (tDetails.hydrateTriggers.has(DeferBlockTrigger.Never)) {\n            isHydrateNeverBlock = true;\n          }\n\n          let rootNodes: any[] = [];\n          collectNativeNodesInLContainer(lContainer, rootNodes);\n\n          // Add defer block into info context.deferBlocks\n          const deferBlockInfo: SerializedDeferBlock = {\n            [NUM_ROOT_NODES]: rootNodes.length,\n            [DEFER_BLOCK_STATE]: lDetails[CURRENT_DEFER_BLOCK_STATE],\n          };\n\n          const serializedTriggers = serializeHydrateTriggers(tDetails.hydrateTriggers);\n          if (serializedTriggers.length > 0) {\n            deferBlockInfo[DEFER_HYDRATE_TRIGGERS] = serializedTriggers;\n          }\n\n          if (parentDeferBlockId !== null) {\n            // Serialize parent id only when it's present.\n            deferBlockInfo[DEFER_PARENT_BLOCK_ID] = parentDeferBlockId;\n          }\n\n          context.deferBlocks.set(deferBlockId, deferBlockInfo);\n\n          const node = unwrapRNode(lContainer);\n          if (node !== undefined) {\n            if ((node as Node).nodeType === Node.COMMENT_NODE) {\n              annotateDeferBlockAnchorForHydration(node as RComment, deferBlockId);\n            }\n          } else {\n            ngDevMode && validateNodeExists(node, childLView, tNode);\n            ngDevMode &&\n              validateMatchingNode(node, Node.COMMENT_NODE, null, childLView, tNode, true);\n\n            annotateDeferBlockAnchorForHydration(node as RComment, deferBlockId);\n          }\n\n          if (!isHydrateNeverBlock) {\n            // Add JSAction attributes for root nodes that use some hydration triggers\n            annotateDeferBlockRootNodesWithJsAction(tDetails, rootNodes, deferBlockId, context);\n          }\n\n          // Use current block id as parent for nested routes.\n          parentDeferBlockId = deferBlockId;\n\n          // Serialize extra info into the view object.\n          // TODO(incremental-hydration): this should be serialized and included at a different level\n          // (not at the view level).\n          serializedView[DEFER_BLOCK_ID] = deferBlockId;\n        }\n        // DEFER_BLOCK_STATE is used for reconciliation in hydration, both regular and incremental.\n        // We need to know which template is rendered when hydrating. So we serialize this state\n        // regardless of hydration type.\n        serializedView[DEFER_BLOCK_STATE] = lDetails[CURRENT_DEFER_BLOCK_STATE];\n      }\n\n      if (!isHydrateNeverBlock) {\n        Object.assign(\n          serializedView,\n          serializeLView(lContainer[i] as LView, parentDeferBlockId, context),\n        );\n      }\n    }\n\n    // Check if the previous view has the same shape (for example, it was\n    // produced by the *ngFor), in which case bump the counter on the previous\n    // view instead of including the same information again.\n    const currentViewAsString = JSON.stringify(serializedView);\n    if (views.length > 0 && currentViewAsString === lastViewAsString) {\n      const previousView = views[views.length - 1];\n      previousView[MULTIPLIER] ??= 1;\n      previousView[MULTIPLIER]++;\n    } else {\n      // Record this view as most recently added.\n      lastViewAsString = currentViewAsString;\n      views.push(serializedView);\n    }\n  }\n  return views;\n}\n\nfunction serializeHydrateTriggers(\n  triggerMap: Map<DeferBlockTrigger, HydrateTriggerDetails | null>,\n): (DeferBlockTrigger | SerializedTriggerDetails)[] {\n  const serializableDeferBlockTrigger = new Set<DeferBlockTrigger>([\n    DeferBlockTrigger.Idle,\n    DeferBlockTrigger.Immediate,\n    DeferBlockTrigger.Viewport,\n    DeferBlockTrigger.Timer,\n  ]);\n  let triggers: (DeferBlockTrigger | SerializedTriggerDetails)[] = [];\n  for (let [trigger, details] of triggerMap) {\n    if (serializableDeferBlockTrigger.has(trigger)) {\n      if (details === null) {\n        triggers.push(trigger);\n      } else {\n        triggers.push({trigger, delay: details.delay});\n      }\n    }\n  }\n  return triggers;\n}\n\n/**\n * Helper function to produce a node path (which navigation steps runtime logic\n * needs to take to locate a node) and stores it in the `NODES` section of the\n * current serialized view.\n */\nfunction appendSerializedNodePath(\n  ngh: SerializedView,\n  tNode: TNode,\n  lView: LView,\n  excludedParentNodes: Set<number> | null,\n) {\n  const noOffsetIndex = tNode.index - HEADER_OFFSET;\n  ngh[NODES] ??= {};\n  // Ensure we don't calculate the path multiple times.\n  ngh[NODES][noOffsetIndex] ??= calcPathForNode(tNode, lView, excludedParentNodes);\n}\n\n/**\n * Helper function to append information about a disconnected node.\n * This info is needed at runtime to avoid DOM lookups for this element\n * and instead, the element would be created from scratch.\n */\nfunction appendDisconnectedNodeIndex(ngh: SerializedView, tNodeOrNoOffsetIndex: TNode | number) {\n  const noOffsetIndex =\n    typeof tNodeOrNoOffsetIndex === 'number'\n      ? tNodeOrNoOffsetIndex\n      : tNodeOrNoOffsetIndex.index - HEADER_OFFSET;\n  ngh[DISCONNECTED_NODES] ??= [];\n  if (!ngh[DISCONNECTED_NODES].includes(noOffsetIndex)) {\n    ngh[DISCONNECTED_NODES].push(noOffsetIndex);\n  }\n}\n\n/**\n * Serializes the lView data into a SerializedView object that will later be added\n * to the TransferState storage and referenced using the `ngh` attribute on a host\n * element.\n *\n * @param lView the lView we are serializing\n * @param context the hydration context\n * @returns the `SerializedView` object containing the data to be added to the host node\n */\nfunction serializeLView(\n  lView: LView,\n  parentDeferBlockId: string | null = null,\n  context: HydrationContext,\n): SerializedView {\n  const ngh: SerializedView = {};\n  const tView = lView[TVIEW];\n  const i18nChildren = getOrComputeI18nChildren(tView, context);\n  const nativeElementsToEventTypes = context.shouldReplayEvents\n    ? collectDomEventsInfo(tView, lView, context.eventTypesToReplay)\n    : null;\n  // Iterate over DOM element references in an LView.\n  for (let i = HEADER_OFFSET; i < tView.bindingStartIndex; i++) {\n    const tNode = tView.data[i];\n    const noOffsetIndex = i - HEADER_OFFSET;\n\n    // Attempt to serialize any i18n data for the given slot. We do this first, as i18n\n    // has its own process for serialization.\n    const i18nData = trySerializeI18nBlock(lView, i, context);\n    if (i18nData) {\n      ngh[I18N_DATA] ??= {};\n      ngh[I18N_DATA][noOffsetIndex] = i18nData.caseQueue;\n\n      for (const nodeNoOffsetIndex of i18nData.disconnectedNodes) {\n        appendDisconnectedNodeIndex(ngh, nodeNoOffsetIndex);\n      }\n\n      for (const nodeNoOffsetIndex of i18nData.disjointNodes) {\n        const tNode = tView.data[nodeNoOffsetIndex + HEADER_OFFSET] as TNode;\n        ngDevMode && assertTNode(tNode);\n        appendSerializedNodePath(ngh, tNode, lView, i18nChildren);\n      }\n\n      continue;\n    }\n\n    // Skip processing of a given slot in the following cases:\n    // - Local refs (e.g. <div #localRef>) take up an extra slot in LViews\n    //   to store the same element. In this case, there is no information in\n    //   a corresponding slot in TNode data structure.\n    // - When a slot contains something other than a TNode. For example, there\n    //   might be some metadata information about a defer block or a control flow block.\n    if (!isTNodeShape(tNode)) {\n      continue;\n    }\n\n    // Skip any nodes that are in an i18n block but are considered detached (i.e. not\n    // present in the template). These nodes are disconnected from the DOM tree, and\n    // so we don't want to serialize any information about them.\n    if (isDetachedByI18n(tNode)) {\n      continue;\n    }\n\n    // Serialize information about template.\n    if (isLContainer(lView[i]) && tNode.tView) {\n      ngh[TEMPLATES] ??= {};\n      ngh[TEMPLATES][noOffsetIndex] = getSsrId(tNode.tView!);\n    }\n\n    // Check if a native node that represents a given TNode is disconnected from the DOM tree.\n    // Such nodes must be excluded from the hydration (since the hydration won't be able to\n    // find them), so the TNode ids are collected and used at runtime to skip the hydration.\n    // This situation may happen during the content projection, when some nodes don't make it\n    // into one of the content projection slots (for example, when there is no default\n    // <ng-content /> slot in projector component's template).\n    if (isDisconnectedNode(tNode, lView) && isContentProjectedNode(tNode)) {\n      appendDisconnectedNodeIndex(ngh, tNode);\n      continue;\n    }\n\n    if (Array.isArray(tNode.projection)) {\n      for (const projectionHeadTNode of tNode.projection) {\n        // We may have `null`s in slots with no projected content.\n        if (!projectionHeadTNode) continue;\n\n        if (!Array.isArray(projectionHeadTNode)) {\n          // If we process re-projected content (i.e. `<ng-content>`\n          // appears at projection location), skip annotations for this content\n          // since all DOM nodes in this projection were handled while processing\n          // a parent lView, which contains those nodes.\n          if (\n            !isProjectionTNode(projectionHeadTNode) &&\n            !isInSkipHydrationBlock(projectionHeadTNode)\n          ) {\n            if (isDisconnectedNode(projectionHeadTNode, lView)) {\n              // Check whether this node is connected, since we may have a TNode\n              // in the data structure as a projection segment head, but the\n              // content projection slot might be disabled (e.g.\n              // <ng-content *ngIf=\"false\" />).\n              appendDisconnectedNodeIndex(ngh, projectionHeadTNode);\n            } else {\n              appendSerializedNodePath(ngh, projectionHeadTNode, lView, i18nChildren);\n            }\n          }\n        } else {\n          // If a value is an array, it means that we are processing a projection\n          // where projectable nodes were passed in as DOM nodes (for example, when\n          // calling `ViewContainerRef.createComponent(CmpA, {projectableNodes: [...]})`).\n          //\n          // In this scenario, nodes can come from anywhere (either created manually,\n          // accessed via `document.querySelector`, etc) and may be in any state\n          // (attached or detached from the DOM tree). As a result, we can not reliably\n          // restore the state for such cases during hydration.\n\n          throw unsupportedProjectionOfDomNodes(unwrapRNode(lView[i]));\n        }\n      }\n    }\n\n    conditionallyAnnotateNodePath(ngh, tNode, lView, i18nChildren);\n    if (isLContainer(lView[i])) {\n      // Serialize views within this LContainer.\n      const hostNode = lView[i][HOST]!; // host node of this container\n\n      // LView[i][HOST] can be of 2 different types:\n      // - either a DOM node\n      // - or an array that represents an LView of a component\n      if (Array.isArray(hostNode)) {\n        // This is a component, serialize info about it.\n        const targetNode = unwrapRNode(hostNode as LView) as RElement;\n        if (!(targetNode as HTMLElement).hasAttribute(SKIP_HYDRATION_ATTR_NAME)) {\n          annotateHostElementForHydration(\n            targetNode,\n            hostNode as LView,\n            parentDeferBlockId,\n            context,\n          );\n        }\n      }\n\n      ngh[CONTAINERS] ??= {};\n      ngh[CONTAINERS][noOffsetIndex] = serializeLContainer(\n        lView[i],\n        tNode,\n        lView,\n        parentDeferBlockId,\n        context,\n      );\n    } else if (Array.isArray(lView[i]) && !isLetDeclaration(tNode)) {\n      // This is a component, annotate the host node with an `ngh` attribute.\n      // Note: Let declarations that return an array are also storing an array in the LView,\n      // we need to exclude them.\n      const targetNode = unwrapRNode(lView[i][HOST]!);\n      if (!(targetNode as HTMLElement).hasAttribute(SKIP_HYDRATION_ATTR_NAME)) {\n        annotateHostElementForHydration(\n          targetNode as RElement,\n          lView[i],\n          parentDeferBlockId,\n          context,\n        );\n      }\n    } else {\n      // <ng-container> case\n      if (tNode.type & TNodeType.ElementContainer) {\n        // An <ng-container> is represented by the number of\n        // top-level nodes. This information is needed to skip over\n        // those nodes to reach a corresponding anchor node (comment node).\n        ngh[ELEMENT_CONTAINERS] ??= {};\n        ngh[ELEMENT_CONTAINERS][noOffsetIndex] = calcNumRootNodes(tView, lView, tNode.child);\n      } else if (tNode.type & (TNodeType.Projection | TNodeType.LetDeclaration)) {\n        // Current TNode represents an `<ng-content>` slot or `@let` declaration,\n        // thus it has no DOM elements associated with it, so the **next sibling**\n        // node would not be able to find an anchor. In this case, use full path instead.\n        let nextTNode = tNode.next;\n        // Skip over all `<ng-content>` slots and `@let` declarations in a row.\n        while (\n          nextTNode !== null &&\n          nextTNode.type & (TNodeType.Projection | TNodeType.LetDeclaration)\n        ) {\n          nextTNode = nextTNode.next;\n        }\n        if (nextTNode && !isInSkipHydrationBlock(nextTNode)) {\n          // Handle a tNode after the `<ng-content>` slot.\n          appendSerializedNodePath(ngh, nextTNode, lView, i18nChildren);\n        }\n      } else if (tNode.type & TNodeType.Text) {\n        const rNode = unwrapRNode(lView[i]);\n        processTextNodeBeforeSerialization(context, rNode);\n      }\n    }\n\n    // Attach `jsaction` attribute to elements that have registered listeners,\n    // thus potentially having a need to do an event replay.\n    if (nativeElementsToEventTypes && tNode.type & TNodeType.Element) {\n      const nativeElement = unwrapRNode(lView[i]) as Element;\n      if (nativeElementsToEventTypes.has(nativeElement)) {\n        setJSActionAttributes(\n          nativeElement,\n          nativeElementsToEventTypes.get(nativeElement)!,\n          parentDeferBlockId,\n        );\n      }\n    }\n  }\n  return ngh;\n}\n\n/**\n * Serializes node location in cases when it's needed, specifically:\n *\n *  1. If `tNode.projectionNext` is different from `tNode.next` - it means that\n *     the next `tNode` after projection is different from the one in the original\n *     template. Since hydration relies on `tNode.next`, this serialized info\n *     is required to help runtime code find the node at the correct location.\n *  2. In certain content projection-based use-cases, it's possible that only\n *     a content of a projected element is rendered. In this case, content nodes\n *     require an extra annotation, since runtime logic can't rely on parent-child\n *     connection to identify the location of a node.\n */\nfunction conditionallyAnnotateNodePath(\n  ngh: SerializedView,\n  tNode: TNode,\n  lView: LView<unknown>,\n  excludedParentNodes: Set<number> | null,\n) {\n  if (isProjectionTNode(tNode)) {\n    // Do not annotate projection nodes (<ng-content />), since\n    // they don't have a corresponding DOM node representing them.\n    return;\n  }\n\n  // Handle case #1 described above.\n  if (\n    tNode.projectionNext &&\n    tNode.projectionNext !== tNode.next &&\n    !isInSkipHydrationBlock(tNode.projectionNext)\n  ) {\n    appendSerializedNodePath(ngh, tNode.projectionNext, lView, excludedParentNodes);\n  }\n\n  // Handle case #2 described above.\n  // Note: we only do that for the first node (i.e. when `tNode.prev === null`),\n  // the rest of the nodes would rely on the current node location, so no extra\n  // annotation is needed.\n  if (\n    tNode.prev === null &&\n    tNode.parent !== null &&\n    isDisconnectedNode(tNode.parent, lView) &&\n    !isDisconnectedNode(tNode, lView)\n  ) {\n    appendSerializedNodePath(ngh, tNode, lView, excludedParentNodes);\n  }\n}\n\n/**\n * Determines whether a component instance that is represented\n * by a given LView uses `ViewEncapsulation.ShadowDom`.\n */\nfunction componentUsesShadowDomEncapsulation(lView: LView): boolean {\n  const instance = lView[CONTEXT];\n  return instance?.constructor\n    ? getComponentDef(instance.constructor)?.encapsulation === ViewEncapsulation.ShadowDom\n    : false;\n}\n\n/**\n * Annotates component host element for hydration:\n * - by either adding the `ngh` attribute and collecting hydration-related info\n *   for the serialization and transferring to the client\n * - or by adding the `ngSkipHydration` attribute in case Angular detects that\n *   component contents is not compatible with hydration.\n *\n * @param element The Host element to be annotated\n * @param lView The associated LView\n * @param context The hydration context\n * @returns An index of serialized view from the transfer state object\n *          or `null` when a given component can not be serialized.\n */\nfunction annotateHostElementForHydration(\n  element: RElement,\n  lView: LView,\n  parentDeferBlockId: string | null,\n  context: HydrationContext,\n): number | null {\n  const renderer = lView[RENDERER];\n  if (\n    (hasI18n(lView) && !isI18nHydrationSupportEnabled()) ||\n    componentUsesShadowDomEncapsulation(lView)\n  ) {\n    // Attach the skip hydration attribute if this component:\n    // - either has i18n blocks, since hydrating such blocks is not yet supported\n    // - or uses ShadowDom view encapsulation, since Domino doesn't support\n    //   shadow DOM, so we can not guarantee that client and server representations\n    //   would exactly match\n    renderer.setAttribute(element, SKIP_HYDRATION_ATTR_NAME, '');\n    return null;\n  } else {\n    const ngh = serializeLView(lView, parentDeferBlockId, context);\n    const index = context.serializedViewCollection.add(ngh);\n    renderer.setAttribute(element, NGH_ATTR_NAME, index.toString());\n    return index;\n  }\n}\n\n/**\n * Annotates defer block comment node for hydration:\n *\n * @param comment The Host element to be annotated\n * @param deferBlockId the id of the target defer block\n */\nfunction annotateDeferBlockAnchorForHydration(comment: RComment, deferBlockId: string): void {\n  comment.textContent = `ngh=${deferBlockId}`;\n}\n\n/**\n * Physically inserts the comment nodes to ensure empty text nodes and adjacent\n * text node separators are preserved after server serialization of the DOM.\n * These get swapped back for empty text nodes or separators once hydration happens\n * on the client.\n *\n * @param corruptedTextNodes The Map of text nodes to be replaced with comments\n * @param doc The document\n */\nfunction insertCorruptedTextNodeMarkers(\n  corruptedTextNodes: Map<HTMLElement, string>,\n  doc: Document,\n) {\n  for (const [textNode, marker] of corruptedTextNodes) {\n    textNode.after(doc.createComment(marker));\n  }\n}\n\n/**\n * Detects whether a given TNode represents a node that\n * is being content projected.\n */\nfunction isContentProjectedNode(tNode: TNode): boolean {\n  let currentTNode = tNode;\n  while (currentTNode != null) {\n    // If we come across a component host node in parent nodes -\n    // this TNode is in the content projection section.\n    if (isComponentHost(currentTNode)) {\n      return true;\n    }\n    currentTNode = currentTNode.parent as TNode;\n  }\n  return false;\n}\n\n/**\n * Incremental hydration requires that any defer block root node\n * with interaction or hover triggers have all of their root nodes\n * trigger hydration with those events. So we need to make sure all\n * the root nodes of that block have the proper jsaction attribute\n * to ensure hydration is triggered, since the content is dehydrated\n */\nfunction annotateDeferBlockRootNodesWithJsAction(\n  tDetails: TDeferBlockDetails,\n  rootNodes: any[],\n  parentDeferBlockId: string,\n  context: HydrationContext,\n) {\n  const actionList = convertHydrateTriggersToJsAction(tDetails.hydrateTriggers);\n  for (let et of actionList) {\n    context.eventTypesToReplay.regular.add(et);\n  }\n\n  if (actionList.length > 0) {\n    const elementNodes = (rootNodes as HTMLElement[]).filter(\n      (rn) => rn.nodeType === Node.ELEMENT_NODE,\n    );\n    for (let rNode of elementNodes) {\n      setJSActionAttributes(rNode, actionList, parentDeferBlockId);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {APP_BOOTSTRAP_LISTENER, ApplicationRef} from '../application/application_ref';\nimport {Console} from '../console';\nimport {\n  ENVIRONMENT_INITIALIZER,\n  EnvironmentProviders,\n  Injector,\n  makeEnvironmentProviders,\n  Provider,\n} from '../di';\nimport {inject} from '../di/injector_compatibility';\nimport {formatRuntimeError, RuntimeError, RuntimeErrorCode} from '../errors';\nimport {enableLocateOrCreateContainerRefImpl} from '../linker/view_container_ref';\nimport {enableLocateOrCreateI18nNodeImpl} from '../render3/i18n/i18n_apply';\nimport {enableLocateOrCreateElementNodeImpl} from '../render3/instructions/element';\nimport {enableLocateOrCreateElementContainerNodeImpl} from '../render3/instructions/element_container';\nimport {enableApplyRootElementTransformImpl} from '../render3/instructions/shared';\nimport {enableLocateOrCreateContainerAnchorImpl} from '../render3/instructions/template';\nimport {enableLocateOrCreateTextNodeImpl} from '../render3/instructions/text';\nimport {getDocument} from '../render3/interfaces/document';\nimport {TransferState} from '../transfer_state';\nimport {performanceMarkFeature} from '../util/performance';\nimport {NgZone} from '../zone';\nimport {withEventReplay} from './event_replay';\n\nimport {cleanupDehydratedViews} from './cleanup';\nimport {\n  enableClaimDehydratedIcuCaseImpl,\n  enablePrepareI18nBlockForHydrationImpl,\n  setIsI18nHydrationSupportEnabled,\n} from './i18n';\nimport {\n  IS_HYDRATION_DOM_REUSE_ENABLED,\n  IS_I18N_HYDRATION_ENABLED,\n  IS_INCREMENTAL_HYDRATION_ENABLED,\n  PRESERVE_HOST_CONTENT,\n} from './tokens';\nimport {\n  appendDeferBlocksToJSActionMap,\n  countBlocksSkippedByHydration,\n  enableRetrieveDeferBlockDataImpl,\n  enableRetrieveHydrationInfoImpl,\n  isIncrementalHydrationEnabled,\n  NGH_DATA_KEY,\n  processBlockData,\n  verifySsrContentsIntegrity,\n} from './utils';\nimport {enableFindMatchingDehydratedViewImpl} from './views';\nimport {DEHYDRATED_BLOCK_REGISTRY, DehydratedBlockRegistry} from '../defer/registry';\nimport {gatherDeferBlocksCommentNodes} from './node_lookup_utils';\nimport {processAndInitTriggers} from '../defer/triggering';\n\n/**\n * Indicates whether the hydration-related code was added,\n * prevents adding it multiple times.\n */\nlet isHydrationSupportEnabled = false;\n\n/**\n * Indicates whether the i18n-related code was added,\n * prevents adding it multiple times.\n *\n * Note: This merely controls whether the code is loaded,\n * while `setIsI18nHydrationSupportEnabled` determines\n * whether i18n blocks are serialized or hydrated.\n */\nlet isI18nHydrationRuntimeSupportEnabled = false;\n\n/**\n * Indicates whether the incremental hydration code was added,\n * prevents adding it multiple times.\n */\nlet isIncrementalHydrationRuntimeSupportEnabled = false;\n\n/**\n * Defines a period of time that Angular waits for the `ApplicationRef.isStable` to emit `true`.\n * If there was no event with the `true` value during this time, Angular reports a warning.\n */\nconst APPLICATION_IS_STABLE_TIMEOUT = 10_000;\n\n/**\n * Brings the necessary hydration code in tree-shakable manner.\n * The code is only present when the `provideClientHydration` is\n * invoked. Otherwise, this code is tree-shaken away during the\n * build optimization step.\n *\n * This technique allows us to swap implementations of methods so\n * tree shaking works appropriately when hydration is disabled or\n * enabled. It brings in the appropriate version of the method that\n * supports hydration only when enabled.\n */\nfunction enableHydrationRuntimeSupport() {\n  if (!isHydrationSupportEnabled) {\n    isHydrationSupportEnabled = true;\n    enableRetrieveHydrationInfoImpl();\n    enableLocateOrCreateElementNodeImpl();\n    enableLocateOrCreateTextNodeImpl();\n    enableLocateOrCreateElementContainerNodeImpl();\n    enableLocateOrCreateContainerAnchorImpl();\n    enableLocateOrCreateContainerRefImpl();\n    enableFindMatchingDehydratedViewImpl();\n    enableApplyRootElementTransformImpl();\n  }\n}\n\n/**\n * Brings the necessary i18n hydration code in tree-shakable manner.\n * Similar to `enableHydrationRuntimeSupport`, the code is only\n * present when `withI18nSupport` is invoked.\n */\nfunction enableI18nHydrationRuntimeSupport() {\n  if (!isI18nHydrationRuntimeSupportEnabled) {\n    isI18nHydrationRuntimeSupportEnabled = true;\n    enableLocateOrCreateI18nNodeImpl();\n    enablePrepareI18nBlockForHydrationImpl();\n    enableClaimDehydratedIcuCaseImpl();\n  }\n}\n\n/**\n * Brings the necessary incremental hydration code in tree-shakable manner.\n * Similar to `enableHydrationRuntimeSupport`, the code is only\n * present when `enableIncrementalHydrationRuntimeSupport` is invoked.\n */\nfunction enableIncrementalHydrationRuntimeSupport() {\n  if (!isIncrementalHydrationRuntimeSupportEnabled) {\n    isIncrementalHydrationRuntimeSupportEnabled = true;\n    enableRetrieveDeferBlockDataImpl();\n  }\n}\n\n/**\n * Outputs a message with hydration stats into a console.\n */\nfunction printHydrationStats(injector: Injector) {\n  const console = injector.get(Console);\n  const message =\n    `Angular hydrated ${ngDevMode!.hydratedComponents} component(s) ` +\n    `and ${ngDevMode!.hydratedNodes} node(s), ` +\n    `${ngDevMode!.componentsSkippedHydration} component(s) were skipped. ` +\n    (isIncrementalHydrationEnabled(injector)\n      ? `${ngDevMode!.deferBlocksWithIncrementalHydration} defer block(s) were configured to use incremental hydration. `\n      : '') +\n    `Learn more at https://angular.dev/guide/hydration.`;\n  // tslint:disable-next-line:no-console\n  console.log(message);\n}\n\n/**\n * Returns a Promise that is resolved when an application becomes stable.\n */\nfunction whenStableWithTimeout(appRef: ApplicationRef): Promise<void> {\n  const whenStablePromise = appRef.whenStable();\n  if (typeof ngDevMode !== 'undefined' && ngDevMode) {\n    const timeoutTime = APPLICATION_IS_STABLE_TIMEOUT;\n    const console = appRef.injector.get(Console);\n    const ngZone = appRef.injector.get(NgZone);\n\n    // The following call should not and does not prevent the app to become stable\n    // We cannot use RxJS timer here because the app would remain unstable.\n    // This also avoids an extra change detection cycle.\n    const timeoutId = ngZone.runOutsideAngular(() => {\n      return setTimeout(() => logWarningOnStableTimedout(timeoutTime, console), timeoutTime);\n    });\n\n    whenStablePromise.finally(() => clearTimeout(timeoutId));\n  }\n\n  return whenStablePromise;\n}\n\n/**\n * Defines a name of an attribute that is added to the <body> tag\n * in the `index.html` file in case a given route was configured\n * with `RenderMode.Client`. 'cm' is an abbreviation for \"Client Mode\".\n */\nexport const CLIENT_RENDER_MODE_FLAG = 'ngcm';\n\n/**\n * Checks whether the `RenderMode.Client` was defined for the current route.\n */\nfunction isClientRenderModeEnabled(): boolean {\n  const doc = getDocument();\n  return (\n    (typeof ngServerMode === 'undefined' || !ngServerMode) &&\n    doc.body.hasAttribute(CLIENT_RENDER_MODE_FLAG)\n  );\n}\n\n/**\n * Returns a set of providers required to setup hydration support\n * for an application that is server side rendered. This function is\n * included into the `provideClientHydration` public API function from\n * the `platform-browser` package.\n *\n * The function sets up an internal flag that would be recognized during\n * the server side rendering time as well, so there is no need to\n * configure or change anything in NgUniversal to enable the feature.\n */\nexport function withDomHydration(): EnvironmentProviders {\n  const providers: Provider[] = [\n    {\n      provide: IS_HYDRATION_DOM_REUSE_ENABLED,\n      useFactory: () => {\n        let isEnabled = true;\n        if (typeof ngServerMode === 'undefined' || !ngServerMode) {\n          // On the client, verify that the server response contains\n          // hydration annotations. Otherwise, keep hydration disabled.\n          const transferState = inject(TransferState, {optional: true});\n          isEnabled = !!transferState?.get(NGH_DATA_KEY, null);\n        }\n        if (isEnabled) {\n          performanceMarkFeature('NgHydration');\n        }\n        return isEnabled;\n      },\n    },\n    {\n      provide: ENVIRONMENT_INITIALIZER,\n      useValue: () => {\n        // i18n support is enabled by calling withI18nSupport(), but there's\n        // no way to turn it off (e.g. for tests), so we turn it off by default.\n        setIsI18nHydrationSupportEnabled(false);\n\n        if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n          // Since this function is used across both server and client,\n          // make sure that the runtime code is only added when invoked\n          // on the client (see the `enableHydrationRuntimeSupport` function\n          // call below).\n          return;\n        }\n\n        if (inject(IS_HYDRATION_DOM_REUSE_ENABLED)) {\n          verifySsrContentsIntegrity(getDocument());\n          enableHydrationRuntimeSupport();\n        } else if (typeof ngDevMode !== 'undefined' && ngDevMode && !isClientRenderModeEnabled()) {\n          const console = inject(Console);\n          const message = formatRuntimeError(\n            RuntimeErrorCode.MISSING_HYDRATION_ANNOTATIONS,\n            'Angular hydration was requested on the client, but there was no ' +\n              'serialized information present in the server response, ' +\n              'thus hydration was not enabled. ' +\n              'Make sure the `provideClientHydration()` is included into the list ' +\n              'of providers in the server part of the application configuration.',\n          );\n          console.warn(message);\n        }\n      },\n      multi: true,\n    },\n  ];\n\n  if (typeof ngServerMode === 'undefined' || !ngServerMode) {\n    providers.push(\n      {\n        provide: PRESERVE_HOST_CONTENT,\n        useFactory: () => {\n          // Preserve host element content only in a browser\n          // environment and when hydration is configured properly.\n          // On a server, an application is rendered from scratch,\n          // so the host content needs to be empty.\n          return inject(IS_HYDRATION_DOM_REUSE_ENABLED);\n        },\n      },\n      {\n        provide: APP_BOOTSTRAP_LISTENER,\n        useFactory: () => {\n          if (inject(IS_HYDRATION_DOM_REUSE_ENABLED)) {\n            const appRef = inject(ApplicationRef);\n\n            return () => {\n              // Wait until an app becomes stable and cleanup all views that\n              // were not claimed during the application bootstrap process.\n              // The timing is similar to when we start the serialization process\n              // on the server.\n              //\n              // Note: the cleanup task *MUST* be scheduled within the Angular zone in Zone apps\n              // to ensure that change detection is properly run afterward.\n              whenStableWithTimeout(appRef).then(() => {\n                // Note: we have to check whether the application is destroyed before\n                // performing other operations with the `injector`.\n                // The application may be destroyed **before** it becomes stable, so when\n                // the `whenStableWithTimeout` resolves, the injector might already be in\n                // a destroyed state. Thus, calling `injector.get` would throw an error\n                // indicating that the injector has already been destroyed.\n                if (appRef.destroyed) {\n                  return;\n                }\n\n                cleanupDehydratedViews(appRef);\n                if (typeof ngDevMode !== 'undefined' && ngDevMode) {\n                  countBlocksSkippedByHydration(appRef.injector);\n                  printHydrationStats(appRef.injector);\n                }\n              });\n            };\n          }\n          return () => {}; // noop\n        },\n        multi: true,\n      },\n    );\n  }\n\n  return makeEnvironmentProviders(providers);\n}\n\n/**\n * Returns a set of providers required to setup support for i18n hydration.\n * Requires hydration to be enabled separately.\n */\nexport function withI18nSupport(): Provider[] {\n  return [\n    {\n      provide: IS_I18N_HYDRATION_ENABLED,\n      useFactory: () => inject(IS_HYDRATION_DOM_REUSE_ENABLED),\n    },\n    {\n      provide: ENVIRONMENT_INITIALIZER,\n      useValue: () => {\n        if (inject(IS_HYDRATION_DOM_REUSE_ENABLED)) {\n          enableI18nHydrationRuntimeSupport();\n          setIsI18nHydrationSupportEnabled(true);\n          performanceMarkFeature('NgI18nHydration');\n        }\n      },\n      multi: true,\n    },\n  ];\n}\n\n/**\n * Returns a set of providers required to setup support for incremental hydration.\n * Requires hydration to be enabled separately.\n * Enabling incremental hydration also enables event replay for the entire app.\n */\nexport function withIncrementalHydration(): Provider[] {\n  const providers: Provider[] = [\n    withEventReplay(),\n    {\n      provide: IS_INCREMENTAL_HYDRATION_ENABLED,\n      useValue: true,\n    },\n    {\n      provide: DEHYDRATED_BLOCK_REGISTRY,\n      useClass: DehydratedBlockRegistry,\n    },\n    {\n      provide: ENVIRONMENT_INITIALIZER,\n      useValue: () => {\n        enableIncrementalHydrationRuntimeSupport();\n        performanceMarkFeature('NgIncrementalHydration');\n      },\n      multi: true,\n    },\n  ];\n\n  if (typeof ngServerMode === 'undefined' || !ngServerMode) {\n    providers.push({\n      provide: APP_BOOTSTRAP_LISTENER,\n      useFactory: () => {\n        const injector = inject(Injector);\n        const doc = getDocument();\n\n        return () => {\n          const deferBlockData = processBlockData(injector);\n          const commentsByBlockId = gatherDeferBlocksCommentNodes(doc, doc.body);\n          processAndInitTriggers(injector, deferBlockData, commentsByBlockId);\n          appendDeferBlocksToJSActionMap(doc, injector);\n        };\n      },\n      multi: true,\n    });\n  }\n\n  return providers;\n}\n\n/**\n *\n * @param time The time in ms until the stable timedout warning message is logged\n */\nfunction logWarningOnStableTimedout(time: number, console: Console): void {\n  const message =\n    `Angular hydration expected the ApplicationRef.isStable() to emit \\`true\\`, but it ` +\n    `didn't happen within ${time}ms. Angular hydration logic depends on the application becoming stable ` +\n    `as a signal to complete hydration process.`;\n\n  console.warn(formatRuntimeError(RuntimeErrorCode.HYDRATION_STABLE_TIMEDOUT, message));\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Transforms a value (typically a string) to a boolean.\n * Intended to be used as a transform function of an input.\n *\n *  @usageNotes\n *  ```ts\n *  status = input({ transform: booleanAttribute });\n *  ```\n * @param value Value to be transformed.\n *\n * @publicApi\n */\nexport function booleanAttribute(value: unknown): boolean {\n  return typeof value === 'boolean' ? value : value != null && value !== 'false';\n}\n\n/**\n * Transforms a value (typically a string) to a number.\n * Intended to be used as a transform function of an input.\n * @param value Value to be transformed.\n * @param fallbackValue Value to use if the provided value can't be parsed as a number.\n *\n *  @usageNotes\n *  ```ts\n *  status = input({ transform: numberAttribute });\n *  ```\n *\n * @publicApi\n */\nexport function numberAttribute(value: unknown, fallbackValue = NaN): number {\n  // parseFloat(value) handles most of the cases we're interested in (it treats null, empty string,\n  // and other non-number values as NaN, where Number just uses 0) but it considers the string\n  // '123hello' to be a valid number. Therefore we also check if Number(value) is NaN.\n  const isNumberValue = !isNaN(parseFloat(value as any)) && !isNaN(Number(value));\n  return isNumberValue ? Number(value) : fallbackValue;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nexport const PERFORMANCE_MARK_PREFIX = '🅰️';\n\nlet enablePerfLogging = false;\n\n/**\n * Function that will start measuring against the performance API\n * Should be used in pair with stopMeasuring\n */\nexport function startMeasuring<T>(label: string): void {\n  if (!enablePerfLogging) {\n    return;\n  }\n\n  const {startLabel} = labels(label);\n  /* tslint:disable:ban */\n  performance.mark(startLabel);\n  /* tslint:enable:ban */\n}\n\n/**\n * Function that will stop measuring against the performance API\n * Should be used in pair with startMeasuring\n */\nexport function stopMeasuring(label: string): void {\n  if (!enablePerfLogging) {\n    return;\n  }\n\n  const {startLabel, labelName, endLabel} = labels(label);\n  /* tslint:disable:ban */\n  performance.mark(endLabel);\n  performance.measure(labelName, startLabel, endLabel);\n  performance.clearMarks(startLabel);\n  performance.clearMarks(endLabel);\n  /* tslint:enable:ban */\n}\n\nexport function labels(label: string) {\n  const labelName = `${PERFORMANCE_MARK_PREFIX}:${label}`;\n  return {\n    labelName,\n    startLabel: `start:${labelName}`,\n    endLabel: `end:${labelName}`,\n  };\n}\n\nlet warningLogged = false;\n/**\n * This enables an internal performance profiler\n *\n * It should not be imported in application code\n */\nexport function enableProfiling() {\n  if (\n    !warningLogged &&\n    (typeof performance === 'undefined' || !performance.mark || !performance.measure)\n  ) {\n    warningLogged = true;\n    console.warn('Performance API is not supported on this platform');\n    return;\n  }\n\n  enablePerfLogging = true;\n}\nexport function disableProfiling() {\n  enablePerfLogging = false;\n}\n", "/*!\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ComponentDef} from '../render3';\nimport {readPatchedLView} from '../render3/context_discovery';\nimport {isComponentHost, isLContainer, isLView} from '../render3/interfaces/type_checks';\nimport {HEADER_OFFSET, HOST, TVIEW} from '../render3/interfaces/view';\nimport {getTNode} from '../render3/util/view_utils';\n\n/**\n * Gets the class name of the closest component to a node.\n * Warning! this function will return minified names if the name of the component is minified. The\n * consumer of the function is responsible for resolving the minified name to its original name.\n * @param node Node from which to start the search.\n */\nexport function getClosestComponentName(node: Node): string | null {\n  let currentNode = node as Node | null;\n\n  while (currentNode) {\n    const lView = readPatchedLView(currentNode);\n\n    if (lView !== null) {\n      for (let i = HEADER_OFFSET; i < lView.length; i++) {\n        const current = lView[i];\n\n        if ((!isLView(current) && !isLContainer(current)) || current[HOST] !== currentNode) {\n          continue;\n        }\n\n        const tView = lView[TVIEW];\n        const tNode = getTNode(tView, i);\n        if (isComponentHost(tNode)) {\n          const def = tView.data[tNode.directiveStart + tNode.componentOffset] as ComponentDef<{}>;\n          const name = def.debugInfo?.className || def.type.name;\n\n          // Note: the name may be an empty string if the class name is\n          // dropped due to minification. In such cases keep going up the tree.\n          if (name) {\n            return name;\n          } else {\n            break;\n          }\n        }\n      }\n    }\n\n    currentNode = currentNode.parentNode;\n  }\n\n  return null;\n}\n", "/*!\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Utility function used during template type checking to assert that a value is of a certain type.\n * @codeGenApi\n */\nexport function ɵassertType<T>(value: unknown): asserts value is T {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  FactoryTarget,\n  getCompilerFacade,\n  JitCompilerUsage,\n  R3DeclareComponentFacade,\n  R3DeclareDirectiveFacade,\n  R3DeclareFactoryFacade,\n  R3DeclareInjectableFacade,\n  R3DeclareInjectorFacade,\n  R3DeclareNgModuleFacade,\n  R3DeclarePipeFacade,\n} from '../../compiler/compiler_facade';\nimport {Type} from '../../interface/type';\nimport {setClassMetadata, setClassMetadataAsync} from '../metadata';\n\nimport {angularCoreEnv} from './environment';\n\n/**\n * Compiles a partial directive declaration object into a full directive definition object.\n *\n * @codeGenApi\n */\nexport function ɵɵngDeclareDirective(decl: R3DeclareDirectiveFacade): unknown {\n  const compiler = getCompilerFacade({\n    usage: JitCompilerUsage.PartialDeclaration,\n    kind: 'directive',\n    type: decl.type,\n  });\n  return compiler.compileDirectiveDeclaration(\n    angularCoreEnv,\n    `ng:///${decl.type.name}/ɵfac.js`,\n    decl,\n  );\n}\n\n/**\n * Evaluates the class metadata declaration.\n *\n * @codeGenApi\n */\nexport function ɵɵngDeclareClassMetadata(decl: {\n  type: Type<any>;\n  decorators: any[];\n  ctorParameters?: () => any[];\n  propDecorators?: {[field: string]: any};\n}): void {\n  setClassMetadata(\n    decl.type,\n    decl.decorators,\n    decl.ctorParameters ?? null,\n    decl.propDecorators ?? null,\n  );\n}\n\n/**\n * Evaluates the class metadata of a component that contains deferred blocks.\n *\n * @codeGenApi\n */\nexport function ɵɵngDeclareClassMetadataAsync(decl: {\n  type: Type<any>;\n  resolveDeferredDeps: () => Promise<Type<unknown>>[];\n  resolveMetadata: (...types: Type<unknown>[]) => {\n    decorators: any[];\n    ctorParameters: (() => any[]) | null;\n    propDecorators: {[field: string]: any} | null;\n  };\n}): void {\n  setClassMetadataAsync(decl.type, decl.resolveDeferredDeps, (...types: Type<unknown>[]) => {\n    const meta = decl.resolveMetadata(...types);\n    setClassMetadata(decl.type, meta.decorators, meta.ctorParameters, meta.propDecorators);\n  });\n}\n\n/**\n * Compiles a partial component declaration object into a full component definition object.\n *\n * @codeGenApi\n */\nexport function ɵɵngDeclareComponent(decl: R3DeclareComponentFacade): unknown {\n  const compiler = getCompilerFacade({\n    usage: JitCompilerUsage.PartialDeclaration,\n    kind: 'component',\n    type: decl.type,\n  });\n  return compiler.compileComponentDeclaration(\n    angularCoreEnv,\n    `ng:///${decl.type.name}/ɵcmp.js`,\n    decl,\n  );\n}\n\n/**\n * Compiles a partial pipe declaration object into a full pipe definition object.\n *\n * @codeGenApi\n */\nexport function ɵɵngDeclareFactory(decl: R3DeclareFactoryFacade): unknown {\n  const compiler = getCompilerFacade({\n    usage: JitCompilerUsage.PartialDeclaration,\n    kind: getFactoryKind(decl.target),\n    type: decl.type,\n  });\n  return compiler.compileFactoryDeclaration(\n    angularCoreEnv,\n    `ng:///${decl.type.name}/ɵfac.js`,\n    decl,\n  );\n}\n\nfunction getFactoryKind(target: FactoryTarget) {\n  switch (target) {\n    case FactoryTarget.Directive:\n      return 'directive';\n    case FactoryTarget.Component:\n      return 'component';\n    case FactoryTarget.Injectable:\n      return 'injectable';\n    case FactoryTarget.Pipe:\n      return 'pipe';\n    case FactoryTarget.NgModule:\n      return 'NgModule';\n  }\n}\n\n/**\n * Compiles a partial injectable declaration object into a full injectable definition object.\n *\n * @codeGenApi\n */\nexport function ɵɵngDeclareInjectable(decl: R3DeclareInjectableFacade): unknown {\n  const compiler = getCompilerFacade({\n    usage: JitCompilerUsage.PartialDeclaration,\n    kind: 'injectable',\n    type: decl.type,\n  });\n  return compiler.compileInjectableDeclaration(\n    angularCoreEnv,\n    `ng:///${decl.type.name}/ɵprov.js`,\n    decl,\n  );\n}\n\n/**\n * These enums are used in the partial factory declaration calls.\n */\nexport {FactoryTarget} from '../../compiler/compiler_facade';\n\n/**\n * Compiles a partial injector declaration object into a full injector definition object.\n *\n * @codeGenApi\n */\nexport function ɵɵngDeclareInjector(decl: R3DeclareInjectorFacade): unknown {\n  const compiler = getCompilerFacade({\n    usage: JitCompilerUsage.PartialDeclaration,\n    kind: 'NgModule',\n    type: decl.type,\n  });\n  return compiler.compileInjectorDeclaration(\n    angularCoreEnv,\n    `ng:///${decl.type.name}/ɵinj.js`,\n    decl,\n  );\n}\n\n/**\n * Compiles a partial NgModule declaration object into a full NgModule definition object.\n *\n * @codeGenApi\n */\nexport function ɵɵngDeclareNgModule(decl: R3DeclareNgModuleFacade): unknown {\n  const compiler = getCompilerFacade({\n    usage: JitCompilerUsage.PartialDeclaration,\n    kind: 'NgModule',\n    type: decl.type,\n  });\n  return compiler.compileNgModuleDeclaration(\n    angularCoreEnv,\n    `ng:///${decl.type.name}/ɵmod.js`,\n    decl,\n  );\n}\n\n/**\n * Compiles a partial pipe declaration object into a full pipe definition object.\n *\n * @codeGenApi\n */\nexport function ɵɵngDeclarePipe(decl: R3DeclarePipeFacade): unknown {\n  const compiler = getCompilerFacade({\n    usage: JitCompilerUsage.PartialDeclaration,\n    kind: 'pipe',\n    type: decl.type,\n  });\n  return compiler.compilePipeDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵpipe.js`, decl);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {getDocument} from '../interfaces/document';\nimport {Injector} from '../../di';\nimport {isInternalHydrationTransferStateKey} from '../../hydration/utils';\nimport {APP_ID} from '../../application/application_tokens';\nimport {retrieveTransferredState} from '../../transfer_state';\n\n/**\n * Retrieves transfer state data from the DOM using the provided injector to get APP_ID.\n * This approach works by getting the APP_ID from the injector and then finding the\n * corresponding transfer state script tag. Internal framework keys used for hydration\n * are stripped from the result.\n *\n * @param injector - The injector to use for getting APP_ID\n * @returns The transfer state data as an object, or empty object if not available\n */\nexport function getTransferState(injector: Injector): Record<string, unknown> {\n  const doc = getDocument();\n  const appId = injector.get(APP_ID);\n\n  const transferState = retrieveTransferredState(doc, appId);\n\n  // Strip internal keys\n  const filteredEntries: Record<string, unknown> = {};\n  for (const [key, value] of Object.entries(transferState)) {\n    if (!isInternalHydrationTransferStateKey(key)) {\n      filteredEntries[key] = value;\n    }\n  }\n\n  return filteredEntries;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  consumerAfterComputation,\n  consumerBeforeComputation,\n  consumerPollProducersForChange,\n  producerAccessed,\n  SIGNAL,\n  SIGNAL_NODE,\n  type SignalNode,\n} from '../../../primitives/signals';\n\nimport {type Signal} from '../reactivity/api';\nimport {type EffectCleanupFn, type EffectCleanupRegisterFn} from './effect';\n\nimport {TracingService, TracingSnapshot} from '../../application/tracing';\nimport {\n  ChangeDetectionScheduler,\n  NotificationSource,\n} from '../../change_detection/scheduling/zoneless_scheduling';\nimport {assertInInjectionContext} from '../../di/contextual';\nimport {Injector} from '../../di/injector';\nimport {inject} from '../../di/injector_compatibility';\nimport {DestroyRef} from '../../linker/destroy_ref';\nimport {AfterRenderPhase, type AfterRenderRef} from '../after_render/api';\nimport {NOOP_AFTER_RENDER_REF, type AfterRenderOptions} from '../after_render/hooks';\nimport {\n  AFTER_RENDER_PHASES,\n  AfterRenderImpl,\n  AfterRenderManager,\n  AfterRenderSequence,\n} from '../after_render/manager';\nimport {LView} from '../interfaces/view';\nimport {ViewContext} from '../view_context';\nimport {assertNotInReactiveContext} from './asserts';\n\nconst NOT_SET = /* @__PURE__ */ Symbol('NOT_SET');\nconst EMPTY_CLEANUP_SET = /* @__PURE__ */ new Set<() => void>();\n\n/** Callback type for an `afterRenderEffect` phase effect */\ntype AfterRenderPhaseEffectHook = (\n  // Either a cleanup function or a pipelined value and a cleanup function\n  ...args:\n    | [onCleanup: EffectCleanupRegisterFn]\n    | [previousPhaseValue: unknown, onCleanup: EffectCleanupRegisterFn]\n) => unknown;\n\n/**\n * Reactive node in the graph for this `afterRenderEffect` phase effect.\n *\n * This node type extends `SignalNode` because `afterRenderEffect` phases effects produce a value\n * which is consumed as a `Signal` by subsequent phases.\n */\ninterface AfterRenderPhaseEffectNode extends SignalNode<unknown> {\n  /** The phase of the effect implemented by this node */\n  phase: AfterRenderPhase;\n  /** The sequence of phases to which this node belongs, used for state of the whole sequence */\n  sequence: AfterRenderEffectSequence;\n  /** The user's callback function */\n  userFn: AfterRenderPhaseEffectHook;\n  /** Signal function that retrieves the value of this node, used as the value for the next phase */\n  signal: Signal<unknown>;\n  /** Registered cleanup functions, or `null` if none have ever been registered */\n  cleanup: Set<() => void> | null;\n  /** Pre-bound helper function passed to the user's callback which writes to `this.cleanup` */\n  registerCleanupFn: EffectCleanupRegisterFn;\n  /** Entrypoint to running this effect that's given to the `afterRender` machinery */\n  phaseFn(previousValue?: unknown): unknown;\n}\n\nconst AFTER_RENDER_PHASE_EFFECT_NODE = /* @__PURE__ */ (() => ({\n  ...SIGNAL_NODE,\n  consumerIsAlwaysLive: true,\n  consumerAllowSignalWrites: true,\n  value: NOT_SET,\n  cleanup: null,\n  /** Called when the effect becomes dirty */\n  consumerMarkedDirty(this: AfterRenderPhaseEffectNode): void {\n    if (this.sequence.impl.executing) {\n      // If hooks are in the middle of executing, then it matters whether this node has yet been\n      // executed within its sequence. If not, then we don't want to notify the scheduler since\n      // this node will be reached naturally.\n      if (this.sequence.lastPhase === null || this.sequence.lastPhase < this.phase) {\n        return;\n      }\n\n      // If during the execution of a later phase an earlier phase became dirty, then we should not\n      // run any further phases until the earlier one reruns.\n      this.sequence.erroredOrDestroyed = true;\n    }\n\n    // Either hooks are not running, or we're marking a node dirty that has already run within its\n    // sequence.\n    this.sequence.scheduler.notify(NotificationSource.RenderHook);\n  },\n  phaseFn(this: AfterRenderPhaseEffectNode, previousValue?: unknown): unknown {\n    this.sequence.lastPhase = this.phase;\n\n    if (!this.dirty) {\n      return this.signal;\n    }\n\n    this.dirty = false;\n    if (this.value !== NOT_SET && !consumerPollProducersForChange(this)) {\n      // None of our producers report a change since the last time they were read, so no\n      // recomputation of our value is necessary.\n      return this.signal;\n    }\n\n    // Run any needed cleanup functions.\n    try {\n      for (const cleanupFn of this.cleanup ?? EMPTY_CLEANUP_SET) {\n        cleanupFn();\n      }\n    } finally {\n      // Even if a cleanup function errors, ensure it's cleared.\n      this.cleanup?.clear();\n    }\n\n    // Prepare to call the user's effect callback. If there was a previous phase, then it gave us\n    // its value as a `Signal`, otherwise `previousValue` will be `undefined`.\n    const args: unknown[] = [];\n    if (previousValue !== undefined) {\n      args.push(previousValue);\n    }\n    args.push(this.registerCleanupFn);\n\n    // Call the user's callback in our reactive context.\n    const prevConsumer = consumerBeforeComputation(this);\n    let newValue;\n    try {\n      newValue = this.userFn.apply(null, args as any);\n    } finally {\n      consumerAfterComputation(this, prevConsumer);\n    }\n\n    if (this.value === NOT_SET || !this.equal(this.value, newValue)) {\n      this.value = newValue;\n      this.version++;\n    }\n\n    return this.signal;\n  },\n}))();\n\n/**\n * An `AfterRenderSequence` that manages an `afterRenderEffect`'s phase effects.\n */\nclass AfterRenderEffectSequence extends AfterRenderSequence {\n  /**\n   * While this sequence is executing, this tracks the last phase which was called by the\n   * `afterRender` machinery.\n   *\n   * When a phase effect is marked dirty, this is used to determine whether it's already run or not.\n   */\n  lastPhase: AfterRenderPhase | null = null;\n\n  /**\n   * The reactive nodes for each phase, if a phase effect is defined for that phase.\n   *\n   * These are initialized to `undefined` but set in the constructor.\n   */\n  private readonly nodes: [\n    AfterRenderPhaseEffectNode | undefined,\n    AfterRenderPhaseEffectNode | undefined,\n    AfterRenderPhaseEffectNode | undefined,\n    AfterRenderPhaseEffectNode | undefined,\n  ] = [undefined, undefined, undefined, undefined];\n\n  constructor(\n    impl: AfterRenderImpl,\n    effectHooks: Array<AfterRenderPhaseEffectHook | undefined>,\n    view: LView | undefined,\n    readonly scheduler: ChangeDetectionScheduler,\n    destroyRef: DestroyRef,\n    snapshot: TracingSnapshot | null = null,\n  ) {\n    // Note that we also initialize the underlying `AfterRenderSequence` hooks to `undefined` and\n    // populate them as we create reactive nodes below.\n    super(impl, [undefined, undefined, undefined, undefined], view, false, destroyRef, snapshot);\n\n    // Setup a reactive node for each phase.\n    for (const phase of AFTER_RENDER_PHASES) {\n      const effectHook = effectHooks[phase];\n      if (effectHook === undefined) {\n        continue;\n      }\n\n      const node = Object.create(AFTER_RENDER_PHASE_EFFECT_NODE) as AfterRenderPhaseEffectNode;\n      node.sequence = this;\n      node.phase = phase;\n      node.userFn = effectHook;\n      node.dirty = true;\n      node.signal = (() => {\n        producerAccessed(node);\n        return node.value;\n      }) as Signal<unknown>;\n      node.signal[SIGNAL] = node;\n      node.registerCleanupFn = (fn: EffectCleanupFn) =>\n        (node.cleanup ??= new Set<() => void>()).add(fn);\n\n      this.nodes[phase] = node;\n\n      // Install the upstream hook which runs the `phaseFn` for this phase.\n      this.hooks[phase] = (value) => node.phaseFn(value);\n    }\n  }\n\n  override afterRun(): void {\n    super.afterRun();\n    // We're done running this sequence, so reset `lastPhase`.\n    this.lastPhase = null;\n  }\n\n  override destroy(): void {\n    super.destroy();\n\n    // Run the cleanup functions for each node.\n    for (const node of this.nodes) {\n      for (const fn of node?.cleanup ?? EMPTY_CLEANUP_SET) {\n        fn();\n      }\n    }\n  }\n}\n\n/**\n * An argument list containing the first non-never type in the given type array, or an empty\n * argument list if there are no non-never types in the type array.\n */\nexport type ɵFirstAvailableSignal<T extends unknown[]> = T extends [infer H, ...infer R]\n  ? [H] extends [never]\n    ? ɵFirstAvailableSignal<R>\n    : [Signal<H>]\n  : [];\n\n/**\n * Register an effect that, when triggered, is invoked when the application finishes rendering, during the\n * `mixedReadWrite` phase.\n *\n * <div class=\"docs-alert docs-alert-critical\">\n *\n * You should prefer specifying an explicit phase for the effect instead, or you risk significant\n * performance degradation.\n *\n * </div>\n *\n * Note that callback-based `afterRenderEffect`s will run\n * - in the order it they are registered\n * - only when dirty\n * - on browser platforms only\n * - during the `mixedReadWrite` phase\n *\n * <div class=\"docs-alert docs-alert-important\">\n *\n * Components are not guaranteed to be [hydrated](guide/hydration) before the callback runs.\n * You must use caution when directly reading or writing the DOM and layout.\n *\n * </div>\n *\n * @param callback An effect callback function to register\n * @param options Options to control the behavior of the callback\n *\n * @publicApi\n */\nexport function afterRenderEffect(\n  callback: (onCleanup: EffectCleanupRegisterFn) => void,\n  options?: AfterRenderOptions,\n): AfterRenderRef;\n/**\n * Register effects that, when triggered, are invoked when the application finishes rendering,\n * during the specified phases. The available phases are:\n * - `earlyRead`\n *   Use this phase to **read** from the DOM before a subsequent `write` callback, for example to\n *   perform custom layout that the browser doesn't natively support. Prefer the `read` phase if\n *   reading can wait until after the write phase. **Never** write to the DOM in this phase.\n * - `write`\n *    Use this phase to **write** to the DOM. **Never** read from the DOM in this phase.\n * - `mixedReadWrite`\n *    Use this phase to read from and write to the DOM simultaneously. **Never** use this phase if\n *    it is possible to divide the work among the other phases instead.\n * - `read`\n *    Use this phase to **read** from the DOM. **Never** write to the DOM in this phase.\n *\n * <div class=\"docs-alert docs-alert-critical\">\n *\n * You should prefer using the `read` and `write` phases over the `earlyRead` and `mixedReadWrite`\n * phases when possible, to avoid performance degradation.\n *\n * </div>\n *\n * Note that:\n * - Effects run in the following phase order, only when dirty through signal dependencies:\n *   1. `earlyRead`\n *   2. `write`\n *   3. `mixedReadWrite`\n *   4. `read`\n * - `afterRenderEffect`s in the same phase run in the order they are registered.\n * - `afterRenderEffect`s run on browser platforms only, they will not run on the server.\n * - `afterRenderEffect`s will run at least once.\n *\n * The first phase callback to run as part of this spec will receive no parameters. Each\n * subsequent phase callback in this spec will receive the return value of the previously run\n * phase callback as a `Signal`. This can be used to coordinate work across multiple phases.\n *\n * Angular is unable to verify or enforce that phases are used correctly, and instead\n * relies on each developer to follow the guidelines documented for each value and\n * carefully choose the appropriate one, refactoring their code if necessary. By doing\n * so, Angular is better able to minimize the performance degradation associated with\n * manual DOM access, ensuring the best experience for the end users of your application\n * or library.\n *\n * <div class=\"docs-alert docs-alert-important\">\n *\n * Components are not guaranteed to be [hydrated](guide/hydration) before the callback runs.\n * You must use caution when directly reading or writing the DOM and layout.\n *\n * </div>\n *\n * @param spec The effect functions to register\n * @param options Options to control the behavior of the effects\n *\n * @usageNotes\n *\n * Use `afterRenderEffect` to create effects that will read or write from the DOM and thus should\n * run after rendering.\n *\n * @publicApi\n */\nexport function afterRenderEffect<E = never, W = never, M = never>(\n  spec: {\n    earlyRead?: (onCleanup: EffectCleanupRegisterFn) => E;\n    write?: (...args: [...ɵFirstAvailableSignal<[E]>, EffectCleanupRegisterFn]) => W;\n    mixedReadWrite?: (...args: [...ɵFirstAvailableSignal<[W, E]>, EffectCleanupRegisterFn]) => M;\n    read?: (...args: [...ɵFirstAvailableSignal<[M, W, E]>, EffectCleanupRegisterFn]) => void;\n  },\n  options?: AfterRenderOptions,\n): AfterRenderRef;\n\n/**\n * @publicApi\n */\nexport function afterRenderEffect<E = never, W = never, M = never>(\n  callbackOrSpec:\n    | ((onCleanup: EffectCleanupRegisterFn) => void)\n    | {\n        earlyRead?: (onCleanup: EffectCleanupRegisterFn) => E;\n        write?: (...args: [...ɵFirstAvailableSignal<[E]>, EffectCleanupRegisterFn]) => W;\n        mixedReadWrite?: (\n          ...args: [...ɵFirstAvailableSignal<[W, E]>, EffectCleanupRegisterFn]\n        ) => M;\n        read?: (...args: [...ɵFirstAvailableSignal<[M, W, E]>, EffectCleanupRegisterFn]) => void;\n      },\n  options?: AfterRenderOptions,\n): AfterRenderRef {\n  ngDevMode &&\n    assertNotInReactiveContext(\n      afterRenderEffect,\n      'Call `afterRenderEffect` outside of a reactive context. For example, create the render ' +\n        'effect inside the component constructor`.',\n    );\n\n  if (ngDevMode && !options?.injector) {\n    assertInInjectionContext(afterRenderEffect);\n  }\n\n  if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n    return NOOP_AFTER_RENDER_REF;\n  }\n\n  const injector = options?.injector ?? inject(Injector);\n  const scheduler = injector.get(ChangeDetectionScheduler);\n  const manager = injector.get(AfterRenderManager);\n  const tracing = injector.get(TracingService, null, {optional: true});\n  manager.impl ??= injector.get(AfterRenderImpl);\n\n  let spec = callbackOrSpec;\n  if (typeof spec === 'function') {\n    spec = {mixedReadWrite: callbackOrSpec as any};\n  }\n\n  const viewContext = injector.get(ViewContext, null, {optional: true});\n\n  const sequence = new AfterRenderEffectSequence(\n    manager.impl,\n    [spec.earlyRead, spec.write, spec.mixedReadWrite, spec.read] as AfterRenderPhaseEffectHook[],\n    viewContext?.view,\n    scheduler,\n    injector.get(DestroyRef),\n    tracing?.snapshot(null),\n  );\n  manager.impl.register(sequence);\n  return sequence;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injector} from '../di/injector';\nimport {EnvironmentInjector, getNullInjector} from '../di/r3_injector';\nimport {Type} from '../interface/type';\nimport {ComponentRef} from '../linker/component_factory';\n\nimport {ComponentFactory} from './component_ref';\nimport {getComponentDef} from './def_getters';\nimport {Binding, DirectiveWithBindings} from './dynamic_bindings';\nimport {assertComponentDef} from './errors';\n\n/**\n * Creates a `ComponentRef` instance based on provided component type and a set of options.\n *\n * @usageNotes\n *\n * The example below demonstrates how the `createComponent` function can be used\n * to create an instance of a ComponentRef dynamically and attach it to an ApplicationRef,\n * so that it gets included into change detection cycles.\n *\n * Note: the example uses standalone components, but the function can also be used for\n * non-standalone components (declared in an NgModule) as well.\n *\n * ```angular-ts\n * @Component({\n *   standalone: true,\n *   template: `Hello {{ name }}!`\n * })\n * class HelloComponent {\n *   name = 'Angular';\n * }\n *\n * @Component({\n *   standalone: true,\n *   template: `<div id=\"hello-component-host\"></div>`\n * })\n * class RootComponent {}\n *\n * // Bootstrap an application.\n * const applicationRef = await bootstrapApplication(RootComponent);\n *\n * // Locate a DOM node that would be used as a host.\n * const hostElement = document.getElementById('hello-component-host');\n *\n * // Get an `EnvironmentInjector` instance from the `ApplicationRef`.\n * const environmentInjector = applicationRef.injector;\n *\n * // We can now create a `ComponentRef` instance.\n * const componentRef = createComponent(HelloComponent, {hostElement, environmentInjector});\n *\n * // Last step is to register the newly created ref using the `ApplicationRef` instance\n * // to include the component view into change detection cycles.\n * applicationRef.attachView(componentRef.hostView);\n * componentRef.changeDetectorRef.detectChanges();\n * ```\n * \n * @param component Component class reference.\n * @param options Set of options to use:\n *  * `environmentInjector`: An `EnvironmentInjector` instance to be used for the component.\n *  * `hostElement` (optional): A DOM node that should act as a host node for the component. If not\n * provided, Angular creates one based on the tag name used in the component selector (and falls\n * back to using `div` if selector doesn't have tag name info).\n *  * `elementInjector` (optional): An `ElementInjector` instance, see additional info about it\n * [here](guide/di/hierarchical-dependency-injection#elementinjector).\n *  * `projectableNodes` (optional): A list of DOM nodes that should be projected through\n * [`<ng-content>`](api/core/ng-content) of the new component instance, e.g.,\n * `[[element1, element2]]`: projects `element1` and `element2` into the same `<ng-content>`.\n * `[[element1, element2], [element3]]`: projects `element1` and `element2` into one `<ng-content>`,\n * and `element3` into a separate `<ng-content>`.\n *  * `directives` (optional): Directives that should be applied to the component.\n *  * `binding` (optional): Bindings to apply to the root component.\n * @returns ComponentRef instance that represents a given Component.\n *\n * @publicApi\n */\nexport function createComponent<C>(\n  component: Type<C>,\n  options: {\n    environmentInjector: EnvironmentInjector;\n    hostElement?: Element;\n    elementInjector?: Injector;\n    projectableNodes?: Node[][];\n    directives?: (Type<unknown> | DirectiveWithBindings<unknown>)[];\n    bindings?: Binding[];\n  },\n): ComponentRef<C> {\n  ngDevMode && assertComponentDef(component);\n  const componentDef = getComponentDef(component)!;\n  const elementInjector = options.elementInjector || getNullInjector();\n  const factory = new ComponentFactory<C>(componentDef);\n  return factory.create(\n    elementInjector,\n    options.projectableNodes,\n    options.hostElement,\n    options.environmentInjector,\n    options.directives,\n    options.bindings,\n  );\n}\n\n/**\n * An interface that describes the subset of component metadata\n * that can be retrieved using the `reflectComponentType` function.\n *\n * @publicApi\n */\nexport interface ComponentMirror<C> {\n  /**\n   * The component's HTML selector.\n   */\n  get selector(): string;\n  /**\n   * The type of component the factory will create.\n   */\n  get type(): Type<C>;\n  /**\n   * The inputs of the component.\n   */\n  get inputs(): ReadonlyArray<{\n    readonly propName: string;\n    readonly templateName: string;\n    readonly transform?: (value: any) => any;\n    readonly isSignal: boolean;\n  }>;\n  /**\n   * The outputs of the component.\n   */\n  get outputs(): ReadonlyArray<{readonly propName: string; readonly templateName: string}>;\n  /**\n   * Selector for all <ng-content> elements in the component.\n   */\n  get ngContentSelectors(): ReadonlyArray<string>;\n  /**\n   * Whether this component is marked as standalone.\n   * Note: an extra flag, not present in `ComponentFactory`.\n   */\n  get isStandalone(): boolean;\n  /**\n   * // TODO(signals): Remove internal and add public documentation\n   * @internal\n   */\n  get isSignal(): boolean;\n}\n\n/**\n * Creates an object that allows to retrieve component metadata.\n *\n * @usageNotes\n *\n * The example below demonstrates how to use the function and how the fields\n * of the returned object map to the component metadata.\n *\n * ```angular-ts\n * @Component({\n *   standalone: true,\n *   selector: 'foo-component',\n *   template: `\n *     <ng-content></ng-content>\n *     <ng-content select=\"content-selector-a\"></ng-content>\n *   `,\n * })\n * class FooComponent {\n *   @Input('inputName') inputPropName: string;\n *   @Output('outputName') outputPropName = new EventEmitter<void>();\n * }\n *\n * const mirror = reflectComponentType(FooComponent);\n * expect(mirror.type).toBe(FooComponent);\n * expect(mirror.selector).toBe('foo-component');\n * expect(mirror.isStandalone).toBe(true);\n * expect(mirror.inputs).toEqual([{propName: 'inputName', templateName: 'inputPropName'}]);\n * expect(mirror.outputs).toEqual([{propName: 'outputName', templateName: 'outputPropName'}]);\n * expect(mirror.ngContentSelectors).toEqual([\n *   '*',                 // first `<ng-content>` in a template, the selector defaults to `*`\n *   'content-selector-a' // second `<ng-content>` in a template\n * ]);\n * ```\n *\n * @param component Component class reference.\n * @returns An object that allows to retrieve component metadata.\n *\n * @publicApi\n */\nexport function reflectComponentType<C>(component: Type<C>): ComponentMirror<C> | null {\n  const componentDef = getComponentDef(component);\n  if (!componentDef) return null;\n\n  const factory = new ComponentFactory<C>(componentDef);\n  return {\n    get selector(): string {\n      return factory.selector;\n    },\n    get type(): Type<C> {\n      return factory.componentType;\n    },\n    get inputs(): ReadonlyArray<{\n      propName: string;\n      templateName: string;\n      transform?: (value: any) => any;\n      isSignal: boolean;\n    }> {\n      return factory.inputs;\n    },\n    get outputs(): ReadonlyArray<{propName: string; templateName: string}> {\n      return factory.outputs;\n    },\n    get ngContentSelectors(): ReadonlyArray<string> {\n      return factory.ngContentSelectors;\n    },\n    get isStandalone(): boolean {\n      return componentDef.standalone;\n    },\n    get isSignal(): boolean {\n      return componentDef.signals;\n    },\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {EnvironmentProviders, Provider} from '../di';\n\n/**\n * Set of config options available during the application bootstrap operation.\n *\n * @publicApi\n */\nexport interface ApplicationConfig {\n  /**\n   * List of providers that should be available to the root component and all its children.\n   */\n  providers: Array<Provider | EnvironmentProviders>;\n}\n\n/**\n * Merge multiple application configurations from left to right.\n *\n * @param configs Two or more configurations to be merged.\n * @returns A merged [ApplicationConfig](api/core/ApplicationConfig).\n *\n * @publicApi\n */\nexport function mergeApplicationConfig(...configs: ApplicationConfig[]): ApplicationConfig {\n  return configs.reduce(\n    (prev, curr) => {\n      return Object.assign(prev, curr, {providers: [...prev.providers, ...curr.providers]});\n    },\n    {providers: []},\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from '../di/injection_token';\n\n/**\n * Injection token representing the current HTTP request object.\n *\n * Use this token to access the current request when handling server-side\n * rendering (SSR).\n *\n * @remarks\n * This token may be `null` in the following scenarios:\n *\n * * During the build processes.\n * * When the application is rendered in the browser (client-side rendering).\n * * When performing static site generation (SSG).\n * * During route extraction in development (at the time of the request).\n *\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Request `Request` on MDN}\n *\n * @publicApi\n */\nexport const REQUEST = new InjectionToken<Request | null>(\n  typeof ngDevMode === 'undefined' || ngDevMode ? 'REQUEST' : '',\n  {\n    providedIn: 'platform',\n    factory: () => null,\n  },\n);\n\n/**\n * Injection token for response initialization options.\n *\n * Use this token to provide response options for configuring or initializing\n * HTTP responses in server-side rendering or API endpoints.\n *\n * @remarks\n * This token may be `null` in the following scenarios:\n *\n * * During the build processes.\n * * When the application is rendered in the browser (client-side rendering).\n * * When performing static site generation (SSG).\n * * During route extraction in development (at the time of the request).\n *\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Response/Response `ResponseInit` on MDN}\n *\n * @publicApi\n */\nexport const RESPONSE_INIT = new InjectionToken<ResponseInit | null>(\n  typeof ngDevMode === 'undefined' || ngDevMode ? 'RESPONSE_INIT' : '',\n  {\n    providedIn: 'platform',\n    factory: () => null,\n  },\n);\n\n/**\n * Injection token for additional request context.\n *\n * Use this token to pass custom metadata or context related to the current request in server-side rendering.\n *\n * @remarks\n * This token is only available during server-side rendering and will be `null` in other contexts.\n *\n * @publicApi\n */\nexport const REQUEST_CONTEXT = new InjectionToken<unknown>(\n  typeof ngDevMode === 'undefined' || ngDevMode ? 'REQUEST_CONTEXT' : '',\n  {\n    providedIn: 'platform',\n    factory: () => null,\n  },\n);\n"], "names": ["ɵɵinjectAttribute", "R3NgModuleFactory", "i0.ɵɵinject", "i1.Injector", "global", "ViewRef", "ɵɵdefineInjectable", "i1.ApplicationRef", "CURRENT_DEFER_BLOCK_STATE", "ViewEncapsulation"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAUO,MAAM,oBAAoB,mBAAkC,MAAM,CAAC,uBAAuB,CAAC;AA6BlG;AACA;AACA;AACO,MAAM,iBAAiB,mBAAsD,CAAC,MAAK;IACxF,OAAO;AACL,QAAA,GAAG,WAAW;AACd,QAAA,WAAW,EAAE,SAAS;QAEtB,uBAAuB,CAAgB,IAAoC,EAAE,KAAQ,EAAA;AACnF,YAAA,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC;SACzB;KACF;AACH,CAAC,GAAG;;MCSS,8BAA8B,mBAAkC,MAAM;AA6CnF;;;;;;AAMG;AACa,SAAA,iBAAiB,CAC/B,YAAe,EACf,OAAqC,EAAA;IAErC,MAAM,IAAI,GAAmC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC;AAE7E,IAAA,IAAI,CAAC,KAAK,GAAG,YAAY;;;AAIzB,IAAA,IAAI,CAAC,WAAW,GAAG,OAAO,EAAE,SAAS;AAErC,IAAA,SAAS,YAAY,GAAA;;QAEnB,gBAAgB,CAAC,IAAI,CAAC;AAEtB,QAAA,IAAI,IAAI,CAAC,KAAK,KAAK,oBAAoB,EAAE;YACvC,IAAI,OAAO,GAAkB,IAAI;YACjC,IAAI,SAAS,EAAE;gBACb,MAAM,IAAI,GAAG,OAAO,EAAE,SAAS,IAAI,OAAO,EAAE,KAAK;AACjD,gBAAA,OAAO,GAAG,CAAA,KAAA,EAAQ,IAAI,GAAG,CAAA,EAAA,EAAK,IAAI,CAAA,CAAA,CAAG,GAAG,EAAE,6CAA6C;;AAEzF,YAAA,MAAM,IAAI,YAAY,CAA2C,CAAA,GAAA,iDAAA,OAAO,CAAC;;QAG3E,OAAO,IAAI,CAAC,KAAK;;AAGlB,IAAA,YAAoB,CAAC,MAAM,CAAC,GAAG,IAAI;IAEpC,IAAI,SAAS,EAAE;QACb,YAAY,CAAC,QAAQ,GAAG,MAAM,CAAkB,eAAA,EAAA,YAAY,EAAE,CAAA,CAAA,CAAG;AACjE,QAAA,IAAI,CAAC,SAAS,GAAG,OAAO,EAAE,SAAS;;AAGrC,IAAA,OAAO,YAAuD;AAChE;;IC7BY;AAAZ,CAAA,UAAY,aAAa,EAAA;AACvB,IAAA,aAAA,CAAA,aAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAa;AACb,IAAA,aAAA,CAAA,aAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAa;AACb,IAAA,aAAA,CAAA,aAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAc;AACd,IAAA,aAAA,CAAA,aAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ;AACR,IAAA,aAAA,CAAA,aAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAY;AACd,CAAC,EANW,aAAa,KAAb,aAAa,GAMxB,EAAA,CAAA,CAAA;AA0LD,IAAY,wBAIX;AAJD,CAAA,UAAY,wBAAwB,EAAA;AAClC,IAAA,wBAAA,CAAA,wBAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAa;AACb,IAAA,wBAAA,CAAA,wBAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ;AACR,IAAA,wBAAA,CAAA,wBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAY;AACd,CAAC,EAJW,wBAAwB,KAAxB,wBAAwB,GAInC,EAAA,CAAA,CAAA;AA8BD,IAAY,iBAKX;AALD,CAAA,UAAY,iBAAiB,EAAA;AAC3B,IAAA,iBAAA,CAAA,iBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAY;;AAEZ,IAAA,iBAAA,CAAA,iBAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ;AACR,IAAA,iBAAA,CAAA,iBAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAa;AACf,CAAC,EALW,iBAAiB,KAAjB,iBAAiB,GAK5B,EAAA,CAAA,CAAA;;AC9VD;;;;;;AAMG;AAIH;;;;;;;;;;;;;;;;;;;;AAoBG;MACU,kBAAkB,CAAA;AACT,IAAA,aAAA;AAApB,IAAA,WAAA,CAAoB,aAAqB,EAAA;QAArB,IAAa,CAAA,aAAA,GAAb,aAAa;;;IAGjC,iBAAiB,GAAG,MAAMA,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC;IAE/D,QAAQ,GAAA;AACN,QAAA,OAAO,CAAsB,mBAAA,EAAA,IAAI,CAAC,aAAa,EAAE;;AAEpD;;ACzBD;;;;;;;;;;;;;;;;;;;;AAoBG;AACU,MAAA,aAAa,GAAG,IAAI,cAAc,CAAS,SAAS,GAAG,eAAe,GAAG,EAAE;AAExF;AACA;AACA;AACC,aAAqB,CAAC,iBAAiB,GAAG,CAAC,KAA0B,KAAI;AACxE,IAAA,MAAM,KAAK,GAAG,eAAe,EAAE;AAC/B,IAAA,IAAI,KAAK,KAAK,IAAI,EAAE;QAClB,MAAM,IAAI,YAAY,CAAA,GAAA,iDAEpB,SAAS;YACP,kEAAkE;AAChE,gBAAA,mFAAmF,CACxF;;AAEH,IAAA,IAAI,KAAK,CAAC,IAAI,GAAA,CAAA,0BAAsB;QAClC,OAAO,KAAK,CAAC,KAAK;;IAEpB,IAAI,KAAK,GAA+B,CAAA,qCAAE;AACxC,QAAA,OAAO,IAAI;;IAEb,MAAM,IAAI,YAAY,CAAA,GAAA,iDAEpB,SAAS;AACP,QAAA,CAAA,0BAAA,EAA6B,kBAAkB,CAC7C,KAAK,CACN,CAAwD,sDAAA,CAAA;AACvD,YAAA,CAAA,oEAAA,CAAsE,CAC3E;AACH,CAAC;AAED,SAAS,kBAAkB,CAAC,KAAY,EAAA;AACtC,IAAA,IAAI,KAAK,CAAC,IAAI,GAAA,CAAA,mCAA+B;AAC3C,QAAA,OAAO,mBAAmB;;AACrB,SAAA,IAAI,KAAK,CAAC,IAAI,GAAA,CAAA,4BAAwB;AAC3C,QAAA,OAAO,kBAAkB;;AACpB,SAAA,IAAI,KAAK,CAAC,IAAI,GAAA,GAAA,iCAA6B;AAChD,QAAA,OAAO,qBAAqB;;SACvB;AACL,QAAA,OAAO,QAAQ;;AAEnB;;ACxDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCG;AACG,SAAU,MAAM,CAAW,IAAoB,EAAA;AACnD,IAAA,SAAS,IAAI,wBAAwB,CAAC,MAAM,CAAC;IAC7C,OAAO,IAAI,gBAAgB,EAAK;AAClC;;AC9CgB,SAAA,aAAa,CAC3B,YAAoB,EACpB,IAAkC,EAAA;AAElC,IAAA,SAAS,IAAI,wBAAwB,CAAC,KAAK,CAAC;AAC5C,IAAA,OAAO,iBAAiB,CAAC,YAAY,EAAE,IAAI,CAAC;AAC9C;AAEM,SAAU,qBAAqB,CACnC,IAAkC,EAAA;AAElC,IAAA,SAAS,IAAI,wBAAwB,CAAC,KAAK,CAAC;AAC5C,IAAA,OAAO,iBAAiB,CAAC,oBAA6B,EAAE,IAAI,CAAC;AAC/D;AAmEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CG;AACU,MAAA,KAAK,GAAkB,CAAC,MAAK;;;;AAIvC,IAAA,aAAqB,CAAC,QAAQ,GAAG,qBAAqB;AACvD,IAAA,OAAO,aAAgF;AACzF,CAAC;;ACzID,SAAS,WAAW,CAClB,OAAyC,EACzC,IAAwD,EAAA;AAExD,IAAA,SAAS,IAAI,wBAAwB,CAAC,SAAS,CAAC;AAChD,IAAA,OAAO,uCAAuC,CAAQ,IAAI,CAAC;AAC7D;AAEA,SAAS,mBAAmB,CAC1B,OAAyC,EACzC,IAAwD,EAAA;AAExD,IAAA,SAAS,IAAI,wBAAwB,CAAC,SAAS,CAAC;AAChD,IAAA,OAAO,uCAAuC,CAAQ,IAAI,CAAC;AAC7D;AAyDA;;;;;;;;;;;;;;;;;;;;;AAqBG;AACU,MAAA,SAAS,GAAsB,CAAC,MAAK;;;;AAI/C,IAAA,WAAmB,CAAC,QAAQ,GAAG,mBAAmB;AACnD,IAAA,OAAO,WAA0E;AACnF,CAAC;AAcD;;;;;;;;;;;;;;;;;;;AAmBG;AACa,SAAA,YAAY,CAC1B,OAAyC,EACzC,IAGC,EAAA;AAED,IAAA,SAAS,IAAI,wBAAwB,CAAC,YAAY,CAAC;AACnD,IAAA,OAAO,8BAA8B,CAAQ,IAAI,CAAC;AACpD;AAEgB,SAAA,cAAc,CAC5B,OAAyC,EACzC,IAIC,EAAA;AAED,IAAA,SAAS,IAAI,wBAAwB,CAAC,YAAY,CAAC;AACnD,IAAA,OAAO,uCAAuC,CAAQ,IAAI,CAAC;AAC7D;AAEA,SAAS,sBAAsB,CAC7B,OAAyC,EACzC,IAIC,EAAA;AAED,IAAA,SAAS,IAAI,wBAAwB,CAAC,eAAe,CAAC;AACtD,IAAA,OAAO,uCAAuC,CAAQ,IAAI,CAAC;AAC7D;AA4DA;;;;;;;;;;;;;;;;;;;;;;AAsBG;AACU,MAAA,YAAY,GAAyB,CAAC,MAAK;;;;AAIrD,IAAA,cAAsB,CAAC,QAAQ,GAAG,sBAAsB;AACzD,IAAA,OAAO,cAAmF;AAC5F,CAAC;AAmBD;;;;;;;;;;;;;;;;;;;;;AAqBG;AACa,SAAA,eAAe,CAC7B,OAAyC,EACzC,IAIC,EAAA;AAED,IAAA,OAAO,8BAA8B,CAAQ,IAAI,CAAC;AACpD;;AC1QA;;;;;;AAMG;AACa,SAAA,iBAAiB,CAAI,YAAe,EAAE,IAAmB,EAAA;IACvE,MAAM,IAAI,GAA0B,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC;AACpE,IAAA,MAAM,UAAU,GAAG,IAAI,gBAAgB,EAAK;AAE5C,IAAA,IAAI,CAAC,KAAK,GAAG,YAAY;AAEzB,IAAA,SAAS,MAAM,GAAA;QACb,gBAAgB,CAAC,IAAI,CAAC;AACtB,QAAA,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;QAC1B,OAAO,IAAI,CAAC,KAAK;;AAGnB,IAAA,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI;IACrB,MAAM,CAAC,UAAU,GAAG,kBAAkB,CAAC,IAAI,CAAC,MAAa,CAAoB;;AAG7E,IAAA,MAAM,CAAC,GAAG,GAAG,CAAC,QAAW,KAAI;AAC3B,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;AACrC,YAAA,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC;AAC3B,YAAA,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC;;AAE7B,KAAC;AAED,IAAA,MAAM,CAAC,MAAM,GAAG,CAAC,QAAyB,KAAI;AAC5C,QAAA,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;QAC1B,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAClC,KAAC;IAED,MAAM,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;AACxD,IAAA,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU;IAEzC,IAAI,SAAS,EAAE;QACb,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAkB,eAAA,EAAA,MAAM,EAAE,CAAA,CAAA,CAAG;AACrD,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,EAAE,SAAS;;AAGlC,IAAA,OAAO,MAMJ;AACL;AAEA;AACA,SAAS,cAAc,CAAC,KAAc,EAAA;AACpC,IAAA,IAAI,KAAK,KAAK,oBAAoB,EAAE;AAClC,QAAA,MAAM,IAAI,YAAY,CAAA,GAAA,iDAEpB,SAAS,IAAI,kDAAkD,CAChE;;AAEL;;ACvGgB,SAAA,aAAa,CAC3B,YAAgB,EAChB,IAAmB,EAAA;AAEnB,IAAA,SAAS,IAAI,wBAAwB,CAAC,KAAK,CAAC;AAE5C,IAAA,OAAO,iBAAiB,CAAC,YAAY,EAAE,IAAI,CAAC;AAC9C;AAEM,SAAU,qBAAqB,CAAI,IAAmB,EAAA;AAC1D,IAAA,SAAS,IAAI,wBAAwB,CAAC,KAAK,CAAC;AAE5C,IAAA,OAAO,iBAAiB,CAAC,oBAAyB,EAAE,IAAI,CAAC;AAC3D;AAiCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CG;AACU,MAAA,KAAK,GAAkB,CAAC,MAAK;;;;AAIvC,IAAA,aAAqB,CAAC,QAAQ,GAAG,qBAAqB;AACvD,IAAA,OAAO,aAAgF;AACzF,CAAC;;AC/BD;AACA;AACO,MAAM,mCAAmC,GAAG,IAAI;AAEvD;;;;;;;;;AASG;MACmB,KAAK,CAAA;AAAG;AA6F9B;;;;;;AAMG;AACU,MAAA,eAAe,GAA6B,iBAAiB,CACxE,iBAAiB,EACjB,CAAC,QAAc,EAAE,IAAY,GAAA,EAAE,MAAM;IACnC,QAAQ;AACR,IAAA,KAAK,EAAE,KAAK;AACZ,IAAA,WAAW,EAAE,KAAK;AAClB,IAAA,WAAW,EAAE,KAAK;AAClB,IAAA,uBAAuB,EAAE,mCAAmC;AAC5D,IAAA,GAAG,IAAI;CACR,CAAC,EACF,KAAK;AAqFP;;;;;;;AAOG;AACU,MAAA,YAAY,GAA0B,iBAAiB,CAClE,cAAc,EACd,CAAC,QAAc,EAAE,IAAY,GAAA,EAAE,MAAM;IACnC,QAAQ;AACR,IAAA,KAAK,EAAE,IAAI;AACX,IAAA,WAAW,EAAE,KAAK;AAClB,IAAA,WAAW,EAAE,IAAI;AACjB,IAAA,GAAG,IAAI;CACR,CAAC,EACF,KAAK;AA+EP;;;;;AAKG;AACU,MAAA,YAAY,GAA0B,iBAAiB,CAClE,cAAc,EACd,CAAC,QAAc,EAAE,IAAY,GAAA,EAAE,MAAM;IACnC,QAAQ;AACR,IAAA,KAAK,EAAE,KAAK;AACZ,IAAA,WAAW,EAAE,IAAI;AACjB,IAAA,WAAW,EAAE,IAAI;AACjB,IAAA,uBAAuB,EAAE,mCAAmC;AAC5D,IAAA,GAAG,IAAI;CACR,CAAC,EACF,KAAK;AAiFP;;;;;AAKG;AACI,MAAM,SAAS,GAAuB,iBAAiB,CAC5D,WAAW,EACX,CAAC,QAAa,EAAE,IAAS,MAAM;IAC7B,QAAQ;AACR,IAAA,KAAK,EAAE,IAAI;AACX,IAAA,WAAW,EAAE,IAAI;AACjB,IAAA,WAAW,EAAE,IAAI;AACjB,IAAA,GAAG,IAAI;CACR,CAAC,EACF,KAAK;;AC1eP;;;;AAIG;MACU,OAAO,CAAA;AAKC,IAAA,IAAA;AAJH,IAAA,KAAK;AACL,IAAA,KAAK;AACL,IAAA,KAAK;AAErB,IAAA,WAAA,CAAmB,IAAY,EAAA;QAAZ,IAAI,CAAA,IAAA,GAAJ,IAAI;QACrB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;AAC7B,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;AACrB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;AACrB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;;AAExC;AAED;;AAEG;MACU,OAAO,GAAG,IAAI,OAAO,CAAC,mBAAmB;;SCRtC,sBAAsB,CACpC,QAAkB,EAClB,OAAwB,EACxB,UAAmB,EAAA;AAEnB,IAAA,SAAS,IAAI,kBAAkB,CAAC,UAAU,CAAC;AAE3C,IAAA,MAAM,aAAa,GAAG,IAAIC,eAAiB,CAAC,UAAU,CAAC;;IAGvD,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,SAAS,EAAE;AAClD,QAAA,OAAO,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC;;AAGvC,IAAA,MAAM,eAAe,GAAG,QAAQ,CAAC,GAAG,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;;;;AAK1E,IAAA,aAAa,CAAC;AACZ,QAAA,oBAAoB,EAAE,YAAY,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,oBAAoB,CAAC,CAAC;AAC5F,QAAA,mBAAmB,EAAE,YAAY,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAC3F,KAAA,CAAC;IAEF,IAAI,uCAAuC,EAAE,EAAE;AAC7C,QAAA,OAAO,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC;;AAGvC,IAAA,MAAM,iBAAiB,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,SAAS,IAAI,EAAE,CAAC;;;;;AAMrF,IAAA,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;AAClC,QAAA,OAAO,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC;;IAGvC,MAAM,QAAQ,GAAG,iBAAiB,CAAC;AACjC,QAAA,KAAK,EAA4B,CAAA;AACjC,QAAA,IAAI,EAAE,UAAU;AAChB,QAAA,IAAI,EAAE,UAAU;AACjB,KAAA,CAAC;AACF,IAAA,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,EAAC,SAAS,EAAE,iBAAiB,EAAC,CAAC;IACxE,MAAM,cAAc,GAAG,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC;;;IAGpE,OAAO,yBAAyB,CAAC,CAAC,GAAG,KAAK,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CACtF,MAAM,aAAa,CACpB;AACH;AAEA,SAAS,YAAY,CAAI,IAAS,EAAA;AAChC,IAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACzC,QAAA,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;AACzB,YAAA,OAAO,IAAI,CAAC,CAAC,CAAC;;;AAGlB,IAAA,OAAO,SAAS;AAClB;;ACjEA;AACA;AACA;AACA;AACA,MAAM,UAAU,GAAG,GAAG;AAEtB,MAAM,yBAAyB,GAAG,IAAI;MAGzB,uBAAuB,CAAA;;IAE1B,MAAM,GAAkB,IAAI;IAC5B,QAAQ,GAA+B,IAAI;AAC3C,IAAA,OAAO,GAAgB,MAAM,CAAC,YAAY,CAAC;AAC3C,IAAA,WAAW;IAEZ,KAAK,GAAA;AACV,QAAA,IACE,CAAC,OAAO,YAAY,KAAK,WAAW,IAAI,YAAY;YACpD,OAAO,mBAAmB,KAAK,WAAW;AAC1C,aAAC,IAAI,CAAC,OAAO,EAAE,uBAAuB,IAAI,IAAI,CAAC,OAAO,EAAE,2BAA2B,CAAC,EACpF;YACA;;AAEF,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,uBAAuB,EAAE;AAC9C,QAAA,MAAM,GAAG,GAAG,WAAW,EAAE;AACzB,QAAA,MAAM,GAAG,GAAG,GAAG,CAAC,WAAW;QAC3B,IAAI,GAAG,EAAE;AACP,YAAA,IAAI,CAAC,MAAM,GAAG,GAAG;;;YAGjB,MAAM,UAAU,GAAG,MAAK;AACtB,gBAAA,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,UAAU,CAAC;AACpD,aAAC;YACD,MAAM,KAAK,GAAG,MAAK;;;;;;AAMjB,gBAAA,IAAI,GAAG,CAAC,UAAU,KAAK,UAAU,EAAE;AACjC,oBAAA,UAAU,EAAE;;qBACP;AACL,oBAAA,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC,MAAM,EAAE,UAAU,EAAE,EAAC,IAAI,EAAE,IAAI,EAAC,CAAC;;AAEnE,aAAC;;;AAGD,YAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;gBAC/B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,KAAK,EAAE,CAAC;;iBACvB;AACL,gBAAA,KAAK,EAAE;;;;IAKb,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,QAAQ,EAAE,UAAU,EAAE;;IAGrB,uBAAuB,GAAA;AAC7B,QAAA,IAAI,OAAO,mBAAmB,KAAK,WAAW,EAAE;AAC9C,YAAA,OAAO,IAAI;;QAEb,MAAM,QAAQ,GAAG,IAAI,mBAAmB,CAAC,CAAC,SAAS,KAAI;AACrD,YAAA,MAAM,OAAO,GAAG,SAAS,CAAC,UAAU,EAAE;AACtC,YAAA,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;gBAAE;;;;;YAK1B,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;;;YAI9C,MAAM,MAAM,GAAI,UAAkB,CAAC,OAAO,EAAE,GAAG,IAAI,EAAE;;AAGrD,YAAA,IAAI,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC;gBAAE;AAC9D,YAAA,IAAI,CAAC,WAAW,GAAG,MAAM;AAC3B,SAAC,CAAC;AACF,QAAA,QAAQ,CAAC,OAAO,CAAC,EAAC,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC;AACpE,QAAA,OAAO,QAAQ;;IAGT,UAAU,GAAA;QAChB,MAAM,MAAM,GAAG,WAAW,EAAE,CAAC,gBAAgB,CAAC,KAAK,CAAC;AACpD,QAAA,IAAI,eAAe,EACjB,yBAAyB,GAAG,KAAK;;;;;AAKnC,QAAA,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;AAClD,YAAA,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;YAE3B,IAAI,CAAC,KAAK,EAAE;gBACV;;AAGF,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,uBAAuB,EAAE;;;AAG1C,gBAAA,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;AAC5D,oBAAA,wBAAwB,CAAC,KAAK,CAAC,GAAG,CAAC;;;YAGvC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,2BAA2B,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClE,IAAI,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,WAAW,EAAE;oBAClC,eAAe,GAAG,IAAI;AACtB,oBAAA,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE;;;;;wBAK5D,yBAAyB,GAAG,IAAI;;;;;AAKxC,QAAA,IACE,eAAe;AACf,YAAA,CAAC,yBAAyB;AAC1B,YAAA,IAAI,CAAC,WAAW;AAChB,YAAA,CAAC,IAAI,CAAC,OAAO,EAAE,2BAA2B,EAC1C;AACA,YAAA,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC;;;AAI/B,IAAA,WAAW,CAAC,KAAuB,EAAA;AACzC,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAChB,YAAA,OAAO,KAAK;;;;;;AAOd,QAAA,MAAM,2BAA2B,GAAG;;;YAGlC,MAAM;SACP;;;;;;AAOD,QAAA,MAAM,WAAW,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,EAAE,EAAE,WAAW,EAAE;AAEnD,QAAA,IAAI,2BAA2B,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE;AACpF,YAAA,OAAO,KAAK;;QAGd,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC;QACzD,IAAI,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACvE,IAAI,cAAc,GAAG,UAAU,CAAC,aAAa,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACzE,MAAM,SAAS,GAAG,aAAa,CAAC,gBAAgB,CAAC,YAAY,CAAC;QAC9D,MAAM,SAAS,GAAG,aAAa,CAAC,gBAAgB,CAAC,YAAY,CAAC;AAE9D,QAAA,IAAI,SAAS,KAAK,CAAO,KAAA,CAAA,EAAE;;;AAGzB,YAAA,OAAO,KAAK;;AAGd,QAAA,IAAI,SAAS,KAAK,YAAY,EAAE;;;YAG9B,MAAM,UAAU,GAAG,aAAa,CAAC,gBAAgB,CAAC,aAAa,CAAC;YAChE,MAAM,YAAY,GAAG,aAAa,CAAC,gBAAgB,CAAC,eAAe,CAAC;YACpE,MAAM,aAAa,GAAG,aAAa,CAAC,gBAAgB,CAAC,gBAAgB,CAAC;YACtE,MAAM,WAAW,GAAG,aAAa,CAAC,gBAAgB,CAAC,cAAc,CAAC;YAClE,aAAa,IAAI,UAAU,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC,WAAW,CAAC;YACnE,cAAc,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,aAAa,CAAC;;AAGtE,QAAA,MAAM,cAAc,GAAG,KAAK,CAAC,YAAY;AACzC,QAAA,MAAM,eAAe,GAAG,KAAK,CAAC,aAAa;QAE3C,MAAM,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAG,aAAa;QACrE,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAG,cAAc;AACvE,QAAA,MAAM,cAAc,GAAG,cAAc,GAAG,gBAAgB,IAAI,yBAAyB;AACrF,QAAA,MAAM,eAAe,GAAG,eAAe,GAAG,iBAAiB,IAAI,yBAAyB;QACxF,OAAO,cAAc,IAAI,eAAe;;iHAjL/B,uBAAuB,GAAA,CAAA,EAAA;6DAAvB,uBAAuB,EAAA,OAAA,EAAvB,uBAAuB,CAAA,IAAA,EAAA,UAAA,EADX,MAAM,EAAA,CAAA;;6EAClB,uBAAuB,EAAA,CAAA;cADnC,UAAU;eAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;AAsLhC,SAAS,iBAAiB,CAAC,GAAW,EAAA;AACpC,IAAA,OAAO,CAAC,IAAI,CACV,kBAAkB,CAEhB,CAAA,GAAA,mDAAA,CAAA,kBAAA,EAAqB,GAAG,CAAiD,+CAAA,CAAA;QACvE,CAAyE,uEAAA,CAAA;QACzE,CAAoE,kEAAA,CAAA;QACpE,CAA0E,wEAAA,CAAA;QAC1E,CAAkE,gEAAA,CAAA;QAClE,CAA8D,4DAAA,CAAA;QAC9D,CAAmC,iCAAA,CAAA,CACtC,CACF;AACH;AAEA,SAAS,wBAAwB,CAAC,GAAW,EAAA;AAC3C,IAAA,OAAO,CAAC,IAAI,CACV,kBAAkB,CAEhB,CAAA,GAAA,mDAAA,CAAA,kBAAA,EAAqB,GAAG,CAAsD,oDAAA,CAAA;QAC5E,CAA6E,2EAAA,CAAA;QAC7E,CAAuE,qEAAA,CAAA;QACvE,CAAmC,iCAAA,CAAA,CACtC,CACF;AACH;;AC5NA;;;;;AAKG;AACI,MAAM,0BAA0B,GAAG,IAAI,cAAc,CAC1D,SAAS,GAAG,0BAA0B,GAAG,EAAE,CAC5C;;ACYD;;;;;;;;;;;;;;;;;;;AAmBG;AACU,MAAA,+BAA+B,GAAG,IAAI,cAAc,CAC/D,SAAS,GAAG,iCAAiC,GAAG,EAAE;AAiBpD,SAAS,4BAA4B,CACnC,MAAmE,EAAA;AAEnE,IAAA,OAAO,CAAE,MAAyC,CAAC,SAAS;AAC9D;AAQM,SAAU,SAAS,CACvB,MAA6D,EAAA;AAE7D,IAAA,MAAM,WAAW,GAAG,4BAA4B,CAAC,MAAM;UACnD,MAAM,CAAC;AACT,UAAE,MAAM,CAAC,SAAS,CAAC,QAAQ;IAC7B,MAAM,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC;AACtC,IAAA,OAAO,MAAM,CAAC,GAAG,CAAC,MAAK;AACrB,QAAA,IAAI,4BAA4B,CAAC,MAAM,CAAC,EAAE;AACxC,YAAA,MAAM,CAAC,UAAU,CAAC,2BAA2B,EAAE;;aAC1C;AACL,YAAA,MAAM,CAAC,SAAS,CAAC,2BAA2B,EAAE;;QAEhD,MAAM,gBAAgB,GAAG,WAAW,CAAC,GAAG,CAAC,kCAAkC,CAAC;AAC5E,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,IAAI,WAAW,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,WAAW,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE;gBAC3E,MAAM,IAAI,YAAY,CAAA,GAAA,yDAEpB,0CAA0C;AACxC,oBAAA,wFAAwF,CAC3F;;;AAIL,QAAA,IAAI,mBAAiC;AACrC,QAAA,MAAM,CAAC,iBAAiB,CAAC,MAAK;AAC5B,YAAA,mBAAmB,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;AAC7C,gBAAA,IAAI,EAAE,gBAAgB;AACvB,aAAA,CAAC;AACJ,SAAC,CAAC;;;AAIF,QAAA,IAAI,4BAA4B,CAAC,MAAM,CAAC,EAAE;YACxC,MAAM,eAAe,GAAG,MAAM,WAAW,CAAC,OAAO,EAAE;YACnD,MAAM,0BAA0B,GAAG,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,0BAA0B,CAAC;AAC1F,YAAA,0BAA0B,CAAC,GAAG,CAAC,eAAe,CAAC;AAE/C,YAAA,WAAW,CAAC,SAAS,CAAC,MAAK;gBACzB,mBAAmB,CAAC,WAAW,EAAE;AACjC,gBAAA,0BAA0B,CAAC,MAAM,CAAC,eAAe,CAAC;AACpD,aAAC,CAAC;;aACG;YACL,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE;YACxD,MAAM,0BAA0B,GAAG,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,0BAA0B,CAAC;AAC1F,YAAA,0BAA0B,CAAC,GAAG,CAAC,eAAe,CAAC;AAE/C,YAAA,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,MAAK;gBAC9B,MAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE,MAAM,CAAC,SAAS,CAAC;gBACnD,mBAAmB,CAAC,WAAW,EAAE;AACjC,gBAAA,0BAA0B,CAAC,MAAM,CAAC,eAAe,CAAC;AACpD,aAAC,CAAC;;AAGJ,QAAA,OAAO,4BAA4B,CAAC,gBAAgB,EAAE,MAAM,EAAE,MAAK;YACjE,MAAM,YAAY,GAAG,WAAW,CAAC,GAAG,CAAC,oBAAoB,CAAC;AAC1D,YAAA,MAAM,MAAM,GAAG,YAAY,CAAC,GAAG,EAAE;YACjC,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,qBAAqB,CAAC;YACzD,UAAU,CAAC,eAAe,EAAE;YAE5B,OAAO,UAAU,CAAC;iBACf,IAAI,CAAC,MAAK;;gBAET,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,iBAAiB,CAAC;AAC9D,gBAAA,WAAW,CAAC,QAAQ,IAAI,iBAAiB,CAAC;gBAE1C,MAAM,2BAA2B,GAAG,WAAW,CAAC,GAAG,CACjD,+BAA+B,EAC/B,IAAI,CACL;gBACD,IAAI,CAAC,2BAA2B,EAAE;AAChC,oBAAA,IAAI,4BAA4B,CAAC,MAAM,CAAC,EAAE;AACxC,wBAAA,OAAO,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC;;oBAGxC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;oBAChD,OAAO,MAAM,CAAC,SAAS;;AAGzB,gBAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;oBACjD,MAAM,uBAAuB,GAAG,WAAW,CAAC,GAAG,CAAC,uBAAuB,CAAC;oBACxE,uBAAuB,CAAC,KAAK,EAAE;;AAGjC,gBAAA,IAAI,4BAA4B,CAAC,MAAM,CAAC,EAAE;oBACxC,MAAM,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC;AAC9C,oBAAA,IAAI,MAAM,CAAC,aAAa,KAAK,SAAS,EAAE;AACtC,wBAAA,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC;;AAExC,oBAAA,OAAO,MAAM;;qBACR;oBACL,mBAAmB,GAAG,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,kBAAkB,CAAC;oBAClE,OAAO,MAAM,CAAC,SAAS;;AAE3B,aAAC;AACA,iBAAA,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACpD,SAAC,CAAC;AACJ,KAAC,CAAC;AACJ;AAEA;;;AAGG;AACH,IAAI,mBAA0D;AAE9D;;AAEG;SACa,sBAAsB,GAAA;IACpC,mBAAmB,GAAG,kBAAkB;AAC1C;AAEA,SAAS,kBAAkB,CACzB,SAAmC,EACnC,kBAA0C,EAAA;IAE1C,MAAM,MAAM,GAAG,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC;IACrD,IAAI,SAAS,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7C,QAAA,SAAS,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;;AAC7D,SAAA,IAAI,SAAS,CAAC,QAAQ,CAAC,aAAa,EAAE;AAC3C,QAAA,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC;;SACnC;QACL,MAAM,IAAI,YAAY,CAAA,CAAA,GAAA,wDAEpB,SAAS;YACP,CAAc,WAAA,EAAA,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAqB,mBAAA,CAAA;gBAC1E,CAAyF,uFAAA,CAAA;AACzF,gBAAA,CAAA,2BAAA,CAA6B,CAClC;;AAEH,IAAA,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC;AACpC;AAEA,SAAS,4BAA4B,CACnC,YAAkC,EAClC,MAAc,EACd,QAAmB,EAAA;AAEnB,IAAA,IAAI;AACF,QAAA,MAAM,MAAM,GAAG,QAAQ,EAAE;AACzB,QAAA,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE;AACrB,YAAA,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,CAAM,KAAI;gBAC7B,MAAM,CAAC,iBAAiB,CAAC,MAAM,YAAY,CAAC,CAAC,CAAC,CAAC;;AAE/C,gBAAA,MAAM,CAAC;AACT,aAAC,CAAC;;AAGJ,QAAA,OAAO,MAAM;;IACb,OAAO,CAAC,EAAE;QACV,MAAM,CAAC,iBAAiB,CAAC,MAAM,YAAY,CAAC,CAAC,CAAC,CAAC;;AAE/C,QAAA,MAAM,CAAC;;AAEX;;AChNA;;;;;;;;AAQG;MAEU,WAAW,CAAA;AAMF,IAAA,SAAA;IALZ,QAAQ,GAAuB,EAAE;IACjC,iBAAiB,GAAsB,EAAE;IACzC,UAAU,GAAY,KAAK;;AAGnC,IAAA,WAAA,CAAoB,SAAmB,EAAA;QAAnB,IAAS,CAAA,SAAA,GAAT,SAAS;;AAE7B;;;;;AAKG;IACH,sBAAsB,CACpB,aAAiC,EACjC,OAA0B,EAAA;AAE1B,QAAA,MAAM,kBAAkB,GAAI,OAAe,EAAE,kBAAkB;QAC/D,MAAM,aAAa,GAAG,MACpB,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE;AACzB,YAAA,GAAG,gBAAgB,CAAC;gBAClB,eAAe,EAAE,OAAO,EAAE,qBAAqB;gBAC/C,aAAa,EAAE,OAAO,EAAE,mBAAmB;aAC5C,CAAC;YACF,kBAAkB;AACnB,SAAA,CAAC;AACJ,QAAA,MAAM,wBAAwB,GAAG,OAAO,EAAE,wBAAwB;AAClE,QAAA,MAAM,eAAe,GAAG;AACtB,YAAA,kCAAkC,CAAC;gBACjC,aAAa;gBACb,wBAAwB;aACzB,CAAC;AACF,YAAA,EAAC,OAAO,EAAE,wBAAwB,EAAE,WAAW,EAAE,4BAA4B,EAAC;YAC9E,kCAAkC;SACnC;AACD,QAAA,MAAM,SAAS,GAAG,8BAA8B,CAC9C,aAAa,CAAC,UAAU,EACxB,IAAI,CAAC,QAAQ,EACb,eAAe,CAChB;AAED,QAAA,sBAAsB,EAAE;AACxB,QAAA,OAAO,SAAS,CAAC;YACf,SAAS;YACT,kBAAkB,EAAE,IAAI,CAAC,QAAQ;YACjC,gBAAgB,EAAE,IAAI,CAAC,QAAQ;AAChC,SAAA,CAAC;;AAGJ;;;;;;;;;;;;;;;AAeG;AACH,IAAA,eAAe,CACb,UAAmB,EACnB,eAAA,GAEgD,EAAE,EAAA;QAElD,MAAM,OAAO,GAAG,cAAc,CAAC,EAAE,EAAE,eAAe,CAAC;AACnD,QAAA,sBAAsB,EAAE;QACxB,OAAO,sBAAsB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,aAAa,KACnF,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,OAAO,CAAC,CACpD;;AAGH;;AAEG;AACH,IAAA,SAAS,CAAC,QAAoB,EAAA;AAC5B,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC;;AAGvC;;;AAGG;AACH,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;AAGvB;;;AAGG;IACH,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,MAAM,IAAI,YAAY,CAAA,GAAA,oDAEpB,SAAS,IAAI,0CAA0C,CACxD;;AAEH,QAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC;AAC3D,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;AAExD,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC;QAC7E,IAAI,gBAAgB,EAAE;YACpB,gBAAgB,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAClD,gBAAgB,CAAC,KAAK,EAAE;;AAG1B,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI;;AAGxB;;AAEG;AACH,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU;;qGAzHb,WAAW,EAAAC,QAAA,CAAAC,QAAA,CAAA,CAAA,CAAA,EAAA;6DAAX,WAAW,EAAA,OAAA,EAAX,WAAW,CAAA,IAAA,EAAA,UAAA,EADC,UAAU,EAAA,CAAA;;6EACtB,WAAW,EAAA,CAAA;cADvB,UAAU;eAAC,EAAC,UAAU,EAAE,UAAU,EAAC;;;ACTpC,IAAI,iBAAiB,GAAoB,IAAI;AAE7C;;;AAGG;AACU,MAAA,wBAAwB,GAAG,IAAI,cAAc,CACxD,SAAS,GAAG,oBAAoB,GAAG,EAAE;AAGvC;;;;;AAKG;AACG,SAAU,cAAc,CAAC,QAAkB,EAAA;AAC/C,IAAA,IAAI,iBAAiB,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,wBAAwB,EAAE,KAAK,CAAC,EAAE;AAChF,QAAA,MAAM,IAAI,YAAY,CAAA,GAAA,4CAEpB,SAAS,IAAI,+EAA+E,CAC7F;;AAEH,IAAA,yBAAyB,EAAE;AAC3B,IAAA,0BAA0B,EAAE;IAC5B,iBAAiB,GAAG,QAAQ;IAC5B,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC;IAC1C,uBAAuB,CAAC,QAAQ,CAAC;AACjC,IAAA,OAAO,QAAQ;AACjB;AAEA;;;;;;;;;;AAUG;AACG,SAAU,qBAAqB,CACnC,qBAAkF,EAClF,IAAY,EACZ,YAA8B,EAAE,EAAA;AAEhC,IAAA,MAAM,IAAI,GAAG,CAAa,UAAA,EAAA,IAAI,EAAE;AAChC,IAAA,MAAM,MAAM,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC;AACvC,IAAA,OAAO,CAAC,cAAA,GAAmC,EAAE,KAAI;AAC/C,QAAA,IAAI,QAAQ,GAAG,WAAW,EAAE;AAC5B,QAAA,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,wBAAwB,EAAE,KAAK,CAAC,EAAE;AACvE,YAAA,MAAM,iBAAiB,GAAqB;AAC1C,gBAAA,GAAG,SAAS;AACZ,gBAAA,GAAG,cAAc;AACjB,gBAAA,EAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAC;aAClC;YACD,IAAI,qBAAqB,EAAE;gBACzB,qBAAqB,CAAC,iBAAiB,CAAC;;iBACnC;gBACL,cAAc,CAAC,sBAAsB,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;;;AAGnE,QAAA,OAAO,cAAc,CAAC,MAAM,CAAC;AAC/B,KAAC;AACH;AAEA;;;AAGG;AACH,SAAS,sBAAsB,CAAC,SAA8B,GAAA,EAAE,EAAE,IAAa,EAAA;IAC7E,OAAO,QAAQ,CAAC,MAAM,CAAC;QACrB,IAAI;AACJ,QAAA,SAAS,EAAE;AACT,YAAA,EAAC,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAC;YAC/C,EAAC,OAAO,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,GAAG,CAAC,CAAC,OAAO,iBAAiB,GAAG,IAAI,CAAC,CAAC,CAAC,EAAC;AAC5F,YAAA,GAAG,SAAS;AACb,SAAA;AACF,KAAA,CAAC;AACJ;AAEA;;;;AAIG;AACG,SAAU,cAAc,CAAC,aAAkB,EAAA;AAC/C,IAAA,MAAM,QAAQ,GAAG,WAAW,EAAE;IAE9B,IAAI,CAAC,QAAQ,EAAE;AACb,QAAA,MAAM,IAAI,YAAY,CAAA,GAAA,4CAAsC,SAAS,IAAI,qBAAqB,CAAC;;AAGjG,IAAA,IACE,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;QAC9C,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,EAC3C;AACA,QAAA,MAAM,IAAI,YAAY,CAEpB,GAAA,4CAAA,sFAAsF,CACvF;;AAGH,IAAA,OAAO,QAAQ;AACjB;AAEA;;;;AAIG;SACa,WAAW,GAAA;IACzB,OAAO,iBAAiB,EAAE,GAAG,CAAC,WAAW,CAAC,IAAI,IAAI;AACpD;AAEA;;;;;AAKG;SACa,eAAe,GAAA;AAC7B,IAAA,WAAW,EAAE,EAAE,OAAO,EAAE;AAC1B;AAEA;;;;AAIG;AACa,SAAA,6BAA6B,CAAC,SAAA,GAA8B,EAAE,EAAA;;;AAG5E,IAAA,IAAI,iBAAiB;AAAE,QAAA,OAAO,iBAAiB;AAE/C,IAAA,yBAAyB,EAAE;;AAE3B,IAAA,MAAM,QAAQ,GAAG,sBAAsB,CAAC,SAAS,CAAC;IAClD,iBAAiB,GAAG,QAAQ;AAC5B,IAAA,0BAA0B,EAAE;IAC5B,uBAAuB,CAAC,QAAQ,CAAC;AACjC,IAAA,OAAO,QAAQ;AACjB;AAEA;;;;;;;;;;;;AAYG;AACG,SAAU,0BAA0B,CAAC,aAAyB,EAAA;AAClE,IAAA,OAAO,wBAAwB,CAAC;AAC9B,QAAA;AACE,YAAA,OAAO,EAAE,oBAAoB;AAC7B,YAAA,QAAQ,EAAE,aAAa;AACvB,YAAA,KAAK,EAAE,IAAI;AACZ,SAAA;AACF,KAAA,CAAC;AACJ;AAEA,SAAS,uBAAuB,CAAC,QAAkB,EAAA;IACjD,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAAC;AACtD,IAAA,qBAAqB,CAAC,QAAQ,EAAE,MAAK;QACnC,KAAK,EAAE,OAAO,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;AAClC,KAAC,CAAC;AACJ;;ACvLM,SAAU,gCAAgC,CAAC,QAAgB,EAAA;IAC/D,OAAO,6BAA6B,CAAC,MAAK;AACxC,QAAA,MAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;AAC7C,QAAA,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;AACzC,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,4BAA4B,CAAC;AACtD,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAE7B,QAAA,SAAS,sBAAsB,GAAA;AAC7B,YAAA,MAAM,CAAC,iBAAiB,CAAC,MAAK;gBAC5B,UAAU,CAAC,MAAK;AACd,oBAAA,IAAI,cAAc,CAAC,SAAS,EAAE;wBAC5B;;oBAEF,IAAI,SAAS,CAAC,mBAAmB,IAAI,SAAS,CAAC,WAAW,EAAE;AAC1D,wBAAA,sBAAsB,EAAE;wBACxB;;AAGF,oBAAA,KAAK,MAAM,IAAI,IAAI,cAAc,CAAC,QAAQ,EAAE;AAC1C,wBAAA,IAAI;4BACF,sBAAsB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,mBAAmB;;wBAC3D,OAAO,CAAC,EAAE;AACV,4BAAA,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC;;;AAI/B,oBAAA,sBAAsB,EAAE;iBACzB,EAAE,QAAQ,CAAC;AACd,aAAC,CAAC;;AAEJ,QAAA,sBAAsB,EAAE;AAC1B,KAAC,CAAC;AACJ;;ACTM,SAAU,2BAA2B,CAAC,OAG3C,EAAA;AACC,IAAA,OAAO,wBAAwB,CAC7B,OAAO,SAAS,KAAK,WAAW,IAAI;AAClC,UAAE;AACE,YAAA;AACE,gBAAA,OAAO,EAAE,2BAA2B;gBACpC,QAAQ,EAAE,OAAO,CAAC,UAAU;AAC7B,aAAA;AACD,YAAA,OAAO,EAAE,QAAQ,KAAK,SAAS,GAAG,gCAAgC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE;AAC1F;UACD,EAAE,CACP;AACH;;AC7CA;;;;;;;;AAQG;SACa,SAAS,GAAA;IACvB,OAAO,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,CAAC,SAAS;AACxD;AAEA;;;;;;;;;;;;;AAaG;SACa,cAAc,GAAA;;;AAG5B,IAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,QAAAC,OAAM,CAAC,WAAW,CAAC,GAAG,KAAK;;AAE/B;;AC7BA;;;;;;AAMG;AACG,SAAU,gBAAgB,CAAC,EAAU,EAAA;AACzC,IAAA,MAAM,IAAI,GAAG,yBAAyB,CAAC,EAAE,CAAC;AAC1C,IAAA,IAAI,CAAC,IAAI;AAAE,QAAA,MAAM,aAAa,CAAC,EAAE,CAAC;AAClC,IAAA,OAAO,IAAIH,eAAiB,CAAC,IAAI,CAAC;AACpC;AAEA;;;;;AAKG;AACG,SAAU,eAAe,CAAI,EAAU,EAAA;AAC3C,IAAA,MAAM,IAAI,GAAG,yBAAyB,CAAC,EAAE,CAAC;AAC1C,IAAA,IAAI,CAAC,IAAI;AAAE,QAAA,MAAM,aAAa,CAAC,EAAE,CAAC;AAClC,IAAA,OAAO,IAAI;AACb;AAEA,SAAS,aAAa,CAAC,EAAU,EAAA;AAC/B,IAAA,OAAO,IAAI,KAAK,CAAC,qBAAqB,EAAE,CAAA,OAAA,CAAS,CAAC;AACpD;;ACzBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CG;MACmB,iBAAiB,CAAA;AA4DrC;;;AAGG;AACH,IAAA,OAAO,iBAAiB,GACtB,uBAAuB;;AAG3B;AACM,SAAU,uBAAuB,CAAC,KAA0B,EAAA;AAChE,IAAA,OAAO,aAAa,CAClB,eAAe,EAAG,EAClB,QAAQ,EAAE,EACV,CAAC,KAAK,GAAA,EAAA,wCAA+B,EAAA,mCACtC;AACH;AAEA;;;;;;;AAOG;AACH,SAAS,aAAa,CAAC,KAAY,EAAE,KAAY,EAAE,MAAe,EAAA;IAChE,IAAI,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE;;;AAGrC,QAAA,MAAM,aAAa,GAAG,wBAAwB,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACnE,QAAA,OAAO,IAAII,SAAO,CAAC,aAAa,EAAE,aAAa,CAAC;;SAC3C,IACL,KAAK,CAAC,IAAI;AACV,SAAC,CAA2C,4BAAA,EAAA,gCAAA,EAAA,uBAA2C,GAAA,gCAAC,EACxF;;;QAGA,MAAM,iBAAiB,GAAG,KAAK,CAAC,0BAA0B,CAAC,CAAC;AAC5D,QAAA,OAAO,IAAIA,SAAO,CAAC,iBAAiB,EAAE,KAAK,CAAC;;AAE9C,IAAA,OAAO,IAAK;AACd;;ACvJA;;;;;;AAMG;AACG,MAAgB,OAAQ,SAAQ,iBAAiB,CAAA;AAmBtD;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDG;AACG,MAAgB,eAAmB,SAAQ,OAAO,CAAA;AAUvD;;MC/EY,4BAA4B,CAAA;AACvC,IAAA,WAAA,GAAA;AACA,IAAA,QAAQ,CAAC,GAA8B,EAAA;AACrC,QAAA,OAAO,kBAAkB,CAAC,GAAG,CAAC;;AAGhC,IAAA,MAAM,CAAI,SAA8B,EAAA;AACtC,QAAA,OAAO,IAAI,qBAAqB,CAAI,SAAS,CAAC;;AAEjD;AAED,MAAM,eAAe,GAAG,CAAC,KAAa,EAAE,IAAS,KAAK,IAAI;AAE1D;;;AAGG;MACU,qBAAqB,CAAA;IAChB,MAAM,GAAW,CAAC;;AAElB,IAAA,UAAU;;IAElB,cAAc,GAA4B,IAAI;;IAE9C,gBAAgB,GAA4B,IAAI;IAChD,eAAe,GAAoC,IAAI;IACvD,OAAO,GAAoC,IAAI;IAC/C,OAAO,GAAoC,IAAI;IAC/C,cAAc,GAAoC,IAAI;IACtD,cAAc,GAAoC,IAAI;IACtD,UAAU,GAAoC,IAAI;IAClD,UAAU,GAAoC,IAAI;IAClD,aAAa,GAAoC,IAAI;IACrD,aAAa,GAAoC,IAAI;;IAErD,oBAAoB,GAAoC,IAAI;IAC5D,oBAAoB,GAAoC,IAAI;AAC5D,IAAA,UAAU;AAElB,IAAA,WAAA,CAAY,SAA8B,EAAA;AACxC,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,IAAI,eAAe;;AAGhD,IAAA,WAAW,CAAC,EAA8C,EAAA;AACxD,QAAA,IAAI,MAAuC;AAC3C,QAAA,KAAK,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE;YAClE,EAAE,CAAC,MAAM,CAAC;;;AAId,IAAA,gBAAgB,CACd,EAIS,EAAA;AAET,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO;AACzB,QAAA,IAAI,UAAU,GAAG,IAAI,CAAC,aAAa;QACnC,IAAI,eAAe,GAAG,CAAC;QACvB,IAAI,WAAW,GAAoB,IAAI;AACvC,QAAA,OAAO,MAAM,IAAI,UAAU,EAAE;;;YAG3B,MAAM,MAAM,GACV,CAAC,UAAU;AACX,iBAAC,MAAM;oBACL,MAAM,CAAC,YAAa,GAAG,gBAAgB,CAAC,UAAU,EAAE,eAAe,EAAE,WAAW,CAAC;AACjF,kBAAE;kBACA,UAAU;YAChB,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,EAAE,eAAe,EAAE,WAAW,CAAC;AAC/E,YAAA,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY;;AAGxC,YAAA,IAAI,MAAM,KAAK,UAAU,EAAE;AACzB,gBAAA,eAAe,EAAE;AACjB,gBAAA,UAAU,GAAG,UAAU,CAAC,YAAY;;iBAC/B;AACL,gBAAA,MAAM,GAAG,MAAO,CAAC,KAAK;AACtB,gBAAA,IAAI,MAAM,CAAC,aAAa,IAAI,IAAI,EAAE;AAChC,oBAAA,eAAe,EAAE;;qBACZ;;AAEL,oBAAA,IAAI,CAAC,WAAW;wBAAE,WAAW,GAAG,EAAE;AAClC,oBAAA,MAAM,sBAAsB,GAAG,gBAAgB,GAAG,eAAe;AACjE,oBAAA,MAAM,iBAAiB,GAAG,YAAa,GAAG,eAAe;AACzD,oBAAA,IAAI,sBAAsB,IAAI,iBAAiB,EAAE;AAC/C,wBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,sBAAsB,EAAE,CAAC,EAAE,EAAE;4BAC/C,MAAM,MAAM,GAAG,CAAC,GAAG,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC7E,4BAAA,MAAM,KAAK,GAAG,MAAM,GAAG,CAAC;4BACxB,IAAI,iBAAiB,IAAI,KAAK,IAAI,KAAK,GAAG,sBAAsB,EAAE;AAChE,gCAAA,WAAW,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC;;;AAG/B,wBAAA,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa;AAC1C,wBAAA,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,GAAG,sBAAsB;;;;AAK7E,YAAA,IAAI,gBAAgB,KAAK,YAAY,EAAE;AACrC,gBAAA,EAAE,CAAC,MAAM,EAAE,gBAAgB,EAAE,YAAY,CAAC;;;;AAKhD,IAAA,mBAAmB,CAAC,EAA8C,EAAA;AAChE,QAAA,IAAI,MAAuC;AAC3C,QAAA,KAAK,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,aAAa,EAAE;YAClF,EAAE,CAAC,MAAM,CAAC;;;AAId,IAAA,gBAAgB,CAAC,EAA8C,EAAA;AAC7D,QAAA,IAAI,MAAuC;AAC3C,QAAA,KAAK,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,UAAU,EAAE;YAC9E,EAAE,CAAC,MAAM,CAAC;;;AAId,IAAA,gBAAgB,CAAC,EAA8C,EAAA;AAC7D,QAAA,IAAI,MAAuC;AAC3C,QAAA,KAAK,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,UAAU,EAAE;YAC1E,EAAE,CAAC,MAAM,CAAC;;;AAId,IAAA,kBAAkB,CAAC,EAA8C,EAAA;AAC/D,QAAA,IAAI,MAAuC;AAC3C,QAAA,KAAK,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,YAAY,EAAE;YAC/E,EAAE,CAAC,MAAM,CAAC;;;AAId,IAAA,qBAAqB,CAAC,EAA8C,EAAA;AAClE,QAAA,IAAI,MAAuC;AAC3C,QAAA,KAAK,MAAM,GAAG,IAAI,CAAC,oBAAoB,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,mBAAmB,EAAE;YAC7F,EAAE,CAAC,MAAM,CAAC;;;AAId,IAAA,IAAI,CAAC,UAA4C,EAAA;QAC/C,IAAI,UAAU,IAAI,IAAI;YAAE,UAAU,GAAG,EAAE;AACvC,QAAA,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE;YACnC,MAAM,IAAI,YAAY,CAAA,GAAA,8CAEpB,SAAS;AACP,gBAAA,CAAA,sBAAA,EAAyB,SAAS,CAAC,UAAU,CAAC,CAAA,wCAAA,CAA0C,CAC3F;;AAGH,QAAA,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;AAC1B,YAAA,OAAO,IAAI;;aACN;AACL,YAAA,OAAO,IAAI;;;AAIf,IAAA,SAAS;AAET,IAAA,KAAK,CAAC,UAAyB,EAAA;QAC7B,IAAI,CAAC,MAAM,EAAE;AAEb,QAAA,IAAI,MAAM,GAAoC,IAAI,CAAC,OAAO;QAC1D,IAAI,UAAU,GAAY,KAAK;AAC/B,QAAA,IAAI,KAAa;AACjB,QAAA,IAAI,IAAO;AACX,QAAA,IAAI,WAAgB;AACpB,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;AAC5B,YAAA,IAAuB,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM;AAEnD,YAAA,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;AAChD,gBAAA,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC;gBACxB,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC;AAC1C,gBAAA,IAAI,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE;AAChE,oBAAA,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,CAAC;oBACzD,UAAU,GAAG,IAAI;;qBACZ;oBACL,IAAI,UAAU,EAAE;;AAEd,wBAAA,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,CAAC;;oBAEpE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;AAAE,wBAAA,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC;;AAG1E,gBAAA,MAAM,GAAG,MAAM,CAAC,KAAK;;;aAElB;YACL,KAAK,GAAG,CAAC;AACT,YAAA,eAAe,CAAC,UAAU,EAAE,CAAC,IAAO,KAAI;gBACtC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC;AAC1C,gBAAA,IAAI,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE;AAChE,oBAAA,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,CAAC;oBACzD,UAAU,GAAG,IAAI;;qBACZ;oBACL,IAAI,UAAU,EAAE;;AAEd,wBAAA,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,CAAC;;oBAEpE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;AAAE,wBAAA,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC;;AAE1E,gBAAA,MAAM,GAAG,MAAM,CAAC,KAAK;AACrB,gBAAA,KAAK,EAAE;AACT,aAAC,CAAC;AACD,YAAA,IAAuB,CAAC,MAAM,GAAG,KAAK;;AAGzC,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AACrB,QAAA,IAAuB,CAAC,UAAU,GAAG,UAAU;QAChD,OAAO,IAAI,CAAC,OAAO;;AAGrB;;AAEG;AACH,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,QACE,IAAI,CAAC,cAAc,KAAK,IAAI;YAC5B,IAAI,CAAC,UAAU,KAAK,IAAI;YACxB,IAAI,CAAC,aAAa,KAAK,IAAI;AAC3B,YAAA,IAAI,CAAC,oBAAoB,KAAK,IAAI;;AAItC;;;;;;;AAOG;IACH,MAAM,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,MAAuC;YAE3C,KAAK,MAAM,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE;AACzF,gBAAA,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,KAAK;;AAGrC,YAAA,KAAK,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,UAAU,EAAE;AAC9E,gBAAA,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,YAAY;;YAE5C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI;AAEhD,YAAA,KAAK,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,UAAU,EAAE;AAC1E,gBAAA,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,YAAY;;YAE5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI;YACxC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI;YAC9C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,GAAG,IAAI;;;;;AAOhE;;;;;;;;;AASG;AACH,IAAA,SAAS,CACP,MAAuC,EACvC,IAAO,EACP,WAAgB,EAChB,KAAa,EAAA;;AAGb,QAAA,IAAI,cAA+C;AAEnD,QAAA,IAAI,MAAM,KAAK,IAAI,EAAE;AACnB,YAAA,cAAc,GAAG,IAAI,CAAC,OAAO;;aACxB;AACL,YAAA,cAAc,GAAG,MAAM,CAAC,KAAK;;AAE7B,YAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;;;QAItB,MAAM,GAAG,IAAI,CAAC,gBAAgB,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC;AAC7F,QAAA,IAAI,MAAM,KAAK,IAAI,EAAE;;;YAGnB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;AAAE,gBAAA,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC;YAExE,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,cAAc,EAAE,KAAK,CAAC;;aAC7C;;YAEL,MAAM,GAAG,IAAI,CAAC,cAAc,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC;AAC1F,YAAA,IAAI,MAAM,KAAK,IAAI,EAAE;;;;gBAInB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;AAAE,oBAAA,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC;gBAExE,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,cAAc,EAAE,KAAK,CAAC;;iBACzC;;AAEL,gBAAA,MAAM,GAAG,IAAI,CAAC,SAAS,CACrB,IAAI,qBAAqB,CAAI,IAAI,EAAE,WAAW,CAAC,EAC/C,cAAc,EACd,KAAK,CACN;;;AAGL,QAAA,OAAO,MAAM;;AAGf;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BG;AACH,IAAA,kBAAkB,CAChB,MAAgC,EAChC,IAAO,EACP,WAAgB,EAChB,KAAa,EAAA;QAEb,IAAI,cAAc,GAChB,IAAI,CAAC,gBAAgB,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC;AACtF,QAAA,IAAI,cAAc,KAAK,IAAI,EAAE;AAC3B,YAAA,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,MAAM,CAAC,KAAM,EAAE,KAAK,CAAC;;AAC7D,aAAA,IAAI,MAAM,CAAC,YAAY,IAAI,KAAK,EAAE;AACvC,YAAA,MAAM,CAAC,YAAY,GAAG,KAAK;AAC3B,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC;;AAEjC,QAAA,OAAO,MAAM;;AAGf;;;;;;AAMG;AACH,IAAA,SAAS,CAAC,MAAuC,EAAA;;AAE/C,QAAA,OAAO,MAAM,KAAK,IAAI,EAAE;AACtB,YAAA,MAAM,UAAU,GAAoC,MAAM,CAAC,KAAK;YAChE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACzC,MAAM,GAAG,UAAU;;AAErB,QAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,EAAE;AAClC,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE;;AAG/B,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;AAChC,YAAA,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG,IAAI;;AAEvC,QAAA,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;AAC5B,YAAA,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,IAAI;;AAEnC,QAAA,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE;AACzB,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI;;AAE3B,QAAA,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;AAC/B,YAAA,IAAI,CAAC,aAAa,CAAC,YAAY,GAAG,IAAI;;AAExC,QAAA,IAAI,IAAI,CAAC,oBAAoB,KAAK,IAAI,EAAE;AACtC,YAAA,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,GAAG,IAAI;;;;AAKxD,IAAA,cAAc,CACZ,MAAgC,EAChC,UAA2C,EAC3C,KAAa,EAAA;AAEb,QAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,EAAE;AAClC,YAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC;;AAEtC,QAAA,MAAM,IAAI,GAAG,MAAM,CAAC,YAAY;AAChC,QAAA,MAAM,IAAI,GAAG,MAAM,CAAC,YAAY;AAEhC,QAAA,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI;;aACpB;AACL,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI;;AAE1B,QAAA,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI;;aACpB;AACL,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI;;QAG1B,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,EAAE,KAAK,CAAC;AAC5C,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC;AAC/B,QAAA,OAAO,MAAM;;;AAIf,IAAA,UAAU,CACR,MAAgC,EAChC,UAA2C,EAC3C,KAAa,EAAA;AAEb,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACpB,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,EAAE,KAAK,CAAC;AAC5C,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC;AAC/B,QAAA,OAAO,MAAM;;;AAIf,IAAA,SAAS,CACP,MAAgC,EAChC,UAA2C,EAC3C,KAAa,EAAA;QAEb,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,EAAE,KAAK,CAAC;AAE5C,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;;;YAGhC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,GAAG,MAAM;;aAC7C;;;;YAIL,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG,MAAM;;AAE/D,QAAA,OAAO,MAAM;;;AAIf,IAAA,YAAY,CACV,MAAgC,EAChC,UAA2C,EAC3C,KAAa,EAAA;;;;;AAOb,QAAA,MAAM,IAAI,GACR,UAAU,KAAK,IAAI,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,KAAK;;;;AAIvD,QAAA,MAAM,CAAC,KAAK,GAAG,IAAI;AACnB,QAAA,MAAM,CAAC,KAAK,GAAG,UAAU;AACzB,QAAA,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,YAAA,IAAI,CAAC,OAAO,GAAG,MAAM;;aAChB;AACL,YAAA,IAAI,CAAC,KAAK,GAAG,MAAM;;AAErB,QAAA,IAAI,UAAU,KAAK,IAAI,EAAE;AACvB,YAAA,IAAI,CAAC,OAAO,GAAG,MAAM;;aAChB;AACL,YAAA,UAAU,CAAC,KAAK,GAAG,MAAM;;AAG3B,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;AAChC,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,aAAa,EAAK;;AAE9C,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC;AAE/B,QAAA,MAAM,CAAC,YAAY,GAAG,KAAK;AAC3B,QAAA,OAAO,MAAM;;;AAIf,IAAA,OAAO,CAAC,MAAgC,EAAA;QACtC,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;;;AAIlD,IAAA,OAAO,CAAC,MAAgC,EAAA;AACtC,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;AAChC,YAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC;;AAGpC,QAAA,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK;AACzB,QAAA,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK;;;;AAMzB,QAAA,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI;;aACd;AACL,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI;;AAEnB,QAAA,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI;;aACd;AACL,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI;;AAGnB,QAAA,OAAO,MAAM;;;IAIf,WAAW,CAAC,MAAgC,EAAE,OAAe,EAAA;;;AAI3D,QAAA,IAAI,MAAM,CAAC,aAAa,KAAK,OAAO,EAAE;AACpC,YAAA,OAAO,MAAM;;AAGf,QAAA,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;;;YAG5B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG,MAAM;;aACrC;;;YAGL,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,MAAM;;AAGvD,QAAA,OAAO,MAAM;;AAGP,IAAA,cAAc,CAAC,MAAgC,EAAA;AACrD,QAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,EAAE;AAClC,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,aAAa,EAAK;;AAEhD,QAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC;AACjC,QAAA,MAAM,CAAC,YAAY,GAAG,IAAI;AAC1B,QAAA,MAAM,CAAC,YAAY,GAAG,IAAI;AAE1B,QAAA,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;;;YAG/B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,MAAM;AAChD,YAAA,MAAM,CAAC,YAAY,GAAG,IAAI;;aACrB;;;;AAIL,YAAA,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa;YACxC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,GAAG,MAAM;;AAE/D,QAAA,OAAO,MAAM;;;IAIf,kBAAkB,CAAC,MAAgC,EAAE,IAAO,EAAA;AAC1D,QAAA,MAAM,CAAC,IAAI,GAAG,IAAI;AAClB,QAAA,IAAI,IAAI,CAAC,oBAAoB,KAAK,IAAI,EAAE;YACtC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,GAAG,MAAM;;aACzD;YACL,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,GAAG,MAAM;;AAEpF,QAAA,OAAO,MAAM;;AAEhB;MAEY,qBAAqB,CAAA;AA0BvB,IAAA,IAAA;AACA,IAAA,SAAA;IA1BT,YAAY,GAAkB,IAAI;IAClC,aAAa,GAAkB,IAAI;;IAGnC,aAAa,GAAoC,IAAI;;IAErD,KAAK,GAAoC,IAAI;;IAE7C,KAAK,GAAoC,IAAI;;IAE7C,QAAQ,GAAoC,IAAI;;IAEhD,QAAQ,GAAoC,IAAI;;IAEhD,YAAY,GAAoC,IAAI;;IAEpD,YAAY,GAAoC,IAAI;;IAEpD,UAAU,GAAoC,IAAI;;IAElD,UAAU,GAAoC,IAAI;;IAElD,mBAAmB,GAAoC,IAAI;IAE3D,WACS,CAAA,IAAO,EACP,SAAc,EAAA;QADd,IAAI,CAAA,IAAA,GAAJ,IAAI;QACJ,IAAS,CAAA,SAAA,GAAT,SAAS;;AAEnB;AAED;AACA,MAAM,wBAAwB,CAAA;;IAE5B,KAAK,GAAoC,IAAI;;IAE7C,KAAK,GAAoC,IAAI;AAE7C;;;;AAIG;AACH,IAAA,GAAG,CAAC,MAAgC,EAAA;AAClC,QAAA,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;YACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,MAAM;AAChC,YAAA,MAAM,CAAC,QAAQ,GAAG,IAAI;AACtB,YAAA,MAAM,CAAC,QAAQ,GAAG,IAAI;;aACjB;;;;AAIL,YAAA,IAAI,CAAC,KAAM,CAAC,QAAQ,GAAG,MAAM;AAC7B,YAAA,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK;AAC5B,YAAA,MAAM,CAAC,QAAQ,GAAG,IAAI;AACtB,YAAA,IAAI,CAAC,KAAK,GAAG,MAAM;;;;;IAMvB,GAAG,CAAC,SAAc,EAAE,cAA6B,EAAA;AAC/C,QAAA,IAAI,MAAuC;AAC3C,QAAA,KAAK,MAAM,GAAG,IAAI,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAE;YACnE,IACE,CAAC,cAAc,KAAK,IAAI,IAAI,cAAc,IAAI,MAAM,CAAC,YAAa;gBAClE,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,EACtC;AACA,gBAAA,OAAO,MAAM;;;AAGjB,QAAA,OAAO,IAAI;;AAGb;;;;AAIG;AACH,IAAA,MAAM,CAAC,MAAgC,EAAA;;;;;;;;;AAUrC,QAAA,MAAM,IAAI,GAAoC,MAAM,CAAC,QAAQ;AAC7D,QAAA,MAAM,IAAI,GAAoC,MAAM,CAAC,QAAQ;AAC7D,QAAA,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI;;aACZ;AACL,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;;AAEtB,QAAA,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI;;aACZ;AACL,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;;AAEtB,QAAA,OAAO,IAAI,CAAC,KAAK,KAAK,IAAI;;AAE7B;AAED,MAAM,aAAa,CAAA;AACjB,IAAA,GAAG,GAAG,IAAI,GAAG,EAAoC;AAEjD,IAAA,GAAG,CAAC,MAAgC,EAAA;AAClC,QAAA,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS;QAE5B,IAAI,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;QAClC,IAAI,CAAC,UAAU,EAAE;AACf,YAAA,UAAU,GAAG,IAAI,wBAAwB,EAAK;YAC9C,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC;;AAE/B,QAAA,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC;;AAGxB;;;;;;AAMG;IACH,GAAG,CAAC,SAAc,EAAE,cAA6B,EAAA;QAC/C,MAAM,GAAG,GAAG,SAAS;QACrB,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;AACpC,QAAA,OAAO,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,cAAc,CAAC,GAAG,IAAI;;AAGtE;;;;AAIG;AACH,IAAA,MAAM,CAAC,MAAgC,EAAA;AACrC,QAAA,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS;QAC5B,MAAM,UAAU,GAAgC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAE;;AAElE,QAAA,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;AAC7B,YAAA,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC;;AAEtB,QAAA,OAAO,MAAM;;AAGf,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC;;IAG5B,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;;AAEnB;AAED,SAAS,gBAAgB,CACvB,IAAS,EACT,eAAuB,EACvB,WAA4B,EAAA;AAE5B,IAAA,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa;IACxC,IAAI,aAAa,KAAK,IAAI;AAAE,QAAA,OAAO,aAAa;IAChD,IAAI,UAAU,GAAG,CAAC;IAClB,IAAI,WAAW,IAAI,aAAa,GAAG,WAAW,CAAC,MAAM,EAAE;AACrD,QAAA,UAAU,GAAG,WAAW,CAAC,aAAa,CAAC;;AAEzC,IAAA,OAAO,aAAa,GAAG,eAAe,GAAG,UAAU;AACrD;;MChvBa,4BAA4B,CAAA;AACvC,IAAA,WAAA,GAAA;AACA,IAAA,QAAQ,CAAC,GAAQ,EAAA;QACf,OAAO,GAAG,YAAY,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC;;IAG9C,MAAM,GAAA;QACJ,OAAO,IAAI,qBAAqB,EAAQ;;AAE3C;MAEY,qBAAqB,CAAA;AACxB,IAAA,QAAQ,GAAG,IAAI,GAAG,EAAkC;IACpD,QAAQ,GAAuC,IAAI;;IAEnD,YAAY,GAAuC,IAAI;IACvD,gBAAgB,GAAuC,IAAI;IAC3D,YAAY,GAAuC,IAAI;IACvD,YAAY,GAAuC,IAAI;IACvD,cAAc,GAAuC,IAAI;IACzD,cAAc,GAAuC,IAAI;IACzD,aAAa,GAAuC,IAAI;IACxD,aAAa,GAAuC,IAAI;AAEhE,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,QACE,IAAI,CAAC,cAAc,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI;;AAI7F,IAAA,WAAW,CAAC,EAA2C,EAAA;AACrD,QAAA,IAAI,MAA0C;AAC9C,QAAA,KAAK,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE;YACnE,EAAE,CAAC,MAAM,CAAC;;;AAId,IAAA,mBAAmB,CAAC,EAA2C,EAAA;AAC7D,QAAA,IAAI,MAA0C;AAC9C,QAAA,KAAK,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,aAAa,EAAE;YACnF,EAAE,CAAC,MAAM,CAAC;;;AAId,IAAA,kBAAkB,CAAC,EAA2C,EAAA;AAC5D,QAAA,IAAI,MAA0C;AAC9C,QAAA,KAAK,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,YAAY,EAAE;YAC9E,EAAE,CAAC,MAAM,CAAC;;;AAId,IAAA,gBAAgB,CAAC,EAA2C,EAAA;AAC1D,QAAA,IAAI,MAA0C;AAC9C,QAAA,KAAK,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,UAAU,EAAE;YAC9E,EAAE,CAAC,MAAM,CAAC;;;AAId,IAAA,kBAAkB,CAAC,EAA2C,EAAA;AAC5D,QAAA,IAAI,MAA0C;AAC9C,QAAA,KAAK,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,YAAY,EAAE;YAC/E,EAAE,CAAC,MAAM,CAAC;;;AAId,IAAA,IAAI,CAAC,GAA+C,EAAA;QAClD,IAAI,CAAC,GAAG,EAAE;AACR,YAAA,GAAG,GAAG,IAAI,GAAG,EAAE;;AACV,aAAA,IAAI,EAAE,GAAG,YAAY,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE;AACnD,YAAA,MAAM,IAAI,YAAY,CAEpB,GAAA,8CAAA,SAAS,IAAI,CAAA,sBAAA,EAAyB,SAAS,CAAC,GAAG,CAAC,CAAsC,oCAAA,CAAA,CAC3F;;AAGH,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;;AAGtC,IAAA,SAAS;AAET;;;AAGG;AACH,IAAA,KAAK,CAAC,GAAuC,EAAA;QAC3C,IAAI,CAAC,MAAM,EAAE;AAEb,QAAA,IAAI,YAAY,GAAG,IAAI,CAAC,QAAQ;AAChC,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;QAExB,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,KAAU,EAAE,GAAQ,KAAI;YAC1C,IAAI,YAAY,IAAI,YAAY,CAAC,GAAG,KAAK,GAAG,EAAE;AAC5C,gBAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,KAAK,CAAC;AAC5C,gBAAA,IAAI,CAAC,YAAY,GAAG,YAAY;AAChC,gBAAA,YAAY,GAAG,YAAY,CAAC,KAAK;;iBAC5B;gBACL,MAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,EAAE,KAAK,CAAC;gBACxD,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,MAAM,CAAC;;AAEnE,SAAC,CAAC;;QAGF,IAAI,YAAY,EAAE;AAChB,YAAA,IAAI,YAAY,CAAC,KAAK,EAAE;AACtB,gBAAA,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;;AAGjC,YAAA,IAAI,CAAC,aAAa,GAAG,YAAY;AAEjC,YAAA,KACE,IAAI,MAAM,GAAuC,YAAY,EAC7D,MAAM,KAAK,IAAI,EACf,MAAM,GAAG,MAAM,CAAC,YAAY,EAC5B;AACA,gBAAA,IAAI,MAAM,KAAK,IAAI,CAAC,QAAQ,EAAE;AAC5B,oBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;;gBAEtB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC;AAChC,gBAAA,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC,KAAK;AAClC,gBAAA,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,YAAY;AAC1C,gBAAA,MAAM,CAAC,YAAY,GAAG,IAAI;AAC1B,gBAAA,MAAM,CAAC,KAAK,GAAG,IAAI;AACnB,gBAAA,MAAM,CAAC,KAAK,GAAG,IAAI;;;;QAKvB,IAAI,IAAI,CAAC,YAAY;AAAE,YAAA,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI;QAC5D,IAAI,IAAI,CAAC,cAAc;AAAE,YAAA,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG,IAAI;QAE9D,OAAO,IAAI,CAAC,OAAO;;AAGrB;;;;;;;AAOG;IACK,qBAAqB,CAC3B,MAA0C,EAC1C,MAAmC,EAAA;QAEnC,IAAI,MAAM,EAAE;AACV,YAAA,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK;AACzB,YAAA,MAAM,CAAC,KAAK,GAAG,MAAM;AACrB,YAAA,MAAM,CAAC,KAAK,GAAG,IAAI;AACnB,YAAA,MAAM,CAAC,KAAK,GAAG,MAAM;YACrB,IAAI,IAAI,EAAE;AACR,gBAAA,IAAI,CAAC,KAAK,GAAG,MAAM;;AAErB,YAAA,IAAI,MAAM,KAAK,IAAI,CAAC,QAAQ,EAAE;AAC5B,gBAAA,IAAI,CAAC,QAAQ,GAAG,MAAM;;AAGxB,YAAA,IAAI,CAAC,YAAY,GAAG,MAAM;AAC1B,YAAA,OAAO,MAAM;;AAGf,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,MAAM;AAChC,YAAA,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY;;aAC3B;AACL,YAAA,IAAI,CAAC,QAAQ,GAAG,MAAM;;AAGxB,QAAA,IAAI,CAAC,YAAY,GAAG,MAAM;AAC1B,QAAA,OAAO,IAAI;;IAGL,wBAAwB,CAAC,GAAM,EAAE,KAAQ,EAAA;QAC/C,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAE;AACtC,YAAA,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,CAAC;AACtC,YAAA,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK;AACzB,YAAA,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK;YACzB,IAAI,IAAI,EAAE;AACR,gBAAA,IAAI,CAAC,KAAK,GAAG,IAAI;;YAEnB,IAAI,IAAI,EAAE;AACR,gBAAA,IAAI,CAAC,KAAK,GAAG,IAAI;;AAEnB,YAAA,MAAM,CAAC,KAAK,GAAG,IAAI;AACnB,YAAA,MAAM,CAAC,KAAK,GAAG,IAAI;AAEnB,YAAA,OAAO,MAAM;;AAGf,QAAA,MAAM,MAAM,GAAG,IAAI,qBAAqB,CAAO,GAAG,CAAC;QACnD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC;AAC9B,QAAA,MAAM,CAAC,YAAY,GAAG,KAAK;AAC3B,QAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;AAC5B,QAAA,OAAO,MAAM;;;IAIf,MAAM,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,MAA0C;;AAE9C,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ;AACrC,YAAA,KAAK,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE;AAC3E,gBAAA,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,KAAK;;;;AAKrC,YAAA,KAAK,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,YAAY,EAAE;AAC9E,gBAAA,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,YAAY;;AAE5C,YAAA,KAAK,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,MAAM,IAAI,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,UAAU,EAAE;AAC7E,gBAAA,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,YAAY;;YAG5C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI;YAC5C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI;AAChD,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI;;;;IAKrB,kBAAkB,CAAC,MAAmC,EAAE,QAAa,EAAA;AAC3E,QAAA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,YAAY,CAAC,EAAE;AAC7C,YAAA,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,YAAY;AAC1C,YAAA,MAAM,CAAC,YAAY,GAAG,QAAQ;AAC9B,YAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;;;AAItB,IAAA,eAAe,CAAC,MAAmC,EAAA;AACzD,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;YAChC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,GAAG,MAAM;;aAC7C;AACL,YAAA,IAAI,CAAC,cAAe,CAAC,UAAU,GAAG,MAAM;AACxC,YAAA,IAAI,CAAC,cAAc,GAAG,MAAM;;;AAIxB,IAAA,aAAa,CAAC,MAAmC,EAAA;AACvD,QAAA,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE;YAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,GAAG,MAAM;;aACzC;AACL,YAAA,IAAI,CAAC,YAAa,CAAC,YAAY,GAAG,MAAM;AACxC,YAAA,IAAI,CAAC,YAAY,GAAG,MAAM;;;;IAKtB,QAAQ,CAAO,GAAiC,EAAE,EAA0B,EAAA;AAClF,QAAA,IAAI,GAAG,YAAY,GAAG,EAAE;AACtB,YAAA,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;;aACV;YACL,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;;AAGnD;AAED,MAAM,qBAAqB,CAAA;AAiBN,IAAA,GAAA;IAhBnB,aAAa,GAAa,IAAI;IAC9B,YAAY,GAAa,IAAI;;IAG7B,aAAa,GAAuC,IAAI;;IAExD,KAAK,GAAuC,IAAI;;IAEhD,KAAK,GAAuC,IAAI;;IAEhD,UAAU,GAAuC,IAAI;;IAErD,YAAY,GAAuC,IAAI;;IAEvD,YAAY,GAAuC,IAAI;AAEvD,IAAA,WAAA,CAAmB,GAAM,EAAA;QAAN,IAAG,CAAA,GAAA,GAAH,GAAG;;AACvB;;SChHe,6BAA6B,GAAA;IAC3C,OAAO,IAAI,eAAe,CAAC,CAAC,IAAI,4BAA4B,EAAE,CAAC,CAAC;AAClE;AAEA;;;;AAIG;MACU,eAAe,CAAA;AAQN,IAAA,SAAA;;IANpB,OAAO,KAAK,6CAA6CC,kBAAkB,CAAC;AAC1E,QAAA,KAAK,EAAE,eAAe;AACtB,QAAA,UAAU,EAAE,MAAM;AAClB,QAAA,OAAO,EAAE,6BAA6B;AACvC,KAAA,CAAC;AAEF,IAAA,WAAA,CAAoB,SAAkC,EAAA;QAAlC,IAAS,CAAA,SAAA,GAAT,SAAS;;AAE7B,IAAA,OAAO,MAAM,CAAC,SAAkC,EAAE,MAAwB,EAAA;AACxE,QAAA,IAAI,MAAM,IAAI,IAAI,EAAE;YAClB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE;AACvC,YAAA,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC;;AAGtC,QAAA,OAAO,IAAI,eAAe,CAAC,SAAS,CAAC;;AAGvC;;;;;;;;;;;;;;;;;;;AAmBG;IACH,OAAO,MAAM,CAAC,SAAkC,EAAA;QAC9C,OAAO;AACL,YAAA,OAAO,EAAE,eAAe;AACxB,YAAA,UAAU,EAAE,CAAC,MAA8B,KAAI;;;;gBAI7C,OAAO,eAAe,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,IAAI,6BAA6B,EAAE,CAAC;aACpF;;AAED,YAAA,IAAI,EAAE,CAAC,CAAC,eAAe,EAAE,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,CAAC,CAAC;SAC1D;;AAGH,IAAA,IAAI,CAAC,QAAa,EAAA;QAChB,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAChE,QAAA,IAAI,OAAO,IAAI,IAAI,EAAE;AACnB,YAAA,OAAO,OAAO;;aACT;YACL,MAAM,IAAI,YAAY,CAAA,GAAA,sDAEpB,SAAS;gBACP,CAA2C,wCAAA,EAAA,QAAQ,cAAc,uBAAuB,CACtF,QAAQ,CACT,CAAA,CAAA,CAAG,CACP;;;;AAKD,SAAU,uBAAuB,CAAC,IAAS,EAAA;AAC/C,IAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAI;AACpC;;SCtJgB,6BAA6B,GAAA;IAC3C,OAAO,IAAI,eAAe,CAAC,CAAC,IAAI,4BAA4B,EAAE,CAAC,CAAC;AAClE;AAEA;;;;AAIG;MACU,eAAe,CAAA;;IAE1B,OAAO,KAAK,6CAA6CA,kBAAkB,CAAC;AAC1E,QAAA,KAAK,EAAE,eAAe;AACtB,QAAA,UAAU,EAAE,MAAM;AAClB,QAAA,OAAO,EAAE,6BAA6B;AACvC,KAAA,CAAC;AAEe,IAAA,SAAS;AAE1B,IAAA,WAAA,CAAY,SAAkC,EAAA;AAC5C,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS;;AAG5B,IAAA,OAAO,MAAM,CAAI,SAAkC,EAAE,MAAwB,EAAA;QAC3E,IAAI,MAAM,EAAE;YACV,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE;AACvC,YAAA,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC;;AAEtC,QAAA,OAAO,IAAI,eAAe,CAAC,SAAS,CAAC;;AAGvC;;;;;;;;;;;;;;;;;;;AAmBG;IACH,OAAO,MAAM,CAAI,SAAkC,EAAA;QACjD,OAAO;AACL,YAAA,OAAO,EAAE,eAAe;AACxB,YAAA,UAAU,EAAE,CAAC,MAAuB,KAAI;;;;gBAItC,OAAO,eAAe,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,IAAI,6BAA6B,EAAE,CAAC;aACpF;;AAED,YAAA,IAAI,EAAE,CAAC,CAAC,eAAe,EAAE,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,CAAC,CAAC;SAC1D;;AAGH,IAAA,IAAI,CAAC,EAAO,EAAA;QACV,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAC1D,IAAI,OAAO,EAAE;AACX,YAAA,OAAO,OAAO;;QAEhB,MAAM,IAAI,YAAY,CAEpB,GAAA,sDAAA,SAAS,IAAI,CAA2C,wCAAA,EAAA,EAAE,CAAG,CAAA,CAAA,CAC9D;;;;AClJL;;AAEG;AACH,MAAM,UAAU,GAA4B,CAAC,IAAI,4BAA4B,EAAE,CAAC;AAEhF;;AAEG;AACH,MAAM,YAAY,GAA4B,CAAC,IAAI,4BAA4B,EAAE,CAAC;MAErE,sBAAsB,GAAG,IAAI,eAAe,CAAC,YAAY;MAEzD,sBAAsB,GAAG,IAAI,eAAe,CAAC,UAAU;;ACxCpE;;;;AAIG;AACI,MAAM,YAAY,GACvB,qBAAqB,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;;ACPxC;;;;;;AAMG;MAEU,iBAAiB,CAAA;;IAE5B,WAAY,CAAA,MAAsB;2GAFvB,iBAAiB,EAAAJ,QAAA,CAAAK,cAAA,CAAA,CAAA,CAAA,EAAA;yDAAjB,iBAAiB,EAAA,CAAA;;;6EAAjB,iBAAiB,EAAA,CAAA;cAD7B;;;ACKD;;;;;;;;;;AAUG;AAEG,SAAU,yBAAyB,CAAC,MAIzC,EAAA;AACC,IAAA,QAAQ,iDAAyC;AACjD,IAAA,IAAI;QACF,MAAM,EAAC,aAAa,EAAE,YAAY,EAAE,iBAAiB,EAAC,GAAG,MAAM;AAE/D,QAAA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,aAAa,KAAK,SAAS,EAAE;YAClF,6BAA6B,CAAC,aAAa,CAAC;;AAG9C,QAAA,MAAM,gBAAgB,GAAG,6BAA6B,CAAC,iBAAqC,CAAC;;;AAI7F,QAAA,MAAM,eAAe,GAAG;YACtB,kCAAkC,CAAC,EAAE,CAAC;AACtC,YAAA,EAAC,OAAO,EAAE,wBAAwB,EAAE,WAAW,EAAE,4BAA4B,EAAC;YAC9E,kCAAkC;AAClC,YAAA,IAAI,YAAY,IAAI,EAAE,CAAC;SACxB;AACD,QAAA,MAAM,OAAO,GAAG,IAAI,6BAA6B,CAAC;AAChD,YAAA,SAAS,EAAE,eAAe;AAC1B,YAAA,MAAM,EAAE,gBAAuC;AAC/C,YAAA,SAAS,EAAE,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,sBAAsB,GAAG,EAAE;;;AAGtF,YAAA,0BAA0B,EAAE,KAAK;AAClC,SAAA,CAAC;AAEF,QAAA,OAAO,SAAS,CAAC;YACf,UAAU,EAAE,OAAO,CAAC,QAAQ;YAC5B,gBAAgB;YAChB,aAAa;AACd,SAAA,CAAC;;IACF,OAAO,CAAC,EAAE;AACV,QAAA,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;;YAChB;AACR,QAAA,QAAQ,+CAAuC;;AAEnD;;AC7BA;;AAEG;AACH,MAAM,mBAAmB,GAAG,IAAI,OAAO,EAAkB;AAEzD;;AAEG;AACH,MAAM,2BAA2B,GAAG,EAAE;AAEtC;;AAEG;AACH,IAAI,eAAe,GAA6C,EAAE;AAElE;;AAEG;AACH,SAAS,uBAAuB,CAAC,QAAkB,EAAA;IACjD,OAAO,QAAQ,CAAC,GAAG,CAAC,uBAAuB,EAAE,4BAA4B,CAAC;AAC5E;AAEA;;;AAGG;SACa,eAAe,GAAA;AAC7B,IAAA,MAAM,SAAS,GAAe;AAC5B,QAAA;AACE,YAAA,OAAO,EAAE,uBAAuB;YAChC,UAAU,EAAE,MAAK;gBACf,IAAI,SAAS,GAAG,IAAI;gBACpB,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,CAAC,YAAY,EAAE;;;;AAIxD,oBAAA,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;oBAC5B,SAAS,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC;;gBAEtC,IAAI,SAAS,EAAE;oBACb,sBAAsB,CAAC,eAAe,CAAC;;AAEzC,gBAAA,OAAO,SAAS;aACjB;AACF,SAAA;KACF;IAED,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,CAAC,YAAY,EAAE;QACxD,SAAS,CAAC,IAAI,CACZ;AACE,YAAA,OAAO,EAAE,uBAAuB;YAChC,QAAQ,EAAE,MAAK;AACb,gBAAA,MAAM,MAAM,GAAG,MAAM,CAAC,cAAc,CAAC;AACrC,gBAAA,MAAM,EAAC,QAAQ,EAAC,GAAG,MAAM;;;;gBAIzB,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;AACpC,oBAAA,MAAM,WAAW,GAAG,MAAM,CAAC,0BAA0B,CAAC;AACtD,oBAAA,IAAI,uBAAuB,CAAC,QAAQ,CAAC,EAAE;AACrC,wBAAA,4BAA4B,EAAE;wBAC9B,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;AAClC,wBAAA,MAAM,YAAY,GAAG,UAAU,CAC7B,KAAK,EACL,CAAC,GAAU,EAAE,SAAiB,EAAE,UAAwB,KAAI;;;;AAI1D,4BAAA,IAAK,GAAY,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY;gCAAE;AAClD,4BAAA,mBAAmB,CAAC,GAAe,EAAE,SAAS,EAAE,UAAU,CAAC;AAC3D,4BAAA,iBAAiB,CAAC,GAAe,EAAE,WAAW,CAAC;AACjD,yBAAC,CACF;;;;AAID,wBAAA,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC;;;aAGnC;AACD,YAAA,KAAK,EAAE,IAAI;SACZ,EACD;AACE,YAAA,OAAO,EAAE,sBAAsB;YAC/B,UAAU,EAAE,MAAK;AACf,gBAAA,MAAM,MAAM,GAAG,MAAM,CAAC,cAAc,CAAC;AACrC,gBAAA,MAAM,EAAC,QAAQ,EAAC,GAAG,MAAM;AAEzB,gBAAA,OAAO,MAAK;;;;AAIV,oBAAA,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,IAAI,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;wBACzE;;AAGF,oBAAA,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC;oBAE/B,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;AAClC,oBAAA,MAAM,CAAC,SAAS,CAAC,MAAK;AACpB,wBAAA,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC;;wBAElC,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,CAAC,YAAY,EAAE;;;;4BAIxD,gCAAgC,CAAC,KAAK,CAAC;;AAE3C,qBAAC,CAAC;;;;AAKF,oBAAA,MAAM,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,MAAK;;;;;;;AAO5B,wBAAA,IAAI,MAAM,CAAC,SAAS,EAAE;4BACpB;;wBAGF,MAAM,oBAAoB,GAAG,QAAQ,CAAC,GAAG,CAAC,uBAAuB,CAAC;AAClE,wBAAA,eAAe,CAAC,oBAAoB,EAAE,QAAQ,CAAC;wBAC/C,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,0BAA0B,CAAC;wBAC5D,WAAW,CAAC,GAAG,CAAC,2BAA2B,CAAC,EAAE,OAAO,CAAC,eAAe,CAAC;AACtE,wBAAA,WAAW,CAAC,MAAM,CAAC,2BAA2B,CAAC;AAE/C,wBAAA,MAAM,aAAa,GAAG,oBAAoB,CAAC,QAAS;;;;AAIpD,wBAAA,IAAI,6BAA6B,CAAC,QAAQ,CAAC,EAAE;;;;;4BAK3C,MAAM,CAAC,SAAS,CAAC,MAAM,aAAa,CAAC,OAAO,EAAE,CAAC;;6BAC1C;4BACL,aAAa,CAAC,OAAO,EAAE;;AAE3B,qBAAC,CAAC;AACJ,iBAAC;aACF;AACD,YAAA,KAAK,EAAE,IAAI;AACZ,SAAA,CACF;;AAGH,IAAA,OAAO,SAAS;AAClB;AAEA,MAAM,eAAe,GAAG,CAAC,eAAqC,EAAE,QAAkB,KAAI;IACpF,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;;IAElC,MAAM,iBAAiB,GAAG,MAAM,CAAC,MAAO,CAAC,KAAK,CAAE;AAChD,IAAA,MAAM,aAAa,IAAI,eAAe,CAAC,QAAQ,GAAG,IAAI,aAAa,CACjE,IAAI,sBAAsB,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAChD,CAAC;AACF,IAAA,KAAK,MAAM,EAAE,IAAI,iBAAiB,CAAC,EAAE,EAAE;AACrC,QAAA,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;;AAE5B,IAAA,KAAK,MAAM,EAAE,IAAI,iBAAiB,CAAC,GAAG,EAAE;AACtC,QAAA,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;;AAE5B,IAAA,MAAM,UAAU,GAAG,4BAA4B,CAAC,KAAK,CAAC;AACtD,IAAA,aAAa,CAAC,qBAAqB,CAAC,UAAU,CAAC;IAC/C,gCAAgC,CAAC,KAAK,CAAC;IACvC,MAAM,UAAU,GAAG,IAAI,eAAe,CAAC,CAAC,KAAK,KAAI;QAC/C,+BAA+B,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC,aAAwB,CAAC;AAClF,KAAC,CAAC;AACF,IAAA,kBAAkB,CAAC,aAAa,EAAE,UAAU,CAAC;AAC/C,CAAC;AAED;;;AAGG;SACa,oBAAoB,CAClC,KAAY,EACZ,KAAY,EACZ,kBAAgE,EAAA;AAEhE,IAAA,MAAM,aAAa,GAAG,IAAI,GAAG,EAAqB;AAClD,IAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC;AAC/B,IAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO;AAC9B,IAAA,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE;AAC1B,QAAA,OAAO,aAAa;;IAEtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAI;AACrC,QAAA,MAAM,UAAU,GAAG,QAAQ,CAAC,CAAC,EAAE,CAAC;AAChC,QAAA,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,EAAE,CAAC;AACjC,QAAA,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;YAClC;;QAEF,MAAM,SAAS,GAAG,UAAU;AAC5B,QAAA,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE;YAChC;;AAEF,QAAA,IAAI,kBAAkB,CAAC,SAAS,CAAC,EAAE;AACjC,YAAA,kBAAkB,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC;;aACpC;AACL,YAAA,kBAAkB,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC;;QAE3C,MAAM,eAAe,GAAG,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,CAAmB;QACzE,CAAC,EAAE,CAAC;AACJ,QAAA,MAAM,gBAAgB,GAAG,QAAQ,CAAC,CAAC,EAAE,CAAC;;;;QAItC,MAAM,UAAU,GAAG,OAAO,gBAAgB,KAAK,SAAS,IAAI,gBAAgB,IAAI,CAAC;QACjF,IAAI,CAAC,UAAU,EAAE;YACf;;QAEF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE;YACvC,aAAa,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC,SAAS,CAAC,CAAC;;aAC1C;YACL,aAAa,CAAC,GAAG,CAAC,eAAe,CAAE,CAAC,IAAI,CAAC,SAAS,CAAC;;;AAGvD,IAAA,OAAO,aAAa;AACtB;SAEgB,+BAA+B,CAC7C,QAAkB,EAClB,KAAY,EACZ,aAA6B,EAAA;AAE7B,IAAA,MAAM,SAAS,GACb,CAAC,aAAa,IAAI,aAAa,CAAC,YAAY,CAAC,4BAA4B,CAAC,KAAK,EAAE;AACnF,IAAA,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;QAC1B,8BAA8B,CAAC,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAc,CAAC;;SACrE,IAAI,KAAK,CAAC,UAAU,KAAK,UAAU,CAAC,MAAM,EAAE;AACjD,QAAA,eAAe,CAAC,KAAK,EAAE,aAAa,CAAC;;AAEzC;AAEA,SAAS,8BAA8B,CACrC,SAAiB,EACjB,QAAkB,EAClB,KAAY,EACZ,aAAsB,EAAA;IAEtB,eAAe,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,aAAa,EAAC,CAAC;AAC5C,IAAA,6BAA6B,CAAC,QAAQ,EAAE,SAAS,EAAE,uBAAuB,CAAC;AAC7E;AAEA,SAAS,uBAAuB,CAAC,cAAwB,EAAA;;AAEvD,IAAA,MAAM,KAAK,GAAG,CAAC,GAAG,eAAe,CAAC;AAClC,IAAA,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAS,cAAc,CAAC;;IAEhD,eAAe,GAAG,EAAE;IACpB,KAAK,IAAI,EAAC,KAAK,EAAE,aAAa,EAAC,IAAI,KAAK,EAAE;QACxC,MAAM,SAAS,GAAG,aAAa,CAAC,YAAY,CAAC,4BAA4B,CAAE;AAC3E,QAAA,IAAI,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;AAC3B,YAAA,eAAe,CAAC,KAAK,EAAE,aAAa,CAAC;;aAChC;;YAEL,eAAe,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,aAAa,EAAC,CAAC;;;AAGlD;;AC1NA;;;;;AAKG;AACH,MAAM,wBAAwB,CAAA;IACpB,KAAK,GAAqB,EAAE;AAC5B,IAAA,cAAc,GAAG,IAAI,GAAG,EAAkB;AAElD,IAAA,GAAG,CAAC,cAA8B,EAAA;QAChC,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;QACnD,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;AAC1C,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM;AAC/B,YAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC;YAC/B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC;AAC5C,YAAA,OAAO,KAAK;;QAEd,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,CAAE;;IAG/C,MAAM,GAAA;QACJ,OAAO,IAAI,CAAC,KAAK;;AAEpB;AAED;;;AAGG;AACH,IAAI,UAAU,GAAG,CAAC;AAElB;;;;;;;AAOG;AACH,SAAS,QAAQ,CAAC,KAAY,EAAA;AAC5B,IAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;AAChB,QAAA,KAAK,CAAC,KAAK,GAAG,IAAI,UAAU,EAAE,EAAE;;IAElC,OAAO,KAAK,CAAC,KAAK;AACpB;AAmBA;;;AAGG;AACH,SAAS,gBAAgB,CAAC,KAAY,EAAE,KAAY,EAAE,KAAmB,EAAA;IACvE,MAAM,SAAS,GAAc,EAAE;IAC/B,kBAAkB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC;IAClD,OAAO,SAAS,CAAC,MAAM;AACzB;AAEA;;AAEG;AACH,SAAS,4BAA4B,CAAC,UAAsB,EAAA;IAC1D,MAAM,SAAS,GAAc,EAAE;AAC/B,IAAA,8BAA8B,CAAC,UAAU,EAAE,SAAS,CAAC;IACrD,OAAO,SAAS,CAAC,MAAM;AACzB;AAEA;;;AAGG;AACH,SAAS,kCAAkC,CACzC,KAAY,EACZ,OAAyB,EACzB,QAAkB,EAAA;AAElB,IAAA,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC;;;IAG/B,IAAI,WAAW,IAAI,CAAE,WAA2B,CAAC,YAAY,CAAC,wBAAwB,CAAC,EAAE;QACvF,OAAO,+BAA+B,CAAC,WAA0B,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC;;AAE1F,IAAA,OAAO,IAAI;AACb;AAEA;;;;;AAKG;AACH,SAAS,8BAA8B,CACrC,UAAsB,EACtB,OAAyB,EACzB,QAAkB,EAAA;IAElB,MAAM,cAAc,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAmB;;IAGtE,MAAM,sBAAsB,GAAG,kCAAkC,CAC/D,cAAc,EACd,OACQ,CACT;AAED,IAAA,IAAI,sBAAsB,KAAK,IAAI,EAAE;;;;;QAKnC;;IAGF,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc,CAAC,IAAI,CAAE,CAAgB;;AAGrE,IAAA,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC;AACpC,IAAA,MAAM,iBAAiB,GAAG,+BAA+B,CAAC,WAAW,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,CAAC;AAEhG,IAAA,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAc;;;;;;;;;;;AAYtD,IAAA,MAAM,UAAU,GAAG,CAAA,EAAG,sBAAsB,CAAI,CAAA,EAAA,iBAAiB,EAAE;IACnE,QAAQ,CAAC,YAAY,CAAC,WAAW,EAAE,aAAa,EAAE,UAAU,CAAC;AAC/D;AAEA;;;;;;;AAOG;AACa,SAAA,oBAAoB,CAAC,MAAsB,EAAE,GAAa,EAAA;AACxE,IAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ;AAChC,IAAA,MAAM,yBAAyB,GAAG,sBAAsB,CAAC,QAAQ,CAAC;AAClE,IAAA,MAAM,gCAAgC,GAAG,6BAA6B,CAAC,QAAQ,CAAC;AAChF,IAAA,MAAM,wBAAwB,GAAG,IAAI,wBAAwB,EAAE;AAC/D,IAAA,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAA+B;AACjE,IAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM;IAC9B,MAAM,kBAAkB,GAAG,QAAQ,CAAC,GAAG,CAAC,uBAAuB,EAAE,4BAA4B,CAAC;AAC9F,IAAA,MAAM,kBAAkB,GAAG;QACzB,OAAO,EAAE,IAAI,GAAG,EAAU;QAC1B,OAAO,EAAE,IAAI,GAAG,EAAU;KAC3B;AACD,IAAA,MAAM,WAAW,GAAG,IAAI,GAAG,EAAgC;IAC3D,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;AACzC,IAAA,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AAC9B,QAAA,MAAM,KAAK,GAAG,oBAAoB,CAAC,OAAO,CAAC;;;AAI3C,QAAA,IAAI,KAAK,KAAK,IAAI,EAAE;AAClB,YAAA,MAAM,OAAO,GAAqB;gBAChC,wBAAwB;gBACxB,kBAAkB;AAClB,gBAAA,sBAAsB,EAAE,yBAAyB;AACjD,gBAAA,6BAA6B,EAAE,gCAAgC;gBAC/D,YAAY,EAAE,IAAI,GAAG,EAAE;gBACvB,kBAAkB;gBAClB,kBAAkB;gBAClB,KAAK;gBACL,WAAW;aACZ;AACD,YAAA,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;AACvB,gBAAA,8BAA8B,CAAC,KAAK,EAAE,OAAiB,CAAC;;iBACnD;AACL,gBAAA,kCAAkC,CAAC,KAAK,EAAE,OAAiB,CAAC;;AAE9D,YAAA,8BAA8B,CAAC,kBAAkB,EAAE,GAAG,CAAC;;;;;;;;AAS3D,IAAA,MAAM,eAAe,GAAG,wBAAwB,CAAC,MAAM,EAAE;IACzD,MAAM,aAAa,GAAG,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC;AACjD,IAAA,aAAa,CAAC,GAAG,CAAC,YAAY,EAAE,eAAe,CAAC;AAEhD,IAAA,IAAI,WAAW,CAAC,IAAI,GAAG,CAAC,EAAE;QACxB,MAAM,MAAM,GAA0C,EAAE;AACxD,QAAA,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE;AAC9C,YAAA,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI;;AAEnB,QAAA,aAAa,CAAC,GAAG,CAAC,oBAAoB,EAAE,MAAM,CAAC;;AAGjD,IAAA,OAAO,kBAAkB;AAC3B;AAEA;;;;;;;;;;AAUG;AACH,SAAS,mBAAmB,CAC1B,UAAsB,EACtB,KAAY,EACZ,KAAY,EACZ,kBAAiC,EACjC,OAAyB,EAAA;IAEzB,MAAM,KAAK,GAA8B,EAAE;IAC3C,IAAI,gBAAgB,GAAG,EAAE;AAEzB,IAAA,KAAK,IAAI,CAAC,GAAG,uBAAuB,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAChE,QAAA,IAAI,UAAU,GAAG,UAAU,CAAC,CAAC,CAAU;AAEvC,QAAA,IAAI,QAAgB;AACpB,QAAA,IAAI,YAAoB;AACxB,QAAA,IAAI,cAAmD;AAEvD,QAAA,IAAI,UAAU,CAAC,UAAU,CAAC,EAAE;;;AAG1B,YAAA,UAAU,GAAG,UAAU,CAAC,aAAa,CAAC;;;;AAKtC,YAAA,IAAI,YAAY,CAAC,UAAU,CAAC,EAAE;;;;;;AAM5B,gBAAA,YAAY,GAAG,4BAA4B,CAAC,UAAU,CAAC,GAAG,CAAC;gBAE3D,8BAA8B,CAAC,UAAU,EAAE,OAAwB,CAAC;gBAEpE,MAAM,cAAc,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAmB;AAEtE,gBAAA,cAAc,GAAG;oBACf,CAAC,WAAW,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,KAAM;oBAC3C,CAAC,cAAc,GAAG,YAAY;iBAC/B;;;QAIL,IAAI,CAAC,cAAc,EAAE;AACnB,YAAA,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC;AAEpC,YAAA,IAAI,UAAU,CAAC,IAAI,KAAA,CAAA,4BAA0B;AAC3C,gBAAA,QAAQ,GAAG,UAAU,CAAC,KAAM;;;gBAI5B,YAAY,GAAG,CAAC;;iBACX;AACL,gBAAA,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC;gBAC/B,YAAY,GAAG,gBAAgB,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC;;AAGhF,YAAA,cAAc,GAAG;gBACf,CAAC,WAAW,GAAG,QAAQ;gBACvB,CAAC,cAAc,GAAG,YAAY;aAC/B;YAED,IAAI,mBAAmB,GAAG,KAAK;;YAG/B,IAAI,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE;gBACrC,MAAM,QAAQ,GAAG,qBAAqB,CAAC,KAAK,EAAE,KAAK,CAAC;gBACpD,MAAM,QAAQ,GAAG,qBAAqB,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC;gBAE3D,IAAI,OAAO,CAAC,6BAA6B,IAAI,QAAQ,CAAC,eAAe,KAAK,IAAI,EAAE;oBAC9E,MAAM,YAAY,GAAG,CAAI,CAAA,EAAA,OAAO,CAAC,WAAW,CAAC,IAAI,CAAA,CAAE;AAEnD,oBAAA,IAAI,QAAQ,CAAC,eAAe,CAAC,GAAG,CAAA,CAAA,+BAAyB,EAAE;wBACzD,mBAAmB,GAAG,IAAI;;oBAG5B,IAAI,SAAS,GAAU,EAAE;AACzB,oBAAA,8BAA8B,CAAC,UAAU,EAAE,SAAS,CAAC;;AAGrD,oBAAA,MAAM,cAAc,GAAyB;AAC3C,wBAAA,CAAC,cAAc,GAAG,SAAS,CAAC,MAAM;AAClC,wBAAA,CAAC,iBAAiB,GAAG,QAAQ,CAACC,mBAAyB,CAAC;qBACzD;oBAED,MAAM,kBAAkB,GAAG,wBAAwB,CAAC,QAAQ,CAAC,eAAe,CAAC;AAC7E,oBAAA,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;AACjC,wBAAA,cAAc,CAAC,sBAAsB,CAAC,GAAG,kBAAkB;;AAG7D,oBAAA,IAAI,kBAAkB,KAAK,IAAI,EAAE;;AAE/B,wBAAA,cAAc,CAAC,qBAAqB,CAAC,GAAG,kBAAkB;;oBAG5D,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,EAAE,cAAc,CAAC;AAErD,oBAAA,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,CAAC;AACpC,oBAAA,IAAI,IAAI,KAAK,SAAS,EAAE;wBACtB,IAAK,IAAa,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY,EAAE;AACjD,4BAAA,oCAAoC,CAAC,IAAgB,EAAE,YAAY,CAAC;;;yBAEjE;wBACL,SAAS,IAAI,kBAAkB,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,CAAC;wBACxD,SAAS;AACP,4BAAA,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC;AAE9E,wBAAA,oCAAoC,CAAC,IAAgB,EAAE,YAAY,CAAC;;oBAGtE,IAAI,CAAC,mBAAmB,EAAE;;wBAExB,uCAAuC,CAAC,QAAQ,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,CAAC;;;oBAIrF,kBAAkB,GAAG,YAAY;;;;AAKjC,oBAAA,cAAc,CAAC,cAAc,CAAC,GAAG,YAAY;;;;;gBAK/C,cAAc,CAAC,iBAAiB,CAAC,GAAG,QAAQ,CAACA,mBAAyB,CAAC;;YAGzE,IAAI,CAAC,mBAAmB,EAAE;AACxB,gBAAA,MAAM,CAAC,MAAM,CACX,cAAc,EACd,cAAc,CAAC,UAAU,CAAC,CAAC,CAAU,EAAE,kBAAkB,EAAE,OAAO,CAAC,CACpE;;;;;;QAOL,MAAM,mBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;QAC1D,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,mBAAmB,KAAK,gBAAgB,EAAE;YAChE,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAC5C,YAAA,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC;AAC9B,YAAA,YAAY,CAAC,UAAU,CAAC,EAAE;;aACrB;;YAEL,gBAAgB,GAAG,mBAAmB;AACtC,YAAA,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC;;;AAG9B,IAAA,OAAO,KAAK;AACd;AAEA,SAAS,wBAAwB,CAC/B,UAAgE,EAAA;AAEhE,IAAA,MAAM,6BAA6B,GAAG,IAAI,GAAG,CAAoB;;;;;AAKhE,KAAA,CAAC;IACF,IAAI,QAAQ,GAAqD,EAAE;IACnE,KAAK,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,UAAU,EAAE;AACzC,QAAA,IAAI,6BAA6B,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AAC9C,YAAA,IAAI,OAAO,KAAK,IAAI,EAAE;AACpB,gBAAA,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;;iBACjB;AACL,gBAAA,QAAQ,CAAC,IAAI,CAAC,EAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAC,CAAC;;;;AAIpD,IAAA,OAAO,QAAQ;AACjB;AAEA;;;;AAIG;AACH,SAAS,wBAAwB,CAC/B,GAAmB,EACnB,KAAY,EACZ,KAAY,EACZ,mBAAuC,EAAA;AAEvC,IAAA,MAAM,aAAa,GAAG,KAAK,CAAC,KAAK,GAAG,aAAa;AACjD,IAAA,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE;;AAEjB,IAAA,GAAG,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC,KAAK,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,mBAAmB,CAAC;AAClF;AAEA;;;;AAIG;AACH,SAAS,2BAA2B,CAAC,GAAmB,EAAE,oBAAoC,EAAA;AAC5F,IAAA,MAAM,aAAa,GACjB,OAAO,oBAAoB,KAAK;AAC9B,UAAE;AACF,UAAE,oBAAoB,CAAC,KAAK,GAAG,aAAa;AAChD,IAAA,GAAG,CAAC,kBAAkB,CAAC,KAAK,EAAE;IAC9B,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;QACpD,GAAG,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;;AAE/C;AAEA;;;;;;;;AAQG;AACH,SAAS,cAAc,CACrB,KAAY,EACZ,kBAAoC,GAAA,IAAI,EACxC,OAAyB,EAAA;IAEzB,MAAM,GAAG,GAAmB,EAAE;AAC9B,IAAA,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IAC1B,MAAM,YAAY,GAAG,wBAAwB,CAAC,KAAK,EAAE,OAAO,CAAC;AAC7D,IAAA,MAAM,0BAA0B,GAAG,OAAO,CAAC;UACvC,oBAAoB,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,kBAAkB;UAC7D,IAAI;;AAER,IAAA,KAAK,IAAI,CAAC,GAAG,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,iBAAiB,EAAE,CAAC,EAAE,EAAE;QAC5D,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3B,QAAA,MAAM,aAAa,GAAG,CAAC,GAAG,aAAa;;;QAIvC,MAAM,QAAQ,GAAG,qBAAqB,CAAC,KAAK,EAAE,CAAC,EAAE,OAAO,CAAC;QACzD,IAAI,QAAQ,EAAE;AACZ,YAAA,GAAG,CAAC,SAAS,CAAC,KAAK,EAAE;YACrB,GAAG,CAAC,SAAS,CAAC,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,SAAS;AAElD,YAAA,KAAK,MAAM,iBAAiB,IAAI,QAAQ,CAAC,iBAAiB,EAAE;AAC1D,gBAAA,2BAA2B,CAAC,GAAG,EAAE,iBAAiB,CAAC;;AAGrD,YAAA,KAAK,MAAM,iBAAiB,IAAI,QAAQ,CAAC,aAAa,EAAE;gBACtD,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAU;AACpE,gBAAA,SAAS,IAAI,WAAW,CAAC,KAAK,CAAC;gBAC/B,wBAAwB,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,CAAC;;YAG3D;;;;;;;;AASF,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;YACxB;;;;;AAMF,QAAA,IAAI,gBAAgB,CAAC,KAAK,CAAC,EAAE;YAC3B;;;AAIF,QAAA,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE;AACzC,YAAA,GAAG,CAAC,SAAS,CAAC,KAAK,EAAE;AACrB,YAAA,GAAG,CAAC,SAAS,CAAC,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAM,CAAC;;;;;;;;AASxD,QAAA,IAAI,kBAAkB,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,sBAAsB,CAAC,KAAK,CAAC,EAAE;AACrE,YAAA,2BAA2B,CAAC,GAAG,EAAE,KAAK,CAAC;YACvC;;QAGF,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;AACnC,YAAA,KAAK,MAAM,mBAAmB,IAAI,KAAK,CAAC,UAAU,EAAE;;AAElD,gBAAA,IAAI,CAAC,mBAAmB;oBAAE;gBAE1B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE;;;;;AAKvC,oBAAA,IACE,CAAC,iBAAiB,CAAC,mBAAmB,CAAC;AACvC,wBAAA,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,EAC5C;AACA,wBAAA,IAAI,kBAAkB,CAAC,mBAAmB,EAAE,KAAK,CAAC,EAAE;;;;;AAKlD,4BAAA,2BAA2B,CAAC,GAAG,EAAE,mBAAmB,CAAC;;6BAChD;4BACL,wBAAwB,CAAC,GAAG,EAAE,mBAAmB,EAAE,KAAK,EAAE,YAAY,CAAC;;;;qBAGtE;;;;;;;;;oBAUL,MAAM,+BAA+B,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;;;;QAKlE,6BAA6B,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,CAAC;QAC9D,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;;YAE1B,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAE,CAAC;;;;AAKjC,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;;AAE3B,gBAAA,MAAM,UAAU,GAAG,WAAW,CAAC,QAAiB,CAAa;gBAC7D,IAAI,CAAE,UAA0B,CAAC,YAAY,CAAC,wBAAwB,CAAC,EAAE;oBACvE,+BAA+B,CAC7B,UAAU,EACV,QAAiB,EACjB,kBAAkB,EAClB,OAAO,CACR;;;AAIL,YAAA,GAAG,CAAC,UAAU,CAAC,KAAK,EAAE;YACtB,GAAG,CAAC,UAAU,CAAC,CAAC,aAAa,CAAC,GAAG,mBAAmB,CAClD,KAAK,CAAC,CAAC,CAAC,EACR,KAAK,EACL,KAAK,EACL,kBAAkB,EAClB,OAAO,CACR;;AACI,aAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;;;;AAI9D,YAAA,MAAM,UAAU,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAE,CAAC;YAC/C,IAAI,CAAE,UAA0B,CAAC,YAAY,CAAC,wBAAwB,CAAC,EAAE;AACvE,gBAAA,+BAA+B,CAC7B,UAAsB,EACtB,KAAK,CAAC,CAAC,CAAC,EACR,kBAAkB,EAClB,OAAO,CACR;;;aAEE;;AAEL,YAAA,IAAI,KAAK,CAAC,IAAI,GAAA,CAAA,mCAA+B;;;;AAI3C,gBAAA,GAAG,CAAC,kBAAkB,CAAC,KAAK,EAAE;AAC9B,gBAAA,GAAG,CAAC,kBAAkB,CAAC,CAAC,aAAa,CAAC,GAAG,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC;;iBAC/E,IAAI,KAAK,CAAC,IAAI,IAAI,EAA+C,8BAAA,GAAA,gCAAC,EAAE;;;;AAIzE,gBAAA,IAAI,SAAS,GAAG,KAAK,CAAC,IAAI;;gBAE1B,OACE,SAAS,KAAK,IAAI;AAClB,oBAAA,SAAS,CAAC,IAAI,IAAI,EAA+C,8BAAA,GAAA,gCAAC,EAClE;AACA,oBAAA,SAAS,GAAG,SAAS,CAAC,IAAI;;gBAE5B,IAAI,SAAS,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,EAAE;;oBAEnD,wBAAwB,CAAC,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,YAAY,CAAC;;;AAE1D,iBAAA,IAAI,KAAK,CAAC,IAAI,GAAA,CAAA,uBAAmB;gBACtC,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACnC,gBAAA,kCAAkC,CAAC,OAAO,EAAE,KAAK,CAAC;;;;;AAMtD,QAAA,IAAI,0BAA0B,IAAI,KAAK,CAAC,IAAI,GAAA,CAAA,0BAAsB;YAChE,MAAM,aAAa,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAY;AACtD,YAAA,IAAI,0BAA0B,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;AACjD,gBAAA,qBAAqB,CACnB,aAAa,EACb,0BAA0B,CAAC,GAAG,CAAC,aAAa,CAAE,EAC9C,kBAAkB,CACnB;;;;AAIP,IAAA,OAAO,GAAG;AACZ;AAEA;;;;;;;;;;;AAWG;AACH,SAAS,6BAA6B,CACpC,GAAmB,EACnB,KAAY,EACZ,KAAqB,EACrB,mBAAuC,EAAA;AAEvC,IAAA,IAAI,iBAAiB,CAAC,KAAK,CAAC,EAAE;;;QAG5B;;;IAIF,IACE,KAAK,CAAC,cAAc;AACpB,QAAA,KAAK,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI;AACnC,QAAA,CAAC,sBAAsB,CAAC,KAAK,CAAC,cAAc,CAAC,EAC7C;QACA,wBAAwB,CAAC,GAAG,EAAE,KAAK,CAAC,cAAc,EAAE,KAAK,EAAE,mBAAmB,CAAC;;;;;;AAOjF,IAAA,IACE,KAAK,CAAC,IAAI,KAAK,IAAI;QACnB,KAAK,CAAC,MAAM,KAAK,IAAI;AACrB,QAAA,kBAAkB,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC;AACvC,QAAA,CAAC,kBAAkB,CAAC,KAAK,EAAE,KAAK,CAAC,EACjC;QACA,wBAAwB,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,CAAC;;AAEpE;AAEA;;;AAGG;AACH,SAAS,mCAAmC,CAAC,KAAY,EAAA;AACvD,IAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC;IAC/B,OAAO,QAAQ,EAAE;AACf,UAAE,eAAe,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,aAAa,KAAKC,mBAAiB,CAAC;UAC3E,KAAK;AACX;AAEA;;;;;;;;;;;;AAYG;AACH,SAAS,+BAA+B,CACtC,OAAiB,EACjB,KAAY,EACZ,kBAAiC,EACjC,OAAyB,EAAA;AAEzB,IAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;IAChC,IACE,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,6BAA6B,EAAE;AACnD,QAAA,mCAAmC,CAAC,KAAK,CAAC,EAC1C;;;;;;QAMA,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,wBAAwB,EAAE,EAAE,CAAC;AAC5D,QAAA,OAAO,IAAI;;SACN;QACL,MAAM,GAAG,GAAG,cAAc,CAAC,KAAK,EAAE,kBAAkB,EAAE,OAAO,CAAC;QAC9D,MAAM,KAAK,GAAG,OAAO,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,CAAC;AACvD,QAAA,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC;AAC/D,QAAA,OAAO,KAAK;;AAEhB;AAEA;;;;;AAKG;AACH,SAAS,oCAAoC,CAAC,OAAiB,EAAE,YAAoB,EAAA;AACnF,IAAA,OAAO,CAAC,WAAW,GAAG,CAAO,IAAA,EAAA,YAAY,EAAE;AAC7C;AAEA;;;;;;;;AAQG;AACH,SAAS,8BAA8B,CACrC,kBAA4C,EAC5C,GAAa,EAAA;IAEb,KAAK,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,kBAAkB,EAAE;QACnD,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;;AAE7C;AAEA;;;AAGG;AACH,SAAS,sBAAsB,CAAC,KAAY,EAAA;IAC1C,IAAI,YAAY,GAAG,KAAK;AACxB,IAAA,OAAO,YAAY,IAAI,IAAI,EAAE;;;AAG3B,QAAA,IAAI,eAAe,CAAC,YAAY,CAAC,EAAE;AACjC,YAAA,OAAO,IAAI;;AAEb,QAAA,YAAY,GAAG,YAAY,CAAC,MAAe;;AAE7C,IAAA,OAAO,KAAK;AACd;AAEA;;;;;;AAMG;AACH,SAAS,uCAAuC,CAC9C,QAA4B,EAC5B,SAAgB,EAChB,kBAA0B,EAC1B,OAAyB,EAAA;IAEzB,MAAM,UAAU,GAAG,gCAAgC,CAAC,QAAQ,CAAC,eAAe,CAAC;AAC7E,IAAA,KAAK,IAAI,EAAE,IAAI,UAAU,EAAE;QACzB,OAAO,CAAC,kBAAkB,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;;AAG5C,IAAA,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AACzB,QAAA,MAAM,YAAY,GAAI,SAA2B,CAAC,MAAM,CACtD,CAAC,EAAE,KAAK,EAAE,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY,CAC1C;AACD,QAAA,KAAK,IAAI,KAAK,IAAI,YAAY,EAAE;AAC9B,YAAA,qBAAqB,CAAC,KAAK,EAAE,UAAU,EAAE,kBAAkB,CAAC;;;AAGlE;;ACl1BA;;;AAGG;AACH,IAAI,yBAAyB,GAAG,KAAK;AAErC;;;;;;;AAOG;AACH,IAAI,oCAAoC,GAAG,KAAK;AAEhD;;;AAGG;AACH,IAAI,2CAA2C,GAAG,KAAK;AAEvD;;;AAGG;AACH,MAAM,6BAA6B,GAAG,MAAM;AAE5C;;;;;;;;;;AAUG;AACH,SAAS,6BAA6B,GAAA;IACpC,IAAI,CAAC,yBAAyB,EAAE;QAC9B,yBAAyB,GAAG,IAAI;AAChC,QAAA,+BAA+B,EAAE;AACjC,QAAA,mCAAmC,EAAE;AACrC,QAAA,gCAAgC,EAAE;AAClC,QAAA,4CAA4C,EAAE;AAC9C,QAAA,uCAAuC,EAAE;AACzC,QAAA,oCAAoC,EAAE;AACtC,QAAA,oCAAoC,EAAE;AACtC,QAAA,mCAAmC,EAAE;;AAEzC;AAEA;;;;AAIG;AACH,SAAS,iCAAiC,GAAA;IACxC,IAAI,CAAC,oCAAoC,EAAE;QACzC,oCAAoC,GAAG,IAAI;AAC3C,QAAA,gCAAgC,EAAE;AAClC,QAAA,sCAAsC,EAAE;AACxC,QAAA,gCAAgC,EAAE;;AAEtC;AAEA;;;;AAIG;AACH,SAAS,wCAAwC,GAAA;IAC/C,IAAI,CAAC,2CAA2C,EAAE;QAChD,2CAA2C,GAAG,IAAI;AAClD,QAAA,gCAAgC,EAAE;;AAEtC;AAEA;;AAEG;AACH,SAAS,mBAAmB,CAAC,QAAkB,EAAA;IAC7C,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC;AACrC,IAAA,MAAM,OAAO,GACX,CAAA,iBAAA,EAAoB,SAAU,CAAC,kBAAkB,CAAgB,cAAA,CAAA;QACjE,CAAO,IAAA,EAAA,SAAU,CAAC,aAAa,CAAY,UAAA,CAAA;QAC3C,CAAG,EAAA,SAAU,CAAC,0BAA0B,CAA8B,4BAAA,CAAA;SACrE,6BAA6B,CAAC,QAAQ;AACrC,cAAE,CAAA,EAAG,SAAU,CAAC,mCAAmC,CAAgE,8DAAA;cACjH,EAAE,CAAC;AACP,QAAA,CAAA,kDAAA,CAAoD;;AAEtD,IAAA,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;AACtB;AAEA;;AAEG;AACH,SAAS,qBAAqB,CAAC,MAAsB,EAAA;AACnD,IAAA,MAAM,iBAAiB,GAAG,MAAM,CAAC,UAAU,EAAE;AAC7C,IAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;QACjD,MAAM,WAAW,GAAG,6BAA6B;QACjD,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC;QAC5C,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;;;;AAK1C,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,iBAAiB,CAAC,MAAK;AAC9C,YAAA,OAAO,UAAU,CAAC,MAAM,0BAA0B,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE,WAAW,CAAC;AACxF,SAAC,CAAC;QAEF,iBAAiB,CAAC,OAAO,CAAC,MAAM,YAAY,CAAC,SAAS,CAAC,CAAC;;AAG1D,IAAA,OAAO,iBAAiB;AAC1B;AAEA;;;;AAIG;AACI,MAAM,uBAAuB,GAAG;AAEvC;;AAEG;AACH,SAAS,yBAAyB,GAAA;AAChC,IAAA,MAAM,GAAG,GAAG,WAAW,EAAE;IACzB,QACE,CAAC,OAAO,YAAY,KAAK,WAAW,IAAI,CAAC,YAAY;QACrD,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC;AAElD;AAEA;;;;;;;;;AASG;SACa,gBAAgB,GAAA;AAC9B,IAAA,MAAM,SAAS,GAAe;AAC5B,QAAA;AACE,YAAA,OAAO,EAAE,8BAA8B;YACvC,UAAU,EAAE,MAAK;gBACf,IAAI,SAAS,GAAG,IAAI;gBACpB,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,CAAC,YAAY,EAAE;;;AAGxD,oBAAA,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;oBAC7D,SAAS,GAAG,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC;;gBAEtD,IAAI,SAAS,EAAE;oBACb,sBAAsB,CAAC,aAAa,CAAC;;AAEvC,gBAAA,OAAO,SAAS;aACjB;AACF,SAAA;AACD,QAAA;AACE,YAAA,OAAO,EAAE,uBAAuB;YAChC,QAAQ,EAAE,MAAK;;;gBAGb,gCAAgC,CAAC,KAAK,CAAC;AAEvC,gBAAA,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,YAAY,EAAE;;;;;oBAKvD;;AAGF,gBAAA,IAAI,MAAM,CAAC,8BAA8B,CAAC,EAAE;AAC1C,oBAAA,0BAA0B,CAAC,WAAW,EAAE,CAAC;AACzC,oBAAA,6BAA6B,EAAE;;qBAC1B,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,IAAI,CAAC,yBAAyB,EAAE,EAAE;AACxF,oBAAA,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;AAC/B,oBAAA,MAAM,OAAO,GAAG,kBAAkB,CAAA,CAAA,GAAA,uDAEhC,kEAAkE;wBAChE,yDAAyD;wBACzD,kCAAkC;wBAClC,qEAAqE;AACrE,wBAAA,mEAAmE,CACtE;AACD,oBAAA,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;;aAExB;AACD,YAAA,KAAK,EAAE,IAAI;AACZ,SAAA;KACF;IAED,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,CAAC,YAAY,EAAE;QACxD,SAAS,CAAC,IAAI,CACZ;AACE,YAAA,OAAO,EAAE,qBAAqB;YAC9B,UAAU,EAAE,MAAK;;;;;AAKf,gBAAA,OAAO,MAAM,CAAC,8BAA8B,CAAC;aAC9C;SACF,EACD;AACE,YAAA,OAAO,EAAE,sBAAsB;YAC/B,UAAU,EAAE,MAAK;AACf,gBAAA,IAAI,MAAM,CAAC,8BAA8B,CAAC,EAAE;AAC1C,oBAAA,MAAM,MAAM,GAAG,MAAM,CAAC,cAAc,CAAC;AAErC,oBAAA,OAAO,MAAK;;;;;;;;AAQV,wBAAA,qBAAqB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAK;;;;;;;AAOtC,4BAAA,IAAI,MAAM,CAAC,SAAS,EAAE;gCACpB;;4BAGF,sBAAsB,CAAC,MAAM,CAAC;AAC9B,4BAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,gCAAA,6BAA6B,CAAC,MAAM,CAAC,QAAQ,CAAC;AAC9C,gCAAA,mBAAmB,CAAC,MAAM,CAAC,QAAQ,CAAC;;AAExC,yBAAC,CAAC;AACJ,qBAAC;;AAEH,gBAAA,OAAO,MAAK,GAAG,CAAC;aACjB;AACD,YAAA,KAAK,EAAE,IAAI;AACZ,SAAA,CACF;;AAGH,IAAA,OAAO,wBAAwB,CAAC,SAAS,CAAC;AAC5C;AAEA;;;AAGG;SACa,eAAe,GAAA;IAC7B,OAAO;AACL,QAAA;AACE,YAAA,OAAO,EAAE,yBAAyB;AAClC,YAAA,UAAU,EAAE,MAAM,MAAM,CAAC,8BAA8B,CAAC;AACzD,SAAA;AACD,QAAA;AACE,YAAA,OAAO,EAAE,uBAAuB;YAChC,QAAQ,EAAE,MAAK;AACb,gBAAA,IAAI,MAAM,CAAC,8BAA8B,CAAC,EAAE;AAC1C,oBAAA,iCAAiC,EAAE;oBACnC,gCAAgC,CAAC,IAAI,CAAC;oBACtC,sBAAsB,CAAC,iBAAiB,CAAC;;aAE5C;AACD,YAAA,KAAK,EAAE,IAAI;AACZ,SAAA;KACF;AACH;AAEA;;;;AAIG;SACa,wBAAwB,GAAA;AACtC,IAAA,MAAM,SAAS,GAAe;AAC5B,QAAA,eAAe,EAAE;AACjB,QAAA;AACE,YAAA,OAAO,EAAE,gCAAgC;AACzC,YAAA,QAAQ,EAAE,IAAI;AACf,SAAA;AACD,QAAA;AACE,YAAA,OAAO,EAAE,yBAAyB;AAClC,YAAA,QAAQ,EAAE,uBAAuB;AAClC,SAAA;AACD,QAAA;AACE,YAAA,OAAO,EAAE,uBAAuB;YAChC,QAAQ,EAAE,MAAK;AACb,gBAAA,wCAAwC,EAAE;gBAC1C,sBAAsB,CAAC,wBAAwB,CAAC;aACjD;AACD,YAAA,KAAK,EAAE,IAAI;AACZ,SAAA;KACF;IAED,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,CAAC,YAAY,EAAE;QACxD,SAAS,CAAC,IAAI,CAAC;AACb,YAAA,OAAO,EAAE,sBAAsB;YAC/B,UAAU,EAAE,MAAK;AACf,gBAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AACjC,gBAAA,MAAM,GAAG,GAAG,WAAW,EAAE;AAEzB,gBAAA,OAAO,MAAK;AACV,oBAAA,MAAM,cAAc,GAAG,gBAAgB,CAAC,QAAQ,CAAC;oBACjD,MAAM,iBAAiB,GAAG,6BAA6B,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC;AACtE,oBAAA,sBAAsB,CAAC,QAAQ,EAAE,cAAc,EAAE,iBAAiB,CAAC;AACnE,oBAAA,8BAA8B,CAAC,GAAG,EAAE,QAAQ,CAAC;AAC/C,iBAAC;aACF;AACD,YAAA,KAAK,EAAE,IAAI;AACZ,SAAA,CAAC;;AAGJ,IAAA,OAAO,SAAS;AAClB;AAEA;;;AAGG;AACH,SAAS,0BAA0B,CAAC,IAAY,EAAE,OAAgB,EAAA;IAChE,MAAM,OAAO,GACX,CAAoF,kFAAA,CAAA;AACpF,QAAA,CAAA,qBAAA,EAAwB,IAAI,CAAyE,uEAAA,CAAA;AACrG,QAAA,CAAA,0CAAA,CAA4C;IAE9C,OAAO,CAAC,IAAI,CAAC,kBAAkB,wDAA6C,OAAO,CAAC,CAAC;AACvF;;ACpYA;;;;;;;;;;;AAWG;AACG,SAAU,gBAAgB,CAAC,KAAc,EAAA;AAC7C,IAAA,OAAO,OAAO,KAAK,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,OAAO;AAChF;AAEA;;;;;;;;;;;;AAYG;SACa,eAAe,CAAC,KAAc,EAAE,aAAa,GAAG,GAAG,EAAA;;;;AAIjE,IAAA,MAAM,aAAa,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,KAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC/E,IAAA,OAAO,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,aAAa;AACtD;;ACnCO,MAAM,uBAAuB,GAAG;AAEvC,IAAI,iBAAiB,GAAG,KAAK;AAE7B;;;AAGG;AACG,SAAU,cAAc,CAAI,KAAa,EAAA;IAC7C,IAAI,CAAC,iBAAiB,EAAE;QACtB;;IAGF,MAAM,EAAC,UAAU,EAAC,GAAG,MAAM,CAAC,KAAK,CAAC;;AAElC,IAAA,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC;;AAE9B;AAEA;;;AAGG;AACG,SAAU,aAAa,CAAC,KAAa,EAAA;IACzC,IAAI,CAAC,iBAAiB,EAAE;QACtB;;AAGF,IAAA,MAAM,EAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAC,GAAG,MAAM,CAAC,KAAK,CAAC;;AAEvD,IAAA,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;IAC1B,WAAW,CAAC,OAAO,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC;AACpD,IAAA,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC;AAClC,IAAA,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC;;AAElC;AAEM,SAAU,MAAM,CAAC,KAAa,EAAA;AAClC,IAAA,MAAM,SAAS,GAAG,CAAA,EAAG,uBAAuB,CAAI,CAAA,EAAA,KAAK,EAAE;IACvD,OAAO;QACL,SAAS;QACT,UAAU,EAAE,CAAS,MAAA,EAAA,SAAS,CAAE,CAAA;QAChC,QAAQ,EAAE,CAAO,IAAA,EAAA,SAAS,CAAE,CAAA;KAC7B;AACH;AAEA,IAAI,aAAa,GAAG,KAAK;AACzB;;;;AAIG;SACa,eAAe,GAAA;AAC7B,IAAA,IACE,CAAC,aAAa;AACd,SAAC,OAAO,WAAW,KAAK,WAAW,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EACjF;QACA,aAAa,GAAG,IAAI;AACpB,QAAA,OAAO,CAAC,IAAI,CAAC,mDAAmD,CAAC;QACjE;;IAGF,iBAAiB,GAAG,IAAI;AAC1B;SACgB,gBAAgB,GAAA;IAC9B,iBAAiB,GAAG,KAAK;AAC3B;;AC1EA;;;;;;AAMG;AAQH;;;;;AAKG;AACG,SAAU,uBAAuB,CAAC,IAAU,EAAA;IAChD,IAAI,WAAW,GAAG,IAAmB;IAErC,OAAO,WAAW,EAAE;AAClB,QAAA,MAAM,KAAK,GAAG,gBAAgB,CAAC,WAAW,CAAC;AAE3C,QAAA,IAAI,KAAK,KAAK,IAAI,EAAE;AAClB,YAAA,KAAK,IAAI,CAAC,GAAG,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,gBAAA,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC;gBAExB,IAAI,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,OAAO,CAAC,IAAI,CAAC,KAAK,WAAW,EAAE;oBAClF;;AAGF,gBAAA,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;gBAC1B,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;AAChC,gBAAA,IAAI,eAAe,CAAC,KAAK,CAAC,EAAE;AAC1B,oBAAA,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,KAAK,CAAC,eAAe,CAAqB;AACxF,oBAAA,MAAM,IAAI,GAAG,GAAG,CAAC,SAAS,EAAE,SAAS,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI;;;oBAItD,IAAI,IAAI,EAAE;AACR,wBAAA,OAAO,IAAI;;yBACN;wBACL;;;;;AAMR,QAAA,WAAW,GAAG,WAAW,CAAC,UAAU;;AAGtC,IAAA,OAAO,IAAI;AACb;;ACvDA;;;;;;AAMG;AAEH;;;AAGG;AACa,SAAA,WAAW,CAAI,KAAc;;ACa7C;;;;AAIG;AACG,SAAU,oBAAoB,CAAC,IAA8B,EAAA;IACjE,MAAM,QAAQ,GAAG,iBAAiB,CAAC;AACjC,QAAA,KAAK,EAAqC,CAAA;AAC1C,QAAA,IAAI,EAAE,WAAW;QACjB,IAAI,EAAE,IAAI,CAAC,IAAI;AAChB,KAAA,CAAC;AACF,IAAA,OAAO,QAAQ,CAAC,2BAA2B,CACzC,cAAc,EACd,CAAS,MAAA,EAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA,QAAA,CAAU,EACjC,IAAI,CACL;AACH;AAEA;;;;AAIG;AACG,SAAU,wBAAwB,CAAC,IAKxC,EAAA;IACC,gBAAgB,CACd,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,cAAc,IAAI,IAAI,EAC3B,IAAI,CAAC,cAAc,IAAI,IAAI,CAC5B;AACH;AAEA;;;;AAIG;AACG,SAAU,6BAA6B,CAAC,IAQ7C,EAAA;AACC,IAAA,qBAAqB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC,GAAG,KAAsB,KAAI;QACvF,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,KAAK,CAAC;AAC3C,QAAA,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC;AACxF,KAAC,CAAC;AACJ;AAEA;;;;AAIG;AACG,SAAU,oBAAoB,CAAC,IAA8B,EAAA;IACjE,MAAM,QAAQ,GAAG,iBAAiB,CAAC;AACjC,QAAA,KAAK,EAAqC,CAAA;AAC1C,QAAA,IAAI,EAAE,WAAW;QACjB,IAAI,EAAE,IAAI,CAAC,IAAI;AAChB,KAAA,CAAC;AACF,IAAA,OAAO,QAAQ,CAAC,2BAA2B,CACzC,cAAc,EACd,CAAS,MAAA,EAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA,QAAA,CAAU,EACjC,IAAI,CACL;AACH;AAEA;;;;AAIG;AACG,SAAU,kBAAkB,CAAC,IAA4B,EAAA;IAC7D,MAAM,QAAQ,GAAG,iBAAiB,CAAC;AACjC,QAAA,KAAK,EAAqC,CAAA;AAC1C,QAAA,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;QACjC,IAAI,EAAE,IAAI,CAAC,IAAI;AAChB,KAAA,CAAC;AACF,IAAA,OAAO,QAAQ,CAAC,yBAAyB,CACvC,cAAc,EACd,CAAS,MAAA,EAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA,QAAA,CAAU,EACjC,IAAI,CACL;AACH;AAEA,SAAS,cAAc,CAAC,MAAqB,EAAA;IAC3C,QAAQ,MAAM;QACZ,KAAK,aAAa,CAAC,SAAS;AAC1B,YAAA,OAAO,WAAW;QACpB,KAAK,aAAa,CAAC,SAAS;AAC1B,YAAA,OAAO,WAAW;QACpB,KAAK,aAAa,CAAC,UAAU;AAC3B,YAAA,OAAO,YAAY;QACrB,KAAK,aAAa,CAAC,IAAI;AACrB,YAAA,OAAO,MAAM;QACf,KAAK,aAAa,CAAC,QAAQ;AACzB,YAAA,OAAO,UAAU;;AAEvB;AAEA;;;;AAIG;AACG,SAAU,qBAAqB,CAAC,IAA+B,EAAA;IACnE,MAAM,QAAQ,GAAG,iBAAiB,CAAC;AACjC,QAAA,KAAK,EAAqC,CAAA;AAC1C,QAAA,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE,IAAI,CAAC,IAAI;AAChB,KAAA,CAAC;AACF,IAAA,OAAO,QAAQ,CAAC,4BAA4B,CAC1C,cAAc,EACd,CAAS,MAAA,EAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA,SAAA,CAAW,EAClC,IAAI,CACL;AACH;AAOA;;;;AAIG;AACG,SAAU,mBAAmB,CAAC,IAA6B,EAAA;IAC/D,MAAM,QAAQ,GAAG,iBAAiB,CAAC;AACjC,QAAA,KAAK,EAAqC,CAAA;AAC1C,QAAA,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,IAAI,CAAC,IAAI;AAChB,KAAA,CAAC;AACF,IAAA,OAAO,QAAQ,CAAC,0BAA0B,CACxC,cAAc,EACd,CAAS,MAAA,EAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA,QAAA,CAAU,EACjC,IAAI,CACL;AACH;AAEA;;;;AAIG;AACG,SAAU,mBAAmB,CAAC,IAA6B,EAAA;IAC/D,MAAM,QAAQ,GAAG,iBAAiB,CAAC;AACjC,QAAA,KAAK,EAAqC,CAAA;AAC1C,QAAA,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,IAAI,CAAC,IAAI;AAChB,KAAA,CAAC;AACF,IAAA,OAAO,QAAQ,CAAC,0BAA0B,CACxC,cAAc,EACd,CAAS,MAAA,EAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA,QAAA,CAAU,EACjC,IAAI,CACL;AACH;AAEA;;;;AAIG;AACG,SAAU,eAAe,CAAC,IAAyB,EAAA;IACvD,MAAM,QAAQ,GAAG,iBAAiB,CAAC;AACjC,QAAA,KAAK,EAAqC,CAAA;AAC1C,QAAA,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,IAAI,CAAC,IAAI;AAChB,KAAA,CAAC;AACF,IAAA,OAAO,QAAQ,CAAC,sBAAsB,CAAC,cAAc,EAAE,CAAS,MAAA,EAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA,SAAA,CAAW,EAAE,IAAI,CAAC;AAClG;;AC9LA;;;;;;;;AAQG;AACG,SAAU,gBAAgB,CAAC,QAAkB,EAAA;AACjD,IAAA,MAAM,GAAG,GAAG,WAAW,EAAE;IACzB,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;IAElC,MAAM,aAAa,GAAG,wBAAwB,CAAC,GAAG,EAAE,KAAK,CAAC;;IAG1D,MAAM,eAAe,GAA4B,EAAE;AACnD,IAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;AACxD,QAAA,IAAI,CAAC,mCAAmC,CAAC,GAAG,CAAC,EAAE;AAC7C,YAAA,eAAe,CAAC,GAAG,CAAC,GAAG,KAAK;;;AAIhC,IAAA,OAAO,eAAe;AACxB;;ACIA,MAAM,OAAO,mBAAmB,MAAM,CAAC,SAAS,CAAC;AACjD,MAAM,iBAAiB,mBAAmB,IAAI,GAAG,EAAc;AAiC/D,MAAM,8BAA8B,mBAAmB,CAAC,OAAO;AAC7D,IAAA,GAAG,WAAW;AACd,IAAA,oBAAoB,EAAE,IAAI;AAC1B,IAAA,yBAAyB,EAAE,IAAI;AAC/B,IAAA,KAAK,EAAE,OAAO;AACd,IAAA,OAAO,EAAE,IAAI;;IAEb,mBAAmB,GAAA;QACjB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE;;;;AAIhC,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,EAAE;gBAC5E;;;;AAKF,YAAA,IAAI,CAAC,QAAQ,CAAC,kBAAkB,GAAG,IAAI;;;;AAKzC,QAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,uCAA+B;KAC9D;AACD,IAAA,OAAO,CAAmC,aAAuB,EAAA;QAC/D,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK;AAEpC,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACf,OAAO,IAAI,CAAC,MAAM;;AAGpB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AAClB,QAAA,IAAI,IAAI,CAAC,KAAK,KAAK,OAAO,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE;;;YAGnE,OAAO,IAAI,CAAC,MAAM;;;AAIpB,QAAA,IAAI;YACF,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,OAAO,IAAI,iBAAiB,EAAE;AACzD,gBAAA,SAAS,EAAE;;;gBAEL;;AAER,YAAA,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE;;;;QAKvB,MAAM,IAAI,GAAc,EAAE;AAC1B,QAAA,IAAI,aAAa,KAAK,SAAS,EAAE;AAC/B,YAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;;AAE1B,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC;;AAGjC,QAAA,MAAM,YAAY,GAAG,yBAAyB,CAAC,IAAI,CAAC;AACpD,QAAA,IAAI,QAAQ;AACZ,QAAA,IAAI;YACF,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAW,CAAC;;gBACvC;AACR,YAAA,wBAAwB,CAAC,IAAI,EAAE,YAAY,CAAC;;AAG9C,QAAA,IAAI,IAAI,CAAC,KAAK,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;AAC/D,YAAA,IAAI,CAAC,KAAK,GAAG,QAAQ;YACrB,IAAI,CAAC,OAAO,EAAE;;QAGhB,OAAO,IAAI,CAAC,MAAM;KACnB;CACF,CAAC,GAAG;AAEL;;AAEG;AACH,MAAM,yBAA0B,SAAQ,mBAAmB,CAAA;AAyB9C,IAAA,SAAA;AAxBX;;;;;AAKG;IACH,SAAS,GAA4B,IAAI;AAEzC;;;;AAIG;IACc,KAAK,GAKlB,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;IAEhD,WACE,CAAA,IAAqB,EACrB,WAA0D,EAC1D,IAAuB,EACd,SAAmC,EAC5C,UAAsB,EACtB,QAAA,GAAmC,IAAI,EAAA;;;QAIvC,KAAK,CAAC,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,CAAC;QANnF,IAAS,CAAA,SAAA,GAAT,SAAS;;AASlB,QAAA,KAAK,MAAM,KAAK,IAAI,mBAAmB,EAAE;AACvC,YAAA,MAAM,UAAU,GAAG,WAAW,CAAC,KAAK,CAAC;AACrC,YAAA,IAAI,UAAU,KAAK,SAAS,EAAE;gBAC5B;;YAGF,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAA+B;AACxF,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;AACpB,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AAClB,YAAA,IAAI,CAAC,MAAM,GAAG,UAAU;AACxB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI;AACjB,YAAA,IAAI,CAAC,MAAM,IAAI,MAAK;gBAClB,gBAAgB,CAAC,IAAI,CAAC;gBACtB,OAAO,IAAI,CAAC,KAAK;AACnB,aAAC,CAAoB;AACrB,YAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI;YAC1B,IAAI,CAAC,iBAAiB,GAAG,CAAC,EAAmB,KAC3C,CAAC,IAAI,CAAC,OAAO,KAAK,IAAI,GAAG,EAAc,EAAE,GAAG,CAAC,EAAE,CAAC;AAElD,YAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI;;AAGxB,YAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;;;IAI7C,QAAQ,GAAA;QACf,KAAK,CAAC,QAAQ,EAAE;;AAEhB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;IAGd,OAAO,GAAA;QACd,KAAK,CAAC,OAAO,EAAE;;AAGf,QAAA,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;YAC7B,KAAK,MAAM,EAAE,IAAI,IAAI,EAAE,OAAO,IAAI,iBAAiB,EAAE;AACnD,gBAAA,EAAE,EAAE;;;;AAIX;AAmHD;;AAEG;AACa,SAAA,iBAAiB,CAC/B,cASK,EACL,OAA4B,EAAA;IAE5B,SAAS;QACP,0BAA0B,CACxB,iBAAiB,EACjB,yFAAyF;AACvF,YAAA,2CAA2C,CAC9C;AAEH,IAAA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnC,wBAAwB,CAAC,iBAAiB,CAAC;;AAG7C,IAAA,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,YAAY,EAAE;AACvD,QAAA,OAAO,qBAAqB;;IAG9B,MAAM,QAAQ,GAAG,OAAO,EAAE,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC;IACtD,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,wBAAwB,CAAC;IACxD,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,kBAAkB,CAAC;AAChD,IAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IACpE,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC,GAAG,CAAC,eAAe,CAAC;IAE9C,IAAI,IAAI,GAAG,cAAc;AACzB,IAAA,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;AAC9B,QAAA,IAAI,GAAG,EAAC,cAAc,EAAE,cAAqB,EAAC;;AAGhD,IAAA,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IAErE,MAAM,QAAQ,GAAG,IAAI,yBAAyB,CAC5C,OAAO,CAAC,IAAI,EACZ,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAiC,EAC5F,WAAW,EAAE,IAAI,EACjB,SAAS,EACT,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,EACxB,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,CACxB;AACD,IAAA,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAC/B,IAAA,OAAO,QAAQ;AACjB;;AC7XA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DG;AACa,SAAA,eAAe,CAC7B,SAAkB,EAClB,OAOC,EAAA;AAED,IAAA,SAAS,IAAI,kBAAkB,CAAC,SAAS,CAAC;AAC1C,IAAA,MAAM,YAAY,GAAG,eAAe,CAAC,SAAS,CAAE;IAChD,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI,eAAe,EAAE;AACpE,IAAA,MAAM,OAAO,GAAG,IAAI,gBAAgB,CAAI,YAAY,CAAC;IACrD,OAAO,OAAO,CAAC,MAAM,CACnB,eAAe,EACf,OAAO,CAAC,gBAAgB,EACxB,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,mBAAmB,EAC3B,OAAO,CAAC,UAAU,EAClB,OAAO,CAAC,QAAQ,CACjB;AACH;AA8CA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCG;AACG,SAAU,oBAAoB,CAAI,SAAkB,EAAA;AACxD,IAAA,MAAM,YAAY,GAAG,eAAe,CAAC,SAAS,CAAC;AAC/C,IAAA,IAAI,CAAC,YAAY;AAAE,QAAA,OAAO,IAAI;AAE9B,IAAA,MAAM,OAAO,GAAG,IAAI,gBAAgB,CAAI,YAAY,CAAC;IACrD,OAAO;AACL,QAAA,IAAI,QAAQ,GAAA;YACV,OAAO,OAAO,CAAC,QAAQ;SACxB;AACD,QAAA,IAAI,IAAI,GAAA;YACN,OAAO,OAAO,CAAC,aAAa;SAC7B;AACD,QAAA,IAAI,MAAM,GAAA;YAMR,OAAO,OAAO,CAAC,MAAM;SACtB;AACD,QAAA,IAAI,OAAO,GAAA;YACT,OAAO,OAAO,CAAC,OAAO;SACvB;AACD,QAAA,IAAI,kBAAkB,GAAA;YACpB,OAAO,OAAO,CAAC,kBAAkB;SAClC;AACD,QAAA,IAAI,YAAY,GAAA;YACd,OAAO,YAAY,CAAC,UAAU;SAC/B;AACD,QAAA,IAAI,QAAQ,GAAA;YACV,OAAO,YAAY,CAAC,OAAO;SAC5B;KACF;AACH;;ACzMA;;;;;;;AAOG;AACa,SAAA,sBAAsB,CAAC,GAAG,OAA4B,EAAA;IACpE,OAAO,OAAO,CAAC,MAAM,CACnB,CAAC,IAAI,EAAE,IAAI,KAAI;QACb,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,EAAC,SAAS,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,EAAC,CAAC;AACvF,KAAC,EACD,EAAC,SAAS,EAAE,EAAE,EAAC,CAChB;AACH;;AC3BA;;;;;;;;;;;;;;;;;AAiBG;MACU,OAAO,GAAG,IAAI,cAAc,CACvC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,SAAS,GAAG,EAAE,EAC9D;AACE,IAAA,UAAU,EAAE,UAAU;AACtB,IAAA,OAAO,EAAE,MAAM,IAAI;AACpB,CAAA;AAGH;;;;;;;;;;;;;;;;;AAiBG;MACU,aAAa,GAAG,IAAI,cAAc,CAC7C,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,eAAe,GAAG,EAAE,EACpE;AACE,IAAA,UAAU,EAAE,UAAU;AACtB,IAAA,OAAO,EAAE,MAAM,IAAI;AACpB,CAAA;AAGH;;;;;;;;;AASG;MACU,eAAe,GAAG,IAAI,cAAc,CAC/C,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,iBAAiB,GAAG,EAAE,EACtE;AACE,IAAA,UAAU,EAAE,UAAU;AACtB,IAAA,OAAO,EAAE,MAAM,IAAI;AACpB,CAAA;;;;"}