import { Injectable } from '@angular/core';
import { Observable, from, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { ProgramFile, FileUploadResult, ValidationResult } from '../models/types';

@Injectable({
  providedIn: 'root'
})
export class FileService {
  private readonly maxFileSize = 16 * 1024 * 1024; // 16MB
  private readonly supportedExtensions = [
    '.txt', '.log', '.err', '.error', '.out',
    '.c', '.cpp', '.h', '.hpp', '.ec',
    '.sql', '.py', '.js', '.ts', '.java'
  ];

  /**
   * Lit le contenu d'un fichier
   */
  readFile(file: File): Observable<FileUploadResult> {
    return from(this.processFile(file)).pipe(
      catchError(error => throwError(() => new Error(`Erreur lors de la lecture du fichier: ${error.message}`)))
    );
  }

  /**
   * Valide un fichier avant traitement
   */
  validateFile(file: File): ValidationResult {
    const errors: string[] = [];

    // Vérifier la taille
    if (file.size > this.maxFileSize) {
      errors.push(`Le fichier est trop volumineux (max ${this.formatFileSize(this.maxFileSize)})`);
    }

    // Vérifier l'extension
    const hasValidExtension = this.supportedExtensions.some(ext => 
      file.name.toLowerCase().endsWith(ext)
    );

    if (!hasValidExtension) {
      errors.push(`Extension non supportée. Extensions autorisées: ${this.supportedExtensions.join(', ')}`);
    }

    // Vérifier le nom du fichier
    if (!file.name || file.name.trim().length === 0) {
      errors.push('Le nom du fichier ne peut pas être vide');
    }

    return {
      valid: errors.length === 0,
      message: errors.length > 0 ? errors[0] : undefined,
      errors
    };
  }

  /**
   * Convertit un fichier en ProgramFile
   */
  fileToProgramFile(file: File, type: 'program' | 'error'): Observable<ProgramFile> {
    return this.readFile(file).pipe(
      map(result => {
        if (!result.success) {
          throw new Error(result.error || 'Erreur lors de la lecture du fichier');
        }

        return {
          name: result.originalName,
          content: result.content,
          type
        };
      })
    );
  }

  /**
   * Détermine le type de fichier basé sur son nom
   */
  getFileType(fileName: string): string {
    const extension = fileName.toLowerCase().split('.').pop();
    
    const typeMap: { [key: string]: string } = {
      'txt': 'text',
      'log': 'error',
      'err': 'error',
      'error': 'error',
      'out': 'error',
      'c': 'code',
      'cpp': 'code',
      'h': 'code',
      'hpp': 'code',
      'ec': 'code',
      'sql': 'sql',
      'py': 'code',
      'js': 'code',
      'ts': 'code',
      'java': 'code'
    };

    return typeMap[extension || ''] || 'unknown';
  }

  /**
   * Formate la taille d'un fichier pour l'affichage
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Télécharge un contenu en tant que fichier
   */
  downloadFile(content: string, fileName: string, mimeType: string = 'text/plain'): void {
    const blob = new Blob([content], { type: mimeType });
    const url = window.URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }

  /**
   * Copie du contenu dans le presse-papiers
   */
  async copyToClipboard(content: string): Promise<void> {
    try {
      await navigator.clipboard.writeText(content);
    } catch (error) {
      // Fallback pour les navigateurs plus anciens
      const textArea = document.createElement('textarea');
      textArea.value = content;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
    }
  }

  /**
   * Traite un fichier et retourne son contenu
   */
  private async processFile(file: File): Promise<FileUploadResult> {
    try {
      // Valider le fichier
      const validation = this.validateFile(file);
      if (!validation.valid) {
        return {
          content: '',
          originalName: file.name,
          fileType: this.getFileType(file.name),
          success: false,
          error: validation.message
        };
      }

      // Lire le contenu
      const content = await this.readFileContent(file);

      return {
        content,
        originalName: file.name,
        fileType: this.getFileType(file.name),
        success: true
      };
    } catch (error) {
      return {
        content: '',
        originalName: file.name,
        fileType: this.getFileType(file.name),
        success: false,
        error: error instanceof Error ? error.message : 'Erreur inconnue'
      };
    }
  }

  /**
   * Lit le contenu textuel d'un fichier
   */
  private readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (event) => {
        const content = event.target?.result as string;
        resolve(content || '');
      };
      
      reader.onerror = () => {
        reject(new Error('Erreur lors de la lecture du fichier'));
      };
      
      reader.readAsText(file, 'UTF-8');
    });
  }
}
