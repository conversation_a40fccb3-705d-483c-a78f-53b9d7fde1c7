# Configuration pour l'application Leoni Agents Flask

# Clé API OpenAI (obligatoire)
OPENAI_API_KEY=your_openai_api_key_here

# Configuration Flask
FLASK_ENV=development
FLASK_DEBUG=True

# Configuration CORS
CORS_ORIGINS=http://localhost:4200

# Configuration de l'application
MAX_CONTENT_LENGTH=16777216  # 16MB en bytes
MAX_TOKENS=3000

# Configuration de logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
