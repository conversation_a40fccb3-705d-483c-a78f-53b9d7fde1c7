{"version": 3, "file": "abort.js", "sourceRoot": "", "sources": ["../src/abort.ts"], "names": [], "mappings": ";;;AAkCA,0BAMC;AAlBD,MAAa,UAAW,SAAQ,KAAK;IACnC,YAAa,MAAyC;QACpD,0CAA0C;QAC1C,mBAAmB;QACnB,KAAK,CAAC,2BAA2B,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;IACxD,CAAC;IAED,IAAI,IAAI;QACN,OAAO,YAAY,CAAC;IACtB,CAAC;CACF;AAVD,gCAUC;AAED,SAAgB,OAAO,CAAE,WAA2B,EAAE,QAAoB;IACxE,IAAI,kBAAkB,IAAI,WAAW,EAAE,CAAC;QACtC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;IAClE,CAAC;SAAM,CAAC;QACN,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IACtC,CAAC;AACH,CAAC"}