{"version": 3, "file": "si.js", "sourceRoot": "", "sources": ["si.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;IAE7G,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QACxC,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,OAAO,EAAC,MAAM,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,OAAO,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,MAAM,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,KAAK,EAAC,OAAO,EAAC,SAAS,EAAC,MAAM,EAAC,KAAK,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,WAAW,EAAC,OAAO,EAAC,gBAAgB,EAAC,UAAU,EAAC,WAAW,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,MAAM,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,IAAI,EAAC,KAAK,EAAC,QAAQ,EAAC,UAAU,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,UAAU,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,SAAS,EAAC,aAAa,EAAC,UAAU,EAAC,WAAW,EAAC,WAAW,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,IAAI,EAAC,KAAK,EAAC,MAAM,EAAC,UAAU,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,UAAU,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,SAAS,EAAC,aAAa,EAAC,UAAU,EAAC,WAAW,EAAC,WAAW,CAAC,CAAC,EAAC,CAAC,CAAC,WAAW,EAAC,UAAU,CAAC,EAAC,CAAC,EAAC,CAAC,iBAAiB,EAAC,gBAAgB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,mBAAmB,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,OAAO,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, i = Math.floor(Math.abs(val)), f = parseInt(val.toString().replace(/^[^.]*\\.?/, ''), 10) || 0;\n\nif (n === 0 || n === 1 || i === 0 && f === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"si\",[[\"පෙ\",\"ප\"],[\"පෙ.ව.\",\"ප.ව.\"],u],[[\"පෙ.ව.\",\"ප.ව.\"],u,u],[[\"ඉ\",\"ස\",\"අ\",\"බ\",\"බ්‍ර\",\"සි\",\"සෙ\"],[\"ඉරිදා\",\"සඳුදා\",\"අඟහ\",\"බදාදා\",\"බ්‍රහස්\",\"සිකු\",\"සෙන\"],[\"ඉරිදා\",\"සඳුදා\",\"අඟහරුවාදා\",\"බදාදා\",\"බ්‍රහස්පතින්දා\",\"සිකුරාදා\",\"සෙනසුරාදා\"],[\"ඉරි\",\"සඳු\",\"අඟ\",\"බදා\",\"බ්‍රහ\",\"සිකු\",\"සෙන\"]],u,[[\"ජ\",\"පෙ\",\"මා\",\"අ\",\"මැ\",\"ජූ\",\"ජූ\",\"අ\",\"සැ\",\"ඔ\",\"නෙ\",\"දෙ\"],[\"ජන\",\"පෙබ\",\"මාර්තු\",\"අප්‍රේල්\",\"මැයි\",\"ජූනි\",\"ජූලි\",\"අගෝ\",\"සැප්\",\"ඔක්\",\"නොවැ\",\"දෙසැ\"],[\"ජනවාරි\",\"පෙබරවාරි\",\"මාර්තු\",\"අප්‍රේල්\",\"මැයි\",\"ජූනි\",\"ජූලි\",\"අගෝස්තු\",\"සැප්තැම්බර්\",\"ඔක්තෝබර්\",\"නොවැම්බර්\",\"දෙසැම්බර්\"]],[[\"ජ\",\"පෙ\",\"මා\",\"අ\",\"මැ\",\"ජූ\",\"ජූ\",\"අ\",\"සැ\",\"ඔ\",\"නෙ\",\"දෙ\"],[\"ජන\",\"පෙබ\",\"මාර්\",\"අප්‍රේල්\",\"මැයි\",\"ජූනි\",\"ජූලි\",\"අගෝ\",\"සැප්\",\"ඔක්\",\"නොවැ\",\"දෙසැ\"],[\"ජනවාරි\",\"පෙබරවාරි\",\"මාර්තු\",\"අප්‍රේල්\",\"මැයි\",\"ජූනි\",\"ජූලි\",\"අගෝස්තු\",\"සැප්තැම්බර්\",\"ඔක්තෝබර්\",\"නොවැම්බර්\",\"දෙසැම්බර්\"]],[[\"ක්‍රි.පූ.\",\"ක්‍රි.ව.\"],u,[\"ක්‍රිස්තු පූර්ව\",\"ක්‍රිස්තු වර්ෂ\"]],1,[6,0],[\"y-MM-dd\",\"y MMM d\",\"y MMMM d\",\"y MMMM d, EEEE\"],[\"HH.mm\",\"HH.mm.ss\",\"HH.mm.ss z\",\"HH.mm.ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\".\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#\"],\"LKR\",\"රු.\",\"ශ්‍රී ලංකා රුපියල\",{\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"],\"LKR\":[\"රු.\"],\"PHP\":[u,\"₱\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"],\"USD\":[\"US$\",\"$\"],\"XOF\":[\"සිෆ්එ\"]},\"ltr\", plural];\n"]}