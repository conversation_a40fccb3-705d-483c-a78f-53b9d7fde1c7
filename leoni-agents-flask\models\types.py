"""
Modèles de données pour l'application Leoni Agents
Équivalent des types TypeScript en Python avec dataclasses
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any, Literal
from datetime import datetime


@dataclass
class ProgramFile:
    """Représente un fichier de programme ou d'erreur"""
    name: str
    content: str
    type: Literal['program', 'error']


@dataclass
class CodeContext:
    """Contexte de code autour d'une ligne spécifique"""
    target_line: str
    context_lines: List[Dict[str, Any]]  # {number, content, is_target}


@dataclass
class ErrorAnalysis:
    """Analyse détaillée d'une erreur"""
    error_type: str
    location: str
    description: str
    possible_causes: List[str]
    solutions: List[str]
    severity: Literal['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']
    line_number: Optional[int] = None
    code_context: Optional[CodeContext] = None


@dataclass
class ErrorAnalysisResult:
    """Résultat complet de l'analyse d'erreurs"""
    summary: str
    errors: List[ErrorAnalysis]
    recommendations: List[str]
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())


@dataclass
class SQLGenerationRequest:
    """Requête pour la génération SQL"""
    specification: str
    database_type: Literal['mysql', 'postgresql', 'sqlite', 'sqlserver'] = 'mysql'
    include_comments: bool = True


@dataclass
class SQLGenerationResult:
    """Résultat de la génération SQL"""
    sql: str
    explanation: str
    tables: List[str]
    operations: List[str]
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())


@dataclass
class Agent:
    """Configuration d'un agent IA"""
    id: str
    name: str
    description: str
    role: str
    goal: str
    prompt: str
    tools: List[str]
    utils: List[str]


@dataclass
class AgentResponse:
    """Réponse standardisée d'un agent"""
    success: bool
    data: Optional[Any] = None
    error: Optional[str] = None
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())


@dataclass
class ParsedError:
    """Erreur parsée depuis un fichier de log"""
    timestamp: str
    level: str
    message: str
    task: Optional[str] = None
    line_number: Optional[int] = None


@dataclass
class FileProcessingResult:
    """Résultat du traitement d'un fichier"""
    content: str
    original_name: str
    file_type: str
    success: bool
    error: Optional[str] = None


# Fonctions utilitaires pour la conversion
def dict_to_dataclass(cls, data: Dict[str, Any]):
    """Convertit un dictionnaire en dataclass"""
    if not isinstance(data, dict):
        return data
    
    # Récupérer les champs de la dataclass
    field_names = {f.name for f in cls.__dataclass_fields__.values()}
    
    # Filtrer les données pour ne garder que les champs valides
    filtered_data = {k: v for k, v in data.items() if k in field_names}
    
    return cls(**filtered_data)


def dataclass_to_dict(obj) -> Dict[str, Any]:
    """Convertit une dataclass en dictionnaire"""
    if hasattr(obj, '__dataclass_fields__'):
        result = {}
        for field_name, field_def in obj.__dataclass_fields__.items():
            value = getattr(obj, field_name)
            if isinstance(value, list):
                result[field_name] = [dataclass_to_dict(item) for item in value]
            elif hasattr(value, '__dataclass_fields__'):
                result[field_name] = dataclass_to_dict(value)
            else:
                result[field_name] = value
        return result
    return obj
