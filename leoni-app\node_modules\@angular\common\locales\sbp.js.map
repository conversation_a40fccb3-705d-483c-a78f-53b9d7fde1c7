{"version": 3, "file": "sbp.js", "sourceRoot": "", "sources": ["sbp.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,WAAW,EAAC,WAAW,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,UAAU,EAAC,SAAS,EAAC,UAAU,EAAC,WAAW,EAAC,QAAQ,EAAC,UAAU,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,cAAc,EAAC,SAAS,EAAC,UAAU,EAAC,OAAO,EAAC,iBAAiB,EAAC,SAAS,EAAC,WAAW,EAAC,UAAU,EAAC,UAAU,EAAC,OAAO,EAAC,gBAAgB,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,oBAAoB,EAAC,qBAAqB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,oBAAoB,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"sbp\",[[\"<PERSON><PERSON>law<PERSON>\",\"<PERSON><PERSON><PERSON>\"],u,u],u,[[\"M\",\"J\",\"J\",\"<PERSON>\",\"A\",\"<PERSON>\",\"J\"],[\"Mul\",\"J<PERSON>\",\"Jnn\",\"Jtn\",\"<PERSON>h\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\"],[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"Ju<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON>ju<PERSON><PERSON>\",\"Ju<PERSON><PERSON><PERSON>\"],[\"Mul\",\"Jtt\",\"Jnn\",\"Jtn\",\"<PERSON><PERSON>\",\"I<PERSON>\",\"<PERSON><PERSON>\"]],u,[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"Mup\",\"<PERSON><PERSON>\",\"<PERSON>h\",\"Mu<PERSON>\",\"Mag\",\"<PERSON><PERSON>\",\"<PERSON>p\",\"Mpg\",\"<PERSON>e\",\"<PERSON>k\",\"Mus\",\"Muh\"],[\"Mupalangulwa\",\"Mwitope\",\"Mu<PERSON>nde\",\"Munyi\",\"Mu<PERSON>nde Magali\",\"Mujimbi\",\"Mushipepo\",\"Mupuguto\",\"Munyense\",\"<PERSON>khu\",\"Musongandembwe\",\"Muha<PERSON>\"]],u,[[\"AK\",\"P<PERSON>\"],u,[\"Ashanali uKilisito\",\"Pamwandi ya Kilisto\"]],1,[6,0],[\"dd/MM/y\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"#,##0.00¤\",\"#E0\"],\"TZS\",\"TSh\",\"Ihela ya Tansaniya\",{\"JPY\":[\"JP¥\",\"¥\"],\"TZS\":[\"TSh\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}