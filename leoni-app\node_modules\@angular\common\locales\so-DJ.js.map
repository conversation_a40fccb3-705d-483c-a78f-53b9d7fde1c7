{"version": 3, "file": "so-DJ.js", "sourceRoot": "", "sources": ["so-DJ.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,OAAO,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,CAAC,EAAC,CAAC,MAAM,EAAC,QAAQ,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,EAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,CAAC,EAAC,CAAC,MAAM,EAAC,QAAQ,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,EAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,eAAe,EAAC,cAAc,EAAC,iBAAiB,EAAC,cAAc,EAAC,eAAe,EAAC,cAAc,EAAC,gBAAgB,EAAC,iBAAiB,EAAC,iBAAiB,EAAC,eAAe,EAAC,uBAAuB,EAAC,wBAAwB,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,UAAU,EAAC,QAAQ,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,EAAC,SAAS,EAAC,OAAO,EAAC,WAAW,EAAC,UAAU,EAAC,UAAU,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,cAAc,EAAC,eAAe,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,UAAU,EAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,SAAS,EAAC,cAAc,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,eAAe,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"so-DJ\",[[\"h\",\"d\"],[\"GH\",\"GD\"],u],[[\"AM\",\"GD\"],u,[\"GH\",\"GD\"]],[[\"A\",\"I\",\"T\",\"A\",\"Kh\",\"J\",\"S\"],[\"Axd\",\"Isn\",\"Tldo\",\"Arbc\",\"Khms\",\"Jmc\",\"Sbti\"],[\"Axad\",\"Isniin\",\"<PERSON>la<PERSON>\",\"Arb<PERSON>\",\"<PERSON>hamii<PERSON>\",\"Jim<PERSON>\",\"<PERSON>bti\"],[\"Axd\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>rb<PERSON>\",\"<PERSON>hm<PERSON>\",\"<PERSON>mc\",\"<PERSON>bti\"]],[[\"A\",\"I\",\"T\",\"A\",\"Kh\",\"J\",\"S\"],[\"Axd\",\"Isn\",\"<PERSON>ldo\",\"Arbc\",\"<PERSON>hms\",\"Jmc\",\"Sbti\"],[\"Axad\",\"Isniin\",\"Talaado\",\"Arbaco\",\"Khamiis\",\"Jimco\",\"Sabti\"],[\"Axd\",\"Isn\",\"Tldo\",\"Arbaco\",\"Khms\",\"Jmc\",\"Sbti\"]],[[\"J\",\"F\",\"M\",\"A\",\"M\",\"J\",\"L\",\"O\",\"S\",\"O\",\"N\",\"D\"],[\"Jan\",\"Feb\",\"Mar\",\"Abr\",\"May\",\"Jun\",\"Lul\",\"Ogs\",\"Seb\",\"Okt\",\"Nof\",\"Dis\"],[\"Bisha Koobaad\",\"Bisha Labaad\",\"Bisha Saddexaad\",\"Bisha Afraad\",\"Bisha Shanaad\",\"Bisha Lixaad\",\"Bisha Todobaad\",\"Bisha Sideedaad\",\"Bisha Sagaalaad\",\"Bisha Tobnaad\",\"Bisha Kow iyo Tobnaad\",\"Bisha Laba iyo Tobnaad\"]],[[\"J\",\"F\",\"M\",\"A\",\"M\",\"J\",\"L\",\"O\",\"S\",\"O\",\"N\",\"D\"],[\"Jan\",\"Feb\",\"Mar\",\"Abr\",\"May\",\"Jun\",\"Lul\",\"Ogs\",\"Seb\",\"Okt\",\"Nof\",\"Dis\"],[\"Jannaayo\",\"Febraayo\",\"Maarso\",\"Abriil\",\"May\",\"Juun\",\"Luuliyo\",\"Ogost\",\"Sebtembar\",\"Oktoobar\",\"Nofembar\",\"Desembar\"]],[[\"B\",\"A\"],[\"BC\",\"AD\"],[\"Ciise Hortii\",\"Ciise Dabadii\"]],6,[6,0],[\"dd/MM/yy\",\"dd-MMM-y\",\"MMMM d, y\",\"EEEE, MMMM d, y\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1} {0}\",\"{1} 'ee' {0}\",u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"MaL\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"DJF\",\"Fdj\",\"Faran Jabuuti\",{\"BBD\":[\"DBB\",\"$\"],\"DJF\":[\"Fdj\"],\"JPY\":[\"JP¥\",\"¥\"],\"SOS\":[\"S\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}