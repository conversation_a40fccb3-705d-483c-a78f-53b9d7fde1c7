"""
Validateurs pour les requêtes API
"""

from typing import Dict, Any


def validate_error_analysis_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Valide une requête d'analyse d'erreurs
    
    Args:
        data: Données de la requête
        
    Returns:
        Dictionnaire avec valid (bool) et message (str)
    """
    if not data:
        return {'valid': False, 'message': 'Aucune donnée fournie'}
    
    # Vérifier la présence des fichiers
    if 'programFile' not in data:
        return {'valid': False, 'message': 'Le fichier programme est requis'}
    
    if 'errorFile' not in data:
        return {'valid': False, 'message': 'Le fichier erreur est requis'}
    
    program_file = data['programFile']
    error_file = data['errorFile']
    
    # Vérifier la structure des fichiers
    if not isinstance(program_file, dict):
        return {'valid': False, 'message': 'Le fichier programme doit être un objet'}
    
    if not isinstance(error_file, dict):
        return {'valid': False, 'message': 'Le fichier erreur doit être un objet'}
    
    # Vérifier les champs requis
    required_fields = ['name', 'content']
    
    for field in required_fields:
        if field not in program_file:
            return {'valid': False, 'message': f'Le champ {field} est requis pour le fichier programme'}
        
        if field not in error_file:
            return {'valid': False, 'message': f'Le champ {field} est requis pour le fichier erreur'}
    
    # Vérifier que le contenu n'est pas vide
    if not program_file['content'] or not program_file['content'].strip():
        return {'valid': False, 'message': 'Le contenu du fichier programme ne peut pas être vide'}
    
    if not error_file['content'] or not error_file['content'].strip():
        return {'valid': False, 'message': 'Le contenu du fichier erreur ne peut pas être vide'}
    
    # Vérifier la taille des fichiers (limite à 1MB par fichier)
    max_size = 1024 * 1024  # 1MB
    
    if len(program_file['content']) > max_size:
        return {'valid': False, 'message': 'Le fichier programme est trop volumineux (max 1MB)'}
    
    if len(error_file['content']) > max_size:
        return {'valid': False, 'message': 'Le fichier erreur est trop volumineux (max 1MB)'}
    
    return {'valid': True, 'message': 'Requête valide'}


def validate_sql_generation_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Valide une requête de génération SQL
    
    Args:
        data: Données de la requête
        
    Returns:
        Dictionnaire avec valid (bool) et message (str)
    """
    if not data:
        return {'valid': False, 'message': 'Aucune donnée fournie'}
    
    # Vérifier la présence de la spécification
    if 'specification' not in data:
        return {'valid': False, 'message': 'La spécification est requise'}
    
    specification = data['specification']
    
    # Vérifier que la spécification n'est pas vide
    if not specification or not specification.strip():
        return {'valid': False, 'message': 'La spécification ne peut pas être vide'}
    
    # Vérifier la taille de la spécification (limite à 500KB)
    max_size = 500 * 1024  # 500KB
    if len(specification) > max_size:
        return {'valid': False, 'message': 'La spécification est trop volumineuse (max 500KB)'}
    
    # Vérifier le type de base de données si fourni
    if 'database_type' in data:
        valid_db_types = ['mysql', 'postgresql', 'sqlite', 'sqlserver']
        if data['database_type'] not in valid_db_types:
            return {
                'valid': False, 
                'message': f'Type de base de données invalide. Types supportés: {", ".join(valid_db_types)}'
            }
    
    # Vérifier le paramètre include_comments si fourni
    if 'include_comments' in data:
        if not isinstance(data['include_comments'], bool):
            return {'valid': False, 'message': 'Le paramètre include_comments doit être un booléen'}
    
    return {'valid': True, 'message': 'Requête valide'}


def validate_file_upload(file_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Valide les données d'un fichier uploadé
    
    Args:
        file_data: Données du fichier
        
    Returns:
        Dictionnaire avec valid (bool) et message (str)
    """
    if not file_data:
        return {'valid': False, 'message': 'Aucune donnée de fichier fournie'}
    
    required_fields = ['name', 'content']
    
    for field in required_fields:
        if field not in file_data:
            return {'valid': False, 'message': f'Le champ {field} est requis'}
    
    # Vérifier le nom du fichier
    if not file_data['name'] or not file_data['name'].strip():
        return {'valid': False, 'message': 'Le nom du fichier ne peut pas être vide'}
    
    # Vérifier l'extension du fichier
    allowed_extensions = [
        '.txt', '.log', '.err', '.error', '.out',
        '.c', '.cpp', '.h', '.hpp', '.ec',
        '.sql', '.py', '.js', '.ts', '.java'
    ]
    
    file_name = file_data['name'].lower()
    has_valid_extension = any(file_name.endswith(ext) for ext in allowed_extensions)
    
    if not has_valid_extension:
        return {
            'valid': False, 
            'message': f'Extension de fichier non supportée. Extensions autorisées: {", ".join(allowed_extensions)}'
        }
    
    return {'valid': True, 'message': 'Fichier valide'}
