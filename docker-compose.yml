version: '3.8'

services:
  # Backend Flask
  backend:
    build: ./leoni-agents-flask
    container_name: leoni-backend
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ./leoni-agents-flask:/app
    restart: unless-stopped
    networks:
      - leoni-network

  # Frontend Angular
  frontend:
    build: ./leoni-agents-angular
    container_name: leoni-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - leoni-network

networks:
  leoni-network:
    driver: bridge

# Pour utiliser ce fichier:
# 1. Créer un fichier .env à la racine avec OPENAI_API_KEY=votre_clé
# 2. Exécuter: docker-compose up --build
