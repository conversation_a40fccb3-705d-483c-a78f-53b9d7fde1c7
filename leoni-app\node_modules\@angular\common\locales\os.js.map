{"version": 3, "file": "os.js", "sourceRoot": "", "sources": ["os.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,iBAAiB,EAAC,iBAAiB,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,WAAW,EAAC,WAAW,EAAC,QAAQ,EAAC,UAAU,EAAC,UAAU,EAAC,WAAW,EAAC,OAAO,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,WAAW,EAAC,WAAW,EAAC,QAAQ,EAAC,UAAU,EAAC,UAAU,EAAC,WAAW,EAAC,OAAO,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,SAAS,EAAC,UAAU,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,OAAO,EAAC,OAAO,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,QAAQ,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,UAAU,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,eAAe,EAAC,gBAAgB,EAAC,sBAAsB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,KAAK,EAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"os\",[[\"AM\",\"PM\"],u,[\"ӕмбисбоны размӕ\",\"ӕмбисбоны фӕстӕ\"]],[[\"AM\",\"PM\"],u,u],[[\"Х\",\"К\",\"Д\",\"Ӕ\",\"Ц\",\"М\",\"С\"],[\"хцб\",\"крс\",\"дцг\",\"ӕрт\",\"цпр\",\"мрб\",\"сбт\"],[\"хуыцаубон\",\"къуырисӕр\",\"дыццӕг\",\"ӕртыццӕг\",\"цыппӕрӕм\",\"майрӕмбон\",\"сабат\"],[\"хцб\",\"крс\",\"дцг\",\"ӕрт\",\"цпр\",\"мрб\",\"сбт\"]],[[\"Х\",\"К\",\"Д\",\"Ӕ\",\"Ц\",\"М\",\"С\"],[\"Хцб\",\"Крс\",\"Дцг\",\"Ӕрт\",\"Цпр\",\"Мрб\",\"Сбт\"],[\"Хуыцаубон\",\"Къуырисӕр\",\"Дыццӕг\",\"Ӕртыццӕг\",\"Цыппӕрӕм\",\"Майрӕмбон\",\"Сабат\"],[\"хцб\",\"крс\",\"дцг\",\"ӕрт\",\"цпр\",\"мрб\",\"сбт\"]],[[\"Я\",\"Ф\",\"М\",\"А\",\"М\",\"И\",\"И\",\"А\",\"С\",\"О\",\"Н\",\"Д\"],[\"янв.\",\"фев.\",\"мар.\",\"апр.\",\"майы\",\"июны\",\"июлы\",\"авг.\",\"сен.\",\"окт.\",\"ноя.\",\"дек.\"],[\"январы\",\"февралы\",\"мартъийы\",\"апрелы\",\"майы\",\"июны\",\"июлы\",\"августы\",\"сентябры\",\"октябры\",\"ноябры\",\"декабры\"]],[[\"Я\",\"Ф\",\"М\",\"А\",\"М\",\"И\",\"И\",\"А\",\"С\",\"О\",\"Н\",\"Д\"],[\"Янв.\",\"Февр.\",\"Март.\",\"Апр.\",\"Май\",\"Июнь\",\"Июль\",\"Авг.\",\"Сент.\",\"Окт.\",\"Нояб.\",\"Дек.\"],[\"Январь\",\"Февраль\",\"Мартъи\",\"Апрель\",\"Май\",\"Июнь\",\"Июль\",\"Август\",\"Сентябрь\",\"Октябрь\",\"Ноябрь\",\"Декабрь\"]],[[\"н.д.а.\",\"н.д.\"],u,u],1,[6,0],[\"dd.MM.yy\",\"dd MMM y 'аз'\",\"d MMMM, y 'аз'\",\"EEEE, d MMMM, y 'аз'\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1}, {0}\",u,u,u],[\",\",\" \",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"НН\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤ #,##0.00\",\"#E0\"],\"GEL\",\"₾\",\"Лар\",{\"GEL\":[\"₾\"],\"JPY\":[\"JP¥\",\"¥\"]},\"ltr\", plural];\n"]}