import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { 
  ApiResponse, 
  ErrorAnalysisResult, 
  SQLGenerationResult, 
  ProgramFile, 
  SQLGenerationRequest,
  Agent 
} from '../models/types';

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private readonly baseUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  /**
   * Analyse les fichiers d'erreur et de programme
   */
  analyzeErrors(programFile: ProgramFile, errorFile: ProgramFile): Observable<ErrorAnalysisResult> {
    const payload = {
      programFile,
      errorFile
    };

    return this.http.post<ApiResponse<ErrorAnalysisResult>>(`${this.baseUrl}/api/error-analysis`, payload)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(response.error || 'Erreur lors de l\'analyse');
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Obtient les informations sur l'agent d'analyse d'erreurs
   */
  getErrorAnalysisAgentInfo(): Observable<Agent> {
    return this.http.get<ApiResponse<Agent>>(`${this.baseUrl}/api/error-analysis`)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(response.error || 'Erreur lors de la récupération des informations');
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Génère un script SQL à partir d'une spécification
   */
  generateSQL(request: SQLGenerationRequest): Observable<SQLGenerationResult> {
    return this.http.post<ApiResponse<SQLGenerationResult>>(`${this.baseUrl}/api/sql-generation`, request)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(response.error || 'Erreur lors de la génération SQL');
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Obtient les informations sur l'agent de génération SQL
   */
  getSQLGenerationAgentInfo(): Observable<Agent> {
    return this.http.get<ApiResponse<Agent>>(`${this.baseUrl}/api/sql-generation`)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(response.error || 'Erreur lors de la récupération des informations');
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Vérifie la santé de l'API
   */
  healthCheck(): Observable<any> {
    return this.http.get<ApiResponse>(`${this.baseUrl}/`)
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          }
          throw new Error(response.error || 'API non disponible');
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Gestion centralisée des erreurs HTTP
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'Une erreur inattendue s\'est produite';

    if (error.error instanceof ErrorEvent) {
      // Erreur côté client
      errorMessage = `Erreur client: ${error.error.message}`;
    } else {
      // Erreur côté serveur
      if (error.error?.error) {
        errorMessage = error.error.error;
      } else if (error.error?.message) {
        errorMessage = error.error.message;
      } else {
        switch (error.status) {
          case 400:
            errorMessage = 'Requête invalide';
            break;
          case 401:
            errorMessage = 'Non autorisé';
            break;
          case 403:
            errorMessage = 'Accès interdit';
            break;
          case 404:
            errorMessage = 'Ressource non trouvée';
            break;
          case 413:
            errorMessage = 'Fichier trop volumineux';
            break;
          case 500:
            errorMessage = 'Erreur interne du serveur';
            break;
          case 503:
            errorMessage = 'Service temporairement indisponible';
            break;
          default:
            errorMessage = `Erreur ${error.status}: ${error.statusText}`;
        }
      }
    }

    console.error('Erreur API:', error);
    return throwError(() => new Error(errorMessage));
  }
}
