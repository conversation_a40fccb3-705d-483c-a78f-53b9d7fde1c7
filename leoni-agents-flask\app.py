"""
Leoni Agents - Backend Flask
Application principale avec les agents IA pour l'analyse d'erreurs et la génération SQL
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
from datetime import datetime
import os
from dotenv import load_dotenv

# Import des services
from services.error_analysis_service import ErrorAnalysisService
from services.sql_generation_service import SQLGenerationService
from utils.validators import validate_error_analysis_request, validate_sql_generation_request
from utils.response_formatter import success_response, error_response

# Charger les variables d'environnement
load_dotenv()

# Initialisation de l'application Flask
app = Flask(__name__)

# Configuration CORS pour permettre les requêtes depuis Angular
CORS(app, origins=["http://localhost:4200"])

# Configuration
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['OPENAI_API_KEY'] = os.getenv('OPENAI_API_KEY')

# Initialisation des services
error_analysis_service = ErrorAnalysisService()
sql_generation_service = SQLGenerationService()


@app.route('/', methods=['GET'])
def health_check():
    """Point de santé de l'API"""
    return success_response({
        'message': 'Leoni Agents API is running',
        'version': '1.0.0',
        'services': ['error-analysis', 'sql-generation']
    })


@app.route('/api/error-analysis', methods=['POST'])
def analyze_errors():
    """
    Endpoint pour l'analyse d'erreurs
    Accepte les fichiers programme et erreur, retourne l'analyse détaillée
    """
    try:
        # Validation de la requête
        validation_result = validate_error_analysis_request(request.json)
        if not validation_result['valid']:
            return error_response(validation_result['message'], 400)
        
        data = request.json
        program_file = data['programFile']
        error_file = data['errorFile']
        
        # Analyse via le service
        result = error_analysis_service.analyze_files(program_file, error_file)
        
        return success_response(result)
        
    except Exception as e:
        app.logger.error(f"Erreur lors de l'analyse d'erreurs: {str(e)}")
        return error_response(f"Erreur interne du serveur: {str(e)}", 500)


@app.route('/api/error-analysis', methods=['GET'])
def get_error_analysis_info():
    """Informations sur l'agent d'analyse d'erreurs"""
    try:
        agent_info = error_analysis_service.get_agent_info()
        return success_response(agent_info)
    except Exception as e:
        return error_response(str(e), 500)


@app.route('/api/sql-generation', methods=['POST'])
def generate_sql():
    """
    Endpoint pour la génération SQL
    Accepte une spécification et génère le script SQL correspondant
    """
    try:
        # Validation de la requête
        validation_result = validate_sql_generation_request(request.json)
        if not validation_result['valid']:
            return error_response(validation_result['message'], 400)
        
        data = request.json
        
        # Génération via le service
        result = sql_generation_service.generate_sql(data)
        
        return success_response(result)
        
    except Exception as e:
        app.logger.error(f"Erreur lors de la génération SQL: {str(e)}")
        return error_response(f"Erreur interne du serveur: {str(e)}", 500)


@app.route('/api/sql-generation', methods=['GET'])
def get_sql_generation_info():
    """Informations sur l'agent de génération SQL"""
    try:
        agent_info = sql_generation_service.get_agent_info()
        return success_response(agent_info)
    except Exception as e:
        return error_response(str(e), 500)


@app.errorhandler(413)
def too_large(e):
    """Gestionnaire pour les fichiers trop volumineux"""
    return error_response("Fichier trop volumineux. Taille maximale: 16MB", 413)


@app.errorhandler(404)
def not_found(e):
    """Gestionnaire pour les routes non trouvées"""
    return error_response("Endpoint non trouvé", 404)


@app.errorhandler(500)
def internal_error(e):
    """Gestionnaire pour les erreurs internes"""
    return error_response("Erreur interne du serveur", 500)


if __name__ == '__main__':
    # Configuration pour le développement
    app.run(
        debug=True,
        host='0.0.0.0',
        port=5000
    )
