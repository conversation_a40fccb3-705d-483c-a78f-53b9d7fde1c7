{"version": 3, "file": "br.js", "sourceRoot": "", "sources": ["br.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC;QACvE,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC;QACvE,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC;QACxL,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,OAAO,KAAK,CAAC;QAC/B,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,QAAQ,EAAC,UAAU,EAAC,MAAM,EAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,MAAM,EAAC,QAAQ,EAAC,OAAO,EAAC,MAAM,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,IAAI,EAAC,MAAM,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,QAAQ,EAAC,OAAO,EAAC,KAAK,EAAC,UAAU,EAAC,QAAQ,EAAC,MAAM,EAAC,UAAU,EAAC,MAAM,EAAC,IAAI,EAAC,OAAO,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,aAAa,EAAC,YAAY,CAAC,EAAC,CAAC,EAAC,CAAC,oBAAoB,EAAC,mBAAmB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,eAAe,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,UAAU,EAAC,cAAc,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,MAAM,EAAC,EAAC,KAAK,EAAC,CAAC,IAAI,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,MAAM,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,MAAM,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,MAAM,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,MAAM,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n % 10 === 1 && !(n % 100 === 11 || (n % 100 === 71 || n % 100 === 91)))\n    return 1;\nif (n % 10 === 2 && !(n % 100 === 12 || (n % 100 === 72 || n % 100 === 92)))\n    return 2;\nif (n % 10 === Math.floor(n % 10) && (n % 10 >= 3 && n % 10 <= 4 || n % 10 === 9) && !(n % 100 >= 10 && n % 100 <= 19 || (n % 100 >= 70 && n % 100 <= 79 || n % 100 >= 90 && n % 100 <= 99)))\n    return 3;\nif (!(n === 0) && n % 1000000 === 0)\n    return 4;\nreturn 5;\n}\n\nexport default [\"br\",[[\"am\",\"gm\"],[\"A.M.\",\"G.M.\"],u],[[\"A.M.\",\"G.M.\"],u,u],[[\"Su\",\"L\",\"Mz\",\"Mc\",\"Y\",\"<PERSON>\",\"Sa\"],[\"<PERSON>\",\"<PERSON>n\",\"<PERSON>u.\",\"Mer.\",\"<PERSON>u\",\"Gwe.\",\"<PERSON>.\"],[\"<PERSON>\",\"<PERSON>n\",\"<PERSON>urzh\",\"<PERSON>rcʼher\",\"<PERSON>u\",\"<PERSON>er\",\"<PERSON><PERSON>\"],[\"<PERSON>\",\"<PERSON>n\",\"<PERSON>u.\",\"Mer.\",\"Yaou\",\"Gwe.\",\"Sad.\"]],u,[[\"01\",\"02\",\"03\",\"04\",\"05\",\"06\",\"07\",\"08\",\"09\",\"10\",\"11\",\"12\"],[\"Gen.\",\"Cʼhwe.\",\"Meur.\",\"Ebr.\",\"Mae\",\"Mezh.\",\"Goue.\",\"Eost\",\"Gwen.\",\"Here\",\"Du\",\"Kzu.\"],[\"Genver\",\"Cʼhwevrer\",\"Meurzh\",\"Ebrel\",\"Mae\",\"Mezheven\",\"Gouere\",\"Eost\",\"Gwengolo\",\"Here\",\"Du\",\"Kerzu\"]],u,[[\"a-raok J.K.\",\"goude J.K.\"],u,[\"a-raok Jezuz-Krist\",\"goude Jezuz-Krist\"]],1,[6,0],[\"dd/MM/y\",\"d MMM y\",\"d MMMM y\",\"EEEE d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",\"{1}, {0}\",\"{1} 'da' {0}\",u],[\",\",\" \",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0 %\",\"#,##0.00 ¤\",\"#E0\"],\"EUR\",\"€\",\"euro\",{\"AUD\":[\"$A\",\"$\"],\"BRL\":[u,\"R$\"],\"BYN\":[u,\"р.\"],\"CAD\":[\"$CA\",\"$\"],\"CNY\":[u,\"¥\"],\"EGP\":[u,\"£ E\"],\"GBP\":[\"£ RU\",\"£\"],\"HKD\":[\"$ HK\",\"$\"],\"ILS\":[u,\"₪\"],\"JPY\":[u,\"¥\"],\"KRW\":[u,\"₩\"],\"LBP\":[u,\"£L\"],\"NZD\":[\"$ ZN\",\"$\"],\"PHP\":[u,\"₱\"],\"RUR\":[u,\"р.\"],\"TOP\":[u,\"$ T\"],\"TWD\":[u,\"$\"],\"USD\":[\"$ SU\",\"$\"],\"VND\":[u,\"₫\"],\"XCD\":[u,\"$\"],\"XXX\":[]},\"ltr\", plural];\n"]}