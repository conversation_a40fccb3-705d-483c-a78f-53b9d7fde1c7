{"version": 3, "file": "saq.js", "sourceRoot": "", "sources": ["saq.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,SAAS,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,eAAe,EAAC,gBAAgB,EAAC,mBAAmB,EAAC,gBAAgB,EAAC,eAAe,EAAC,gBAAgB,EAAC,eAAe,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,aAAa,EAAC,eAAe,EAAC,eAAe,EAAC,iBAAiB,EAAC,cAAc,EAAC,aAAa,EAAC,cAAc,EAAC,eAAe,EAAC,cAAc,EAAC,eAAe,EAAC,mBAAmB,EAAC,qBAAqB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,kBAAkB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,oBAAoB,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"saq\",[[\"Tesiran\",\"Teip<PERSON>\"],u,u],u,[[\"A\",\"K\",\"O\",\"I\",\"I\",\"S\",\"K\"],[\"Are\",\"Kun\",\"Ong\",\"Ine\",\"Ile\",\"Sap\",\"Kwe\"],[\"Mderot ee are\",\"Mderot ee kuni\",\"Mderot ee ong’wan\",\"Mderot ee inet\",\"Mderot ee ile\",\"Mderot ee sapa\",\"Mderot ee kwe\"],[\"Are\",\"Kun\",\"Ong\",\"Ine\",\"Ile\",\"Sap\",\"Kwe\"]],u,[[\"O\",\"W\",\"O\",\"O\",\"I\",\"I\",\"S\",\"I\",\"S\",\"T\",\"T\",\"T\"],[\"Obo\",\"Waa\",\"Oku\",\"Ong\",\"Ime\",\"Ile\",\"Sap\",\"Isi\",\"Saa\",\"Tom\",\"Tob\",\"Tow\"],[\"Lapa le obo\",\"Lapa le waare\",\"Lapa le okuni\",\"Lapa le ong’wan\",\"Lapa le imet\",\"Lapa le ile\",\"Lapa le sapa\",\"Lapa le isiet\",\"Lapa le saal\",\"Lapa le tomon\",\"Lapa le tomon obo\",\"Lapa le tomon waare\"]],u,[[\"KK\",\"BK\"],u,[\"Kabla ya Christo\",\"Baada ya Christo\"]],0,[6,0],[\"dd/MM/y\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"KES\",\"Ksh\",\"Njilingi eel Kenya\",{\"JPY\":[\"JP¥\",\"¥\"],\"KES\":[\"Ksh\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}