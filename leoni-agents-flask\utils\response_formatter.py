"""
Formateurs de réponse pour l'API Flask
"""

from flask import jsonify
from datetime import datetime
from typing import Any, Dict


def success_response(data: Any, message: str = None) -> Dict[str, Any]:
    """
    Formate une réponse de succès
    
    Args:
        data: Données à retourner
        message: Message optionnel
        
    Returns:
        Réponse formatée
    """
    response = {
        'success': True,
        'data': data,
        'timestamp': datetime.now().isoformat()
    }
    
    if message:
        response['message'] = message
    
    return jsonify(response)


def error_response(message: str, status_code: int = 400, error_code: str = None) -> Dict[str, Any]:
    """
    Formate une réponse d'erreur
    
    Args:
        message: Message d'erreur
        status_code: Code de statut HTTP
        error_code: Code d'erreur spécifique
        
    Returns:
        Réponse d'erreur formatée
    """
    response = {
        'success': False,
        'error': message,
        'timestamp': datetime.now().isoformat()
    }
    
    if error_code:
        response['error_code'] = error_code
    
    return jsonify(response), status_code


def validation_error_response(validation_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Formate une réponse d'erreur de validation
    
    Args:
        validation_result: Résultat de la validation
        
    Returns:
        Réponse d'erreur formatée
    """
    return error_response(
        message=validation_result.get('message', 'Erreur de validation'),
        status_code=400,
        error_code='VALIDATION_ERROR'
    )


def not_found_response(resource: str = 'Ressource') -> Dict[str, Any]:
    """
    Formate une réponse 404
    
    Args:
        resource: Nom de la ressource non trouvée
        
    Returns:
        Réponse 404 formatée
    """
    return error_response(
        message=f'{resource} non trouvée',
        status_code=404,
        error_code='NOT_FOUND'
    )


def internal_error_response(message: str = 'Erreur interne du serveur') -> Dict[str, Any]:
    """
    Formate une réponse d'erreur interne
    
    Args:
        message: Message d'erreur
        
    Returns:
        Réponse d'erreur 500 formatée
    """
    return error_response(
        message=message,
        status_code=500,
        error_code='INTERNAL_ERROR'
    )


def paginated_response(data: list, page: int, per_page: int, total: int) -> Dict[str, Any]:
    """
    Formate une réponse paginée
    
    Args:
        data: Données de la page
        page: Numéro de page actuel
        per_page: Nombre d'éléments par page
        total: Nombre total d'éléments
        
    Returns:
        Réponse paginée formatée
    """
    total_pages = (total + per_page - 1) // per_page
    
    pagination_info = {
        'page': page,
        'per_page': per_page,
        'total': total,
        'total_pages': total_pages,
        'has_next': page < total_pages,
        'has_prev': page > 1
    }
    
    return success_response({
        'items': data,
        'pagination': pagination_info
    })
