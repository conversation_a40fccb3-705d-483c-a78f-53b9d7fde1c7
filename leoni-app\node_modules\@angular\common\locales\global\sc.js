/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\.?/, '').length;

if (i === 1 && v === 0)
    return 1;
return 5;
}
    global.ng.common.locales['sc'] = ["sc",[["m.","b."],["AM","PM"],u],[["AM","PM"],u,u],[["D","L","M","M","G","C","S"],["dom","lun","mar","mèr","giò","che","sàb"],["dom<PERSON>niga","lunis","martis","mè<PERSON>uris","giòbia","chenàbura","sàbadu"],["dom","lun","mar","mèr","giò","che","sàb"]],u,[["G","F","M","A","M","L","T","A","C","S","S","N"],["ghe","fre","mar","abr","maj","làm","trì","aus","cab","stG","stA","nad"],["ghennàrgiu","freàrgiu","martzu","abrile","maju","làmpadas","trìulas","austu","cabudanni","santugaine","santandria","nadale"]],u,[["a.C.","p.C."],u,["in antis de Cristu","a pustis de Cristu"]],1,[6,0],["dd/MM/y","d 'de' MMM y","d 'de' MMMM 'de' 'su' y","d 'de' MMMM 'de' 'su' y, 'de' EEEE"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1}, {0}",u,"{1} 'a' 'sas' {0}",u],[",",".",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","#,##0.00 ¤","#E0"],"EUR","€","èuro",{"JPY":["JP¥","¥"],"USD":["US$","$"],"XDR":["DIP"]},"ltr", plural, []];
  })(globalThis);
    