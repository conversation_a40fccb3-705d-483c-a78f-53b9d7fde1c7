{"version": 3, "file": "bs-Latn.js", "sourceRoot": "", "sources": ["bs-Latn.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,eAAe,CAAC,CAAC,CAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,cAAc,EAAC,SAAS,EAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nexport default [[[\"ponoć\",\"podne\",\"ujutro\",\"poslijepodne\",\"nave<PERSON>er\",\"po noći\"],u,u],u,[\"00:00\",\"12:00\",[\"04:00\",\"12:00\"],[\"12:00\",\"18:00\"],[\"18:00\",\"21:00\"],[\"21:00\",\"04:00\"]]];\n"]}