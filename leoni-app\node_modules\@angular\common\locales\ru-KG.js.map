{"version": 3, "file": "ru-KG.js", "sourceRoot": "", "sources": ["ru-KG.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC;IAEjG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC;QAC9C,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC;QAC/G,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;QAC3L,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,OAAO,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,aAAa,EAAC,aAAa,EAAC,SAAS,EAAC,OAAO,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,OAAO,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,SAAS,EAAC,UAAU,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,MAAM,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,UAAU,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,EAAC,MAAM,CAAC,EAAC,CAAC,UAAU,EAAC,OAAO,CAAC,EAAC,CAAC,uBAAuB,EAAC,uBAAuB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,cAAc,EAAC,eAAe,EAAC,qBAAqB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,UAAU,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,gBAAgB,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,MAAM,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length;\n\nif (v === 0 && (i % 10 === 1 && !(i % 100 === 11)))\n    return 1;\nif (v === 0 && (i % 10 === Math.floor(i % 10) && (i % 10 >= 2 && i % 10 <= 4) && !(i % 100 >= 12 && i % 100 <= 14)))\n    return 3;\nif (v === 0 && i % 10 === 0 || (v === 0 && (i % 10 === Math.floor(i % 10) && (i % 10 >= 5 && i % 10 <= 9)) || v === 0 && (i % 100 === Math.floor(i % 100) && (i % 100 >= 11 && i % 100 <= 14))))\n    return 4;\nreturn 5;\n}\n\nexport default [\"ru-KG\",[[\"AM\",\"PM\"],u,u],u,[[\"В\",\"П\",\"В\",\"С\",\"Ч\",\"П\",\"С\"],[\"вс\",\"пн\",\"вт\",\"ср\",\"чт\",\"пт\",\"сб\"],[\"воскресенье\",\"понедельник\",\"вторник\",\"среда\",\"четверг\",\"пятница\",\"суббота\"],[\"вс\",\"пн\",\"вт\",\"ср\",\"чт\",\"пт\",\"сб\"]],u,[[\"Я\",\"Ф\",\"М\",\"А\",\"М\",\"И\",\"И\",\"А\",\"С\",\"О\",\"Н\",\"Д\"],[\"янв.\",\"февр.\",\"мар.\",\"апр.\",\"мая\",\"июн.\",\"июл.\",\"авг.\",\"сент.\",\"окт.\",\"нояб.\",\"дек.\"],[\"января\",\"февраля\",\"марта\",\"апреля\",\"мая\",\"июня\",\"июля\",\"августа\",\"сентября\",\"октября\",\"ноября\",\"декабря\"]],[[\"Я\",\"Ф\",\"М\",\"А\",\"М\",\"И\",\"И\",\"А\",\"С\",\"О\",\"Н\",\"Д\"],[\"янв.\",\"февр.\",\"март\",\"апр.\",\"май\",\"июнь\",\"июль\",\"авг.\",\"сент.\",\"окт.\",\"нояб.\",\"дек.\"],[\"январь\",\"февраль\",\"март\",\"апрель\",\"май\",\"июнь\",\"июль\",\"август\",\"сентябрь\",\"октябрь\",\"ноябрь\",\"декабрь\"]],[[\"до н.э.\",\"н.э.\"],[\"до н. э.\",\"н. э.\"],[\"до Рождества Христова\",\"от Рождества Христова\"]],1,[6,0],[\"dd.MM.y\",\"d MMM y 'г'.\",\"d MMMM y 'г'.\",\"EEEE, d MMMM y 'г'.\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1}, {0}\",u,u,u],[\",\",\" \",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"не число\",\":\"],[\"#,##0.###\",\"#,##0 %\",\"#,##0.00 ¤\",\"#E0\"],\"KGS\",\"сом\",\"киргизский сом\",{\"BYN\":[u,\"р.\"],\"GEL\":[u,\"ლ\"],\"KGS\":[\"сом\"],\"PHP\":[u,\"₱\"],\"RON\":[u,\"L\"],\"RUB\":[\"₽\"],\"RUR\":[\"р.\"],\"THB\":[\"฿\"],\"TMT\":[\"ТМТ\"],\"TWD\":[\"NT$\"],\"UAH\":[\"₴\"],\"XXX\":[\"XXXX\"]},\"ltr\", plural];\n"]}