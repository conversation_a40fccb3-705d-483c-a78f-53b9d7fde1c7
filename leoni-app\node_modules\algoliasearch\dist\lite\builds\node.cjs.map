{"version": 3, "sources": ["../../../lite/builds/node.ts", "../../../lite/src/liteClient.ts"], "sourcesContent": ["// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nexport type LiteClient = ReturnType<typeof createLiteClient>;\n\nimport { createHttpRequester } from '@algolia/requester-node-http';\n\nimport { createMemoryCache, createNullCache, createNullLogger } from '@algolia/client-common';\n\nimport type { ClientOptions } from '@algolia/client-common';\n\nimport { createLiteClient } from '../src/liteClient';\n\nexport { apiClientVersion } from '../src/liteClient';\n\nexport * from '../model';\n\nexport function liteClient(appId: string, apiKey: string, options?: ClientOptions | undefined): LiteClient {\n  if (!appId || typeof appId !== 'string') {\n    throw new Error('`appId` is missing.');\n  }\n\n  if (!apiKey || typeof apiKey !== 'string') {\n    throw new Error('`apiKey` is missing.');\n  }\n\n  return createLiteClient({\n    appId,\n    apiKey,\n    timeouts: {\n      connect: 2000,\n      read: 5000,\n      write: 30000,\n    },\n    logger: createNullLogger(),\n    requester: createHttpRequester(),\n    algoliaAgents: [{ segment: 'Node.js', version: process.versions.node }],\n    responsesCache: createNullCache(),\n    requestsCache: createNullCache(),\n    hostsCache: createMemoryCache(),\n    ...options,\n  });\n}\n", "// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nimport type {\n  CreateClientOptions,\n  Headers,\n  Host,\n  QueryParameters,\n  Request,\n  RequestOptions,\n} from '@algolia/client-common';\nimport { createAuth, createTransporter, getAlgoliaAgent, shuffle } from '@algolia/client-common';\n\nimport type { GetRecommendationsParams } from '../model/getRecommendationsParams';\nimport type { GetRecommendationsResponse } from '../model/getRecommendationsResponse';\nimport type { SearchMethodParams } from '../model/searchMethodParams';\nimport type { SearchResponses } from '../model/searchResponses';\n\nimport type {\n  CustomPostProps,\n  LegacyGetRecommendationsParams,\n  LegacySearchMethodProps,\n} from '../model/clientMethodProps';\n\nimport type { SearchForFacetValuesResponse } from '../model/searchForFacetValuesResponse';\nimport type { SearchResponse } from '../model/searchResponse';\n\nexport const apiClientVersion = '5.32.0';\n\nfunction getDefaultHosts(appId: string): Host[] {\n  return (\n    [\n      {\n        url: `${appId}-dsn.algolia.net`,\n        accept: 'read',\n        protocol: 'https',\n      },\n      {\n        url: `${appId}.algolia.net`,\n        accept: 'write',\n        protocol: 'https',\n      },\n    ] as Host[]\n  ).concat(\n    shuffle([\n      {\n        url: `${appId}-1.algolianet.com`,\n        accept: 'readWrite',\n        protocol: 'https',\n      },\n      {\n        url: `${appId}-2.algolianet.com`,\n        accept: 'readWrite',\n        protocol: 'https',\n      },\n      {\n        url: `${appId}-3.algolianet.com`,\n        accept: 'readWrite',\n        protocol: 'https',\n      },\n    ]),\n  );\n}\n\nexport function createLiteClient({\n  appId: appIdOption,\n  apiKey: apiKeyOption,\n  authMode,\n  algoliaAgents,\n  ...options\n}: CreateClientOptions) {\n  const auth = createAuth(appIdOption, apiKeyOption, authMode);\n  const transporter = createTransporter({\n    hosts: getDefaultHosts(appIdOption),\n    ...options,\n    algoliaAgent: getAlgoliaAgent({\n      algoliaAgents,\n      client: 'Lite',\n      version: apiClientVersion,\n    }),\n    baseHeaders: {\n      'content-type': 'text/plain',\n      ...auth.headers(),\n      ...options.baseHeaders,\n    },\n    baseQueryParameters: {\n      ...auth.queryParameters(),\n      ...options.baseQueryParameters,\n    },\n  });\n\n  return {\n    transporter,\n\n    /**\n     * The `appId` currently in use.\n     */\n    appId: appIdOption,\n\n    /**\n     * The `apiKey` currently in use.\n     */\n    apiKey: apiKeyOption,\n\n    /**\n     * Clears the cache of the transporter for the `requestsCache` and `responsesCache` properties.\n     */\n    clearCache(): Promise<void> {\n      return Promise.all([transporter.requestsCache.clear(), transporter.responsesCache.clear()]).then(() => undefined);\n    },\n\n    /**\n     * Get the value of the `algoliaAgent`, used by our libraries internally and telemetry system.\n     */\n    get _ua(): string {\n      return transporter.algoliaAgent.value;\n    },\n\n    /**\n     * Adds a `segment` to the `x-algolia-agent` sent with every requests.\n     *\n     * @param segment - The algolia agent (user-agent) segment to add.\n     * @param version - The version of the agent.\n     */\n    addAlgoliaAgent(segment: string, version?: string | undefined): void {\n      transporter.algoliaAgent.add({ segment, version });\n    },\n\n    /**\n     * Helper method to switch the API key used to authenticate the requests.\n     *\n     * @param params - Method params.\n     * @param params.apiKey - The new API Key to use.\n     */\n    setClientApiKey({ apiKey }: { apiKey: string }): void {\n      if (!authMode || authMode === 'WithinHeaders') {\n        transporter.baseHeaders['x-algolia-api-key'] = apiKey;\n      } else {\n        transporter.baseQueryParameters['x-algolia-api-key'] = apiKey;\n      }\n    },\n\n    /**\n     * Helper: calls the `search` method but with certainty that we will only request Algolia records (hits) and not facets.\n     * Disclaimer: We don't assert that the parameters you pass to this method only contains `hits` requests to prevent impacting search performances, this helper is purely for typing purposes.\n     *\n     * @summary Search multiple indices for `hits`.\n     * @param searchMethodParams - Query requests and strategies. Results will be received in the same order as the queries.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchForHits<T>(\n      searchMethodParams: LegacySearchMethodProps | SearchMethodParams,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<{ results: Array<SearchResponse<T>> }> {\n      return this.search(searchMethodParams, requestOptions) as Promise<{ results: Array<SearchResponse<T>> }>;\n    },\n\n    /**\n     * Helper: calls the `search` method but with certainty that we will only request Algolia facets and not records (hits).\n     * Disclaimer: We don't assert that the parameters you pass to this method only contains `facets` requests to prevent impacting search performances, this helper is purely for typing purposes.\n     *\n     * @summary Search multiple indices for `facets`.\n     * @param searchMethodParams - Query requests and strategies. Results will be received in the same order as the queries.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchForFacets(\n      searchMethodParams: LegacySearchMethodProps | SearchMethodParams,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<{ results: Array<SearchForFacetValuesResponse> }> {\n      return this.search(searchMethodParams, requestOptions) as Promise<{\n        results: Array<SearchForFacetValuesResponse>;\n      }>;\n    },\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customPost - The customPost object.\n     * @param customPost.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customPost.parameters - Query parameters to apply to the current query.\n     * @param customPost.body - Parameters to send with the custom request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customPost(\n      { path, parameters, body }: CustomPostProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customPost`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body ? body : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves recommendations from selected AI models.\n     *\n     * Required API Key ACLs:\n     *  - search\n     * @param getRecommendationsParams - The getRecommendationsParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getRecommendations(\n      getRecommendationsParams: GetRecommendationsParams | LegacyGetRecommendationsParams,\n      requestOptions?: RequestOptions,\n    ): Promise<GetRecommendationsResponse> {\n      if (getRecommendationsParams && Array.isArray(getRecommendationsParams)) {\n        const newSignatureRequest: GetRecommendationsParams = {\n          requests: getRecommendationsParams,\n        };\n\n        getRecommendationsParams = newSignatureRequest;\n      }\n\n      if (!getRecommendationsParams) {\n        throw new Error('Parameter `getRecommendationsParams` is required when calling `getRecommendations`.');\n      }\n\n      if (!getRecommendationsParams.requests) {\n        throw new Error('Parameter `getRecommendationsParams.requests` is required when calling `getRecommendations`.');\n      }\n\n      const requestPath = '/1/indexes/*/recommendations';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: getRecommendationsParams,\n        useReadTransporter: true,\n        cacheable: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Sends multiple search requests to one or more indices.  This can be useful in these cases:  - Different indices for different purposes, such as, one index for products, another one for marketing content. - Multiple searches to the same index—for example, with different filters.  Use the helper `searchForHits` or `searchForFacets` to get the results in a more convenient format, if you already know the return type you want.\n     *\n     * Required API Key ACLs:\n     *  - search\n     * @param searchMethodParams - Muli-search request body. Results are returned in the same order as the requests.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    search<T>(\n      searchMethodParams: SearchMethodParams | LegacySearchMethodProps,\n      requestOptions?: RequestOptions,\n    ): Promise<SearchResponses<T>> {\n      if (searchMethodParams && Array.isArray(searchMethodParams)) {\n        const newSignatureRequest: SearchMethodParams = {\n          requests: searchMethodParams.map(({ params, ...legacyRequest }) => {\n            if (legacyRequest.type === 'facet') {\n              return {\n                ...legacyRequest,\n                ...params,\n                type: 'facet',\n              };\n            }\n\n            return {\n              ...legacyRequest,\n              ...params,\n              facet: undefined,\n              maxFacetHits: undefined,\n              facetQuery: undefined,\n            };\n          }),\n        };\n\n        searchMethodParams = newSignatureRequest;\n      }\n\n      if (!searchMethodParams) {\n        throw new Error('Parameter `searchMethodParams` is required when calling `search`.');\n      }\n\n      if (!searchMethodParams.requests) {\n        throw new Error('Parameter `searchMethodParams.requests` is required when calling `search`.');\n      }\n\n      const requestPath = '/1/indexes/*/queries';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: searchMethodParams,\n        useReadTransporter: true,\n        cacheable: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,iCAAoC;AAEpC,IAAAA,wBAAqE;;;ACIrE,2BAAwE;AAgBjE,IAAM,mBAAmB;AAEhC,SAAS,gBAAgB,OAAuB;AAC9C,SACE;AAAA,IACE;AAAA,MACE,KAAK,GAAG,KAAK;AAAA,MACb,QAAQ;AAAA,MACR,UAAU;AAAA,IACZ;AAAA,IACA;AAAA,MACE,KAAK,GAAG,KAAK;AAAA,MACb,QAAQ;AAAA,MACR,UAAU;AAAA,IACZ;AAAA,EACF,EACA;AAAA,QACA,8BAAQ;AAAA,MACN;AAAA,QACE,KAAK,GAAG,KAAK;AAAA,QACb,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,KAAK,GAAG,KAAK;AAAA,QACb,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,KAAK,GAAG,KAAK;AAAA,QACb,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEO,SAAS,iBAAiB;AAAA,EAC/B,OAAO;AAAA,EACP,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA,GAAG;AACL,GAAwB;AACtB,QAAM,WAAO,iCAAW,aAAa,cAAc,QAAQ;AAC3D,QAAM,kBAAc,wCAAkB;AAAA,IACpC,OAAO,gBAAgB,WAAW;AAAA,IAClC,GAAG;AAAA,IACH,kBAAc,sCAAgB;AAAA,MAC5B;AAAA,MACA,QAAQ;AAAA,MACR,SAAS;AAAA,IACX,CAAC;AAAA,IACD,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,GAAG,KAAK,QAAQ;AAAA,MAChB,GAAG,QAAQ;AAAA,IACb;AAAA,IACA,qBAAqB;AAAA,MACnB,GAAG,KAAK,gBAAgB;AAAA,MACxB,GAAG,QAAQ;AAAA,IACb;AAAA,EACF,CAAC;AAED,SAAO;AAAA,IACL;AAAA;AAAA;AAAA;AAAA,IAKA,OAAO;AAAA;AAAA;AAAA;AAAA,IAKP,QAAQ;AAAA;AAAA;AAAA;AAAA,IAKR,aAA4B;AAC1B,aAAO,QAAQ,IAAI,CAAC,YAAY,cAAc,MAAM,GAAG,YAAY,eAAe,MAAM,CAAC,CAAC,EAAE,KAAK,MAAM,MAAS;AAAA,IAClH;AAAA;AAAA;AAAA;AAAA,IAKA,IAAI,MAAc;AAChB,aAAO,YAAY,aAAa;AAAA,IAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,gBAAgB,SAAiB,SAAoC;AACnE,kBAAY,aAAa,IAAI,EAAE,SAAS,QAAQ,CAAC;AAAA,IACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,gBAAgB,EAAE,OAAO,GAA6B;AACpD,UAAI,CAAC,YAAY,aAAa,iBAAiB;AAC7C,oBAAY,YAAY,mBAAmB,IAAI;AAAA,MACjD,OAAO;AACL,oBAAY,oBAAoB,mBAAmB,IAAI;AAAA,MACzD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,cACE,oBACA,gBACgD;AAChD,aAAO,KAAK,OAAO,oBAAoB,cAAc;AAAA,IACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,gBACE,oBACA,gBAC2D;AAC3D,aAAO,KAAK,OAAO,oBAAoB,cAAc;AAAA,IAGvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,WACE,EAAE,MAAM,YAAY,KAAK,GACzB,gBACkC;AAClC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,yDAAyD;AAAA,MAC3E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,OAAO,OAAO,CAAC;AAAA,MACvB;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,mBACE,0BACA,gBACqC;AACrC,UAAI,4BAA4B,MAAM,QAAQ,wBAAwB,GAAG;AACvE,cAAM,sBAAgD;AAAA,UACpD,UAAU;AAAA,QACZ;AAEA,mCAA2B;AAAA,MAC7B;AAEA,UAAI,CAAC,0BAA0B;AAC7B,cAAM,IAAI,MAAM,qFAAqF;AAAA,MACvG;AAEA,UAAI,CAAC,yBAAyB,UAAU;AACtC,cAAM,IAAI,MAAM,8FAA8F;AAAA,MAChH;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,oBAAoB;AAAA,QACpB,WAAW;AAAA,MACb;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,OACE,oBACA,gBAC6B;AAC7B,UAAI,sBAAsB,MAAM,QAAQ,kBAAkB,GAAG;AAC3D,cAAM,sBAA0C;AAAA,UAC9C,UAAU,mBAAmB,IAAI,CAAC,EAAE,QAAQ,GAAG,cAAc,MAAM;AACjE,gBAAI,cAAc,SAAS,SAAS;AAClC,qBAAO;AAAA,gBACL,GAAG;AAAA,gBACH,GAAG;AAAA,gBACH,MAAM;AAAA,cACR;AAAA,YACF;AAEA,mBAAO;AAAA,cACL,GAAG;AAAA,cACH,GAAG;AAAA,cACH,OAAO;AAAA,cACP,cAAc;AAAA,cACd,YAAY;AAAA,YACd;AAAA,UACF,CAAC;AAAA,QACH;AAEA,6BAAqB;AAAA,MACvB;AAEA,UAAI,CAAC,oBAAoB;AACvB,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AAEA,UAAI,CAAC,mBAAmB,UAAU;AAChC,cAAM,IAAI,MAAM,4EAA4E;AAAA,MAC9F;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,oBAAoB;AAAA,QACpB,WAAW;AAAA,MACb;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA,EACF;AACF;;;ADrSO,SAAS,WAAW,OAAe,QAAgB,SAAiD;AACzG,MAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACvC,UAAM,IAAI,MAAM,qBAAqB;AAAA,EACvC;AAEA,MAAI,CAAC,UAAU,OAAO,WAAW,UAAU;AACzC,UAAM,IAAI,MAAM,sBAAsB;AAAA,EACxC;AAEA,SAAO,iBAAiB;AAAA,IACtB;AAAA,IACA;AAAA,IACA,UAAU;AAAA,MACR,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,YAAQ,wCAAiB;AAAA,IACzB,eAAW,gDAAoB;AAAA,IAC/B,eAAe,CAAC,EAAE,SAAS,WAAW,SAAS,QAAQ,SAAS,KAAK,CAAC;AAAA,IACtE,oBAAgB,uCAAgB;AAAA,IAChC,mBAAe,uCAAgB;AAAA,IAC/B,gBAAY,yCAAkB;AAAA,IAC9B,GAAG;AAAA,EACL,CAAC;AACH;", "names": ["import_client_common"]}