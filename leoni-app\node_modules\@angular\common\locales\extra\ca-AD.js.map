{"version": 3, "file": "ca-AD.js", "sourceRoot": "", "sources": ["ca-AD.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,eAAe,CAAC,CAAC,CAAC,UAAU,EAAC,MAAM,EAAC,MAAM,EAAC,IAAI,EAAC,OAAO,EAAC,QAAQ,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,UAAU,EAAC,MAAM,EAAC,QAAQ,EAAC,OAAO,EAAC,QAAQ,EAAC,KAAK,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,UAAU,EAAC,UAAU,EAAC,MAAM,EAAC,QAAQ,EAAC,OAAO,EAAC,QAAQ,EAAC,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nexport default [[[\"mitjanit\",\"mat.\",\"matí\",\"md\",\"tarda\",\"vespre\",\"nit\"],[\"mitjanit\",\"matinada\",\"matí\",\"migdia\",\"tarda\",\"vespre\",\"nit\"],u],[[\"mitjanit\",\"matinada\",\"matí\",\"migdia\",\"tarda\",\"vespre\",\"nit\"],u,u],[\"00:00\",[\"00:00\",\"06:00\"],[\"06:00\",\"12:00\"],[\"12:00\",\"13:00\"],[\"13:00\",\"19:00\"],[\"19:00\",\"21:00\"],[\"21:00\",\"24:00\"]]];\n"]}