# 🤖 Leoni Agents - Version Finale

**Architecture Flask + Angular** pour les agents IA spécialisés de Leoni.

## 🎯 Vue d'ensemble

Leoni Agents est une application complète d'intelligence artificielle proposant deux agents spécialisés :

- **🐛 Agent d'Analyse d'Erreurs** : Analyse automatique des fichiers de programme et d'erreur
- **🗄️ Agent de Génération SQL** : Génération de scripts SQL à partir de spécifications fonctionnelles

## 🏗️ Architecture

```
Leoni Agents/
├── 🔧 leoni-agents-flask/     # Backend API Flask
├── 🅰️ leoni-agents-angular/   # Frontend Angular
├── 🐳 docker-compose.yml      # Orchestration Docker
├── 🚀 start.sh               # Script de démarrage
└── 📚 README.md              # Documentation
```

### Stack Technologique

**Backend (Flask)**
- Python 3.11
- Flask 3.0
- OpenAI GPT-4
- CORS configuré

**Frontend (Angular)**
- Angular 17
- Material Design
- TypeScript
- Responsive Design

## 🚀 Démarrage Rapide

### Option 1: Script Automatique (Recommandé)
```bash
chmod +x start.sh
./start.sh
```

### Option 2: Docker Compose
```bash
# 1. Configurer la clé OpenAI
echo "OPENAI_API_KEY=your_key_here" > .env

# 2. Démarrer avec Docker
docker-compose up --build
```

### Option 3: Développement Local

**Backend Flask:**
```bash
cd leoni-agents-flask
python -m venv venv
source venv/bin/activate  # Linux/Mac
# ou venv\Scripts\activate  # Windows
pip install -r requirements.txt
cp .env.example .env
# Éditer .env avec votre clé OpenAI
python app.py
```

**Frontend Angular:**
```bash
cd leoni-agents-angular
npm install
npm start
```

## 🌐 Accès aux Applications

- **Frontend Angular**: http://localhost:4200
- **Backend Flask**: http://localhost:5000
- **API Documentation**: http://localhost:5000/api/

## 📋 Prérequis

### Pour Docker
- Docker 20.10+
- Docker Compose 2.0+

### Pour le Développement Local
- Python 3.8+
- Node.js 18+
- npm ou yarn

### Configuration Requise
- **Clé OpenAI** (obligatoire)
- 4GB RAM minimum
- 2GB espace disque

## 🔧 Configuration

### Variables d'Environnement

Créez un fichier `.env` à la racine :

```env
# Clé API OpenAI (obligatoire)
OPENAI_API_KEY=your_openai_api_key_here

# Configuration Flask (optionnel)
FLASK_ENV=development
FLASK_DEBUG=True

# Configuration CORS (optionnel)
CORS_ORIGINS=http://localhost:4200
```

## 📡 API Endpoints

### Analyse d'Erreurs
- `POST /api/error-analysis` - Analyser les erreurs
- `GET /api/error-analysis` - Informations sur l'agent

### Génération SQL
- `POST /api/sql-generation` - Générer du SQL
- `GET /api/sql-generation` - Informations sur l'agent

### Santé
- `GET /` - Point de santé de l'API

## 🎨 Fonctionnalités

### Agent d'Analyse d'Erreurs
- ✅ Upload de fichiers (drag & drop)
- ✅ Analyse automatique avec IA
- ✅ Localisation précise des erreurs
- ✅ Solutions détaillées
- ✅ Contexte de code
- ✅ Export des rapports

### Agent de Génération SQL
- ✅ Spécifications textuelles ou fichiers
- ✅ Support multi-SGBD (MySQL, PostgreSQL, SQLite, SQL Server)
- ✅ Génération optimisée
- ✅ Commentaires explicatifs
- ✅ Export des scripts

### Interface Utilisateur
- ✅ Design Material moderne
- ✅ Responsive (mobile, tablette, desktop)
- ✅ Animations fluides
- ✅ Notifications en temps réel
- ✅ Thème corporate Leoni

## 🧪 Tests

### Backend
```bash
cd leoni-agents-flask
pytest tests/
```

### Frontend
```bash
cd leoni-agents-angular
npm test
```

## 🚀 Déploiement

### Production avec Docker
```bash
# Build et déploiement
docker-compose -f docker-compose.prod.yml up --build -d

# Monitoring
docker-compose logs -f
```

### Déploiement Cloud
- **Backend**: Compatible Heroku, AWS, Google Cloud
- **Frontend**: Compatible Netlify, Vercel, AWS S3

## 📊 Monitoring et Logs

### Logs Backend
```bash
# Logs en temps réel
docker-compose logs -f backend

# Logs spécifiques
tail -f leoni-agents-flask/logs/app.log
```

### Métriques Frontend
- Performance via Angular DevTools
- Monitoring des erreurs JavaScript
- Analytics d'utilisation

## 🔒 Sécurité

### Mesures Implémentées
- ✅ Validation des entrées
- ✅ Limitation de taille des fichiers
- ✅ CORS configuré
- ✅ Headers de sécurité
- ✅ Gestion des erreurs

### Recommandations Production
- Utiliser HTTPS
- Configurer un reverse proxy (Nginx)
- Limiter les taux de requêtes
- Monitoring des logs de sécurité

## 🤝 Contribution

### Structure du Code
- **Backend**: Architecture modulaire avec services
- **Frontend**: Composants Angular standalone
- **Tests**: Couverture complète
- **Documentation**: Inline et README

### Standards
- Code formaté avec Prettier/Black
- Linting avec ESLint/Flake8
- Tests unitaires obligatoires
- Documentation des API

## 📞 Support

### Problèmes Courants

**Erreur de clé OpenAI**
```bash
# Vérifier la configuration
grep OPENAI_API_KEY .env
```

**Port déjà utilisé**
```bash
# Changer les ports dans docker-compose.yml
ports:
  - "5001:5000"  # Backend
  - "4201:80"    # Frontend
```

**Problème de CORS**
```bash
# Vérifier la configuration CORS dans app.py
CORS(app, origins=["http://localhost:4200"])
```

## 📈 Roadmap

### Version 2.0 (Prévue)
- [ ] Authentification utilisateur
- [ ] Historique des analyses
- [ ] API REST étendue
- [ ] Support de plus de langages
- [ ] Interface d'administration

### Améliorations Continues
- [ ] Optimisation des performances
- [ ] Tests d'intégration
- [ ] Documentation API Swagger
- [ ] Monitoring avancé

## 📄 Licence

Propriété de Leoni. Tous droits réservés.

---

**🎉 Félicitations ! Votre application Leoni Agents est prête à l'emploi !**

Pour toute question ou support, consultez la documentation dans chaque dossier ou contactez l'équipe de développement.
