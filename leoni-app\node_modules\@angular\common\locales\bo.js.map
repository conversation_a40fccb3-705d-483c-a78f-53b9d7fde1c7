{"version": 3, "file": "bo.js", "sourceRoot": "", "sources": ["bo.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,SAAS,EAAC,UAAU,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,UAAU,EAAC,QAAQ,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,CAAC,EAAC,CAAC,WAAW,EAAC,WAAW,EAAC,cAAc,EAAC,YAAY,EAAC,aAAa,EAAC,YAAY,EAAC,aAAa,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,UAAU,EAAC,QAAQ,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,YAAY,EAAC,aAAa,EAAC,aAAa,EAAC,YAAY,EAAC,WAAW,EAAC,aAAa,EAAC,aAAa,EAAC,cAAc,EAAC,YAAY,EAAC,YAAY,EAAC,iBAAiB,EAAC,iBAAiB,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,aAAa,EAAC,cAAc,EAAC,cAAc,EAAC,aAAa,EAAC,YAAY,EAAC,cAAc,EAAC,cAAc,EAAC,eAAe,EAAC,aAAa,EAAC,aAAa,EAAC,kBAAkB,EAAC,kBAAkB,CAAC,CAAC,EAAC,CAAC,CAAC,eAAe,EAAC,UAAU,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,iBAAiB,EAAC,wBAAwB,EAAC,sBAAsB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,QAAQ,EAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"bo\",[[\"སྔ་དྲོ་\",\"ཕྱི་དྲོ་\"],u,u],u,[[\"ཉི\",\"ཟླ\",\"མིག\",\"ལྷག\",\"ཕུར\",\"སངས\",\"སྤེན\"],[\"ཉི་མ་\",\"ཟླ་བ་\",\"མིག་དམར་\",\"ལྷག་པ་\",\"ཕུར་བུ་\",\"པ་སངས་\",\"སྤེན་པ་\"],[\"གཟའ་ཉི་མ་\",\"གཟའ་ཟླ་བ་\",\"གཟའ་མིག་དམར་\",\"གཟའ་ལྷག་པ་\",\"གཟའ་ཕུར་བུ་\",\"གཟའ་པ་སངས་\",\"གཟའ་སྤེན་པ་\"],[\"ཉི་མ་\",\"ཟླ་བ་\",\"མིག་དམར་\",\"ལྷག་པ་\",\"ཕུར་བུ་\",\"པ་སངས་\",\"སྤེན་པ་\"]],u,[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"ཟླ་༡\",\"ཟླ་༢\",\"ཟླ་༣\",\"ཟླ་༤\",\"ཟླ་༥\",\"ཟླ་༦\",\"ཟླ་༧\",\"ཟླ་༨\",\"ཟླ་༩\",\"ཟླ་༡༠\",\"ཟླ་༡༡\",\"ཟླ་༡༢\"],[\"ཟླ་བ་དང་པོ\",\"ཟླ་བ་གཉིས་པ\",\"ཟླ་བ་གསུམ་པ\",\"ཟླ་བ་བཞི་པ\",\"ཟླ་བ་ལྔ་པ\",\"ཟླ་བ་དྲུག་པ\",\"ཟླ་བ་བདུན་པ\",\"ཟླ་བ་བརྒྱད་པ\",\"ཟླ་བ་དགུ་པ\",\"ཟླ་བ་བཅུ་པ\",\"ཟླ་བ་བཅུ་གཅིག་པ\",\"ཟླ་བ་བཅུ་གཉིས་པ\"]],[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"ཟླ་༡\",\"ཟླ་༢\",\"ཟླ་༣\",\"ཟླ་༤\",\"ཟླ་༥\",\"ཟླ་༦\",\"ཟླ་༧\",\"ཟླ་༨\",\"ཟླ་༩\",\"ཟླ་༡༠\",\"ཟླ་༡༡\",\"ཟླ་༡༢\"],[\"ཟླ་བ་དང་པོ་\",\"ཟླ་བ་གཉིས་པ་\",\"ཟླ་བ་གསུམ་པ་\",\"ཟླ་བ་བཞི་པ་\",\"ཟླ་བ་ལྔ་པ་\",\"ཟླ་བ་དྲུག་པ་\",\"ཟླ་བ་བདུན་པ་\",\"ཟླ་བ་བརྒྱད་པ་\",\"ཟླ་བ་དགུ་པ་\",\"ཟླ་བ་བཅུ་པ་\",\"ཟླ་བ་བཅུ་གཅིག་པ་\",\"ཟླ་བ་བཅུ་གཉིས་པ་\"]],[[\"སྤྱི་ལོ་སྔོན་\",\"སྤྱི་ལོ་\"],u,u],0,[6,0],[\"y-MM-dd\",\"y ལོའི་MMMཚེས་d\",\"སྤྱི་ལོ་y MMMMའི་ཚེས་d\",\"y MMMMའི་ཚེས་d, EEEE\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤ #,##0.00\",\"#E0\"],\"CNY\",\"¥\",\"ཡུ་ཨན་\",{\"CNY\":[\"¥\"],\"JPY\":[\"JP¥\",\"¥\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}