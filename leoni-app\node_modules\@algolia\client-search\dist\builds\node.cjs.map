{"version": 3, "sources": ["../../builds/node.ts", "../../src/searchClient.ts"], "sourcesContent": ["// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nexport type SearchClient = ReturnType<typeof createSearchClient> & SearchClientNodeHelpers;\n\nimport { createHmac } from 'node:crypto';\n\nimport { createHttpRequester } from '@algolia/requester-node-http';\n\nimport {\n  IndexAlreadyExistsError,\n  IndexNotFoundError,\n  IndicesInSameAppError,\n  createMemoryCache,\n  createNullCache,\n  createNullLogger,\n  serializeQueryParameters,\n} from '@algolia/client-common';\n\nimport type { ClientOptions, RequestOptions } from '@algolia/client-common';\n\nimport { createSearchClient } from '../src/searchClient';\n\nexport { apiClientVersion } from '../src/searchClient';\n\nexport * from '../model';\n\nimport type {\n  AccountCopyIndexOptions,\n  BrowseResponse,\n  GenerateSecuredApiKeyOptions,\n  GetSecuredApiKeyRemainingValidityOptions,\n  SearchClientNodeHelpers,\n  SearchRulesResponse,\n  SearchSynonymsResponse,\n  UpdatedAtResponse,\n} from '../model';\n\nexport function searchClient(appId: string, apiKey: string, options?: ClientOptions | undefined): SearchClient {\n  if (!appId || typeof appId !== 'string') {\n    throw new Error('`appId` is missing.');\n  }\n\n  if (!apiKey || typeof apiKey !== 'string') {\n    throw new Error('`apiKey` is missing.');\n  }\n\n  return {\n    ...createSearchClient({\n      appId,\n      apiKey,\n      timeouts: {\n        connect: 2000,\n        read: 5000,\n        write: 30000,\n      },\n      logger: createNullLogger(),\n      requester: createHttpRequester(),\n      algoliaAgents: [{ segment: 'Node.js', version: process.versions.node }],\n      responsesCache: createNullCache(),\n      requestsCache: createNullCache(),\n      hostsCache: createMemoryCache(),\n      ...options,\n    }),\n    /**\n     * Helper: Generates a secured API key based on the given `parentApiKey` and given `restrictions`.\n     *\n     * @summary Helper: Generates a secured API key based on the given `parentApiKey` and given `restrictions`.\n     * @param generateSecuredApiKey - The `generateSecuredApiKey` object.\n     * @param generateSecuredApiKey.parentApiKey - The base API key from which to generate the new secured one.\n     * @param generateSecuredApiKey.restrictions - A set of properties defining the restrictions of the secured API key.\n     */\n    generateSecuredApiKey: ({ parentApiKey, restrictions = {} }: GenerateSecuredApiKeyOptions): string => {\n      let mergedRestrictions = restrictions;\n      if (restrictions.searchParams) {\n        // merge searchParams with the root restrictions\n        mergedRestrictions = {\n          ...restrictions,\n          ...restrictions.searchParams,\n        };\n\n        delete mergedRestrictions.searchParams;\n      }\n\n      mergedRestrictions = Object.keys(mergedRestrictions)\n        .sort()\n        .reduce(\n          (acc, key) => {\n            acc[key] = (mergedRestrictions as any)[key];\n            return acc;\n          },\n          {} as Record<string, unknown>,\n        );\n\n      const queryParameters = serializeQueryParameters(mergedRestrictions);\n      return Buffer.from(\n        createHmac('sha256', parentApiKey).update(queryParameters).digest('hex') + queryParameters,\n      ).toString('base64');\n    },\n\n    /**\n     * Helper: Copies the given `sourceIndexName` records, rules and synonyms to an other Algolia application for the given `destinationIndexName`.\n     * See https://api-clients-automation.netlify.app/docs/add-new-api-client#5-helpers for implementation details.\n     *\n     * @summary Helper: Copies the given `sourceIndexName` records, rules and synonyms to an other Algolia application for the given `destinationIndexName`.\n     * @param accountCopyIndex - The `accountCopyIndex` object.\n     * @param accountCopyIndex.sourceIndexName - The name of the index to copy.\n     * @param accountCopyIndex.destinationAppID - The application ID to write the index to.\n     * @param accountCopyIndex.destinationApiKey - The API Key of the `destinationAppID` to write the index to, must have write ACLs.\n     * @param accountCopyIndex.destinationIndexName - The name of the index to write the copied index to.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `setSettings`, `saveRules`, `saveSynonyms` and `saveObjects` method and merged with the transporter requestOptions.\n     */\n    async accountCopyIndex(\n      { sourceIndexName, destinationAppID, destinationApiKey, destinationIndexName }: AccountCopyIndexOptions,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<void> {\n      const responses: Array<{ taskID: UpdatedAtResponse['taskID'] }> = [];\n\n      if (this.appId === destinationAppID) {\n        throw new IndicesInSameAppError();\n      }\n\n      if (!(await this.indexExists({ indexName: sourceIndexName }))) {\n        throw new IndexNotFoundError(sourceIndexName);\n      }\n\n      const destinationClient = createSearchClient({\n        appId: destinationAppID,\n        apiKey: destinationApiKey,\n        timeouts: {\n          connect: 2000,\n          read: 5000,\n          write: 30000,\n        },\n        logger: createNullLogger(),\n        requester: createHttpRequester(),\n        algoliaAgents: [{ segment: 'accountCopyIndex', version: process.versions.node }],\n        responsesCache: createNullCache(),\n        requestsCache: createNullCache(),\n        hostsCache: createMemoryCache(),\n        ...options,\n      });\n\n      if (await destinationClient.indexExists({ indexName: destinationIndexName })) {\n        throw new IndexAlreadyExistsError(destinationIndexName);\n      }\n\n      responses.push(\n        await destinationClient.setSettings(\n          {\n            indexName: destinationIndexName,\n            indexSettings: await this.getSettings({ indexName: sourceIndexName }),\n          },\n          requestOptions,\n        ),\n      );\n\n      await this.browseRules({\n        indexName: sourceIndexName,\n        async aggregator(response: SearchRulesResponse) {\n          responses.push(\n            await destinationClient.saveRules(\n              { indexName: destinationIndexName, rules: response.hits },\n              requestOptions,\n            ),\n          );\n        },\n      });\n\n      await this.browseSynonyms({\n        indexName: sourceIndexName,\n        async aggregator(response: SearchSynonymsResponse) {\n          responses.push(\n            await destinationClient.saveSynonyms(\n              { indexName: destinationIndexName, synonymHit: response.hits },\n              requestOptions,\n            ),\n          );\n        },\n      });\n\n      await this.browseObjects({\n        indexName: sourceIndexName,\n        async aggregator(response: BrowseResponse) {\n          responses.push(\n            ...(await destinationClient.saveObjects(\n              { indexName: destinationIndexName, objects: response.hits },\n              requestOptions,\n            )),\n          );\n        },\n      });\n\n      for (const response of responses) {\n        await destinationClient.waitForTask({ indexName: destinationIndexName, taskID: response.taskID });\n      }\n    },\n    /**\n     * Helper: Retrieves the remaining validity of the previous generated `securedApiKey`, the `ValidUntil` parameter must have been provided.\n     *\n     * @summary Helper: Retrieves the remaining validity of the previous generated `secured_api_key`, the `ValidUntil` parameter must have been provided.\n     * @param getSecuredApiKeyRemainingValidity - The `getSecuredApiKeyRemainingValidity` object.\n     * @param getSecuredApiKeyRemainingValidity.securedApiKey - The secured API key generated with the `generateSecuredApiKey` method.\n     */\n    getSecuredApiKeyRemainingValidity: ({ securedApiKey }: GetSecuredApiKeyRemainingValidityOptions): number => {\n      const decodedString = atob(securedApiKey);\n      const regex = /validUntil=(\\d+)/;\n      const match = decodedString.match(regex);\n\n      if (match === null) {\n        throw new Error('validUntil not found in given secured api key.');\n      }\n\n      return parseInt(match[1], 10) - Math.round(new Date().getTime() / 1000);\n    },\n  };\n}\n", "// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nimport type {\n  CreateClientOptions,\n  Headers,\n  Host,\n  IterableOptions,\n  QueryParameters,\n  Request,\n  RequestOptions,\n} from '@algolia/client-common';\nimport {\n  ApiError,\n  createAuth,\n  createIterablePromise,\n  createTransporter,\n  getAlgoliaAgent,\n  shuffle,\n} from '@algolia/client-common';\n\nimport type { AddApiKeyResponse } from '../model/addApiKeyResponse';\nimport type { ApiKey } from '../model/apiKey';\n\nimport type { BatchParams } from '../model/batchParams';\nimport type { BatchResponse } from '../model/batchResponse';\n\nimport type { BrowseResponse } from '../model/browseResponse';\nimport type { CreatedAtResponse } from '../model/createdAtResponse';\nimport type { DeleteApiKeyResponse } from '../model/deleteApiKeyResponse';\n\nimport type { DeleteSourceResponse } from '../model/deleteSourceResponse';\nimport type { DeletedAtResponse } from '../model/deletedAtResponse';\nimport type { DictionarySettingsParams } from '../model/dictionarySettingsParams';\n\nimport type { GetApiKeyResponse } from '../model/getApiKeyResponse';\nimport type { GetDictionarySettingsResponse } from '../model/getDictionarySettingsResponse';\nimport type { GetLogsResponse } from '../model/getLogsResponse';\nimport type { GetObjectsParams } from '../model/getObjectsParams';\nimport type { GetObjectsResponse } from '../model/getObjectsResponse';\nimport type { GetTaskResponse } from '../model/getTaskResponse';\nimport type { GetTopUserIdsResponse } from '../model/getTopUserIdsResponse';\nimport type { HasPendingMappingsResponse } from '../model/hasPendingMappingsResponse';\n\nimport type { Languages } from '../model/languages';\nimport type { ListApiKeysResponse } from '../model/listApiKeysResponse';\nimport type { ListClustersResponse } from '../model/listClustersResponse';\nimport type { ListIndicesResponse } from '../model/listIndicesResponse';\nimport type { ListUserIdsResponse } from '../model/listUserIdsResponse';\n\nimport type { MultipleBatchResponse } from '../model/multipleBatchResponse';\n\nimport type { RemoveUserIdResponse } from '../model/removeUserIdResponse';\nimport type { ReplaceAllObjectsResponse } from '../model/replaceAllObjectsResponse';\n\nimport type { ReplaceSourceResponse } from '../model/replaceSourceResponse';\nimport type { Rule } from '../model/rule';\nimport type { SaveObjectResponse } from '../model/saveObjectResponse';\nimport type { SaveSynonymResponse } from '../model/saveSynonymResponse';\n\nimport type { SearchDictionaryEntriesResponse } from '../model/searchDictionaryEntriesResponse';\n\nimport type { SearchForFacetValuesResponse } from '../model/searchForFacetValuesResponse';\nimport type { SearchMethodParams } from '../model/searchMethodParams';\n\nimport type { SearchResponse } from '../model/searchResponse';\nimport type { SearchResponses } from '../model/searchResponses';\n\nimport type { SearchRulesResponse } from '../model/searchRulesResponse';\n\nimport type { SearchSynonymsResponse } from '../model/searchSynonymsResponse';\nimport type { SearchUserIdsParams } from '../model/searchUserIdsParams';\nimport type { SearchUserIdsResponse } from '../model/searchUserIdsResponse';\n\nimport type { SettingsResponse } from '../model/settingsResponse';\nimport type { Source } from '../model/source';\nimport type { SynonymHit } from '../model/synonymHit';\nimport type { UpdateApiKeyResponse } from '../model/updateApiKeyResponse';\nimport type { UpdatedAtResponse } from '../model/updatedAtResponse';\nimport type { UpdatedAtWithObjectIdResponse } from '../model/updatedAtWithObjectIdResponse';\nimport type { UserId } from '../model/userId';\n\nimport type {\n  AddOrUpdateObjectProps,\n  AssignUserIdProps,\n  BatchAssignUserIdsProps,\n  BatchDictionaryEntriesProps,\n  BatchProps,\n  BrowseOptions,\n  BrowseProps,\n  ChunkedBatchOptions,\n  ClearObjectsProps,\n  ClearRulesProps,\n  ClearSynonymsProps,\n  CustomDeleteProps,\n  CustomGetProps,\n  CustomPostProps,\n  CustomPutProps,\n  DeleteApiKeyProps,\n  DeleteByProps,\n  DeleteIndexProps,\n  DeleteObjectProps,\n  DeleteObjectsOptions,\n  DeleteRuleProps,\n  DeleteSourceProps,\n  DeleteSynonymProps,\n  GetApiKeyProps,\n  GetAppTaskProps,\n  GetLogsProps,\n  GetObjectProps,\n  GetRuleProps,\n  GetSettingsProps,\n  GetSynonymProps,\n  GetTaskProps,\n  GetUserIdProps,\n  HasPendingMappingsProps,\n  LegacySearchMethodProps,\n  ListIndicesProps,\n  ListUserIdsProps,\n  OperationIndexProps,\n  PartialUpdateObjectProps,\n  PartialUpdateObjectsOptions,\n  RemoveUserIdProps,\n  ReplaceAllObjectsOptions,\n  ReplaceSourcesProps,\n  RestoreApiKeyProps,\n  SaveObjectProps,\n  SaveObjectsOptions,\n  SaveRuleProps,\n  SaveRulesProps,\n  SaveSynonymProps,\n  SaveSynonymsProps,\n  SearchDictionaryEntriesProps,\n  SearchForFacetValuesProps,\n  SearchRulesProps,\n  SearchSingleIndexProps,\n  SearchSynonymsProps,\n  SetSettingsProps,\n  UpdateApiKeyProps,\n  WaitForApiKeyOptions,\n  WaitForAppTaskOptions,\n  WaitForTaskOptions,\n} from '../model/clientMethodProps';\n\nimport type { BatchRequest } from '../model/batchRequest';\n\nexport const apiClientVersion = '5.32.0';\n\nfunction getDefaultHosts(appId: string): Host[] {\n  return (\n    [\n      {\n        url: `${appId}-dsn.algolia.net`,\n        accept: 'read',\n        protocol: 'https',\n      },\n      {\n        url: `${appId}.algolia.net`,\n        accept: 'write',\n        protocol: 'https',\n      },\n    ] as Host[]\n  ).concat(\n    shuffle([\n      {\n        url: `${appId}-1.algolianet.com`,\n        accept: 'readWrite',\n        protocol: 'https',\n      },\n      {\n        url: `${appId}-2.algolianet.com`,\n        accept: 'readWrite',\n        protocol: 'https',\n      },\n      {\n        url: `${appId}-3.algolianet.com`,\n        accept: 'readWrite',\n        protocol: 'https',\n      },\n    ]),\n  );\n}\n\nexport function createSearchClient({\n  appId: appIdOption,\n  apiKey: apiKeyOption,\n  authMode,\n  algoliaAgents,\n  ...options\n}: CreateClientOptions) {\n  const auth = createAuth(appIdOption, apiKeyOption, authMode);\n  const transporter = createTransporter({\n    hosts: getDefaultHosts(appIdOption),\n    ...options,\n    algoliaAgent: getAlgoliaAgent({\n      algoliaAgents,\n      client: 'Search',\n      version: apiClientVersion,\n    }),\n    baseHeaders: {\n      'content-type': 'text/plain',\n      ...auth.headers(),\n      ...options.baseHeaders,\n    },\n    baseQueryParameters: {\n      ...auth.queryParameters(),\n      ...options.baseQueryParameters,\n    },\n  });\n\n  return {\n    transporter,\n\n    /**\n     * The `appId` currently in use.\n     */\n    appId: appIdOption,\n\n    /**\n     * The `apiKey` currently in use.\n     */\n    apiKey: apiKeyOption,\n\n    /**\n     * Clears the cache of the transporter for the `requestsCache` and `responsesCache` properties.\n     */\n    clearCache(): Promise<void> {\n      return Promise.all([transporter.requestsCache.clear(), transporter.responsesCache.clear()]).then(() => undefined);\n    },\n\n    /**\n     * Get the value of the `algoliaAgent`, used by our libraries internally and telemetry system.\n     */\n    get _ua(): string {\n      return transporter.algoliaAgent.value;\n    },\n\n    /**\n     * Adds a `segment` to the `x-algolia-agent` sent with every requests.\n     *\n     * @param segment - The algolia agent (user-agent) segment to add.\n     * @param version - The version of the agent.\n     */\n    addAlgoliaAgent(segment: string, version?: string | undefined): void {\n      transporter.algoliaAgent.add({ segment, version });\n    },\n\n    /**\n     * Helper method to switch the API key used to authenticate the requests.\n     *\n     * @param params - Method params.\n     * @param params.apiKey - The new API Key to use.\n     */\n    setClientApiKey({ apiKey }: { apiKey: string }): void {\n      if (!authMode || authMode === 'WithinHeaders') {\n        transporter.baseHeaders['x-algolia-api-key'] = apiKey;\n      } else {\n        transporter.baseQueryParameters['x-algolia-api-key'] = apiKey;\n      }\n    },\n\n    /**\n     * Helper: Wait for a task to be published (completed) for a given `indexName` and `taskID`.\n     *\n     * @summary Helper method that waits for a task to be published (completed).\n     * @param waitForTaskOptions - The `waitForTaskOptions` object.\n     * @param waitForTaskOptions.indexName - The `indexName` where the operation was performed.\n     * @param waitForTaskOptions.taskID - The `taskID` returned in the method response.\n     * @param waitForTaskOptions.maxRetries - The maximum number of retries. 50 by default.\n     * @param waitForTaskOptions.timeout - The function to decide how long to wait between retries.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `getTask` method and merged with the transporter requestOptions.\n     */\n    waitForTask(\n      {\n        indexName,\n        taskID,\n        maxRetries = 50,\n        timeout = (retryCount: number): number => Math.min(retryCount * 200, 5000),\n      }: WaitForTaskOptions,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<GetTaskResponse> {\n      let retryCount = 0;\n\n      return createIterablePromise({\n        func: () => this.getTask({ indexName, taskID }, requestOptions),\n        validate: (response) => response.status === 'published',\n        aggregator: () => (retryCount += 1),\n        error: {\n          validate: () => retryCount >= maxRetries,\n          message: () => `The maximum number of retries exceeded. (${retryCount}/${maxRetries})`,\n        },\n        timeout: () => timeout(retryCount),\n      });\n    },\n\n    /**\n     * Helper: Wait for an application-level task to complete for a given `taskID`.\n     *\n     * @summary Helper method that waits for a task to be published (completed).\n     * @param waitForAppTaskOptions - The `waitForTaskOptions` object.\n     * @param waitForAppTaskOptions.taskID - The `taskID` returned in the method response.\n     * @param waitForAppTaskOptions.maxRetries - The maximum number of retries. 50 by default.\n     * @param waitForAppTaskOptions.timeout - The function to decide how long to wait between retries.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `getTask` method and merged with the transporter requestOptions.\n     */\n    waitForAppTask(\n      {\n        taskID,\n        maxRetries = 50,\n        timeout = (retryCount: number): number => Math.min(retryCount * 200, 5000),\n      }: WaitForAppTaskOptions,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<GetTaskResponse> {\n      let retryCount = 0;\n\n      return createIterablePromise({\n        func: () => this.getAppTask({ taskID }, requestOptions),\n        validate: (response) => response.status === 'published',\n        aggregator: () => (retryCount += 1),\n        error: {\n          validate: () => retryCount >= maxRetries,\n          message: () => `The maximum number of retries exceeded. (${retryCount}/${maxRetries})`,\n        },\n        timeout: () => timeout(retryCount),\n      });\n    },\n\n    /**\n     * Helper: Wait for an API key to be added, updated or deleted based on a given `operation`.\n     *\n     * @summary Helper method that waits for an API key task to be processed.\n     * @param waitForApiKeyOptions - The `waitForApiKeyOptions` object.\n     * @param waitForApiKeyOptions.operation - The `operation` that was done on a `key`.\n     * @param waitForApiKeyOptions.key - The `key` that has been added, deleted or updated.\n     * @param waitForApiKeyOptions.apiKey - Necessary to know if an `update` operation has been processed, compare fields of the response with it.\n     * @param waitForApiKeyOptions.maxRetries - The maximum number of retries. 50 by default.\n     * @param waitForApiKeyOptions.timeout - The function to decide how long to wait between retries.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `getApikey` method and merged with the transporter requestOptions.\n     */\n    waitForApiKey(\n      {\n        operation,\n        key,\n        apiKey,\n        maxRetries = 50,\n        timeout = (retryCount: number): number => Math.min(retryCount * 200, 5000),\n      }: WaitForApiKeyOptions,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<GetApiKeyResponse | undefined> {\n      let retryCount = 0;\n      const baseIteratorOptions: IterableOptions<GetApiKeyResponse | undefined> = {\n        aggregator: () => (retryCount += 1),\n        error: {\n          validate: () => retryCount >= maxRetries,\n          message: () => `The maximum number of retries exceeded. (${retryCount}/${maxRetries})`,\n        },\n        timeout: () => timeout(retryCount),\n      };\n\n      if (operation === 'update') {\n        if (!apiKey) {\n          throw new Error('`apiKey` is required when waiting for an `update` operation.');\n        }\n\n        return createIterablePromise({\n          ...baseIteratorOptions,\n          func: () => this.getApiKey({ key }, requestOptions),\n          validate: (response) => {\n            for (const field of Object.keys(apiKey)) {\n              const value = apiKey[field as keyof ApiKey];\n              const resValue = response[field as keyof ApiKey];\n              if (Array.isArray(value) && Array.isArray(resValue)) {\n                if (value.length !== resValue.length || value.some((v, index) => v !== resValue[index])) {\n                  return false;\n                }\n              } else if (value !== resValue) {\n                return false;\n              }\n            }\n            return true;\n          },\n        });\n      }\n\n      return createIterablePromise({\n        ...baseIteratorOptions,\n        func: () =>\n          this.getApiKey({ key }, requestOptions).catch((error: ApiError) => {\n            if (error.status === 404) {\n              return undefined;\n            }\n\n            throw error;\n          }),\n        validate: (response) => (operation === 'add' ? response !== undefined : response === undefined),\n      });\n    },\n\n    /**\n     * Helper: Iterate on the `browse` method of the client to allow aggregating objects of an index.\n     *\n     * @summary Helper method that iterates on the `browse` method.\n     * @param browseObjects - The `browseObjects` object.\n     * @param browseObjects.indexName - The index in which to perform the request.\n     * @param browseObjects.browseParams - The `browse` parameters.\n     * @param browseObjects.validate - The validator function. It receive the resolved return of the API call. By default, stops when there is no `cursor` in the response.\n     * @param browseObjects.aggregator - The function that runs right after the API call has been resolved, allows you to do anything with the response before `validate`.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `browse` method and merged with the transporter requestOptions.\n     */\n    browseObjects<T>(\n      { indexName, browseParams, ...browseObjectsOptions }: BrowseOptions<BrowseResponse<T>> & BrowseProps,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<BrowseResponse<T>> {\n      return createIterablePromise<BrowseResponse<T>>({\n        func: (previousResponse) => {\n          return this.browse(\n            {\n              indexName,\n              browseParams: {\n                cursor: previousResponse ? previousResponse.cursor : undefined,\n                hitsPerPage: 1000,\n                ...browseParams,\n              },\n            },\n            requestOptions,\n          );\n        },\n        validate: (response) => response.cursor === undefined,\n        ...browseObjectsOptions,\n      });\n    },\n\n    /**\n     * Helper: Iterate on the `searchRules` method of the client to allow aggregating rules of an index.\n     *\n     * @summary Helper method that iterates on the `searchRules` method.\n     * @param browseRules - The `browseRules` object.\n     * @param browseRules.indexName - The index in which to perform the request.\n     * @param browseRules.searchRulesParams - The `searchRules` method parameters.\n     * @param browseRules.validate - The validator function. It receive the resolved return of the API call. By default, stops when there is less hits returned than the number of maximum hits (1000).\n     * @param browseRules.aggregator - The function that runs right after the API call has been resolved, allows you to do anything with the response before `validate`.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `searchRules` method and merged with the transporter requestOptions.\n     */\n    browseRules(\n      { indexName, searchRulesParams, ...browseRulesOptions }: BrowseOptions<SearchRulesResponse> & SearchRulesProps,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<SearchRulesResponse> {\n      const params = {\n        ...searchRulesParams,\n        hitsPerPage: searchRulesParams?.hitsPerPage || 1000,\n      };\n\n      return createIterablePromise<SearchRulesResponse>({\n        func: (previousResponse) => {\n          return this.searchRules(\n            {\n              indexName,\n              searchRulesParams: {\n                ...params,\n                page: previousResponse ? previousResponse.page + 1 : params.page || 0,\n              },\n            },\n            requestOptions,\n          );\n        },\n        validate: (response) => response.hits.length < params.hitsPerPage,\n        ...browseRulesOptions,\n      });\n    },\n\n    /**\n     * Helper: Iterate on the `searchSynonyms` method of the client to allow aggregating rules of an index.\n     *\n     * @summary Helper method that iterates on the `searchSynonyms` method.\n     * @param browseSynonyms - The `browseSynonyms` object.\n     * @param browseSynonyms.indexName - The index in which to perform the request.\n     * @param browseSynonyms.validate - The validator function. It receive the resolved return of the API call. By default, stops when there is less hits returned than the number of maximum hits (1000).\n     * @param browseSynonyms.aggregator - The function that runs right after the API call has been resolved, allows you to do anything with the response before `validate`.\n     * @param browseSynonyms.searchSynonymsParams - The `searchSynonyms` method parameters.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `searchSynonyms` method and merged with the transporter requestOptions.\n     */\n    browseSynonyms(\n      {\n        indexName,\n        searchSynonymsParams,\n        ...browseSynonymsOptions\n      }: BrowseOptions<SearchSynonymsResponse> & SearchSynonymsProps,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<SearchSynonymsResponse> {\n      const params = {\n        ...searchSynonymsParams,\n        page: searchSynonymsParams?.page || 0,\n        hitsPerPage: 1000,\n      };\n\n      return createIterablePromise<SearchSynonymsResponse>({\n        func: (_) => {\n          const resp = this.searchSynonyms(\n            {\n              indexName,\n              searchSynonymsParams: {\n                ...params,\n                page: params.page,\n              },\n            },\n            requestOptions,\n          );\n          params.page += 1;\n          return resp;\n        },\n        validate: (response) => response.hits.length < params.hitsPerPage,\n        ...browseSynonymsOptions,\n      });\n    },\n\n    /**\n     * Helper: Chunks the given `objects` list in subset of 1000 elements max in order to make it fit in `batch` requests.\n     *\n     * @summary Helper: Chunks the given `objects` list in subset of 1000 elements max in order to make it fit in `batch` requests.\n     * @param chunkedBatch - The `chunkedBatch` object.\n     * @param chunkedBatch.indexName - The `indexName` to replace `objects` in.\n     * @param chunkedBatch.objects - The array of `objects` to store in the given Algolia `indexName`.\n     * @param chunkedBatch.action - The `batch` `action` to perform on the given array of `objects`, defaults to `addObject`.\n     * @param chunkedBatch.waitForTasks - Whether or not we should wait until every `batch` tasks has been processed, this operation may slow the total execution time of this method but is more reliable.\n     * @param chunkedBatch.batchSize - The size of the chunk of `objects`. The number of `batch` calls will be equal to `length(objects) / batchSize`. Defaults to 1000.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `getTask` method and merged with the transporter requestOptions.\n     */\n    async chunkedBatch(\n      { indexName, objects, action = 'addObject', waitForTasks, batchSize = 1000 }: ChunkedBatchOptions,\n      requestOptions?: RequestOptions,\n    ): Promise<Array<BatchResponse>> {\n      let requests: Array<BatchRequest> = [];\n      const responses: Array<BatchResponse> = [];\n\n      const objectEntries = objects.entries();\n      for (const [i, obj] of objectEntries) {\n        requests.push({ action, body: obj });\n        if (requests.length === batchSize || i === objects.length - 1) {\n          responses.push(await this.batch({ indexName, batchWriteParams: { requests } }, requestOptions));\n          requests = [];\n        }\n      }\n\n      if (waitForTasks) {\n        for (const resp of responses) {\n          await this.waitForTask({ indexName, taskID: resp.taskID });\n        }\n      }\n\n      return responses;\n    },\n\n    /**\n     * Helper: Saves the given array of objects in the given index. The `chunkedBatch` helper is used under the hood, which creates a `batch` requests with at most 1000 objects in it.\n     *\n     * @summary Helper: Saves the given array of objects in the given index. The `chunkedBatch` helper is used under the hood, which creates a `batch` requests with at most 1000 objects in it.\n     * @param saveObjects - The `saveObjects` object.\n     * @param saveObjects.indexName - The `indexName` to save `objects` in.\n     * @param saveObjects.objects - The array of `objects` to store in the given Algolia `indexName`.\n     * @param saveObjects.batchSize - The size of the chunk of `objects`. The number of `batch` calls will be equal to `length(objects) / batchSize`. Defaults to 1000.\n     * @param saveObjects.waitForTasks - Whether or not we should wait until every `batch` tasks has been processed, this operation may slow the total execution time of this method but is more reliable.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `batch` method and merged with the transporter requestOptions.\n     */\n    async saveObjects(\n      { indexName, objects, waitForTasks, batchSize }: SaveObjectsOptions,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<BatchResponse[]> {\n      return await this.chunkedBatch(\n        { indexName, objects, action: 'addObject', waitForTasks, batchSize },\n        requestOptions,\n      );\n    },\n\n    /**\n     * Helper: Deletes every records for the given objectIDs. The `chunkedBatch` helper is used under the hood, which creates a `batch` requests with at most 1000 objectIDs in it.\n     *\n     * @summary Helper: Deletes every records for the given objectIDs. The `chunkedBatch` helper is used under the hood, which creates a `batch` requests with at most 1000 objectIDs in it.\n     * @param deleteObjects - The `deleteObjects` object.\n     * @param deleteObjects.indexName - The `indexName` to delete `objectIDs` from.\n     * @param deleteObjects.objectIDs - The objectIDs to delete.\n     * @param deleteObjects.batchSize - The size of the chunk of `objects`. The number of `batch` calls will be equal to `length(objects) / batchSize`. Defaults to 1000.\n     * @param deleteObjects.waitForTasks - Whether or not we should wait until every `batch` tasks has been processed, this operation may slow the total execution time of this method but is more reliable.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `batch` method and merged with the transporter requestOptions.\n     */\n    async deleteObjects(\n      { indexName, objectIDs, waitForTasks, batchSize }: DeleteObjectsOptions,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<BatchResponse[]> {\n      return await this.chunkedBatch(\n        {\n          indexName,\n          objects: objectIDs.map((objectID) => ({ objectID })),\n          action: 'deleteObject',\n          waitForTasks,\n          batchSize,\n        },\n        requestOptions,\n      );\n    },\n\n    /**\n     * Helper: Replaces object content of all the given objects according to their respective `objectID` field. The `chunkedBatch` helper is used under the hood, which creates a `batch` requests with at most 1000 objects in it.\n     *\n     * @summary Helper: Replaces object content of all the given objects according to their respective `objectID` field. The `chunkedBatch` helper is used under the hood, which creates a `batch` requests with at most 1000 objects in it.\n     * @param partialUpdateObjects - The `partialUpdateObjects` object.\n     * @param partialUpdateObjects.indexName - The `indexName` to update `objects` in.\n     * @param partialUpdateObjects.objects - The array of `objects` to update in the given Algolia `indexName`.\n     * @param partialUpdateObjects.createIfNotExists - To be provided if non-existing objects are passed, otherwise, the call will fail..\n     * @param partialUpdateObjects.batchSize - The size of the chunk of `objects`. The number of `batch` calls will be equal to `length(objects) / batchSize`. Defaults to 1000.\n     * @param partialUpdateObjects.waitForTasks - Whether or not we should wait until every `batch` tasks has been processed, this operation may slow the total execution time of this method but is more reliable.\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `getTask` method and merged with the transporter requestOptions.\n     */\n    async partialUpdateObjects(\n      { indexName, objects, createIfNotExists, waitForTasks, batchSize }: PartialUpdateObjectsOptions,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<BatchResponse[]> {\n      return await this.chunkedBatch(\n        {\n          indexName,\n          objects,\n          action: createIfNotExists ? 'partialUpdateObject' : 'partialUpdateObjectNoCreate',\n          batchSize,\n          waitForTasks,\n        },\n        requestOptions,\n      );\n    },\n\n    /**\n     * Helper: Replaces all objects (records) in the given `index_name` with the given `objects`. A temporary index is created during this process in order to backup your data.\n     * See https://api-clients-automation.netlify.app/docs/add-new-api-client#5-helpers for implementation details.\n     *\n     * @summary Helper: Replaces all objects (records) in the given `index_name` with the given `objects`. A temporary index is created during this process in order to backup your data.\n     * @param replaceAllObjects - The `replaceAllObjects` object.\n     * @param replaceAllObjects.indexName - The `indexName` to replace `objects` in.\n     * @param replaceAllObjects.objects - The array of `objects` to store in the given Algolia `indexName`.\n     * @param replaceAllObjects.batchSize - The size of the chunk of `objects`. The number of `batch` calls will be equal to `objects.length / batchSize`. Defaults to 1000.\n     * @param replaceAllObjects.scopes - The `scopes` to keep from the index. Defaults to ['settings', 'rules', 'synonyms'].\n     * @param requestOptions - The requestOptions to send along with the query, they will be forwarded to the `batch`, `operationIndex` and `getTask` method and merged with the transporter requestOptions.\n     */\n    async replaceAllObjects(\n      { indexName, objects, batchSize, scopes }: ReplaceAllObjectsOptions,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<ReplaceAllObjectsResponse> {\n      const randomSuffix = Math.floor(Math.random() * 1000000) + 100000;\n      const tmpIndexName = `${indexName}_tmp_${randomSuffix}`;\n\n      if (scopes === undefined) {\n        scopes = ['settings', 'rules', 'synonyms'];\n      }\n\n      try {\n        let copyOperationResponse = await this.operationIndex(\n          {\n            indexName,\n            operationIndexParams: {\n              operation: 'copy',\n              destination: tmpIndexName,\n              scope: scopes,\n            },\n          },\n          requestOptions,\n        );\n\n        const batchResponses = await this.chunkedBatch(\n          { indexName: tmpIndexName, objects, waitForTasks: true, batchSize },\n          requestOptions,\n        );\n\n        await this.waitForTask({\n          indexName: tmpIndexName,\n          taskID: copyOperationResponse.taskID,\n        });\n\n        copyOperationResponse = await this.operationIndex(\n          {\n            indexName,\n            operationIndexParams: {\n              operation: 'copy',\n              destination: tmpIndexName,\n              scope: scopes,\n            },\n          },\n          requestOptions,\n        );\n        await this.waitForTask({\n          indexName: tmpIndexName,\n          taskID: copyOperationResponse.taskID,\n        });\n\n        const moveOperationResponse = await this.operationIndex(\n          {\n            indexName: tmpIndexName,\n            operationIndexParams: { operation: 'move', destination: indexName },\n          },\n          requestOptions,\n        );\n        await this.waitForTask({\n          indexName: tmpIndexName,\n          taskID: moveOperationResponse.taskID,\n        });\n\n        return { copyOperationResponse, batchResponses, moveOperationResponse };\n      } catch (error) {\n        await this.deleteIndex({ indexName: tmpIndexName });\n\n        throw error;\n      }\n    },\n\n    async indexExists({ indexName }: GetSettingsProps): Promise<boolean> {\n      try {\n        await this.getSettings({ indexName });\n      } catch (error) {\n        if (error instanceof ApiError && error.status === 404) {\n          return false;\n        }\n        throw error;\n      }\n\n      return true;\n    },\n\n    /**\n     * Helper: calls the `search` method but with certainty that we will only request Algolia records (hits) and not facets.\n     * Disclaimer: We don't assert that the parameters you pass to this method only contains `hits` requests to prevent impacting search performances, this helper is purely for typing purposes.\n     *\n     * @summary Search multiple indices for `hits`.\n     * @param searchMethodParams - Query requests and strategies. Results will be received in the same order as the queries.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchForHits<T>(\n      searchMethodParams: LegacySearchMethodProps | SearchMethodParams,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<{ results: Array<SearchResponse<T>> }> {\n      return this.search(searchMethodParams, requestOptions) as Promise<{ results: Array<SearchResponse<T>> }>;\n    },\n\n    /**\n     * Helper: calls the `search` method but with certainty that we will only request Algolia facets and not records (hits).\n     * Disclaimer: We don't assert that the parameters you pass to this method only contains `facets` requests to prevent impacting search performances, this helper is purely for typing purposes.\n     *\n     * @summary Search multiple indices for `facets`.\n     * @param searchMethodParams - Query requests and strategies. Results will be received in the same order as the queries.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchForFacets(\n      searchMethodParams: LegacySearchMethodProps | SearchMethodParams,\n      requestOptions?: RequestOptions | undefined,\n    ): Promise<{ results: Array<SearchForFacetValuesResponse> }> {\n      return this.search(searchMethodParams, requestOptions) as Promise<{\n        results: Array<SearchForFacetValuesResponse>;\n      }>;\n    },\n    /**\n     * Creates a new API key with specific permissions and restrictions.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     * @param apiKey - The apiKey object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    addApiKey(apiKey: ApiKey, requestOptions?: RequestOptions): Promise<AddApiKeyResponse> {\n      if (!apiKey) {\n        throw new Error('Parameter `apiKey` is required when calling `addApiKey`.');\n      }\n\n      if (!apiKey.acl) {\n        throw new Error('Parameter `apiKey.acl` is required when calling `addApiKey`.');\n      }\n\n      const requestPath = '/1/keys';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: apiKey,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * If a record with the specified object ID exists, the existing record is replaced. Otherwise, a new record is added to the index.  If you want to use auto-generated object IDs, use the [`saveObject` operation](#tag/Records/operation/saveObject). To update _some_ attributes of an existing record, use the [`partial` operation](#tag/Records/operation/partialUpdateObject) instead. To add, update, or replace multiple records, use the [`batch` operation](#tag/Records/operation/batch).\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     * @param addOrUpdateObject - The addOrUpdateObject object.\n     * @param addOrUpdateObject.indexName - Name of the index on which to perform the operation.\n     * @param addOrUpdateObject.objectID - Unique record identifier.\n     * @param addOrUpdateObject.body - The record. A schemaless object with attributes that are useful in the context of search and discovery.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    addOrUpdateObject<T extends object>(\n      { indexName, objectID, body }: AddOrUpdateObjectProps<T>,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtWithObjectIdResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `addOrUpdateObject`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `addOrUpdateObject`.');\n      }\n\n      if (!body) {\n        throw new Error('Parameter `body` is required when calling `addOrUpdateObject`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/{objectID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Adds a source to the list of allowed sources.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     * @param source - Source to add.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    appendSource(source: Source, requestOptions?: RequestOptions): Promise<CreatedAtResponse> {\n      if (!source) {\n        throw new Error('Parameter `source` is required when calling `appendSource`.');\n      }\n\n      if (!source.source) {\n        throw new Error('Parameter `source.source` is required when calling `appendSource`.');\n      }\n\n      const requestPath = '/1/security/sources/append';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: source,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Assigns or moves a user ID to a cluster.  The time it takes to move a user is proportional to the amount of data linked to the user ID.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     *\n     * @deprecated\n     * @param assignUserId - The assignUserId object.\n     * @param assignUserId.xAlgoliaUserID - Unique identifier of the user who makes the search request.\n     * @param assignUserId.assignUserIdParams - The assignUserIdParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    assignUserId(\n      { xAlgoliaUserID, assignUserIdParams }: AssignUserIdProps,\n      requestOptions?: RequestOptions,\n    ): Promise<CreatedAtResponse> {\n      if (!xAlgoliaUserID) {\n        throw new Error('Parameter `xAlgoliaUserID` is required when calling `assignUserId`.');\n      }\n\n      if (!assignUserIdParams) {\n        throw new Error('Parameter `assignUserIdParams` is required when calling `assignUserId`.');\n      }\n\n      if (!assignUserIdParams.cluster) {\n        throw new Error('Parameter `assignUserIdParams.cluster` is required when calling `assignUserId`.');\n      }\n\n      const requestPath = '/1/clusters/mapping';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (xAlgoliaUserID !== undefined) {\n        headers['X-Algolia-User-ID'] = xAlgoliaUserID.toString();\n      }\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: assignUserIdParams,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Adds, updates, or deletes records in one index with a single API request.  Batching index updates reduces latency and increases data integrity.  - Actions are applied in the order they\\'re specified. - Actions are equivalent to the individual API requests of the same name.  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).\n     * @param batch - The batch object.\n     * @param batch.indexName - Name of the index on which to perform the operation.\n     * @param batch.batchWriteParams - The batchWriteParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    batch({ indexName, batchWriteParams }: BatchProps, requestOptions?: RequestOptions): Promise<BatchResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `batch`.');\n      }\n\n      if (!batchWriteParams) {\n        throw new Error('Parameter `batchWriteParams` is required when calling `batch`.');\n      }\n\n      if (!batchWriteParams.requests) {\n        throw new Error('Parameter `batchWriteParams.requests` is required when calling `batch`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/batch'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: batchWriteParams,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Assigns multiple user IDs to a cluster.  **You can\\'t move users with this operation**.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     *\n     * @deprecated\n     * @param batchAssignUserIds - The batchAssignUserIds object.\n     * @param batchAssignUserIds.xAlgoliaUserID - Unique identifier of the user who makes the search request.\n     * @param batchAssignUserIds.batchAssignUserIdsParams - The batchAssignUserIdsParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    batchAssignUserIds(\n      { xAlgoliaUserID, batchAssignUserIdsParams }: BatchAssignUserIdsProps,\n      requestOptions?: RequestOptions,\n    ): Promise<CreatedAtResponse> {\n      if (!xAlgoliaUserID) {\n        throw new Error('Parameter `xAlgoliaUserID` is required when calling `batchAssignUserIds`.');\n      }\n\n      if (!batchAssignUserIdsParams) {\n        throw new Error('Parameter `batchAssignUserIdsParams` is required when calling `batchAssignUserIds`.');\n      }\n\n      if (!batchAssignUserIdsParams.cluster) {\n        throw new Error('Parameter `batchAssignUserIdsParams.cluster` is required when calling `batchAssignUserIds`.');\n      }\n      if (!batchAssignUserIdsParams.users) {\n        throw new Error('Parameter `batchAssignUserIdsParams.users` is required when calling `batchAssignUserIds`.');\n      }\n\n      const requestPath = '/1/clusters/mapping/batch';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (xAlgoliaUserID !== undefined) {\n        headers['X-Algolia-User-ID'] = xAlgoliaUserID.toString();\n      }\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: batchAssignUserIdsParams,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Adds or deletes multiple entries from your plurals, segmentation, or stop word dictionaries.\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param batchDictionaryEntries - The batchDictionaryEntries object.\n     * @param batchDictionaryEntries.dictionaryName - Dictionary type in which to search.\n     * @param batchDictionaryEntries.batchDictionaryEntriesParams - The batchDictionaryEntriesParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    batchDictionaryEntries(\n      { dictionaryName, batchDictionaryEntriesParams }: BatchDictionaryEntriesProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!dictionaryName) {\n        throw new Error('Parameter `dictionaryName` is required when calling `batchDictionaryEntries`.');\n      }\n\n      if (!batchDictionaryEntriesParams) {\n        throw new Error('Parameter `batchDictionaryEntriesParams` is required when calling `batchDictionaryEntries`.');\n      }\n\n      if (!batchDictionaryEntriesParams.requests) {\n        throw new Error(\n          'Parameter `batchDictionaryEntriesParams.requests` is required when calling `batchDictionaryEntries`.',\n        );\n      }\n\n      const requestPath = '/1/dictionaries/{dictionaryName}/batch'.replace(\n        '{dictionaryName}',\n        encodeURIComponent(dictionaryName),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: batchDictionaryEntriesParams,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves records from an index, up to 1,000 per request.  While searching retrieves _hits_ (records augmented with attributes for highlighting and ranking details), browsing _just_ returns matching records. This can be useful if you want to export your indices.  - The Analytics API doesn\\'t collect data when using `browse`. - Records are ranked by attributes and custom ranking. - There\\'s no ranking for: typo-tolerance, number of matched words, proximity, geo distance.  Browse requests automatically apply these settings:  - `advancedSyntax`: `false` - `attributesToHighlight`: `[]` - `attributesToSnippet`: `[]` - `distinct`: `false` - `enablePersonalization`: `false` - `enableRules`: `false` - `facets`: `[]` - `getRankingInfo`: `false` - `ignorePlurals`: `false` - `optionalFilters`: `[]` - `typoTolerance`: `true` or `false` (`min` and `strict` evaluate to `true`)  If you send these parameters with your browse requests, they\\'ll be ignored.\n     *\n     * Required API Key ACLs:\n     *  - browse\n     * @param browse - The browse object.\n     * @param browse.indexName - Name of the index on which to perform the operation.\n     * @param browse.browseParams - The browseParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    browse<T>({ indexName, browseParams }: BrowseProps, requestOptions?: RequestOptions): Promise<BrowseResponse<T>> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `browse`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/browse'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: browseParams ? browseParams : {},\n        useReadTransporter: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes only the records from an index while keeping settings, synonyms, and rules. This operation is resource-intensive and subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).\n     *\n     * Required API Key ACLs:\n     *  - deleteIndex\n     * @param clearObjects - The clearObjects object.\n     * @param clearObjects.indexName - Name of the index on which to perform the operation.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    clearObjects({ indexName }: ClearObjectsProps, requestOptions?: RequestOptions): Promise<UpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `clearObjects`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/clear'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes all rules from the index.\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param clearRules - The clearRules object.\n     * @param clearRules.indexName - Name of the index on which to perform the operation.\n     * @param clearRules.forwardToReplicas - Whether changes are applied to replica indices.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    clearRules(\n      { indexName, forwardToReplicas }: ClearRulesProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `clearRules`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/rules/clear'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (forwardToReplicas !== undefined) {\n        queryParameters['forwardToReplicas'] = forwardToReplicas.toString();\n      }\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes all synonyms from the index.\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param clearSynonyms - The clearSynonyms object.\n     * @param clearSynonyms.indexName - Name of the index on which to perform the operation.\n     * @param clearSynonyms.forwardToReplicas - Whether changes are applied to replica indices.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    clearSynonyms(\n      { indexName, forwardToReplicas }: ClearSynonymsProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `clearSynonyms`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/synonyms/clear'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (forwardToReplicas !== undefined) {\n        queryParameters['forwardToReplicas'] = forwardToReplicas.toString();\n      }\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customDelete - The customDelete object.\n     * @param customDelete.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customDelete.parameters - Query parameters to apply to the current query.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customDelete(\n      { path, parameters }: CustomDeleteProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customDelete`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customGet - The customGet object.\n     * @param customGet.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customGet.parameters - Query parameters to apply to the current query.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customGet({ path, parameters }: CustomGetProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customGet`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customPost - The customPost object.\n     * @param customPost.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customPost.parameters - Query parameters to apply to the current query.\n     * @param customPost.body - Parameters to send with the custom request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customPost(\n      { path, parameters, body }: CustomPostProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customPost`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body ? body : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method lets you send requests to the Algolia REST API.\n     * @param customPut - The customPut object.\n     * @param customPut.path - Path of the endpoint, for example `1/newFeature`.\n     * @param customPut.parameters - Query parameters to apply to the current query.\n     * @param customPut.body - Parameters to send with the custom request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customPut(\n      { path, parameters, body }: CustomPutProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customPut`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body ? body : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes the API key.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     * @param deleteApiKey - The deleteApiKey object.\n     * @param deleteApiKey.key - API key.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteApiKey({ key }: DeleteApiKeyProps, requestOptions?: RequestOptions): Promise<DeleteApiKeyResponse> {\n      if (!key) {\n        throw new Error('Parameter `key` is required when calling `deleteApiKey`.');\n      }\n\n      const requestPath = '/1/keys/{key}'.replace('{key}', encodeURIComponent(key));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This operation doesn\\'t accept empty filters.  This operation is resource-intensive. You should only use it if you can\\'t get the object IDs of the records you want to delete. It\\'s more efficient to get a list of object IDs with the [`browse` operation](#tag/Search/operation/browse), and then delete the records using the [`batch` operation](#tag/Records/operation/batch).  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).\n     *\n     * Required API Key ACLs:\n     *  - deleteIndex\n     * @param deleteBy - The deleteBy object.\n     * @param deleteBy.indexName - Name of the index on which to perform the operation.\n     * @param deleteBy.deleteByParams - The deleteByParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteBy(\n      { indexName, deleteByParams }: DeleteByProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `deleteBy`.');\n      }\n\n      if (!deleteByParams) {\n        throw new Error('Parameter `deleteByParams` is required when calling `deleteBy`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/deleteByQuery'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: deleteByParams,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes an index and all its settings.  - Deleting an index doesn\\'t delete its analytics data. - If you try to delete a non-existing index, the operation is ignored without warning. - If the index you want to delete has replica indices, the replicas become independent indices. - If the index you want to delete is a replica index, you must first unlink it from its primary index before you can delete it.   For more information, see [Delete replica indices](https://www.algolia.com/doc/guides/managing-results/refine-results/sorting/how-to/deleting-replicas/).\n     *\n     * Required API Key ACLs:\n     *  - deleteIndex\n     * @param deleteIndex - The deleteIndex object.\n     * @param deleteIndex.indexName - Name of the index on which to perform the operation.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteIndex({ indexName }: DeleteIndexProps, requestOptions?: RequestOptions): Promise<DeletedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `deleteIndex`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a record by its object ID.  To delete more than one record, use the [`batch` operation](#tag/Records/operation/batch). To delete records matching a query, use the [`deleteBy` operation](#tag/Records/operation/deleteBy).\n     *\n     * Required API Key ACLs:\n     *  - deleteObject\n     * @param deleteObject - The deleteObject object.\n     * @param deleteObject.indexName - Name of the index on which to perform the operation.\n     * @param deleteObject.objectID - Unique record identifier.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteObject(\n      { indexName, objectID }: DeleteObjectProps,\n      requestOptions?: RequestOptions,\n    ): Promise<DeletedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `deleteObject`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `deleteObject`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/{objectID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a rule by its ID. To find the object ID for rules, use the [`search` operation](#tag/Rules/operation/searchRules).\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param deleteRule - The deleteRule object.\n     * @param deleteRule.indexName - Name of the index on which to perform the operation.\n     * @param deleteRule.objectID - Unique identifier of a rule object.\n     * @param deleteRule.forwardToReplicas - Whether changes are applied to replica indices.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteRule(\n      { indexName, objectID, forwardToReplicas }: DeleteRuleProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `deleteRule`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `deleteRule`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/rules/{objectID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (forwardToReplicas !== undefined) {\n        queryParameters['forwardToReplicas'] = forwardToReplicas.toString();\n      }\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a source from the list of allowed sources.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     * @param deleteSource - The deleteSource object.\n     * @param deleteSource.source - IP address range of the source.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteSource({ source }: DeleteSourceProps, requestOptions?: RequestOptions): Promise<DeleteSourceResponse> {\n      if (!source) {\n        throw new Error('Parameter `source` is required when calling `deleteSource`.');\n      }\n\n      const requestPath = '/1/security/sources/{source}'.replace('{source}', encodeURIComponent(source));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a synonym by its ID. To find the object IDs of your synonyms, use the [`search` operation](#tag/Synonyms/operation/searchSynonyms).\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param deleteSynonym - The deleteSynonym object.\n     * @param deleteSynonym.indexName - Name of the index on which to perform the operation.\n     * @param deleteSynonym.objectID - Unique identifier of a synonym object.\n     * @param deleteSynonym.forwardToReplicas - Whether changes are applied to replica indices.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    deleteSynonym(\n      { indexName, objectID, forwardToReplicas }: DeleteSynonymProps,\n      requestOptions?: RequestOptions,\n    ): Promise<DeletedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `deleteSynonym`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `deleteSynonym`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/synonyms/{objectID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (forwardToReplicas !== undefined) {\n        queryParameters['forwardToReplicas'] = forwardToReplicas.toString();\n      }\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Gets the permissions and restrictions of an API key.  When authenticating with the admin API key, you can request information for any of your application\\'s keys. When authenticating with other API keys, you can only retrieve information for that key, with the description replaced by `<redacted>`.\n     * @param getApiKey - The getApiKey object.\n     * @param getApiKey.key - API key.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getApiKey({ key }: GetApiKeyProps, requestOptions?: RequestOptions): Promise<GetApiKeyResponse> {\n      if (!key) {\n        throw new Error('Parameter `key` is required when calling `getApiKey`.');\n      }\n\n      const requestPath = '/1/keys/{key}'.replace('{key}', encodeURIComponent(key));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Checks the status of a given application task.\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param getAppTask - The getAppTask object.\n     * @param getAppTask.taskID - Unique task identifier.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getAppTask({ taskID }: GetAppTaskProps, requestOptions?: RequestOptions): Promise<GetTaskResponse> {\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `getAppTask`.');\n      }\n\n      const requestPath = '/1/task/{taskID}'.replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Lists supported languages with their supported dictionary types and number of custom entries.\n     *\n     * Required API Key ACLs:\n     *  - settings\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getDictionaryLanguages(requestOptions?: RequestOptions | undefined): Promise<{ [key: string]: Languages }> {\n      const requestPath = '/1/dictionaries/*/languages';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the languages for which standard dictionary entries are turned off.\n     *\n     * Required API Key ACLs:\n     *  - settings\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getDictionarySettings(requestOptions?: RequestOptions | undefined): Promise<GetDictionarySettingsResponse> {\n      const requestPath = '/1/dictionaries/*/settings';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * The request must be authenticated by an API key with the [`logs` ACL](https://www.algolia.com/doc/guides/security/api-keys/#access-control-list-acl).  - Logs are held for the last seven days. - Up to 1,000 API requests per server are logged. - This request counts towards your [operations quota](https://support.algolia.com/hc/en-us/articles/4406981829777-How-does-Algolia-count-records-and-operations-) but doesn\\'t appear in the logs itself.\n     *\n     * Required API Key ACLs:\n     *  - logs\n     * @param getLogs - The getLogs object.\n     * @param getLogs.offset - First log entry to retrieve. The most recent entries are listed first.\n     * @param getLogs.length - Maximum number of entries to retrieve.\n     * @param getLogs.indexName - Index for which to retrieve log entries. By default, log entries are retrieved for all indices.\n     * @param getLogs.type - Type of log entries to retrieve. By default, all log entries are retrieved.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getLogs(\n      { offset, length, indexName, type }: GetLogsProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<GetLogsResponse> {\n      const requestPath = '/1/logs';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (offset !== undefined) {\n        queryParameters['offset'] = offset.toString();\n      }\n\n      if (length !== undefined) {\n        queryParameters['length'] = length.toString();\n      }\n\n      if (indexName !== undefined) {\n        queryParameters['indexName'] = indexName.toString();\n      }\n\n      if (type !== undefined) {\n        queryParameters['type'] = type.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves one record by its object ID.  To retrieve more than one record, use the [`objects` operation](#tag/Records/operation/getObjects).\n     *\n     * Required API Key ACLs:\n     *  - search\n     * @param getObject - The getObject object.\n     * @param getObject.indexName - Name of the index on which to perform the operation.\n     * @param getObject.objectID - Unique record identifier.\n     * @param getObject.attributesToRetrieve - Attributes to include with the records in the response. This is useful to reduce the size of the API response. By default, all retrievable attributes are returned.  `objectID` is always retrieved.  Attributes included in `unretrievableAttributes` won\\'t be retrieved unless the request is authenticated with the admin API key.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getObject(\n      { indexName, objectID, attributesToRetrieve }: GetObjectProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `getObject`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `getObject`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/{objectID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (attributesToRetrieve !== undefined) {\n        queryParameters['attributesToRetrieve'] = attributesToRetrieve.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves one or more records, potentially from different indices.  Records are returned in the same order as the requests.\n     *\n     * Required API Key ACLs:\n     *  - search\n     * @param getObjectsParams - Request object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getObjects<T>(getObjectsParams: GetObjectsParams, requestOptions?: RequestOptions): Promise<GetObjectsResponse<T>> {\n      if (!getObjectsParams) {\n        throw new Error('Parameter `getObjectsParams` is required when calling `getObjects`.');\n      }\n\n      if (!getObjectsParams.requests) {\n        throw new Error('Parameter `getObjectsParams.requests` is required when calling `getObjects`.');\n      }\n\n      const requestPath = '/1/indexes/*/objects';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: getObjectsParams,\n        useReadTransporter: true,\n        cacheable: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a rule by its ID. To find the object ID of rules, use the [`search` operation](#tag/Rules/operation/searchRules).\n     *\n     * Required API Key ACLs:\n     *  - settings\n     * @param getRule - The getRule object.\n     * @param getRule.indexName - Name of the index on which to perform the operation.\n     * @param getRule.objectID - Unique identifier of a rule object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getRule({ indexName, objectID }: GetRuleProps, requestOptions?: RequestOptions): Promise<Rule> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `getRule`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `getRule`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/rules/{objectID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves an object with non-null index settings.\n     *\n     * Required API Key ACLs:\n     *  - settings\n     * @param getSettings - The getSettings object.\n     * @param getSettings.indexName - Name of the index on which to perform the operation.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getSettings({ indexName }: GetSettingsProps, requestOptions?: RequestOptions): Promise<SettingsResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `getSettings`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/settings'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves all allowed IP addresses with access to your application.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getSources(requestOptions?: RequestOptions | undefined): Promise<Array<Source>> {\n      const requestPath = '/1/security/sources';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves a synonym by its ID. To find the object IDs for your synonyms, use the [`search` operation](#tag/Synonyms/operation/searchSynonyms).\n     *\n     * Required API Key ACLs:\n     *  - settings\n     * @param getSynonym - The getSynonym object.\n     * @param getSynonym.indexName - Name of the index on which to perform the operation.\n     * @param getSynonym.objectID - Unique identifier of a synonym object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getSynonym({ indexName, objectID }: GetSynonymProps, requestOptions?: RequestOptions): Promise<SynonymHit> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `getSynonym`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `getSynonym`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/synonyms/{objectID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Checks the status of a given task.  Indexing tasks are asynchronous. When you add, update, or delete records or indices, a task is created on a queue and completed depending on the load on the server.  The indexing tasks\\' responses include a task ID that you can use to check the status.\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     * @param getTask - The getTask object.\n     * @param getTask.indexName - Name of the index on which to perform the operation.\n     * @param getTask.taskID - Unique task identifier.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getTask({ indexName, taskID }: GetTaskProps, requestOptions?: RequestOptions): Promise<GetTaskResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `getTask`.');\n      }\n\n      if (!taskID) {\n        throw new Error('Parameter `taskID` is required when calling `getTask`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/task/{taskID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{taskID}', encodeURIComponent(taskID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Get the IDs of the 10 users with the highest number of records per cluster.  Since it can take a few seconds to get the data from the different clusters, the response isn\\'t real-time.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     *\n     * @deprecated\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getTopUserIds(requestOptions?: RequestOptions | undefined): Promise<GetTopUserIdsResponse> {\n      const requestPath = '/1/clusters/mapping/top';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Returns the user ID data stored in the mapping.  Since it can take a few seconds to get the data from the different clusters, the response isn\\'t real-time.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     *\n     * @deprecated\n     * @param getUserId - The getUserId object.\n     * @param getUserId.userID - Unique identifier of the user who makes the search request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getUserId({ userID }: GetUserIdProps, requestOptions?: RequestOptions): Promise<UserId> {\n      if (!userID) {\n        throw new Error('Parameter `userID` is required when calling `getUserId`.');\n      }\n\n      const requestPath = '/1/clusters/mapping/{userID}'.replace('{userID}', encodeURIComponent(userID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * To determine when the time-consuming process of creating a large batch of users or migrating users from one cluster to another is complete, this operation retrieves the status of the process.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     *\n     * @deprecated\n     * @param hasPendingMappings - The hasPendingMappings object.\n     * @param hasPendingMappings.getClusters - Whether to include the cluster\\'s pending mapping state in the response.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    hasPendingMappings(\n      { getClusters }: HasPendingMappingsProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<HasPendingMappingsResponse> {\n      const requestPath = '/1/clusters/mapping/pending';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (getClusters !== undefined) {\n        queryParameters['getClusters'] = getClusters.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Lists all API keys associated with your Algolia application, including their permissions and restrictions.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listApiKeys(requestOptions?: RequestOptions | undefined): Promise<ListApiKeysResponse> {\n      const requestPath = '/1/keys';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Lists the available clusters in a multi-cluster setup.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     *\n     * @deprecated\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listClusters(requestOptions?: RequestOptions | undefined): Promise<ListClustersResponse> {\n      const requestPath = '/1/clusters';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Lists all indices in the current Algolia application.  The request follows any index restrictions of the API key you use to make the request.\n     *\n     * Required API Key ACLs:\n     *  - listIndexes\n     * @param listIndices - The listIndices object.\n     * @param listIndices.page - Requested page of the API response. If `null`, the API response is not paginated.\n     * @param listIndices.hitsPerPage - Number of hits per page.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listIndices(\n      { page, hitsPerPage }: ListIndicesProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<ListIndicesResponse> {\n      const requestPath = '/1/indexes';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (page !== undefined) {\n        queryParameters['page'] = page.toString();\n      }\n\n      if (hitsPerPage !== undefined) {\n        queryParameters['hitsPerPage'] = hitsPerPage.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Lists the userIDs assigned to a multi-cluster application.  Since it can take a few seconds to get the data from the different clusters, the response isn\\'t real-time.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     *\n     * @deprecated\n     * @param listUserIds - The listUserIds object.\n     * @param listUserIds.page - Requested page of the API response. If `null`, the API response is not paginated.\n     * @param listUserIds.hitsPerPage - Number of hits per page.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    listUserIds(\n      { page, hitsPerPage }: ListUserIdsProps = {},\n      requestOptions: RequestOptions | undefined = undefined,\n    ): Promise<ListUserIdsResponse> {\n      const requestPath = '/1/clusters/mapping';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (page !== undefined) {\n        queryParameters['page'] = page.toString();\n      }\n\n      if (hitsPerPage !== undefined) {\n        queryParameters['hitsPerPage'] = hitsPerPage.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Adds, updates, or deletes records in multiple indices with a single API request.  - Actions are applied in the order they are specified. - Actions are equivalent to the individual API requests of the same name.  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).\n     * @param batchParams - The batchParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    multipleBatch(batchParams: BatchParams, requestOptions?: RequestOptions): Promise<MultipleBatchResponse> {\n      if (!batchParams) {\n        throw new Error('Parameter `batchParams` is required when calling `multipleBatch`.');\n      }\n\n      if (!batchParams.requests) {\n        throw new Error('Parameter `batchParams.requests` is required when calling `multipleBatch`.');\n      }\n\n      const requestPath = '/1/indexes/*/batch';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: batchParams,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Copies or moves (renames) an index within the same Algolia application.  - Existing destination indices are overwritten, except for their analytics data. - If the destination index doesn\\'t exist yet, it\\'ll be created. - This operation is resource-intensive.  **Copy**  - Copying a source index that doesn\\'t exist creates a new index with 0 records and default settings. - The API keys of the source index are merged with the existing keys in the destination index. - You can\\'t copy the `enableReRanking`, `mode`, and `replicas` settings. - You can\\'t copy to a destination index that already has replicas. - Be aware of the [size limits](https://www.algolia.com/doc/guides/scaling/algolia-service-limits/#application-record-and-index-limits). - Related guide: [Copy indices](https://www.algolia.com/doc/guides/sending-and-managing-data/manage-indices-and-apps/manage-indices/how-to/copy-indices/)  **Move**  - Moving a source index that doesn\\'t exist is ignored without returning an error. - When moving an index, the analytics data keeps its original name, and a new set of analytics data is started for the new name.   To access the original analytics in the dashboard, create an index with the original name. - If the destination index has replicas, moving will overwrite the existing index and copy the data to the replica indices. - Related guide: [Move indices](https://www.algolia.com/doc/guides/sending-and-managing-data/manage-indices-and-apps/manage-indices/how-to/move-indices/).  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     * @param operationIndex - The operationIndex object.\n     * @param operationIndex.indexName - Name of the index on which to perform the operation.\n     * @param operationIndex.operationIndexParams - The operationIndexParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    operationIndex(\n      { indexName, operationIndexParams }: OperationIndexProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `operationIndex`.');\n      }\n\n      if (!operationIndexParams) {\n        throw new Error('Parameter `operationIndexParams` is required when calling `operationIndex`.');\n      }\n\n      if (!operationIndexParams.operation) {\n        throw new Error('Parameter `operationIndexParams.operation` is required when calling `operationIndex`.');\n      }\n      if (!operationIndexParams.destination) {\n        throw new Error('Parameter `operationIndexParams.destination` is required when calling `operationIndex`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/operation'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: operationIndexParams,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Adds new attributes to a record, or updates existing ones.  - If a record with the specified object ID doesn\\'t exist,   a new record is added to the index **if** `createIfNotExists` is true. - If the index doesn\\'t exist yet, this method creates a new index. - You can use any first-level attribute but not nested attributes.   If you specify a nested attribute, this operation replaces its first-level ancestor.  To update an attribute without pushing the entire record, you can use these built-in operations. These operations can be helpful if you don\\'t have access to your initial data.  - Increment: increment a numeric attribute - Decrement: decrement a numeric attribute - Add: append a number or string element to an array attribute - Remove: remove all matching number or string elements from an array attribute made of numbers or strings - AddUnique: add a number or string element to an array attribute made of numbers or strings only if it\\'s not already present - IncrementFrom: increment a numeric integer attribute only if the provided value matches the current value, and otherwise ignore the whole object update. For example, if you pass an IncrementFrom value of 2 for the version attribute, but the current value of the attribute is 1, the engine ignores the update. If the object doesn\\'t exist, the engine only creates it if you pass an IncrementFrom value of 0. - IncrementSet: increment a numeric integer attribute only if the provided value is greater than the current value, and otherwise ignore the whole object update. For example, if you pass an IncrementSet value of 2 for the version attribute, and the current value of the attribute is 1, the engine updates the object. If the object doesn\\'t exist yet, the engine only creates it if you pass an IncrementSet value greater than 0.  You can specify an operation by providing an object with the attribute to update as the key and its value being an object with the following properties:  - _operation: the operation to apply on the attribute - value: the right-hand side argument to the operation, for example, increment or decrement step, value to add or remove.  When updating multiple attributes or using multiple operations targeting the same record, you should use a single partial update for faster processing.  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     * @param partialUpdateObject - The partialUpdateObject object.\n     * @param partialUpdateObject.indexName - Name of the index on which to perform the operation.\n     * @param partialUpdateObject.objectID - Unique record identifier.\n     * @param partialUpdateObject.attributesToUpdate - Attributes with their values.\n     * @param partialUpdateObject.createIfNotExists - Whether to create a new record if it doesn\\'t exist.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    partialUpdateObject(\n      { indexName, objectID, attributesToUpdate, createIfNotExists }: PartialUpdateObjectProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtWithObjectIdResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `partialUpdateObject`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `partialUpdateObject`.');\n      }\n\n      if (!attributesToUpdate) {\n        throw new Error('Parameter `attributesToUpdate` is required when calling `partialUpdateObject`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/{objectID}/partial'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (createIfNotExists !== undefined) {\n        queryParameters['createIfNotExists'] = createIfNotExists.toString();\n      }\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: attributesToUpdate,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Deletes a user ID and its associated data from the clusters.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     *\n     * @deprecated\n     * @param removeUserId - The removeUserId object.\n     * @param removeUserId.userID - Unique identifier of the user who makes the search request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    removeUserId({ userID }: RemoveUserIdProps, requestOptions?: RequestOptions): Promise<RemoveUserIdResponse> {\n      if (!userID) {\n        throw new Error('Parameter `userID` is required when calling `removeUserId`.');\n      }\n\n      const requestPath = '/1/clusters/mapping/{userID}'.replace('{userID}', encodeURIComponent(userID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Replaces the list of allowed sources.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     * @param replaceSources - The replaceSources object.\n     * @param replaceSources.source - Allowed sources.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    replaceSources({ source }: ReplaceSourcesProps, requestOptions?: RequestOptions): Promise<ReplaceSourceResponse> {\n      if (!source) {\n        throw new Error('Parameter `source` is required when calling `replaceSources`.');\n      }\n\n      const requestPath = '/1/security/sources';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: source,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Restores a deleted API key.  Restoring resets the `validity` attribute to `0`.  Algolia stores up to 1,000 API keys per application. If you create more, the oldest API keys are deleted and can\\'t be restored.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     * @param restoreApiKey - The restoreApiKey object.\n     * @param restoreApiKey.key - API key.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    restoreApiKey({ key }: RestoreApiKeyProps, requestOptions?: RequestOptions): Promise<AddApiKeyResponse> {\n      if (!key) {\n        throw new Error('Parameter `key` is required when calling `restoreApiKey`.');\n      }\n\n      const requestPath = '/1/keys/{key}/restore'.replace('{key}', encodeURIComponent(key));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Adds a record to an index or replaces it.  - If the record doesn\\'t have an object ID, a new record with an auto-generated object ID is added to your index. - If a record with the specified object ID exists, the existing record is replaced. - If a record with the specified object ID doesn\\'t exist, a new record is added to your index. - If you add a record to an index that doesn\\'t exist yet, a new index is created.  To update _some_ attributes of a record, use the [`partial` operation](#tag/Records/operation/partialUpdateObject). To add, update, or replace multiple records, use the [`batch` operation](#tag/Records/operation/batch).  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).\n     *\n     * Required API Key ACLs:\n     *  - addObject\n     * @param saveObject - The saveObject object.\n     * @param saveObject.indexName - Name of the index on which to perform the operation.\n     * @param saveObject.body - The record. A schemaless object with attributes that are useful in the context of search and discovery.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    saveObject<T extends object>(\n      { indexName, body }: SaveObjectProps<T>,\n      requestOptions?: RequestOptions,\n    ): Promise<SaveObjectResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `saveObject`.');\n      }\n\n      if (!body) {\n        throw new Error('Parameter `body` is required when calling `saveObject`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * If a rule with the specified object ID doesn\\'t exist, it\\'s created. Otherwise, the existing rule is replaced.  To create or update more than one rule, use the [`batch` operation](#tag/Rules/operation/saveRules).\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param saveRule - The saveRule object.\n     * @param saveRule.indexName - Name of the index on which to perform the operation.\n     * @param saveRule.objectID - Unique identifier of a rule object.\n     * @param saveRule.rule - The rule object.\n     * @param saveRule.forwardToReplicas - Whether changes are applied to replica indices.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    saveRule(\n      { indexName, objectID, rule, forwardToReplicas }: SaveRuleProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `saveRule`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `saveRule`.');\n      }\n\n      if (!rule) {\n        throw new Error('Parameter `rule` is required when calling `saveRule`.');\n      }\n\n      if (!rule.objectID) {\n        throw new Error('Parameter `rule.objectID` is required when calling `saveRule`.');\n      }\n      if (!rule.consequence) {\n        throw new Error('Parameter `rule.consequence` is required when calling `saveRule`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/rules/{objectID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (forwardToReplicas !== undefined) {\n        queryParameters['forwardToReplicas'] = forwardToReplicas.toString();\n      }\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: rule,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Create or update multiple rules.  If a rule with the specified object ID doesn\\'t exist, Algolia creates a new one. Otherwise, existing rules are replaced.  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param saveRules - The saveRules object.\n     * @param saveRules.indexName - Name of the index on which to perform the operation.\n     * @param saveRules.rules - The rules object.\n     * @param saveRules.forwardToReplicas - Whether changes are applied to replica indices.\n     * @param saveRules.clearExistingRules - Whether existing rules should be deleted before adding this batch.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    saveRules(\n      { indexName, rules, forwardToReplicas, clearExistingRules }: SaveRulesProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `saveRules`.');\n      }\n\n      if (!rules) {\n        throw new Error('Parameter `rules` is required when calling `saveRules`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/rules/batch'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (forwardToReplicas !== undefined) {\n        queryParameters['forwardToReplicas'] = forwardToReplicas.toString();\n      }\n\n      if (clearExistingRules !== undefined) {\n        queryParameters['clearExistingRules'] = clearExistingRules.toString();\n      }\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: rules,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * If a synonym with the specified object ID doesn\\'t exist, Algolia adds a new one. Otherwise, the existing synonym is replaced. To add multiple synonyms in a single API request, use the [`batch` operation](#tag/Synonyms/operation/saveSynonyms).\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param saveSynonym - The saveSynonym object.\n     * @param saveSynonym.indexName - Name of the index on which to perform the operation.\n     * @param saveSynonym.objectID - Unique identifier of a synonym object.\n     * @param saveSynonym.synonymHit - The synonymHit object.\n     * @param saveSynonym.forwardToReplicas - Whether changes are applied to replica indices.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    saveSynonym(\n      { indexName, objectID, synonymHit, forwardToReplicas }: SaveSynonymProps,\n      requestOptions?: RequestOptions,\n    ): Promise<SaveSynonymResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `saveSynonym`.');\n      }\n\n      if (!objectID) {\n        throw new Error('Parameter `objectID` is required when calling `saveSynonym`.');\n      }\n\n      if (!synonymHit) {\n        throw new Error('Parameter `synonymHit` is required when calling `saveSynonym`.');\n      }\n\n      if (!synonymHit.objectID) {\n        throw new Error('Parameter `synonymHit.objectID` is required when calling `saveSynonym`.');\n      }\n      if (!synonymHit.type) {\n        throw new Error('Parameter `synonymHit.type` is required when calling `saveSynonym`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/synonyms/{objectID}'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{objectID}', encodeURIComponent(objectID));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (forwardToReplicas !== undefined) {\n        queryParameters['forwardToReplicas'] = forwardToReplicas.toString();\n      }\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: synonymHit,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * If a synonym with the `objectID` doesn\\'t exist, Algolia adds a new one. Otherwise, existing synonyms are replaced.  This operation is subject to [indexing rate limits](https://support.algolia.com/hc/en-us/articles/4406975251089-Is-there-a-rate-limit-for-indexing-on-Algolia).\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param saveSynonyms - The saveSynonyms object.\n     * @param saveSynonyms.indexName - Name of the index on which to perform the operation.\n     * @param saveSynonyms.synonymHit - The synonymHit object.\n     * @param saveSynonyms.forwardToReplicas - Whether changes are applied to replica indices.\n     * @param saveSynonyms.replaceExistingSynonyms - Whether to replace all synonyms in the index with the ones sent with this request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    saveSynonyms(\n      { indexName, synonymHit, forwardToReplicas, replaceExistingSynonyms }: SaveSynonymsProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `saveSynonyms`.');\n      }\n\n      if (!synonymHit) {\n        throw new Error('Parameter `synonymHit` is required when calling `saveSynonyms`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/synonyms/batch'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (forwardToReplicas !== undefined) {\n        queryParameters['forwardToReplicas'] = forwardToReplicas.toString();\n      }\n\n      if (replaceExistingSynonyms !== undefined) {\n        queryParameters['replaceExistingSynonyms'] = replaceExistingSynonyms.toString();\n      }\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: synonymHit,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Sends multiple search requests to one or more indices.  This can be useful in these cases:  - Different indices for different purposes, such as, one index for products, another one for marketing content. - Multiple searches to the same index—for example, with different filters.  Use the helper `searchForHits` or `searchForFacets` to get the results in a more convenient format, if you already know the return type you want.\n     *\n     * Required API Key ACLs:\n     *  - search\n     * @param searchMethodParams - Muli-search request body. Results are returned in the same order as the requests.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    search<T>(\n      searchMethodParams: SearchMethodParams | LegacySearchMethodProps,\n      requestOptions?: RequestOptions,\n    ): Promise<SearchResponses<T>> {\n      if (searchMethodParams && Array.isArray(searchMethodParams)) {\n        const newSignatureRequest: SearchMethodParams = {\n          requests: searchMethodParams.map(({ params, ...legacyRequest }) => {\n            if (legacyRequest.type === 'facet') {\n              return {\n                ...legacyRequest,\n                ...params,\n                type: 'facet',\n              };\n            }\n\n            return {\n              ...legacyRequest,\n              ...params,\n              facet: undefined,\n              maxFacetHits: undefined,\n              facetQuery: undefined,\n            };\n          }),\n        };\n\n        searchMethodParams = newSignatureRequest;\n      }\n\n      if (!searchMethodParams) {\n        throw new Error('Parameter `searchMethodParams` is required when calling `search`.');\n      }\n\n      if (!searchMethodParams.requests) {\n        throw new Error('Parameter `searchMethodParams.requests` is required when calling `search`.');\n      }\n\n      const requestPath = '/1/indexes/*/queries';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: searchMethodParams,\n        useReadTransporter: true,\n        cacheable: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for standard and custom dictionary entries.\n     *\n     * Required API Key ACLs:\n     *  - settings\n     * @param searchDictionaryEntries - The searchDictionaryEntries object.\n     * @param searchDictionaryEntries.dictionaryName - Dictionary type in which to search.\n     * @param searchDictionaryEntries.searchDictionaryEntriesParams - The searchDictionaryEntriesParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchDictionaryEntries(\n      { dictionaryName, searchDictionaryEntriesParams }: SearchDictionaryEntriesProps,\n      requestOptions?: RequestOptions,\n    ): Promise<SearchDictionaryEntriesResponse> {\n      if (!dictionaryName) {\n        throw new Error('Parameter `dictionaryName` is required when calling `searchDictionaryEntries`.');\n      }\n\n      if (!searchDictionaryEntriesParams) {\n        throw new Error(\n          'Parameter `searchDictionaryEntriesParams` is required when calling `searchDictionaryEntries`.',\n        );\n      }\n\n      if (!searchDictionaryEntriesParams.query) {\n        throw new Error(\n          'Parameter `searchDictionaryEntriesParams.query` is required when calling `searchDictionaryEntries`.',\n        );\n      }\n\n      const requestPath = '/1/dictionaries/{dictionaryName}/search'.replace(\n        '{dictionaryName}',\n        encodeURIComponent(dictionaryName),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: searchDictionaryEntriesParams,\n        useReadTransporter: true,\n        cacheable: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for values of a specified facet attribute.  - By default, facet values are sorted by decreasing count.   You can adjust this with the `sortFacetValueBy` parameter. - Searching for facet values doesn\\'t work if you have **more than 65 searchable facets and searchable attributes combined**.\n     *\n     * Required API Key ACLs:\n     *  - search\n     * @param searchForFacetValues - The searchForFacetValues object.\n     * @param searchForFacetValues.indexName - Name of the index on which to perform the operation.\n     * @param searchForFacetValues.facetName - Facet attribute in which to search for values.  This attribute must be included in the `attributesForFaceting` index setting with the `searchable()` modifier.\n     * @param searchForFacetValues.searchForFacetValuesRequest - The searchForFacetValuesRequest object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchForFacetValues(\n      { indexName, facetName, searchForFacetValuesRequest }: SearchForFacetValuesProps,\n      requestOptions?: RequestOptions,\n    ): Promise<SearchForFacetValuesResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `searchForFacetValues`.');\n      }\n\n      if (!facetName) {\n        throw new Error('Parameter `facetName` is required when calling `searchForFacetValues`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/facets/{facetName}/query'\n        .replace('{indexName}', encodeURIComponent(indexName))\n        .replace('{facetName}', encodeURIComponent(facetName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: searchForFacetValuesRequest ? searchForFacetValuesRequest : {},\n        useReadTransporter: true,\n        cacheable: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for rules in your index.\n     *\n     * Required API Key ACLs:\n     *  - settings\n     * @param searchRules - The searchRules object.\n     * @param searchRules.indexName - Name of the index on which to perform the operation.\n     * @param searchRules.searchRulesParams - The searchRulesParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchRules(\n      { indexName, searchRulesParams }: SearchRulesProps,\n      requestOptions?: RequestOptions,\n    ): Promise<SearchRulesResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `searchRules`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/rules/search'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: searchRulesParams ? searchRulesParams : {},\n        useReadTransporter: true,\n        cacheable: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches a single index and returns matching search results (_hits_).  This method lets you retrieve up to 1,000 hits. If you need more, use the [`browse` operation](#tag/Search/operation/browse) or increase the `paginatedLimitedTo` index setting.\n     *\n     * Required API Key ACLs:\n     *  - search\n     * @param searchSingleIndex - The searchSingleIndex object.\n     * @param searchSingleIndex.indexName - Name of the index on which to perform the operation.\n     * @param searchSingleIndex.searchParams - The searchParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchSingleIndex<T>(\n      { indexName, searchParams }: SearchSingleIndexProps,\n      requestOptions?: RequestOptions,\n    ): Promise<SearchResponse<T>> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `searchSingleIndex`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/query'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: searchParams ? searchParams : {},\n        useReadTransporter: true,\n        cacheable: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Searches for synonyms in your index.\n     *\n     * Required API Key ACLs:\n     *  - settings\n     * @param searchSynonyms - The searchSynonyms object.\n     * @param searchSynonyms.indexName - Name of the index on which to perform the operation.\n     * @param searchSynonyms.searchSynonymsParams - Body of the `searchSynonyms` operation.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchSynonyms(\n      { indexName, searchSynonymsParams }: SearchSynonymsProps,\n      requestOptions?: RequestOptions,\n    ): Promise<SearchSynonymsResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `searchSynonyms`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/synonyms/search'.replace(\n        '{indexName}',\n        encodeURIComponent(indexName),\n      );\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: searchSynonymsParams ? searchSynonymsParams : {},\n        useReadTransporter: true,\n        cacheable: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Since it can take a few seconds to get the data from the different clusters, the response isn\\'t real-time.  To ensure rapid updates, the user IDs index isn\\'t built at the same time as the mapping. Instead, it\\'s built every 12 hours, at the same time as the update of user ID usage. For example, if you add or move a user ID, the search will show an old value until the next time the mapping is rebuilt (every 12 hours).\n     *\n     * Required API Key ACLs:\n     *  - admin\n     *\n     * @deprecated\n     * @param searchUserIdsParams - The searchUserIdsParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchUserIds(\n      searchUserIdsParams: SearchUserIdsParams,\n      requestOptions?: RequestOptions,\n    ): Promise<SearchUserIdsResponse> {\n      if (!searchUserIdsParams) {\n        throw new Error('Parameter `searchUserIdsParams` is required when calling `searchUserIds`.');\n      }\n\n      if (!searchUserIdsParams.query) {\n        throw new Error('Parameter `searchUserIdsParams.query` is required when calling `searchUserIds`.');\n      }\n\n      const requestPath = '/1/clusters/mapping/search';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: searchUserIdsParams,\n        useReadTransporter: true,\n        cacheable: true,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Turns standard stop word dictionary entries on or off for a given language.\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param dictionarySettingsParams - The dictionarySettingsParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    setDictionarySettings(\n      dictionarySettingsParams: DictionarySettingsParams,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!dictionarySettingsParams) {\n        throw new Error('Parameter `dictionarySettingsParams` is required when calling `setDictionarySettings`.');\n      }\n\n      if (!dictionarySettingsParams.disableStandardEntries) {\n        throw new Error(\n          'Parameter `dictionarySettingsParams.disableStandardEntries` is required when calling `setDictionarySettings`.',\n        );\n      }\n\n      const requestPath = '/1/dictionaries/*/settings';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: dictionarySettingsParams,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Update the specified index settings.  Index settings that you don\\'t specify are left unchanged. Specify `null` to reset a setting to its default value.  For best performance, update the index settings before you add new records to your index.\n     *\n     * Required API Key ACLs:\n     *  - editSettings\n     * @param setSettings - The setSettings object.\n     * @param setSettings.indexName - Name of the index on which to perform the operation.\n     * @param setSettings.indexSettings - The indexSettings object.\n     * @param setSettings.forwardToReplicas - Whether changes are applied to replica indices.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    setSettings(\n      { indexName, indexSettings, forwardToReplicas }: SetSettingsProps,\n      requestOptions?: RequestOptions,\n    ): Promise<UpdatedAtResponse> {\n      if (!indexName) {\n        throw new Error('Parameter `indexName` is required when calling `setSettings`.');\n      }\n\n      if (!indexSettings) {\n        throw new Error('Parameter `indexSettings` is required when calling `setSettings`.');\n      }\n\n      const requestPath = '/1/indexes/{indexName}/settings'.replace('{indexName}', encodeURIComponent(indexName));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (forwardToReplicas !== undefined) {\n        queryParameters['forwardToReplicas'] = forwardToReplicas.toString();\n      }\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: indexSettings,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Replaces the permissions of an existing API key.  Any unspecified attribute resets that attribute to its default value.\n     *\n     * Required API Key ACLs:\n     *  - admin\n     * @param updateApiKey - The updateApiKey object.\n     * @param updateApiKey.key - API key.\n     * @param updateApiKey.apiKey - The apiKey object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    updateApiKey({ key, apiKey }: UpdateApiKeyProps, requestOptions?: RequestOptions): Promise<UpdateApiKeyResponse> {\n      if (!key) {\n        throw new Error('Parameter `key` is required when calling `updateApiKey`.');\n      }\n\n      if (!apiKey) {\n        throw new Error('Parameter `apiKey` is required when calling `updateApiKey`.');\n      }\n\n      if (!apiKey.acl) {\n        throw new Error('Parameter `apiKey.acl` is required when calling `updateApiKey`.');\n      }\n\n      const requestPath = '/1/keys/{key}'.replace('{key}', encodeURIComponent(key));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: apiKey,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,yBAA2B;AAE3B,iCAAoC;AAEpC,IAAAA,wBAQO;;;ACLP,2BAOO;AA+HA,IAAM,mBAAmB;AAEhC,SAAS,gBAAgB,OAAuB;AAC9C,SACE;AAAA,IACE;AAAA,MACE,KAAK,GAAG,KAAK;AAAA,MACb,QAAQ;AAAA,MACR,UAAU;AAAA,IACZ;AAAA,IACA;AAAA,MACE,KAAK,GAAG,KAAK;AAAA,MACb,QAAQ;AAAA,MACR,UAAU;AAAA,IACZ;AAAA,EACF,EACA;AAAA,QACA,8BAAQ;AAAA,MACN;AAAA,QACE,KAAK,GAAG,KAAK;AAAA,QACb,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,KAAK,GAAG,KAAK;AAAA,QACb,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,KAAK,GAAG,KAAK;AAAA,QACb,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEO,SAAS,mBAAmB;AAAA,EACjC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA,GAAG;AACL,GAAwB;AACtB,QAAM,WAAO,iCAAW,aAAa,cAAc,QAAQ;AAC3D,QAAM,kBAAc,wCAAkB;AAAA,IACpC,OAAO,gBAAgB,WAAW;AAAA,IAClC,GAAG;AAAA,IACH,kBAAc,sCAAgB;AAAA,MAC5B;AAAA,MACA,QAAQ;AAAA,MACR,SAAS;AAAA,IACX,CAAC;AAAA,IACD,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,GAAG,KAAK,QAAQ;AAAA,MAChB,GAAG,QAAQ;AAAA,IACb;AAAA,IACA,qBAAqB;AAAA,MACnB,GAAG,KAAK,gBAAgB;AAAA,MACxB,GAAG,QAAQ;AAAA,IACb;AAAA,EACF,CAAC;AAED,SAAO;AAAA,IACL;AAAA;AAAA;AAAA;AAAA,IAKA,OAAO;AAAA;AAAA;AAAA;AAAA,IAKP,QAAQ;AAAA;AAAA;AAAA;AAAA,IAKR,aAA4B;AAC1B,aAAO,QAAQ,IAAI,CAAC,YAAY,cAAc,MAAM,GAAG,YAAY,eAAe,MAAM,CAAC,CAAC,EAAE,KAAK,MAAM,MAAS;AAAA,IAClH;AAAA;AAAA;AAAA;AAAA,IAKA,IAAI,MAAc;AAChB,aAAO,YAAY,aAAa;AAAA,IAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,gBAAgB,SAAiB,SAAoC;AACnE,kBAAY,aAAa,IAAI,EAAE,SAAS,QAAQ,CAAC;AAAA,IACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,gBAAgB,EAAE,OAAO,GAA6B;AACpD,UAAI,CAAC,YAAY,aAAa,iBAAiB;AAC7C,oBAAY,YAAY,mBAAmB,IAAI;AAAA,MACjD,OAAO;AACL,oBAAY,oBAAoB,mBAAmB,IAAI;AAAA,MACzD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,YACE;AAAA,MACE;AAAA,MACA;AAAA,MACA,aAAa;AAAA,MACb,UAAU,CAAC,eAA+B,KAAK,IAAI,aAAa,KAAK,GAAI;AAAA,IAC3E,GACA,gBAC0B;AAC1B,UAAI,aAAa;AAEjB,iBAAO,4CAAsB;AAAA,QAC3B,MAAM,MAAM,KAAK,QAAQ,EAAE,WAAW,OAAO,GAAG,cAAc;AAAA,QAC9D,UAAU,CAAC,aAAa,SAAS,WAAW;AAAA,QAC5C,YAAY,MAAO,cAAc;AAAA,QACjC,OAAO;AAAA,UACL,UAAU,MAAM,cAAc;AAAA,UAC9B,SAAS,MAAM,4CAA4C,UAAU,IAAI,UAAU;AAAA,QACrF;AAAA,QACA,SAAS,MAAM,QAAQ,UAAU;AAAA,MACnC,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,eACE;AAAA,MACE;AAAA,MACA,aAAa;AAAA,MACb,UAAU,CAAC,eAA+B,KAAK,IAAI,aAAa,KAAK,GAAI;AAAA,IAC3E,GACA,gBAC0B;AAC1B,UAAI,aAAa;AAEjB,iBAAO,4CAAsB;AAAA,QAC3B,MAAM,MAAM,KAAK,WAAW,EAAE,OAAO,GAAG,cAAc;AAAA,QACtD,UAAU,CAAC,aAAa,SAAS,WAAW;AAAA,QAC5C,YAAY,MAAO,cAAc;AAAA,QACjC,OAAO;AAAA,UACL,UAAU,MAAM,cAAc;AAAA,UAC9B,SAAS,MAAM,4CAA4C,UAAU,IAAI,UAAU;AAAA,QACrF;AAAA,QACA,SAAS,MAAM,QAAQ,UAAU;AAAA,MACnC,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,cACE;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa;AAAA,MACb,UAAU,CAAC,eAA+B,KAAK,IAAI,aAAa,KAAK,GAAI;AAAA,IAC3E,GACA,gBACwC;AACxC,UAAI,aAAa;AACjB,YAAM,sBAAsE;AAAA,QAC1E,YAAY,MAAO,cAAc;AAAA,QACjC,OAAO;AAAA,UACL,UAAU,MAAM,cAAc;AAAA,UAC9B,SAAS,MAAM,4CAA4C,UAAU,IAAI,UAAU;AAAA,QACrF;AAAA,QACA,SAAS,MAAM,QAAQ,UAAU;AAAA,MACnC;AAEA,UAAI,cAAc,UAAU;AAC1B,YAAI,CAAC,QAAQ;AACX,gBAAM,IAAI,MAAM,8DAA8D;AAAA,QAChF;AAEA,mBAAO,4CAAsB;AAAA,UAC3B,GAAG;AAAA,UACH,MAAM,MAAM,KAAK,UAAU,EAAE,IAAI,GAAG,cAAc;AAAA,UAClD,UAAU,CAAC,aAAa;AACtB,uBAAW,SAAS,OAAO,KAAK,MAAM,GAAG;AACvC,oBAAM,QAAQ,OAAO,KAAqB;AAC1C,oBAAM,WAAW,SAAS,KAAqB;AAC/C,kBAAI,MAAM,QAAQ,KAAK,KAAK,MAAM,QAAQ,QAAQ,GAAG;AACnD,oBAAI,MAAM,WAAW,SAAS,UAAU,MAAM,KAAK,CAAC,GAAG,UAAU,MAAM,SAAS,KAAK,CAAC,GAAG;AACvF,yBAAO;AAAA,gBACT;AAAA,cACF,WAAW,UAAU,UAAU;AAC7B,uBAAO;AAAA,cACT;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AAAA,QACF,CAAC;AAAA,MACH;AAEA,iBAAO,4CAAsB;AAAA,QAC3B,GAAG;AAAA,QACH,MAAM,MACJ,KAAK,UAAU,EAAE,IAAI,GAAG,cAAc,EAAE,MAAM,CAAC,UAAoB;AACjE,cAAI,MAAM,WAAW,KAAK;AACxB,mBAAO;AAAA,UACT;AAEA,gBAAM;AAAA,QACR,CAAC;AAAA,QACH,UAAU,CAAC,aAAc,cAAc,QAAQ,aAAa,SAAY,aAAa;AAAA,MACvF,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,cACE,EAAE,WAAW,cAAc,GAAG,qBAAqB,GACnD,gBAC4B;AAC5B,iBAAO,4CAAyC;AAAA,QAC9C,MAAM,CAAC,qBAAqB;AAC1B,iBAAO,KAAK;AAAA,YACV;AAAA,cACE;AAAA,cACA,cAAc;AAAA,gBACZ,QAAQ,mBAAmB,iBAAiB,SAAS;AAAA,gBACrD,aAAa;AAAA,gBACb,GAAG;AAAA,cACL;AAAA,YACF;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA,UAAU,CAAC,aAAa,SAAS,WAAW;AAAA,QAC5C,GAAG;AAAA,MACL,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,YACE,EAAE,WAAW,mBAAmB,GAAG,mBAAmB,GACtD,gBAC8B;AAC9B,YAAM,SAAS;AAAA,QACb,GAAG;AAAA,QACH,cAAa,uDAAmB,gBAAe;AAAA,MACjD;AAEA,iBAAO,4CAA2C;AAAA,QAChD,MAAM,CAAC,qBAAqB;AAC1B,iBAAO,KAAK;AAAA,YACV;AAAA,cACE;AAAA,cACA,mBAAmB;AAAA,gBACjB,GAAG;AAAA,gBACH,MAAM,mBAAmB,iBAAiB,OAAO,IAAI,OAAO,QAAQ;AAAA,cACtE;AAAA,YACF;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA,UAAU,CAAC,aAAa,SAAS,KAAK,SAAS,OAAO;AAAA,QACtD,GAAG;AAAA,MACL,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,eACE;AAAA,MACE;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL,GACA,gBACiC;AACjC,YAAM,SAAS;AAAA,QACb,GAAG;AAAA,QACH,OAAM,6DAAsB,SAAQ;AAAA,QACpC,aAAa;AAAA,MACf;AAEA,iBAAO,4CAA8C;AAAA,QACnD,MAAM,CAAC,MAAM;AACX,gBAAM,OAAO,KAAK;AAAA,YAChB;AAAA,cACE;AAAA,cACA,sBAAsB;AAAA,gBACpB,GAAG;AAAA,gBACH,MAAM,OAAO;AAAA,cACf;AAAA,YACF;AAAA,YACA;AAAA,UACF;AACA,iBAAO,QAAQ;AACf,iBAAO;AAAA,QACT;AAAA,QACA,UAAU,CAAC,aAAa,SAAS,KAAK,SAAS,OAAO;AAAA,QACtD,GAAG;AAAA,MACL,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,MAAM,aACJ,EAAE,WAAW,SAAS,SAAS,aAAa,cAAc,YAAY,IAAK,GAC3E,gBAC+B;AAC/B,UAAI,WAAgC,CAAC;AACrC,YAAM,YAAkC,CAAC;AAEzC,YAAM,gBAAgB,QAAQ,QAAQ;AACtC,iBAAW,CAAC,GAAG,GAAG,KAAK,eAAe;AACpC,iBAAS,KAAK,EAAE,QAAQ,MAAM,IAAI,CAAC;AACnC,YAAI,SAAS,WAAW,aAAa,MAAM,QAAQ,SAAS,GAAG;AAC7D,oBAAU,KAAK,MAAM,KAAK,MAAM,EAAE,WAAW,kBAAkB,EAAE,SAAS,EAAE,GAAG,cAAc,CAAC;AAC9F,qBAAW,CAAC;AAAA,QACd;AAAA,MACF;AAEA,UAAI,cAAc;AAChB,mBAAW,QAAQ,WAAW;AAC5B,gBAAM,KAAK,YAAY,EAAE,WAAW,QAAQ,KAAK,OAAO,CAAC;AAAA,QAC3D;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,MAAM,YACJ,EAAE,WAAW,SAAS,cAAc,UAAU,GAC9C,gBAC0B;AAC1B,aAAO,MAAM,KAAK;AAAA,QAChB,EAAE,WAAW,SAAS,QAAQ,aAAa,cAAc,UAAU;AAAA,QACnE;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,MAAM,cACJ,EAAE,WAAW,WAAW,cAAc,UAAU,GAChD,gBAC0B;AAC1B,aAAO,MAAM,KAAK;AAAA,QAChB;AAAA,UACE;AAAA,UACA,SAAS,UAAU,IAAI,CAAC,cAAc,EAAE,SAAS,EAAE;AAAA,UACnD,QAAQ;AAAA,UACR;AAAA,UACA;AAAA,QACF;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,MAAM,qBACJ,EAAE,WAAW,SAAS,mBAAmB,cAAc,UAAU,GACjE,gBAC0B;AAC1B,aAAO,MAAM,KAAK;AAAA,QAChB;AAAA,UACE;AAAA,UACA;AAAA,UACA,QAAQ,oBAAoB,wBAAwB;AAAA,UACpD;AAAA,UACA;AAAA,QACF;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,MAAM,kBACJ,EAAE,WAAW,SAAS,WAAW,OAAO,GACxC,gBACoC;AACpC,YAAM,eAAe,KAAK,MAAM,KAAK,OAAO,IAAI,GAAO,IAAI;AAC3D,YAAM,eAAe,GAAG,SAAS,QAAQ,YAAY;AAErD,UAAI,WAAW,QAAW;AACxB,iBAAS,CAAC,YAAY,SAAS,UAAU;AAAA,MAC3C;AAEA,UAAI;AACF,YAAI,wBAAwB,MAAM,KAAK;AAAA,UACrC;AAAA,YACE;AAAA,YACA,sBAAsB;AAAA,cACpB,WAAW;AAAA,cACX,aAAa;AAAA,cACb,OAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA;AAAA,QACF;AAEA,cAAM,iBAAiB,MAAM,KAAK;AAAA,UAChC,EAAE,WAAW,cAAc,SAAS,cAAc,MAAM,UAAU;AAAA,UAClE;AAAA,QACF;AAEA,cAAM,KAAK,YAAY;AAAA,UACrB,WAAW;AAAA,UACX,QAAQ,sBAAsB;AAAA,QAChC,CAAC;AAED,gCAAwB,MAAM,KAAK;AAAA,UACjC;AAAA,YACE;AAAA,YACA,sBAAsB;AAAA,cACpB,WAAW;AAAA,cACX,aAAa;AAAA,cACb,OAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA;AAAA,QACF;AACA,cAAM,KAAK,YAAY;AAAA,UACrB,WAAW;AAAA,UACX,QAAQ,sBAAsB;AAAA,QAChC,CAAC;AAED,cAAM,wBAAwB,MAAM,KAAK;AAAA,UACvC;AAAA,YACE,WAAW;AAAA,YACX,sBAAsB,EAAE,WAAW,QAAQ,aAAa,UAAU;AAAA,UACpE;AAAA,UACA;AAAA,QACF;AACA,cAAM,KAAK,YAAY;AAAA,UACrB,WAAW;AAAA,UACX,QAAQ,sBAAsB;AAAA,QAChC,CAAC;AAED,eAAO,EAAE,uBAAuB,gBAAgB,sBAAsB;AAAA,MACxE,SAAS,OAAO;AACd,cAAM,KAAK,YAAY,EAAE,WAAW,aAAa,CAAC;AAElD,cAAM;AAAA,MACR;AAAA,IACF;AAAA,IAEA,MAAM,YAAY,EAAE,UAAU,GAAuC;AACnE,UAAI;AACF,cAAM,KAAK,YAAY,EAAE,UAAU,CAAC;AAAA,MACtC,SAAS,OAAO;AACd,YAAI,iBAAiB,iCAAY,MAAM,WAAW,KAAK;AACrD,iBAAO;AAAA,QACT;AACA,cAAM;AAAA,MACR;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,cACE,oBACA,gBACgD;AAChD,aAAO,KAAK,OAAO,oBAAoB,cAAc;AAAA,IACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,gBACE,oBACA,gBAC2D;AAC3D,aAAO,KAAK,OAAO,oBAAoB,cAAc;AAAA,IAGvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,UAAU,QAAgB,gBAA6D;AACrF,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,0DAA0D;AAAA,MAC5E;AAEA,UAAI,CAAC,OAAO,KAAK;AACf,cAAM,IAAI,MAAM,8DAA8D;AAAA,MAChF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,kBACE,EAAE,WAAW,UAAU,KAAK,GAC5B,gBACwC;AACxC,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,qEAAqE;AAAA,MACvF;AAEA,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,oEAAoE;AAAA,MACtF;AAEA,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,gEAAgE;AAAA,MAClF;AAEA,YAAM,cAAc,oCACjB,QAAQ,eAAe,mBAAmB,SAAS,CAAC,EACpD,QAAQ,cAAc,mBAAmB,QAAQ,CAAC;AACrD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,aAAa,QAAgB,gBAA6D;AACxF,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,6DAA6D;AAAA,MAC/E;AAEA,UAAI,CAAC,OAAO,QAAQ;AAClB,cAAM,IAAI,MAAM,oEAAoE;AAAA,MACtF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,aACE,EAAE,gBAAgB,mBAAmB,GACrC,gBAC4B;AAC5B,UAAI,CAAC,gBAAgB;AACnB,cAAM,IAAI,MAAM,qEAAqE;AAAA,MACvF;AAEA,UAAI,CAAC,oBAAoB;AACvB,cAAM,IAAI,MAAM,yEAAyE;AAAA,MAC3F;AAEA,UAAI,CAAC,mBAAmB,SAAS;AAC/B,cAAM,IAAI,MAAM,iFAAiF;AAAA,MACnG;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,mBAAmB,QAAW;AAChC,gBAAQ,mBAAmB,IAAI,eAAe,SAAS;AAAA,MACzD;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,MAAM,EAAE,WAAW,iBAAiB,GAAe,gBAAyD;AAC1G,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,yDAAyD;AAAA,MAC3E;AAEA,UAAI,CAAC,kBAAkB;AACrB,cAAM,IAAI,MAAM,gEAAgE;AAAA,MAClF;AAEA,UAAI,CAAC,iBAAiB,UAAU;AAC9B,cAAM,IAAI,MAAM,yEAAyE;AAAA,MAC3F;AAEA,YAAM,cAAc,+BAA+B,QAAQ,eAAe,mBAAmB,SAAS,CAAC;AACvG,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,mBACE,EAAE,gBAAgB,yBAAyB,GAC3C,gBAC4B;AAC5B,UAAI,CAAC,gBAAgB;AACnB,cAAM,IAAI,MAAM,2EAA2E;AAAA,MAC7F;AAEA,UAAI,CAAC,0BAA0B;AAC7B,cAAM,IAAI,MAAM,qFAAqF;AAAA,MACvG;AAEA,UAAI,CAAC,yBAAyB,SAAS;AACrC,cAAM,IAAI,MAAM,6FAA6F;AAAA,MAC/G;AACA,UAAI,CAAC,yBAAyB,OAAO;AACnC,cAAM,IAAI,MAAM,2FAA2F;AAAA,MAC7G;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,mBAAmB,QAAW;AAChC,gBAAQ,mBAAmB,IAAI,eAAe,SAAS;AAAA,MACzD;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,uBACE,EAAE,gBAAgB,6BAA6B,GAC/C,gBAC4B;AAC5B,UAAI,CAAC,gBAAgB;AACnB,cAAM,IAAI,MAAM,+EAA+E;AAAA,MACjG;AAEA,UAAI,CAAC,8BAA8B;AACjC,cAAM,IAAI,MAAM,6FAA6F;AAAA,MAC/G;AAEA,UAAI,CAAC,6BAA6B,UAAU;AAC1C,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,YAAM,cAAc,yCAAyC;AAAA,QAC3D;AAAA,QACA,mBAAmB,cAAc;AAAA,MACnC;AACA,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,OAAU,EAAE,WAAW,aAAa,GAAgB,gBAA6D;AAC/G,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,0DAA0D;AAAA,MAC5E;AAEA,YAAM,cAAc,gCAAgC,QAAQ,eAAe,mBAAmB,SAAS,CAAC;AACxG,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,eAAe,eAAe,CAAC;AAAA,QACrC,oBAAoB;AAAA,MACtB;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,aAAa,EAAE,UAAU,GAAsB,gBAA6D;AAC1G,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,gEAAgE;AAAA,MAClF;AAEA,YAAM,cAAc,+BAA+B,QAAQ,eAAe,mBAAmB,SAAS,CAAC;AACvG,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,WACE,EAAE,WAAW,kBAAkB,GAC/B,gBAC4B;AAC5B,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,8DAA8D;AAAA,MAChF;AAEA,YAAM,cAAc,qCAAqC,QAAQ,eAAe,mBAAmB,SAAS,CAAC;AAC7G,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,sBAAsB,QAAW;AACnC,wBAAgB,mBAAmB,IAAI,kBAAkB,SAAS;AAAA,MACpE;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,cACE,EAAE,WAAW,kBAAkB,GAC/B,gBAC4B;AAC5B,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,iEAAiE;AAAA,MACnF;AAEA,YAAM,cAAc,wCAAwC,QAAQ,eAAe,mBAAmB,SAAS,CAAC;AAChH,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,sBAAsB,QAAW;AACnC,wBAAgB,mBAAmB,IAAI,kBAAkB,SAAS;AAAA,MACpE;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,aACE,EAAE,MAAM,WAAW,GACnB,gBACkC;AAClC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,UAAU,EAAE,MAAM,WAAW,GAAmB,gBAAmE;AACjH,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,WACE,EAAE,MAAM,YAAY,KAAK,GACzB,gBACkC;AAClC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,yDAAyD;AAAA,MAC3E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,OAAO,OAAO,CAAC;AAAA,MACvB;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,UACE,EAAE,MAAM,YAAY,KAAK,GACzB,gBACkC;AAClC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,OAAO,OAAO,CAAC;AAAA,MACvB;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,aAAa,EAAE,IAAI,GAAsB,gBAAgE;AACvG,UAAI,CAAC,KAAK;AACR,cAAM,IAAI,MAAM,0DAA0D;AAAA,MAC5E;AAEA,YAAM,cAAc,gBAAgB,QAAQ,SAAS,mBAAmB,GAAG,CAAC;AAC5E,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,SACE,EAAE,WAAW,eAAe,GAC5B,gBAC4B;AAC5B,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,4DAA4D;AAAA,MAC9E;AAEA,UAAI,CAAC,gBAAgB;AACnB,cAAM,IAAI,MAAM,iEAAiE;AAAA,MACnF;AAEA,YAAM,cAAc,uCAAuC,QAAQ,eAAe,mBAAmB,SAAS,CAAC;AAC/G,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,YAAY,EAAE,UAAU,GAAqB,gBAA6D;AACxG,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF;AAEA,YAAM,cAAc,yBAAyB,QAAQ,eAAe,mBAAmB,SAAS,CAAC;AACjG,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,aACE,EAAE,WAAW,SAAS,GACtB,gBAC4B;AAC5B,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,gEAAgE;AAAA,MAClF;AAEA,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF;AAEA,YAAM,cAAc,oCACjB,QAAQ,eAAe,mBAAmB,SAAS,CAAC,EACpD,QAAQ,cAAc,mBAAmB,QAAQ,CAAC;AACrD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,WACE,EAAE,WAAW,UAAU,kBAAkB,GACzC,gBAC4B;AAC5B,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,8DAA8D;AAAA,MAChF;AAEA,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,6DAA6D;AAAA,MAC/E;AAEA,YAAM,cAAc,0CACjB,QAAQ,eAAe,mBAAmB,SAAS,CAAC,EACpD,QAAQ,cAAc,mBAAmB,QAAQ,CAAC;AACrD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,sBAAsB,QAAW;AACnC,wBAAgB,mBAAmB,IAAI,kBAAkB,SAAS;AAAA,MACpE;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,aAAa,EAAE,OAAO,GAAsB,gBAAgE;AAC1G,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,6DAA6D;AAAA,MAC/E;AAEA,YAAM,cAAc,+BAA+B,QAAQ,YAAY,mBAAmB,MAAM,CAAC;AACjG,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,cACE,EAAE,WAAW,UAAU,kBAAkB,GACzC,gBAC4B;AAC5B,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,iEAAiE;AAAA,MACnF;AAEA,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,gEAAgE;AAAA,MAClF;AAEA,YAAM,cAAc,6CACjB,QAAQ,eAAe,mBAAmB,SAAS,CAAC,EACpD,QAAQ,cAAc,mBAAmB,QAAQ,CAAC;AACrD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,sBAAsB,QAAW;AACnC,wBAAgB,mBAAmB,IAAI,kBAAkB,SAAS;AAAA,MACpE;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,UAAU,EAAE,IAAI,GAAmB,gBAA6D;AAC9F,UAAI,CAAC,KAAK;AACR,cAAM,IAAI,MAAM,uDAAuD;AAAA,MACzE;AAEA,YAAM,cAAc,gBAAgB,QAAQ,SAAS,mBAAmB,GAAG,CAAC;AAC5E,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,WAAW,EAAE,OAAO,GAAoB,gBAA2D;AACjG,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAEA,YAAM,cAAc,mBAAmB,QAAQ,YAAY,mBAAmB,MAAM,CAAC;AACrF,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,uBAAuB,gBAAoF;AACzG,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,sBAAsB,gBAAqF;AACzG,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,QACE,EAAE,QAAQ,QAAQ,WAAW,KAAK,IAAkB,CAAC,GACrD,iBAA6C,QACnB;AAC1B,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,WAAW,QAAW;AACxB,wBAAgB,QAAQ,IAAI,OAAO,SAAS;AAAA,MAC9C;AAEA,UAAI,WAAW,QAAW;AACxB,wBAAgB,QAAQ,IAAI,OAAO,SAAS;AAAA,MAC9C;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,UACE,EAAE,WAAW,UAAU,qBAAqB,GAC5C,gBACkC;AAClC,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,6DAA6D;AAAA,MAC/E;AAEA,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,4DAA4D;AAAA,MAC9E;AAEA,YAAM,cAAc,oCACjB,QAAQ,eAAe,mBAAmB,SAAS,CAAC,EACpD,QAAQ,cAAc,mBAAmB,QAAQ,CAAC;AACrD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,yBAAyB,QAAW;AACtC,wBAAgB,sBAAsB,IAAI,qBAAqB,SAAS;AAAA,MAC1E;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,WAAc,kBAAoC,gBAAiE;AACjH,UAAI,CAAC,kBAAkB;AACrB,cAAM,IAAI,MAAM,qEAAqE;AAAA,MACvF;AAEA,UAAI,CAAC,iBAAiB,UAAU;AAC9B,cAAM,IAAI,MAAM,8EAA8E;AAAA,MAChG;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,oBAAoB;AAAA,QACpB,WAAW;AAAA,MACb;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,QAAQ,EAAE,WAAW,SAAS,GAAiB,gBAAgD;AAC7F,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAEA,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,0DAA0D;AAAA,MAC5E;AAEA,YAAM,cAAc,0CACjB,QAAQ,eAAe,mBAAmB,SAAS,CAAC,EACpD,QAAQ,cAAc,mBAAmB,QAAQ,CAAC;AACrD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,YAAY,EAAE,UAAU,GAAqB,gBAA4D;AACvG,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF;AAEA,YAAM,cAAc,kCAAkC,QAAQ,eAAe,mBAAmB,SAAS,CAAC;AAC1G,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,WAAW,gBAAqE;AAC9E,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,WAAW,EAAE,WAAW,SAAS,GAAoB,gBAAsD;AACzG,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,8DAA8D;AAAA,MAChF;AAEA,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,6DAA6D;AAAA,MAC/E;AAEA,YAAM,cAAc,6CACjB,QAAQ,eAAe,mBAAmB,SAAS,CAAC,EACpD,QAAQ,cAAc,mBAAmB,QAAQ,CAAC;AACrD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,QAAQ,EAAE,WAAW,OAAO,GAAiB,gBAA2D;AACtG,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAEA,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAEA,YAAM,cAAc,uCACjB,QAAQ,eAAe,mBAAmB,SAAS,CAAC,EACpD,QAAQ,YAAY,mBAAmB,MAAM,CAAC;AACjD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,cAAc,gBAA6E;AACzF,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,UAAU,EAAE,OAAO,GAAmB,gBAAkD;AACtF,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,0DAA0D;AAAA,MAC5E;AAEA,YAAM,cAAc,+BAA+B,QAAQ,YAAY,mBAAmB,MAAM,CAAC;AACjG,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,mBACE,EAAE,YAAY,IAA6B,CAAC,GAC5C,iBAA6C,QACR;AACrC,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,gBAAgB,QAAW;AAC7B,wBAAgB,aAAa,IAAI,YAAY,SAAS;AAAA,MACxD;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,YAAY,gBAA2E;AACrF,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,aAAa,gBAA4E;AACvF,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,YACE,EAAE,MAAM,YAAY,IAAsB,CAAC,GAC3C,iBAA6C,QACf;AAC9B,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,gBAAgB,QAAW;AAC7B,wBAAgB,aAAa,IAAI,YAAY,SAAS;AAAA,MACxD;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,YACE,EAAE,MAAM,YAAY,IAAsB,CAAC,GAC3C,iBAA6C,QACf;AAC9B,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,UAAI,gBAAgB,QAAW;AAC7B,wBAAgB,aAAa,IAAI,YAAY,SAAS;AAAA,MACxD;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,cAAc,aAA0B,gBAAiE;AACvG,UAAI,CAAC,aAAa;AAChB,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AAEA,UAAI,CAAC,YAAY,UAAU;AACzB,cAAM,IAAI,MAAM,4EAA4E;AAAA,MAC9F;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,eACE,EAAE,WAAW,qBAAqB,GAClC,gBAC4B;AAC5B,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,kEAAkE;AAAA,MACpF;AAEA,UAAI,CAAC,sBAAsB;AACzB,cAAM,IAAI,MAAM,6EAA6E;AAAA,MAC/F;AAEA,UAAI,CAAC,qBAAqB,WAAW;AACnC,cAAM,IAAI,MAAM,uFAAuF;AAAA,MACzG;AACA,UAAI,CAAC,qBAAqB,aAAa;AACrC,cAAM,IAAI,MAAM,yFAAyF;AAAA,MAC3G;AAEA,YAAM,cAAc,mCAAmC,QAAQ,eAAe,mBAAmB,SAAS,CAAC;AAC3G,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,oBACE,EAAE,WAAW,UAAU,oBAAoB,kBAAkB,GAC7D,gBACwC;AACxC,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,uEAAuE;AAAA,MACzF;AAEA,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,sEAAsE;AAAA,MACxF;AAEA,UAAI,CAAC,oBAAoB;AACvB,cAAM,IAAI,MAAM,gFAAgF;AAAA,MAClG;AAEA,YAAM,cAAc,4CACjB,QAAQ,eAAe,mBAAmB,SAAS,CAAC,EACpD,QAAQ,cAAc,mBAAmB,QAAQ,CAAC;AACrD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,sBAAsB,QAAW;AACnC,wBAAgB,mBAAmB,IAAI,kBAAkB,SAAS;AAAA,MACpE;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,aAAa,EAAE,OAAO,GAAsB,gBAAgE;AAC1G,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,6DAA6D;AAAA,MAC/E;AAEA,YAAM,cAAc,+BAA+B,QAAQ,YAAY,mBAAmB,MAAM,CAAC;AACjG,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,eAAe,EAAE,OAAO,GAAwB,gBAAiE;AAC/G,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,cAAc,EAAE,IAAI,GAAuB,gBAA6D;AACtG,UAAI,CAAC,KAAK;AACR,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAEA,YAAM,cAAc,wBAAwB,QAAQ,SAAS,mBAAmB,GAAG,CAAC;AACpF,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,WACE,EAAE,WAAW,KAAK,GAClB,gBAC6B;AAC7B,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,8DAA8D;AAAA,MAChF;AAEA,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,yDAAyD;AAAA,MAC3E;AAEA,YAAM,cAAc,yBAAyB,QAAQ,eAAe,mBAAmB,SAAS,CAAC;AACjG,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,SACE,EAAE,WAAW,UAAU,MAAM,kBAAkB,GAC/C,gBAC4B;AAC5B,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,4DAA4D;AAAA,MAC9E;AAEA,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAEA,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,uDAAuD;AAAA,MACzE;AAEA,UAAI,CAAC,KAAK,UAAU;AAClB,cAAM,IAAI,MAAM,gEAAgE;AAAA,MAClF;AACA,UAAI,CAAC,KAAK,aAAa;AACrB,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AAEA,YAAM,cAAc,0CACjB,QAAQ,eAAe,mBAAmB,SAAS,CAAC,EACpD,QAAQ,cAAc,mBAAmB,QAAQ,CAAC;AACrD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,sBAAsB,QAAW;AACnC,wBAAgB,mBAAmB,IAAI,kBAAkB,SAAS;AAAA,MACpE;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,UACE,EAAE,WAAW,OAAO,mBAAmB,mBAAmB,GAC1D,gBAC4B;AAC5B,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,6DAA6D;AAAA,MAC/E;AAEA,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,yDAAyD;AAAA,MAC3E;AAEA,YAAM,cAAc,qCAAqC,QAAQ,eAAe,mBAAmB,SAAS,CAAC;AAC7G,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,sBAAsB,QAAW;AACnC,wBAAgB,mBAAmB,IAAI,kBAAkB,SAAS;AAAA,MACpE;AAEA,UAAI,uBAAuB,QAAW;AACpC,wBAAgB,oBAAoB,IAAI,mBAAmB,SAAS;AAAA,MACtE;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,YACE,EAAE,WAAW,UAAU,YAAY,kBAAkB,GACrD,gBAC8B;AAC9B,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF;AAEA,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,8DAA8D;AAAA,MAChF;AAEA,UAAI,CAAC,YAAY;AACf,cAAM,IAAI,MAAM,gEAAgE;AAAA,MAClF;AAEA,UAAI,CAAC,WAAW,UAAU;AACxB,cAAM,IAAI,MAAM,yEAAyE;AAAA,MAC3F;AACA,UAAI,CAAC,WAAW,MAAM;AACpB,cAAM,IAAI,MAAM,qEAAqE;AAAA,MACvF;AAEA,YAAM,cAAc,6CACjB,QAAQ,eAAe,mBAAmB,SAAS,CAAC,EACpD,QAAQ,cAAc,mBAAmB,QAAQ,CAAC;AACrD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,sBAAsB,QAAW;AACnC,wBAAgB,mBAAmB,IAAI,kBAAkB,SAAS;AAAA,MACpE;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,aACE,EAAE,WAAW,YAAY,mBAAmB,wBAAwB,GACpE,gBAC4B;AAC5B,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,gEAAgE;AAAA,MAClF;AAEA,UAAI,CAAC,YAAY;AACf,cAAM,IAAI,MAAM,iEAAiE;AAAA,MACnF;AAEA,YAAM,cAAc,wCAAwC,QAAQ,eAAe,mBAAmB,SAAS,CAAC;AAChH,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,sBAAsB,QAAW;AACnC,wBAAgB,mBAAmB,IAAI,kBAAkB,SAAS;AAAA,MACpE;AAEA,UAAI,4BAA4B,QAAW;AACzC,wBAAgB,yBAAyB,IAAI,wBAAwB,SAAS;AAAA,MAChF;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,OACE,oBACA,gBAC6B;AAC7B,UAAI,sBAAsB,MAAM,QAAQ,kBAAkB,GAAG;AAC3D,cAAM,sBAA0C;AAAA,UAC9C,UAAU,mBAAmB,IAAI,CAAC,EAAE,QAAQ,GAAG,cAAc,MAAM;AACjE,gBAAI,cAAc,SAAS,SAAS;AAClC,qBAAO;AAAA,gBACL,GAAG;AAAA,gBACH,GAAG;AAAA,gBACH,MAAM;AAAA,cACR;AAAA,YACF;AAEA,mBAAO;AAAA,cACL,GAAG;AAAA,cACH,GAAG;AAAA,cACH,OAAO;AAAA,cACP,cAAc;AAAA,cACd,YAAY;AAAA,YACd;AAAA,UACF,CAAC;AAAA,QACH;AAEA,6BAAqB;AAAA,MACvB;AAEA,UAAI,CAAC,oBAAoB;AACvB,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AAEA,UAAI,CAAC,mBAAmB,UAAU;AAChC,cAAM,IAAI,MAAM,4EAA4E;AAAA,MAC9F;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,oBAAoB;AAAA,QACpB,WAAW;AAAA,MACb;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,wBACE,EAAE,gBAAgB,8BAA8B,GAChD,gBAC0C;AAC1C,UAAI,CAAC,gBAAgB;AACnB,cAAM,IAAI,MAAM,gFAAgF;AAAA,MAClG;AAEA,UAAI,CAAC,+BAA+B;AAClC,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,UAAI,CAAC,8BAA8B,OAAO;AACxC,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,YAAM,cAAc,0CAA0C;AAAA,QAC5D;AAAA,QACA,mBAAmB,cAAc;AAAA,MACnC;AACA,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,oBAAoB;AAAA,QACpB,WAAW;AAAA,MACb;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,qBACE,EAAE,WAAW,WAAW,4BAA4B,GACpD,gBACuC;AACvC,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,wEAAwE;AAAA,MAC1F;AAEA,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,wEAAwE;AAAA,MAC1F;AAEA,YAAM,cAAc,kDACjB,QAAQ,eAAe,mBAAmB,SAAS,CAAC,EACpD,QAAQ,eAAe,mBAAmB,SAAS,CAAC;AACvD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,8BAA8B,8BAA8B,CAAC;AAAA,QACnE,oBAAoB;AAAA,QACpB,WAAW;AAAA,MACb;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,YACE,EAAE,WAAW,kBAAkB,GAC/B,gBAC8B;AAC9B,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF;AAEA,YAAM,cAAc,sCAAsC,QAAQ,eAAe,mBAAmB,SAAS,CAAC;AAC9G,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,oBAAoB,oBAAoB,CAAC;AAAA,QAC/C,oBAAoB;AAAA,QACpB,WAAW;AAAA,MACb;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,kBACE,EAAE,WAAW,aAAa,GAC1B,gBAC4B;AAC5B,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,qEAAqE;AAAA,MACvF;AAEA,YAAM,cAAc,+BAA+B,QAAQ,eAAe,mBAAmB,SAAS,CAAC;AACvG,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,eAAe,eAAe,CAAC;AAAA,QACrC,oBAAoB;AAAA,QACpB,WAAW;AAAA,MACb;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,eACE,EAAE,WAAW,qBAAqB,GAClC,gBACiC;AACjC,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,kEAAkE;AAAA,MACpF;AAEA,YAAM,cAAc,yCAAyC;AAAA,QAC3D;AAAA,QACA,mBAAmB,SAAS;AAAA,MAC9B;AACA,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,uBAAuB,uBAAuB,CAAC;AAAA,QACrD,oBAAoB;AAAA,QACpB,WAAW;AAAA,MACb;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,cACE,qBACA,gBACgC;AAChC,UAAI,CAAC,qBAAqB;AACxB,cAAM,IAAI,MAAM,2EAA2E;AAAA,MAC7F;AAEA,UAAI,CAAC,oBAAoB,OAAO;AAC9B,cAAM,IAAI,MAAM,iFAAiF;AAAA,MACnG;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,oBAAoB;AAAA,QACpB,WAAW;AAAA,MACb;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,sBACE,0BACA,gBAC4B;AAC5B,UAAI,CAAC,0BAA0B;AAC7B,cAAM,IAAI,MAAM,wFAAwF;AAAA,MAC1G;AAEA,UAAI,CAAC,yBAAyB,wBAAwB;AACpD,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaA,YACE,EAAE,WAAW,eAAe,kBAAkB,GAC9C,gBAC4B;AAC5B,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF;AAEA,UAAI,CAAC,eAAe;AAClB,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AAEA,YAAM,cAAc,kCAAkC,QAAQ,eAAe,mBAAmB,SAAS,CAAC;AAC1G,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,sBAAsB,QAAW;AACnC,wBAAgB,mBAAmB,IAAI,kBAAkB,SAAS;AAAA,MACpE;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,aAAa,EAAE,KAAK,OAAO,GAAsB,gBAAgE;AAC/G,UAAI,CAAC,KAAK;AACR,cAAM,IAAI,MAAM,0DAA0D;AAAA,MAC5E;AAEA,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,6DAA6D;AAAA,MAC/E;AAEA,UAAI,CAAC,OAAO,KAAK;AACf,cAAM,IAAI,MAAM,iEAAiE;AAAA,MACnF;AAEA,YAAM,cAAc,gBAAgB,QAAQ,SAAS,mBAAmB,GAAG,CAAC;AAC5E,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA,EACF;AACF;;;ADl4FO,SAAS,aAAa,OAAe,QAAgB,SAAmD;AAC7G,MAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACvC,UAAM,IAAI,MAAM,qBAAqB;AAAA,EACvC;AAEA,MAAI,CAAC,UAAU,OAAO,WAAW,UAAU;AACzC,UAAM,IAAI,MAAM,sBAAsB;AAAA,EACxC;AAEA,SAAO;AAAA,IACL,GAAG,mBAAmB;AAAA,MACpB;AAAA,MACA;AAAA,MACA,UAAU;AAAA,QACR,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA,YAAQ,wCAAiB;AAAA,MACzB,eAAW,gDAAoB;AAAA,MAC/B,eAAe,CAAC,EAAE,SAAS,WAAW,SAAS,QAAQ,SAAS,KAAK,CAAC;AAAA,MACtE,oBAAgB,uCAAgB;AAAA,MAChC,mBAAe,uCAAgB;AAAA,MAC/B,gBAAY,yCAAkB;AAAA,MAC9B,GAAG;AAAA,IACL,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASD,uBAAuB,CAAC,EAAE,cAAc,eAAe,CAAC,EAAE,MAA4C;AACpG,UAAI,qBAAqB;AACzB,UAAI,aAAa,cAAc;AAE7B,6BAAqB;AAAA,UACnB,GAAG;AAAA,UACH,GAAG,aAAa;AAAA,QAClB;AAEA,eAAO,mBAAmB;AAAA,MAC5B;AAEA,2BAAqB,OAAO,KAAK,kBAAkB,EAChD,KAAK,EACL;AAAA,QACC,CAAC,KAAK,QAAQ;AACZ,cAAI,GAAG,IAAK,mBAA2B,GAAG;AAC1C,iBAAO;AAAA,QACT;AAAA,QACA,CAAC;AAAA,MACH;AAEF,YAAM,sBAAkB,gDAAyB,kBAAkB;AACnE,aAAO,OAAO;AAAA,YACZ,+BAAW,UAAU,YAAY,EAAE,OAAO,eAAe,EAAE,OAAO,KAAK,IAAI;AAAA,MAC7E,EAAE,SAAS,QAAQ;AAAA,IACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,MAAM,iBACJ,EAAE,iBAAiB,kBAAkB,mBAAmB,qBAAqB,GAC7E,gBACe;AACf,YAAM,YAA4D,CAAC;AAEnE,UAAI,KAAK,UAAU,kBAAkB;AACnC,cAAM,IAAI,4CAAsB;AAAA,MAClC;AAEA,UAAI,CAAE,MAAM,KAAK,YAAY,EAAE,WAAW,gBAAgB,CAAC,GAAI;AAC7D,cAAM,IAAI,yCAAmB,eAAe;AAAA,MAC9C;AAEA,YAAM,oBAAoB,mBAAmB;AAAA,QAC3C,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,UAAU;AAAA,UACR,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA,YAAQ,wCAAiB;AAAA,QACzB,eAAW,gDAAoB;AAAA,QAC/B,eAAe,CAAC,EAAE,SAAS,oBAAoB,SAAS,QAAQ,SAAS,KAAK,CAAC;AAAA,QAC/E,oBAAgB,uCAAgB;AAAA,QAChC,mBAAe,uCAAgB;AAAA,QAC/B,gBAAY,yCAAkB;AAAA,QAC9B,GAAG;AAAA,MACL,CAAC;AAED,UAAI,MAAM,kBAAkB,YAAY,EAAE,WAAW,qBAAqB,CAAC,GAAG;AAC5E,cAAM,IAAI,8CAAwB,oBAAoB;AAAA,MACxD;AAEA,gBAAU;AAAA,QACR,MAAM,kBAAkB;AAAA,UACtB;AAAA,YACE,WAAW;AAAA,YACX,eAAe,MAAM,KAAK,YAAY,EAAE,WAAW,gBAAgB,CAAC;AAAA,UACtE;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,KAAK,YAAY;AAAA,QACrB,WAAW;AAAA,QACX,MAAM,WAAW,UAA+B;AAC9C,oBAAU;AAAA,YACR,MAAM,kBAAkB;AAAA,cACtB,EAAE,WAAW,sBAAsB,OAAO,SAAS,KAAK;AAAA,cACxD;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAED,YAAM,KAAK,eAAe;AAAA,QACxB,WAAW;AAAA,QACX,MAAM,WAAW,UAAkC;AACjD,oBAAU;AAAA,YACR,MAAM,kBAAkB;AAAA,cACtB,EAAE,WAAW,sBAAsB,YAAY,SAAS,KAAK;AAAA,cAC7D;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAED,YAAM,KAAK,cAAc;AAAA,QACvB,WAAW;AAAA,QACX,MAAM,WAAW,UAA0B;AACzC,oBAAU;AAAA,YACR,GAAI,MAAM,kBAAkB;AAAA,cAC1B,EAAE,WAAW,sBAAsB,SAAS,SAAS,KAAK;AAAA,cAC1D;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAED,iBAAW,YAAY,WAAW;AAChC,cAAM,kBAAkB,YAAY,EAAE,WAAW,sBAAsB,QAAQ,SAAS,OAAO,CAAC;AAAA,MAClG;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,mCAAmC,CAAC,EAAE,cAAc,MAAwD;AAC1G,YAAM,gBAAgB,KAAK,aAAa;AACxC,YAAM,QAAQ;AACd,YAAM,QAAQ,cAAc,MAAM,KAAK;AAEvC,UAAI,UAAU,MAAM;AAClB,cAAM,IAAI,MAAM,gDAAgD;AAAA,MAClE;AAEA,aAAO,SAAS,MAAM,CAAC,GAAG,EAAE,IAAI,KAAK,OAAM,oBAAI,KAAK,GAAE,QAAQ,IAAI,GAAI;AAAA,IACxE;AAAA,EACF;AACF;", "names": ["import_client_common"]}