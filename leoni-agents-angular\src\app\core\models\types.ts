/**
 * <PERSON><PERSON><PERSON><PERSON> de données pour l'application Leoni Agents Angular
 */

export interface ProgramFile {
  name: string;
  content: string;
  type: 'program' | 'error';
}

export interface CodeContext {
  targetLine: string;
  contextLines: Array<{
    number: number;
    content: string;
    isTarget: boolean;
  }>;
}

export interface ErrorAnalysis {
  errorType: string;
  location: string;
  description: string;
  possibleCauses: string[];
  solutions: string[];
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  lineNumber?: number;
  codeContext?: CodeContext;
}

export interface ErrorAnalysisResult {
  summary: string;
  errors: ErrorAnalysis[];
  recommendations: string[];
  timestamp: string;
}

export interface SQLGenerationRequest {
  specification: string;
  databaseType?: 'mysql' | 'postgresql' | 'sqlite' | 'sqlserver';
  includeComments?: boolean;
}

export interface SQLGenerationResult {
  sql: string;
  explanation: string;
  tables: string[];
  operations: string[];
  timestamp: string;
}

export interface Agent {
  id: string;
  name: string;
  description: string;
  role: string;
  goal: string;
  prompt: string;
  tools: string[];
  utils: string[];
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface FileUploadResult {
  content: string;
  originalName: string;
  fileType: string;
  success: boolean;
  error?: string;
}

export interface LoadingState {
  isLoading: boolean;
  message?: string;
}

export interface ValidationResult {
  valid: boolean;
  message?: string;
  errors?: string[];
}

// Énumérations pour les constantes
export enum DatabaseType {
  MYSQL = 'mysql',
  POSTGRESQL = 'postgresql',
  SQLITE = 'sqlite',
  SQLSERVER = 'sqlserver'
}

export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export enum FileType {
  PROGRAM = 'program',
  ERROR = 'error'
}

// Types utilitaires
export type DatabaseTypeKeys = keyof typeof DatabaseType;
export type ErrorSeverityKeys = keyof typeof ErrorSeverity;

// Interfaces pour les composants UI
export interface TabItem {
  label: string;
  value: string;
  icon?: string;
}

export interface ButtonConfig {
  text: string;
  icon?: string;
  color?: 'primary' | 'accent' | 'warn';
  disabled?: boolean;
  loading?: boolean;
}

export interface CardConfig {
  title: string;
  subtitle?: string;
  icon?: string;
  color?: string;
  actions?: ButtonConfig[];
}

// Configuration de l'application
export interface AppConfig {
  apiBaseUrl: string;
  maxFileSize: number;
  supportedFileTypes: string[];
  defaultDatabaseType: DatabaseType;
}

// Types pour les notifications
export interface NotificationConfig {
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  action?: string;
}
