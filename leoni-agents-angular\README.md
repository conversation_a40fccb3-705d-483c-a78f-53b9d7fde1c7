# Leoni Agents - Frontend Angular

Frontend Angular pour l'application Leoni Agents avec agents IA spécialisés.

## 🚀 Fonctionnalités

- **Agent d'Analyse d'Erreurs** : Interface intuitive pour l'analyse des erreurs
- **Agent de Génération SQL** : Génération de scripts SQL à partir de spécifications
- **Interface Moderne** : Design Material avec animations et expérience utilisateur optimisée
- **Responsive** : Compatible avec tous les appareils (desktop, tablette, mobile)
- **Intégration API** : Communication avec le backend Flask

## 📋 Prérequis

- Node.js 18+
- npm ou yarn
- Angular CLI 17+

## 🛠️ Installation

1. **C<PERSON><PERSON> le projet**
```bash
cd leoni-agents-angular
```

2. **Installer les dépendances**
```bash
npm install
# ou
yarn install
```

3. **Lancer l'application en développement**
```bash
npm start
# ou
yarn start
```

L'application sera disponible sur `http://localhost:4200`

## 📦 Build pour la production

```bash
npm run build
# ou
yarn build
```

Les fichiers de build seront générés dans le dossier `dist/`.

## 🧪 Tests

```bash
npm test
# ou
yarn test
```

## 🏗️ Structure du Projet

```
leoni-agents-angular/
├── src/
│   ├── app/
│   │   ├── core/                # Services et modèles core
│   │   │   ├── models/         # Interfaces et types
│   │   │   └── services/       # Services partagés
│   │   ├── features/           # Composants de fonctionnalités
│   │   │   ├── home/          # Page d'accueil
│   │   │   ├── error-analysis/ # Analyse d'erreurs
│   │   │   └── sql-generation/ # Génération SQL
│   │   ├── shared/             # Composants partagés
│   │   │   └── components/    # UI components réutilisables
│   │   ├── app.component.ts    # Composant racine
│   │   ├── app.config.ts       # Configuration de l'app
│   │   └── app.routes.ts       # Routes de l'application
│   ├── assets/                  # Images, fonts, etc.
│   ├── environments/            # Configuration par environnement
│   ├── styles.scss              # Styles globaux
│   └── main.ts                  # Point d'entrée
├── angular.json                 # Configuration Angular
├── package.json                 # Dépendances
└── README.md
```

## 🔧 Configuration

L'application utilise des fichiers d'environnement pour la configuration :

- `src/environments/environment.ts` : Configuration de développement
- `src/environments/environment.prod.ts` : Configuration de production

Principales configurations :
- `apiUrl` : URL de l'API backend Flask
- `appName` : Nom de l'application
- `version` : Version de l'application

## 🎨 Styles et Thème

L'application utilise :
- **Angular Material** : Composants UI Material Design
- **SCSS** : Préprocesseur CSS
- **Variables CSS** : Personnalisation des couleurs et thèmes

Couleurs principales :
- Bleu Leoni : `#002857`
- Orange Leoni : `#ff7514`

## 📱 Responsive Design

L'application est entièrement responsive avec des breakpoints pour :
- Mobile : < 480px
- Tablette : < 768px
- Desktop : > 768px

## 🔌 Intégration API

Les services Angular communiquent avec le backend Flask via HTTP :
- `ApiService` : Service principal pour les appels API
- `FileService` : Gestion des fichiers
- `NotificationService` : Notifications utilisateur

## 🚀 Déploiement

### Avec Docker
```bash
docker build -t leoni-agents-angular .
docker run -p 80:80 leoni-agents-angular
```

### Avec Nginx
1. Build l'application
2. Copier le contenu du dossier `dist/` dans le répertoire de Nginx
3. Configurer Nginx pour servir l'application Angular

## 📝 Exemples d'utilisation

### Analyse d'erreurs
1. Naviguer vers la page "Analyse d'Erreurs"
2. Télécharger ou coller le contenu du fichier programme
3. Télécharger ou coller le contenu du fichier d'erreur
4. Cliquer sur "Analyser les Erreurs"
5. Consulter les résultats détaillés avec solutions

### Génération SQL
1. Naviguer vers la page "Génération SQL"
2. Saisir la spécification fonctionnelle
3. Choisir le type de base de données
4. Configurer les options (commentaires, etc.)
5. Cliquer sur "Générer le Script SQL"
6. Copier ou télécharger le script généré
