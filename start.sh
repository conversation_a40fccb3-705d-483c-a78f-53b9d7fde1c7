#!/bin/bash

# Script de démarrage pour Leoni Agents (Flask + Angular)

echo "🚀 Démarrage de Leoni Agents"
echo "================================"

# Vérifier si Docker est installé
if ! command -v docker &> /dev/null; then
    echo "❌ Docker n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

# Vérifier si docker-compose est installé
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

# Vérifier si le fichier .env existe
if [ ! -f .env ]; then
    echo "⚠️  Fichier .env non trouvé. Création d'un exemple..."
    cat > .env << EOF
# Configuration Leoni Agents
OPENAI_API_KEY=your_openai_api_key_here

# Remplacez 'your_openai_api_key_here' par votre vraie clé OpenAI
EOF
    echo "📝 Fichier .env créé. Veuillez éditer ce fichier avec votre clé OpenAI."
    echo "   Puis relancez ce script."
    exit 1
fi

# Vérifier si la clé OpenAI est configurée
source .env
if [ "$OPENAI_API_KEY" = "your_openai_api_key_here" ]; then
    echo "⚠️  Veuillez configurer votre clé OpenAI dans le fichier .env"
    exit 1
fi

echo "✅ Configuration validée"

# Choix du mode de démarrage
echo ""
echo "Choisissez le mode de démarrage:"
echo "1) Docker (recommandé pour la production)"
echo "2) Développement local"
read -p "Votre choix (1 ou 2): " choice

case $choice in
    1)
        echo "🐳 Démarrage avec Docker..."
        docker-compose up --build
        ;;
    2)
        echo "💻 Démarrage en mode développement..."
        
        # Démarrer le backend Flask
        echo "🔧 Démarrage du backend Flask..."
        cd leoni-agents-flask
        
        # Vérifier si l'environnement virtuel existe
        if [ ! -d "venv" ]; then
            echo "📦 Création de l'environnement virtuel Python..."
            python3 -m venv venv
        fi
        
        # Activer l'environnement virtuel
        source venv/bin/activate
        
        # Installer les dépendances
        pip install -r requirements.txt
        
        # Copier le fichier .env
        cp ../.env .env
        
        # Démarrer Flask en arrière-plan
        python app.py &
        FLASK_PID=$!
        
        cd ..
        
        # Démarrer le frontend Angular
        echo "🅰️  Démarrage du frontend Angular..."
        cd leoni-agents-angular
        
        # Vérifier si node_modules existe
        if [ ! -d "node_modules" ]; then
            echo "📦 Installation des dépendances Node.js..."
            npm install
        fi
        
        # Démarrer Angular
        npm start &
        ANGULAR_PID=$!
        
        cd ..
        
        echo ""
        echo "✅ Applications démarrées !"
        echo "🌐 Frontend Angular: http://localhost:4200"
        echo "🔧 Backend Flask: http://localhost:5000"
        echo ""
        echo "Appuyez sur Ctrl+C pour arrêter les services"
        
        # Attendre l'interruption
        trap "echo '🛑 Arrêt des services...'; kill $FLASK_PID $ANGULAR_PID; exit" INT
        wait
        ;;
    *)
        echo "❌ Choix invalide"
        exit 1
        ;;
esac
