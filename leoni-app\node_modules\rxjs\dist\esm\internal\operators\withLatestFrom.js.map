{"version": 3, "file": "withLatestFrom.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/withLatestFrom.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,wBAAwB,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAC5C,OAAO,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AACpC,OAAO,EAAE,iBAAiB,EAAE,MAAM,cAAc,CAAC;AAoDjD,MAAM,UAAU,cAAc,CAAO,GAAG,MAAa;IACnD,MAAM,OAAO,GAAG,iBAAiB,CAAC,MAAM,CAAwC,CAAC;IAEjF,OAAO,OAAO,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE;QACpC,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;QAC1B,MAAM,WAAW,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QAInC,IAAI,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;QAGvC,IAAI,KAAK,GAAG,KAAK,CAAC;QAMlB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAC5B,wBAAwB,CACtB,UAAU,EACV,CAAC,KAAK,EAAE,EAAE;gBACR,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;gBACvB,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;oBAE1B,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;oBAKnB,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAK,CAAC,CAAC;iBAC1D;YACH,CAAC,EAGD,IAAI,CACL,CACF,CAAC;SACH;QAGD,MAAM,CAAC,SAAS,CACd,wBAAwB,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,EAAE;YAC7C,IAAI,KAAK,EAAE;gBAET,MAAM,MAAM,GAAG,CAAC,KAAK,EAAE,GAAG,WAAW,CAAC,CAAC;gBACvC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;aACxD;QACH,CAAC,CAAC,CACH,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC"}