import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    loadComponent: () => import('./features/home/<USER>').then(m => m.HomeComponent)
  },
  {
    path: 'error-analysis',
    loadComponent: () => import('./features/error-analysis/error-analysis.component').then(m => m.ErrorAnalysisComponent)
  },
  {
    path: 'sql-generation',
    loadComponent: () => import('./features/sql-generation/sql-generation.component').then(m => m.SqlGenerationComponent)
  },
  {
    path: '**',
    redirectTo: ''
  }
];
