{"version": 3, "file": "or.js", "sourceRoot": "", "sources": ["or.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,SAAS,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,QAAQ,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,YAAY,EAAC,SAAS,EAAC,SAAS,EAAC,UAAU,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,cAAc,EAAC,aAAa,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,cAAc,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,cAAc,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,cAAc,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"or\",[[\"ପୂ\",\"ଅ\"],[\"AM\",\"PM\"],u],[[\"AM\",\"ଅପରାହ୍ନ\"],[\"ପୂର୍ବାହ୍ନ\",\"ଅପରାହ୍ନ\"],u],[[\"ର\",\"ସୋ\",\"ମ\",\"ବୁ\",\"ଗୁ\",\"ଶୁ\",\"ଶ\"],[\"ରବି\",\"ସୋମ\",\"ମଙ୍ଗଳ\",\"ବୁଧ\",\"ଗୁରୁ\",\"ଶୁକ୍ର\",\"ଶନି\"],[\"ରବିବାର\",\"ସୋମବାର\",\"ମଙ୍ଗଳବାର\",\"ବୁଧବାର\",\"ଗୁରୁବାର\",\"ଶୁକ୍ରବାର\",\"ଶନିବାର\"],[\"ରବି\",\"ସୋମ\",\"ମଙ୍ଗଳ\",\"ବୁଧ\",\"ଗୁରୁ\",\"ଶୁକ୍ର\",\"ଶନି\"]],u,[[\"ଜା\",\"ଫେ\",\"ମା\",\"ଅ\",\"ମଇ\",\"ଜୁ\",\"ଜୁ\",\"ଅ\",\"ସେ\",\"ଅ\",\"ନ\",\"ଡି\"],[\"ଜାନୁଆରୀ\",\"ଫେବୃଆରୀ\",\"ମାର୍ଚ୍ଚ\",\"ଅପ୍ରେଲ\",\"ମଇ\",\"ଜୁନ\",\"ଜୁଲାଇ\",\"ଅଗଷ୍ଟ\",\"ସେପ୍ଟେମ୍ବର\",\"ଅକ୍ଟୋବର\",\"ନଭେମ୍ବର\",\"ଡିସେମ୍ବର\"],u],u,[[\"BC\",\"AD\"],u,[\"ଖ୍ରୀଷ୍ଟପୂର୍ବ\",\"ଖ୍ରୀଷ୍ଟାବ୍ଦ\"]],0,[0,0],[\"M/d/yy\",\"MMM d, y\",\"MMMM d, y\",\"EEEE, MMMM d, y\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1}, {0}\",u,\"{0} ଠାରେ {1}\",u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"INR\",\"₹\",\"ଭାରତୀୟ ଟଙ୍କା\",{\"BYN\":[u,\"р.\"],\"PHP\":[u,\"₱\"]},\"ltr\", plural];\n"]}