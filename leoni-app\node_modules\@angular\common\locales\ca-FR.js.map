{"version": 3, "file": "ca-FR.js", "sourceRoot": "", "sources": ["ca-FR.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC;IAEjG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QAClB,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,OAAO,EAAC,CAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,QAAQ,EAAC,WAAW,EAAC,UAAU,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,SAAS,EAAC,UAAU,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,OAAO,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,EAAC,SAAS,CAAC,EAAC,CAAC,UAAU,EAAC,WAAW,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,WAAW,EAAC,SAAS,EAAC,aAAa,EAAC,WAAW,EAAC,aAAa,EAAC,aAAa,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,OAAO,EAAC,QAAQ,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,OAAO,EAAC,UAAU,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,gBAAgB,EAAC,kBAAkB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,eAAe,EAAC,qBAAqB,CAAC,EAAC,CAAC,MAAM,EAAC,SAAS,EAAC,WAAW,EAAC,gBAAgB,CAAC,EAAC,CAAC,SAAS,EAAC,UAAU,EAAC,oBAAoB,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,MAAM,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,MAAM,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length;\n\nif (i === 1 && v === 0)\n    return 1;\nreturn 5;\n}\n\nexport default [\"ca-FR\",[[\"a. m.\",\"p. m.\"],u,u],u,[[\"dg\",\"dl\",\"dt\",\"dc\",\"dj\",\"dv\",\"ds\"],[\"dg.\",\"dl.\",\"dt.\",\"dc.\",\"dj.\",\"dv.\",\"ds.\"],[\"diumenge\",\"dilluns\",\"dimarts\",\"dimecres\",\"dijous\",\"divendres\",\"dissabte\"],[\"dg.\",\"dl.\",\"dt.\",\"dc.\",\"dj.\",\"dv.\",\"ds.\"]],u,[[\"GN\",\"FB\",\"MÇ\",\"AB\",\"MG\",\"JN\",\"JL\",\"AG\",\"ST\",\"OC\",\"NV\",\"DS\"],[\"de gen.\",\"de febr.\",\"de març\",\"d’abr.\",\"de maig\",\"de juny\",\"de jul.\",\"d’ag.\",\"de set.\",\"d’oct.\",\"de nov.\",\"de des.\"],[\"de gener\",\"de febrer\",\"de març\",\"d’abril\",\"de maig\",\"de juny\",\"de juliol\",\"d’agost\",\"de setembre\",\"d’octubre\",\"de novembre\",\"de desembre\"]],[[\"GN\",\"FB\",\"MÇ\",\"AB\",\"MG\",\"JN\",\"JL\",\"AG\",\"ST\",\"OC\",\"NV\",\"DS\"],[\"gen.\",\"febr.\",\"març\",\"abr.\",\"maig\",\"juny\",\"jul.\",\"ag.\",\"set.\",\"oct.\",\"nov.\",\"des.\"],[\"gener\",\"febrer\",\"març\",\"abril\",\"maig\",\"juny\",\"juliol\",\"agost\",\"setembre\",\"octubre\",\"novembre\",\"desembre\"]],[[\"aC\",\"dC\"],u,[\"abans de Crist\",\"després de Crist\"]],1,[6,0],[\"d/M/yy\",\"d MMM y\",\"d MMMM 'de' y\",\"EEEE, d MMMM 'de' y\"],[\"H:mm\",\"H:mm:ss\",\"H:mm:ss z\",\"H:mm:ss (zzzz)\"],[\"{1} {0}\",\"{1}, {0}\",\"{1}, 'a' 'les' {0}\",u],[\",\",\".\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"#,##0.00 ¤\",\"#E0\"],\"EUR\",\"€\",\"euro\",{\"AUD\":[\"AU$\",\"$\"],\"BRL\":[u,\"R$\"],\"BYN\":[u,\"р.\"],\"CAD\":[u,\"$\"],\"CNY\":[u,\"¥\"],\"ESP\":[\"₧\"],\"FRF\":[\"F\"],\"MXN\":[u,\"$\"],\"PHP\":[u,\"₱\"],\"THB\":[\"฿\"],\"USD\":[u,\"$\"],\"VEF\":[u,\"Bs F\"],\"XCD\":[u,\"$\"],\"XXX\":[]},\"ltr\", plural];\n"]}