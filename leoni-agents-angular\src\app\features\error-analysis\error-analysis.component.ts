import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTabsModule } from '@angular/material/tabs';
import { MatChipsModule } from '@angular/material/chips';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBarModule } from '@angular/material/snack-bar';

import { ApiService } from '../../core/services/api.service';
import { FileService } from '../../core/services/file.service';
import { NotificationService } from '../../core/services/notification.service';
import { 
  ProgramFile, 
  ErrorAnalysisResult, 
  ErrorAnalysis, 
  LoadingState 
} from '../../core/models/types';

@Component({
  selector: 'app-error-analysis',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatProgressSpinnerModule,
    MatTabsModule,
    MatChipsModule,
    MatExpansionModule,
    MatDividerModule,
    MatTooltipModule,
    MatSnackBarModule
  ],
  template: `
    <div class="error-analysis-container">
      <!-- Header -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-icon">
            <mat-icon>bug_report</mat-icon>
          </div>
          <div class="header-text">
            <h1>Agent d'Analyse d'Erreurs</h1>
            <p>Analysez vos fichiers de programme et d'erreur avec l'IA pour obtenir des solutions détaillées</p>
          </div>
        </div>
      </div>

      <!-- Formulaire d'upload -->
      <mat-card class="upload-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>upload_file</mat-icon>
            Téléchargement des Fichiers
          </mat-card-title>
          <mat-card-subtitle>
            Fournissez vos fichiers de programme et d'erreur pour l'analyse
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <form [formGroup]="analysisForm" (ngSubmit)="onAnalyze()">
            <div class="upload-grid">
              <!-- Fichier Programme -->
              <div class="upload-section">
                <h3>
                  <mat-icon>code</mat-icon>
                  Fichier Programme
                </h3>
                
                <mat-tab-group>
                  <mat-tab label="Upload Fichier">
                    <div class="tab-content">
                      <div class="file-upload-area" 
                           (dragover)="onDragOver($event)" 
                           (dragleave)="onDragLeave($event)"
                           (drop)="onDrop($event, 'program')"
                           [class.drag-over]="isDragOverProgram">
                        <input type="file" 
                               #programFileInput 
                               (change)="onFileSelected($event, 'program')"
                               accept=".c,.cpp,.h,.hpp,.ec,.txt,.log"
                               style="display: none;">
                        
                        <div class="upload-content" (click)="programFileInput.click()">
                          <mat-icon class="upload-icon">cloud_upload</mat-icon>
                          <p>Cliquez ou glissez votre fichier programme ici</p>
                          <small>Formats supportés: .c, .cpp, .h, .hpp, .ec, .txt, .log</small>
                        </div>
                      </div>
                      
                      <div *ngIf="programFile" class="file-info">
                        <mat-icon>description</mat-icon>
                        <span>{{ programFile.name }}</span>
                        <button mat-icon-button (click)="clearFile('program')" type="button">
                          <mat-icon>close</mat-icon>
                        </button>
                      </div>
                    </div>
                  </mat-tab>
                  
                  <mat-tab label="Coller le Code">
                    <div class="tab-content">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Contenu du fichier programme</mat-label>
                        <textarea matInput 
                                  formControlName="programContent"
                                  rows="10"
                                  placeholder="Collez votre code source ici..."></textarea>
                      </mat-form-field>
                      
                      <mat-form-field appearance="outline">
                        <mat-label>Nom du fichier</mat-label>
                        <input matInput 
                               formControlName="programFileName"
                               placeholder="ex: main.c">
                      </mat-form-field>
                    </div>
                  </mat-tab>
                </mat-tab-group>
              </div>

              <!-- Fichier Erreur -->
              <div class="upload-section">
                <h3>
                  <mat-icon>error</mat-icon>
                  Fichier d'Erreur
                </h3>
                
                <mat-tab-group>
                  <mat-tab label="Upload Fichier">
                    <div class="tab-content">
                      <div class="file-upload-area" 
                           (dragover)="onDragOver($event)" 
                           (dragleave)="onDragLeave($event)"
                           (drop)="onDrop($event, 'error')"
                           [class.drag-over]="isDragOverError">
                        <input type="file" 
                               #errorFileInput 
                               (change)="onFileSelected($event, 'error')"
                               accept=".log,.err,.error,.out,.txt"
                               style="display: none;">
                        
                        <div class="upload-content" (click)="errorFileInput.click()">
                          <mat-icon class="upload-icon">bug_report</mat-icon>
                          <p>Cliquez ou glissez votre fichier d'erreur ici</p>
                          <small>Formats supportés: .log, .err, .error, .out, .txt</small>
                        </div>
                      </div>
                      
                      <div *ngIf="errorFile" class="file-info">
                        <mat-icon>description</mat-icon>
                        <span>{{ errorFile.name }}</span>
                        <button mat-icon-button (click)="clearFile('error')" type="button">
                          <mat-icon>close</mat-icon>
                        </button>
                      </div>
                    </div>
                  </mat-tab>
                  
                  <mat-tab label="Coller les Erreurs">
                    <div class="tab-content">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Contenu du fichier d'erreur</mat-label>
                        <textarea matInput 
                                  formControlName="errorContent"
                                  rows="10"
                                  placeholder="Collez vos messages d'erreur ici..."></textarea>
                      </mat-form-field>
                      
                      <mat-form-field appearance="outline">
                        <mat-label>Nom du fichier</mat-label>
                        <input matInput 
                               formControlName="errorFileName"
                               placeholder="ex: error.log">
                      </mat-form-field>
                    </div>
                  </mat-tab>
                </mat-tab-group>
              </div>
            </div>

            <!-- Bouton d'analyse -->
            <div class="action-section">
              <button mat-raised-button 
                      color="primary" 
                      type="submit"
                      [disabled]="!canAnalyze() || loadingState.isLoading"
                      class="analyze-button">
                <mat-spinner *ngIf="loadingState.isLoading" diameter="20"></mat-spinner>
                <mat-icon *ngIf="!loadingState.isLoading">psychology</mat-icon>
                {{ loadingState.isLoading ? 'Analyse en cours...' : 'Analyser les Erreurs' }}
              </button>
              
              <p class="help-text" *ngIf="!canAnalyze()">
                <mat-icon>info</mat-icon>
                Veuillez fournir les deux fichiers (programme et erreur) pour commencer l'analyse
              </p>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

      <!-- Résultats de l'analyse -->
      <mat-card *ngIf="analysisResult" class="results-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>analytics</mat-icon>
            Résultats de l'Analyse
          </mat-card-title>
          <mat-card-subtitle>
            Analyse terminée le {{ analysisResult.timestamp | date:'dd/MM/yyyy à HH:mm' }}
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <!-- Résumé -->
          <div class="summary-section">
            <h3>
              <mat-icon>summarize</mat-icon>
              Résumé de l'Analyse
            </h3>
            <p class="summary-text">{{ analysisResult.summary }}</p>
          </div>

          <mat-divider></mat-divider>

          <!-- Erreurs détectées -->
          <div class="errors-section">
            <h3>
              <mat-icon>error_outline</mat-icon>
              Erreurs Détectées ({{ analysisResult.errors.length }})
            </h3>

            <mat-accordion *ngIf="analysisResult.errors.length > 0">
              <mat-expansion-panel *ngFor="let error of analysisResult.errors; let i = index"
                                   [expanded]="i === 0">
                <mat-expansion-panel-header>
                  <mat-panel-title>
                    <mat-chip [color]="getSeverityColor(error.severity)">
                      {{ error.severity }}
                    </mat-chip>
                    <span class="error-title">{{ error.errorType }}</span>
                  </mat-panel-title>
                  <mat-panel-description>
                    {{ error.location }}
                    <mat-icon *ngIf="error.lineNumber" matTooltip="Ligne {{ error.lineNumber }}">
                      my_location
                    </mat-icon>
                  </mat-panel-description>
                </mat-expansion-panel-header>

                <div class="error-details">
                  <!-- Description -->
                  <div class="detail-section">
                    <h4>
                      <mat-icon>description</mat-icon>
                      Description
                    </h4>
                    <p>{{ error.description }}</p>
                  </div>

                  <!-- Contexte de code -->
                  <div *ngIf="error.codeContext" class="detail-section">
                    <h4>
                      <mat-icon>code</mat-icon>
                      Contexte de Code
                      <span *ngIf="error.lineNumber" class="line-info">(Ligne {{ error.lineNumber }})</span>
                    </h4>
                    <div class="code-context">
                      <div *ngFor="let line of error.codeContext.contextLines"
                           class="code-line"
                           [class.target-line]="line.isTarget">
                        <span class="line-number">{{ line.number }}</span>
                        <span class="line-content">{{ line.content }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- Causes possibles -->
                  <div class="detail-section">
                    <h4>
                      <mat-icon>help_outline</mat-icon>
                      Causes Possibles
                    </h4>
                    <ul class="causes-list">
                      <li *ngFor="let cause of error.possibleCauses">{{ cause }}</li>
                    </ul>
                  </div>

                  <!-- Solutions -->
                  <div class="detail-section">
                    <h4>
                      <mat-icon>build</mat-icon>
                      Solutions Recommandées
                    </h4>
                    <ol class="solutions-list">
                      <li *ngFor="let solution of error.solutions">{{ solution }}</li>
                    </ol>
                  </div>
                </div>
              </mat-expansion-panel>
            </mat-accordion>

            <div *ngIf="analysisResult.errors.length === 0" class="no-errors">
              <mat-icon>check_circle</mat-icon>
              <p>Aucune erreur détectée dans votre code !</p>
            </div>
          </div>

          <mat-divider></mat-divider>

          <!-- Recommandations -->
          <div class="recommendations-section" *ngIf="analysisResult.recommendations.length > 0">
            <h3>
              <mat-icon>lightbulb</mat-icon>
              Recommandations Générales
            </h3>
            <ul class="recommendations-list">
              <li *ngFor="let recommendation of analysisResult.recommendations">
                <mat-icon>arrow_right</mat-icon>
                {{ recommendation }}
              </li>
            </ul>
          </div>

          <!-- Actions -->
          <div class="actions-section">
            <button mat-raised-button color="primary" (click)="downloadReport()">
              <mat-icon>download</mat-icon>
              Télécharger le Rapport
            </button>

            <button mat-stroked-button (click)="copyReport()">
              <mat-icon>content_copy</mat-icon>
              Copier le Rapport
            </button>

            <button mat-stroked-button (click)="resetAnalysis()">
              <mat-icon>refresh</mat-icon>
              Nouvelle Analyse
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .error-analysis-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem 1rem;
    }

    .page-header {
      background: linear-gradient(135deg, #ff7514 0%, #ff8c42 100%);
      color: white;
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: 0 4px 20px rgba(255, 117, 20, 0.3);
    }

    .header-content {
      display: flex;
      align-items: center;
      gap: 1.5rem;
    }

    .header-icon {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .header-icon mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
    }

    .header-text h1 {
      margin: 0 0 0.5rem 0;
      font-size: 2.5rem;
      font-weight: 700;
    }

    .header-text p {
      margin: 0;
      opacity: 0.9;
      font-size: 1.1rem;
    }

    .upload-card, .results-card {
      margin-bottom: 2rem;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .upload-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    .upload-section h3 {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #002857;
      margin-bottom: 1rem;
    }

    .tab-content {
      padding: 1rem 0;
    }

    .file-upload-area {
      border: 2px dashed #ddd;
      border-radius: 12px;
      padding: 2rem;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      background: #fafafa;
    }

    .file-upload-area:hover,
    .file-upload-area.drag-over {
      border-color: #ff7514;
      background: rgba(255, 117, 20, 0.05);
    }

    .upload-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.5rem;
    }

    .upload-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      color: #ff7514;
      margin-bottom: 0.5rem;
    }

    .file-info {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      background: #e8f5e8;
      padding: 0.75rem;
      border-radius: 8px;
      margin-top: 1rem;
    }

    .full-width {
      width: 100%;
    }

    .action-section {
      text-align: center;
      padding: 1rem 0;
    }

    .analyze-button {
      padding: 1rem 2rem;
      font-size: 1.1rem;
      border-radius: 12px;
      min-width: 200px;
    }

    .help-text {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      color: #666;
      margin-top: 1rem;
    }

    .summary-section {
      margin-bottom: 2rem;
    }

    .summary-section h3 {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #002857;
      margin-bottom: 1rem;
    }

    .summary-text {
      background: #f0f8ff;
      padding: 1rem;
      border-radius: 8px;
      border-left: 4px solid #002857;
      font-size: 1.1rem;
      line-height: 1.6;
    }

    .errors-section h3,
    .recommendations-section h3 {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #002857;
      margin: 1.5rem 0 1rem 0;
    }

    .error-title {
      margin-left: 1rem;
      font-weight: 500;
    }

    .error-details {
      padding: 1rem 0;
    }

    .detail-section {
      margin-bottom: 1.5rem;
    }

    .detail-section h4 {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #333;
      margin-bottom: 0.5rem;
      font-size: 1rem;
    }

    .line-info {
      font-size: 0.8rem;
      color: #666;
      font-weight: normal;
    }

    .code-context {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      overflow-x: auto;
      font-family: 'Courier New', monospace;
      font-size: 0.9rem;
    }

    .code-line {
      display: flex;
      padding: 0.25rem 0.5rem;
      border-bottom: 1px solid #eee;
    }

    .code-line.target-line {
      background: #fff3cd;
      border-left: 4px solid #ff7514;
    }

    .line-number {
      width: 3rem;
      color: #666;
      text-align: right;
      margin-right: 1rem;
      user-select: none;
    }

    .line-content {
      flex: 1;
      white-space: pre;
    }

    .causes-list,
    .solutions-list,
    .recommendations-list {
      margin: 0.5rem 0;
      padding-left: 1.5rem;
    }

    .causes-list li,
    .solutions-list li {
      margin-bottom: 0.5rem;
      line-height: 1.5;
    }

    .recommendations-list {
      list-style: none;
      padding-left: 0;
    }

    .recommendations-list li {
      display: flex;
      align-items: flex-start;
      gap: 0.5rem;
      margin-bottom: 0.75rem;
      line-height: 1.5;
    }

    .no-errors {
      text-align: center;
      padding: 2rem;
      color: #4caf50;
    }

    .no-errors mat-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      margin-bottom: 1rem;
    }

    .actions-section {
      display: flex;
      gap: 1rem;
      justify-content: center;
      margin-top: 2rem;
      padding-top: 1rem;
      border-top: 1px solid #eee;
    }

    /* Responsive */
    @media (max-width: 768px) {
      .upload-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .header-content {
        flex-direction: column;
        text-align: center;
      }

      .header-text h1 {
        font-size: 2rem;
      }

      .actions-section {
        flex-direction: column;
        align-items: center;
      }
    }
  `]
})
export class ErrorAnalysisComponent implements OnInit {
  analysisForm: FormGroup;
  programFile: ProgramFile | null = null;
  errorFile: ProgramFile | null = null;
  analysisResult: ErrorAnalysisResult | null = null;
  loadingState: LoadingState = { isLoading: false };
  isDragOverProgram = false;
  isDragOverError = false;

  constructor(
    private fb: FormBuilder,
    private apiService: ApiService,
    private fileService: FileService,
    private notificationService: NotificationService
  ) {
    this.analysisForm = this.fb.group({
      programContent: [''],
      programFileName: [''],
      errorContent: [''],
      errorFileName: ['']
    });
  }

  ngOnInit(): void {
    // Initialisation si nécessaire
  }

  // Gestion du drag & drop
  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOverProgram = false;
    this.isDragOverError = false;
  }

  onDrop(event: DragEvent, type: 'program' | 'error'): void {
    event.preventDefault();
    event.stopPropagation();

    if (type === 'program') {
      this.isDragOverProgram = false;
    } else {
      this.isDragOverError = false;
    }

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      this.processFile(files[0], type);
    }
  }

  // Sélection de fichier
  onFileSelected(event: any, type: 'program' | 'error'): void {
    const file = event.target.files[0];
    if (file) {
      this.processFile(file, type);
    }
  }

  // Traitement du fichier
  private processFile(file: File, type: 'program' | 'error'): void {
    this.fileService.fileToProgramFile(file, type).subscribe({
      next: (programFile) => {
        if (type === 'program') {
          this.programFile = programFile;
          this.analysisForm.patchValue({
            programContent: programFile.content,
            programFileName: programFile.name
          });
        } else {
          this.errorFile = programFile;
          this.analysisForm.patchValue({
            errorContent: programFile.content,
            errorFileName: programFile.name
          });
        }
        this.notificationService.showFileSuccess('Chargement', file.name);
      },
      error: (error) => {
        this.notificationService.showFileError('Chargement', file.name, error.message);
      }
    });
  }

  // Effacer un fichier
  clearFile(type: 'program' | 'error'): void {
    if (type === 'program') {
      this.programFile = null;
      this.analysisForm.patchValue({
        programContent: '',
        programFileName: ''
      });
    } else {
      this.errorFile = null;
      this.analysisForm.patchValue({
        errorContent: '',
        errorFileName: ''
      });
    }
  }

  // Vérifier si l'analyse peut être lancée
  canAnalyze(): boolean {
    const formValues = this.analysisForm.value;

    // Vérifier si on a les fichiers uploadés OU le contenu collé
    const hasProgramData = this.programFile ||
      (formValues.programContent && formValues.programContent.trim() &&
       formValues.programFileName && formValues.programFileName.trim());

    const hasErrorData = this.errorFile ||
      (formValues.errorContent && formValues.errorContent.trim() &&
       formValues.errorFileName && formValues.errorFileName.trim());

    return hasProgramData && hasErrorData;
  }

  // Lancer l'analyse
  onAnalyze(): void {
    if (!this.canAnalyze()) {
      this.notificationService.showWarning('Veuillez fournir les deux fichiers pour l\'analyse');
      return;
    }

    this.loadingState = { isLoading: true, message: 'Analyse en cours...' };

    // Préparer les fichiers
    const formValues = this.analysisForm.value;

    const programFile: ProgramFile = this.programFile || {
      name: formValues.programFileName,
      content: formValues.programContent,
      type: 'program'
    };

    const errorFile: ProgramFile = this.errorFile || {
      name: formValues.errorFileName,
      content: formValues.errorContent,
      type: 'error'
    };

    // Appel API
    this.apiService.analyzeErrors(programFile, errorFile).subscribe({
      next: (result) => {
        this.analysisResult = result;
        this.loadingState = { isLoading: false };
        this.notificationService.showApiSuccess('Analyse d\'erreurs');

        // Scroll vers les résultats
        setTimeout(() => {
          const resultsElement = document.querySelector('.results-card');
          if (resultsElement) {
            resultsElement.scrollIntoView({ behavior: 'smooth' });
          }
        }, 100);
      },
      error: (error) => {
        this.loadingState = { isLoading: false };
        this.notificationService.showApiError('Analyse d\'erreurs', error.message);
      }
    });
  }

  // Obtenir la couleur selon la sévérité
  getSeverityColor(severity: string): 'primary' | 'accent' | 'warn' {
    switch (severity) {
      case 'CRITICAL':
      case 'HIGH':
        return 'warn';
      case 'MEDIUM':
        return 'accent';
      case 'LOW':
      default:
        return 'primary';
    }
  }

  // Télécharger le rapport
  downloadReport(): void {
    if (!this.analysisResult) return;

    const report = this.generateReportText();
    const fileName = `analyse-erreurs-${new Date().toISOString().split('T')[0]}.txt`;

    this.fileService.downloadFile(report, fileName, 'text/plain');
    this.notificationService.showSuccess('Rapport téléchargé avec succès');
  }

  // Copier le rapport
  async copyReport(): Promise<void> {
    if (!this.analysisResult) return;

    try {
      const report = this.generateReportText();
      await this.fileService.copyToClipboard(report);
      this.notificationService.showSuccess('Rapport copié dans le presse-papiers');
    } catch (error) {
      this.notificationService.showError('Erreur lors de la copie du rapport');
    }
  }

  // Réinitialiser l'analyse
  resetAnalysis(): void {
    this.analysisResult = null;
    this.programFile = null;
    this.errorFile = null;
    this.analysisForm.reset();
    this.loadingState = { isLoading: false };

    // Scroll vers le haut
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  // Générer le texte du rapport
  private generateReportText(): string {
    if (!this.analysisResult) return '';

    let report = '=== RAPPORT D\'ANALYSE D\'ERREURS ===\n\n';
    report += `Date: ${new Date(this.analysisResult.timestamp).toLocaleString('fr-FR')}\n\n`;

    report += '=== RÉSUMÉ ===\n';
    report += `${this.analysisResult.summary}\n\n`;

    report += `=== ERREURS DÉTECTÉES (${this.analysisResult.errors.length}) ===\n\n`;

    this.analysisResult.errors.forEach((error, index) => {
      report += `${index + 1}. ${error.errorType} [${error.severity}]\n`;
      report += `   Emplacement: ${error.location}\n`;
      if (error.lineNumber) {
        report += `   Ligne: ${error.lineNumber}\n`;
      }
      report += `   Description: ${error.description}\n`;

      report += '   Causes possibles:\n';
      error.possibleCauses.forEach(cause => {
        report += `   - ${cause}\n`;
      });

      report += '   Solutions recommandées:\n';
      error.solutions.forEach((solution, i) => {
        report += `   ${i + 1}. ${solution}\n`;
      });

      report += '\n';
    });

    if (this.analysisResult.recommendations.length > 0) {
      report += '=== RECOMMANDATIONS GÉNÉRALES ===\n';
      this.analysisResult.recommendations.forEach(rec => {
        report += `- ${rec}\n`;
      });
    }

    return report;
  }
}
