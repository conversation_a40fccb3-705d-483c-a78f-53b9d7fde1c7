/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["sg", [["ND", "LK"], u, u], u, [["K", "S", "T", "S", "K", "P", "Y"], ["Bk1", "Bk2", "Bk3", "Bk4", "Bk5", "Lâp", "<PERSON>â<PERSON>"], ["Bikua-ô<PERSON>", "Bïkua-û<PERSON>", "Bïkua-ptâ", "Bïkua-usïö", "Bïkua-okü", "Lâpôsö", "Lâyenga"], ["Bk1", "Bk2", "Bk3", "Bk4", "Bk5", "<PERSON>â<PERSON>", "<PERSON><PERSON><PERSON>"]], u, [["N", "F", "M", "N", "B", "F", "L", "K", "M", "N", "N", "K"], ["Nye", "Ful", "Mbä", "Ngu", "Bêl", "Fön", "Len", "Kük", "Mvu", "Ngb", "Nab", "Kak"], ["Nyenye", "Fulundïgi", "Mbängü", "Ngubùe", "Bêläwü", "Föndo", "Lengua", "Kükürü", "Mvuka", "Ngberere", "Nabändüru", "Kakauka"]], u, [["KnK", "NpK"], u, ["Kôzo na Krîstu", "Na pekô tî Krîstu"]], 1, [6, 0], ["d/M/y", "d MMM, y", "d MMMM y", "EEEE d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00;¤-#,##0.00", "#E0"], "XAF", "FCFA", "farânga CFA (BEAC)", { "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=sg.js.map