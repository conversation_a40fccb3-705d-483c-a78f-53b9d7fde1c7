"""
Utilitaires pour parser les fichiers d'erreur
Équivalent de utils.ts pour le parsing d'erreurs
"""

import re
from typing import List
from models.types import ParsedError


def parse_error_file(content: str) -> List[ParsedError]:
    """
    Parse un fichier d'erreur et extrait les informations structurées
    
    Args:
        content: Contenu du fichier d'erreur
        
    Returns:
        Liste des erreurs parsées
    """
    lines = [line.strip() for line in content.split('\n') if line.strip()]
    parsed_errors = []
    
    for line in lines:
        # Pattern pour les logs avec timestamp
        match = re.match(r'^(\d{2}\.\d{2}\.\d{4} \d{2}:\d{2}:\d{2})\s+(\w+)\s+(.+)$', line)
        
        if match:
            timestamp, level, message = match.groups()
            
            # Extraire la tâche si présente
            task_match = re.search(r'task \[([^\]]+)\]', message)
            task = task_match.group(1) if task_match else None
            
            # Essayer d'extraire le numéro de ligne du message d'erreur
            line_number = extract_line_number_from_message(message)
            
            parsed_error = ParsedError(
                timestamp=timestamp,
                level=level,
                message=message,
                task=task,
                line_number=line_number
            )
            parsed_errors.append(parsed_error)
        else:
            # Si le format ne correspond pas, créer une entrée générique
            line_number = extract_line_number_from_message(line)
            parsed_error = ParsedError(
                timestamp="",
                level="UNKNOWN",
                message=line,
                task=None,
                line_number=line_number
            )
            parsed_errors.append(parsed_error)
    
    return parsed_errors


def extract_line_number_from_message(message: str) -> int:
    """
    Extrait le numéro de ligne d'un message d'erreur
    
    Args:
        message: Message d'erreur
        
    Returns:
        Numéro de ligne ou None si non trouvé
    """
    # Différents patterns pour extraire les numéros de ligne
    patterns = [
        r'line[:\s]+(\d+)',
        r'ligne[:\s]+(\d+)',
        r'\[.*line[:\s]+(\d+)',
        r'caofors\.ec line: (\d+)',
        r'at line (\d+)',
        r'ligne (\d+)',
        r'line (\d+)',
        r':(\d+):',  # Format file:line:column
        r'\((\d+)\)',  # Numéro entre parenthèses
    ]
    
    for pattern in patterns:
        match = re.search(pattern, message, re.IGNORECASE)
        if match:
            try:
                return int(match.group(1))
            except (ValueError, IndexError):
                continue
    
    return None


def format_error_for_display(error: ParsedError) -> str:
    """
    Formate une erreur pour l'affichage
    
    Args:
        error: Erreur parsée
        
    Returns:
        Chaîne formatée pour l'affichage
    """
    parts = []
    
    if error.timestamp:
        parts.append(f"[{error.timestamp}]")
    
    if error.level:
        parts.append(f"{error.level}:")
    
    if error.task:
        parts.append(f"Task [{error.task}]")
    
    parts.append(error.message)
    
    if error.line_number:
        parts.append(f"(Ligne {error.line_number})")
    
    return " ".join(parts)


def group_errors_by_type(errors: List[ParsedError]) -> dict:
    """
    Groupe les erreurs par type/niveau
    
    Args:
        errors: Liste des erreurs parsées
        
    Returns:
        Dictionnaire groupé par niveau d'erreur
    """
    grouped = {}
    
    for error in errors:
        level = error.level or "UNKNOWN"
        if level not in grouped:
            grouped[level] = []
        grouped[level].append(error)
    
    return grouped
