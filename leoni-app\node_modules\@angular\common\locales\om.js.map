{"version": 3, "file": "om.js", "sourceRoot": "", "sources": ["om.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,aAAa,EAAC,aAAa,EAAC,MAAM,EAAC,QAAQ,EAAC,YAAY,EAAC,YAAY,EAAC,SAAS,EAAC,UAAU,EAAC,cAAc,EAAC,SAAS,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,KAAK,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,UAAU,EAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,IAAI,EAAC,mBAAmB,EAAC,EAAC,KAAK,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"om\",[[\"WD\",\"WB\"],u,u],u,[[\"S\",\"M\",\"T\",\"W\",\"T\",\"F\",\"S\"],[\"Dil\",\"Wix\",\"Qib\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\"],[\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON>oo<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\"],[\"Dil\",\"Wix\",\"Qib\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\"]],u,[[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"A\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"O\",\"N\",\"D\"],[\"Ama\",\"<PERSON><PERSON>\",\"<PERSON>\",\"Elb\",\"<PERSON>\",\"Wax\",\"<PERSON><PERSON>\",\"<PERSON>g\",\"<PERSON>l\",\"Onk\",\"<PERSON>\",\"Mud\"],[\"Amajjii\",\"Guraandhala\",\"<PERSON>ooteessa\",\"Elba\",\"Caamsa\",\"Waxabajjii\",\"<PERSON>ooleessa\",\"Hagayya\",\"Fuulbana\",\"<PERSON><PERSON>loleessa\",\"<PERSON>aasa\",\"Muddee\"]],u,[[\"BCE\",\"CE\"],u,[\"Dheengadda Jeesu\",\"CE\"]],0,[6,0],[\"dd/MM/yy\",\"dd-MMM-y\",\"dd MMMM y\",\"EEEE, MMMM d, y\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"ETB\",\"Br\",\"Itoophiyaa Birrii\",{\"ETB\":[\"Br\"],\"JPY\":[\"JP¥\",\"¥\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}