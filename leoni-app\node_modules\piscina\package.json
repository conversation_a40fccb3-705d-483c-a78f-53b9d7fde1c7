{"name": "piscina", "version": "5.1.2", "description": "A fast, efficient Node.js Worker Thread Pool implementation", "main": "./dist/main.js", "types": "./dist/index.d.ts", "exports": {"types": "./dist/index.d.ts", "import": "./dist/esm-wrapper.mjs", "require": "./dist/main.js"}, "engines": {"node": ">=20.x"}, "scripts": {"build": "tsc && gen-esm-wrapper . dist/esm-wrapper.mjs", "lint": "eslint", "test": "node scripts/run-tests.js --pattern='test/**/*test.ts'", "test:ci": "npm run lint && npm run build && npm run test:coverage", "test:coverage": "node scripts/run-tests.js --coverage --pattern='test/**/*test.ts'", "prepack": "npm run build", "benchmark": "npm run benchmark:queue && npm run benchmark:piscina", "benchmark:piscina": "npm run benchmark:default && npm run benchmark:queue:fixed && npm run benchmark:default:comparison", "benchmark:default": "node benchmark/simple-benchmark.js", "benchmark:default:async": "node benchmark/simple-benchmark.js", "benchmark:default:comparison": "node benchmark/piscina-queue-comparison.js", "benchmark:queue": "npm run benchmark:queue:comparison", "benchmark:queue:fixed": "node benchmark/simple-benchmark-fixed-queue.js", "benchmark:queue:comparison": "node benchmark/queue-comparison.js"}, "repository": {"type": "git", "url": "git+https://github.com/piscinajs/piscina.git"}, "keywords": ["fast", "worker threads", "thread pool", "wade wilson"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "MIT", "devDependencies": {"@types/node": "^22.4.1", "abort-controller": "^3.0.0", "c8": "^10.1.2", "concat-stream": "^2.0.0", "eslint": "^9.16.0", "gen-esm-wrapper": "^1.1.1", "glob": "^11.0.2", "neostandard": "^0.12.0", "tinybench": "^4.0.1", "tsx": "^4.19.4", "typescript": "5.8.3"}, "optionalDependencies": {"@napi-rs/nice": "^1.0.1"}, "bugs": {"url": "https://github.com/piscinajs/piscina/issues"}, "homepage": "https://github.com/piscinajs/piscina#readme", "directories": {"example": "examples", "test": "test"}}