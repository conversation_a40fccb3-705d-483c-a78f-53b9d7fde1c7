{"version": 3, "file": "chr.js", "sourceRoot": "", "sources": ["chr.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,MAAM,CAAC,EAAC,CAAC,KAAK,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,CAAC,KAAK,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,KAAK,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,SAAS,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,OAAO,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,iBAAiB,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,aAAa,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,QAAQ,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"chr\",[[\"Ꮜ\",\"Ꮢ\"],[\"ᏌᎾᎴ\",\"ᏒᎯᏱᎢ\"],[\"ᏌᎾᎴ\",\"ᏒᎯᏱᎢᏗᏢ\"]],[[\"ᏌᎾᎴ\",\"ᏒᎯᏱᎢ\"],u,[\"ᏌᎾᎴ\",\"ᏒᎯᏱᎢᏗᏢ\"]],[[\"Ꮖ\",\"Ꮙ\",\"Ꮤ\",\"Ꮶ\",\"Ꮕ\",\"Ꮷ\",\"Ꭴ\"],[\"ᏆᏍᎬ\",\"ᏉᏅᎯ\",\"ᏔᎵᏁ\",\"ᏦᎢᏁ\",\"ᏅᎩᏁ\",\"ᏧᎾᎩ\",\"ᏈᏕᎾ\"],[\"ᎤᎾᏙᏓᏆᏍᎬ\",\"ᎤᎾᏙᏓᏉᏅᎯ\",\"ᏔᎵᏁᎢᎦ\",\"ᏦᎢᏁᎢᎦ\",\"ᏅᎩᏁᎢᎦ\",\"ᏧᎾᎩᎶᏍᏗ\",\"ᎤᎾᏙᏓᏈᏕᎾ\"],[\"ᏍᎬ\",\"ᏅᎯ\",\"ᏔᎵ\",\"ᏦᎢ\",\"ᏅᎩ\",\"ᏧᎾ\",\"ᏕᎾ\"]],u,[[\"Ꭴ\",\"Ꭷ\",\"Ꭰ\",\"Ꭷ\",\"Ꭰ\",\"Ꮥ\",\"Ꭻ\",\"Ꭶ\",\"Ꮪ\",\"Ꮪ\",\"Ꮕ\",\"Ꭵ\"],[\"ᎤᏃ\",\"ᎧᎦ\",\"ᎠᏅ\",\"ᎧᏬ\",\"ᎠᏂ\",\"ᏕᎭ\",\"ᎫᏰ\",\"ᎦᎶ\",\"ᏚᎵ\",\"ᏚᏂ\",\"ᏅᏓ\",\"ᎥᏍ\"],[\"ᎤᏃᎸᏔᏅ\",\"ᎧᎦᎵ\",\"ᎠᏅᏱ\",\"ᎧᏬᏂ\",\"ᎠᏂᏍᎬᏘ\",\"ᏕᎭᎷᏱ\",\"ᎫᏰᏉᏂ\",\"ᎦᎶᏂ\",\"ᏚᎵᏍᏗ\",\"ᏚᏂᏅᏗ\",\"ᏅᏓᏕᏆ\",\"ᎥᏍᎩᏱ\"]],u,[[\"BC\",\"AD\"],u,[\"ᏧᏓᎷᎸ ᎤᎷᎯᏍᏗ ᎦᎶᏁᏛ\",\"ᎠᏃ ᏙᎻᏂ\"]],0,[6,0],[\"M/d/yy\",\"MMM d, y\",\"MMMM d, y\",\"EEEE, MMMM d, y\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1}, {0}\",u,\"{1} ᎤᎾᎢ {0}\",u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"USD\",\"$\",\"US ᎠᏕᎳ\",{\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"],\"PHP\":[u,\"₱\"]},\"ltr\", plural];\n"]}