{"version": 3, "file": "bg.js", "sourceRoot": "", "sources": ["bg.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,QAAQ,EAAC,YAAY,EAAC,SAAS,EAAC,OAAO,EAAC,WAAW,EAAC,OAAO,EAAC,QAAQ,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,QAAQ,EAAC,WAAW,EAAC,UAAU,EAAC,SAAS,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,cAAc,EAAC,aAAa,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,cAAc,EAAC,aAAa,EAAC,eAAe,EAAC,qBAAqB,CAAC,EAAC,CAAC,WAAW,EAAC,cAAc,EAAC,gBAAgB,EAAC,mBAAmB,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,QAAQ,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,eAAe,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,MAAM,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"bg\",[[\"am\",\"pm\"],u,[\"пр.об.\",\"сл.об.\"]],[[\"am\",\"pm\"],u,u],[[\"н\",\"п\",\"в\",\"с\",\"ч\",\"п\",\"с\"],[\"нд\",\"пн\",\"вт\",\"ср\",\"чт\",\"пт\",\"сб\"],[\"неделя\",\"понеделник\",\"вторник\",\"сряда\",\"четвъртък\",\"петък\",\"събота\"],[\"нд\",\"пн\",\"вт\",\"ср\",\"чт\",\"пт\",\"сб\"]],u,[[\"я\",\"ф\",\"м\",\"а\",\"м\",\"ю\",\"ю\",\"а\",\"с\",\"о\",\"н\",\"д\"],[\"яну\",\"фев\",\"март\",\"апр\",\"май\",\"юни\",\"юли\",\"авг\",\"сеп\",\"окт\",\"ное\",\"дек\"],[\"януари\",\"февруари\",\"март\",\"април\",\"май\",\"юни\",\"юли\",\"август\",\"септември\",\"октомври\",\"ноември\",\"декември\"]],u,[[\"пр.Хр.\",\"сл.Хр.\"],u,[\"преди Христа\",\"след Христа\"]],1,[6,0],[\"d.MM.yy 'г'.\",\"d.MM.y 'г'.\",\"d MMMM y 'г'.\",\"EEEE, d MMMM y 'г'.\"],[\"H:mm 'ч'.\",\"H:mm:ss 'ч'.\",\"H:mm:ss 'ч'. z\",\"H:mm:ss 'ч'. zzzz\"],[\"{1}, {0}\",u,u,u],[\",\",\" \",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"0.00 ¤\",\"#E0\"],\"BGN\",\"лв.\",\"Български лев\",{\"AFN\":[u,\"Af\"],\"AMD\":[],\"ARS\":[],\"AUD\":[],\"AZN\":[],\"BBD\":[],\"BDT\":[],\"BGN\":[\"лв.\"],\"BMD\":[],\"BND\":[],\"BRL\":[],\"BSD\":[],\"BZD\":[],\"CAD\":[],\"CLP\":[],\"CNY\":[],\"COP\":[],\"CRC\":[],\"CUP\":[],\"DOP\":[],\"FJD\":[],\"FKP\":[],\"GBP\":[u,\"£\"],\"GHS\":[],\"GIP\":[],\"GYD\":[],\"HKD\":[],\"ILS\":[],\"INR\":[],\"JMD\":[],\"JPY\":[u,\"¥\"],\"KHR\":[],\"KRW\":[],\"KYD\":[],\"KZT\":[],\"LAK\":[],\"LRD\":[],\"MNT\":[],\"MXN\":[],\"NAD\":[],\"NGN\":[],\"NZD\":[],\"PHP\":[],\"PYG\":[],\"RON\":[],\"SBD\":[],\"SGD\":[],\"SRD\":[],\"SSP\":[],\"TRY\":[],\"TTD\":[],\"TWD\":[],\"UAH\":[],\"USD\":[\"щ.д.\",\"$\"],\"UYU\":[],\"VND\":[],\"XCD\":[u,\"$\"]},\"ltr\", plural];\n"]}