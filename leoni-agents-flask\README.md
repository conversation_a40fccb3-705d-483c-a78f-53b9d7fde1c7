# Leoni Agents - Backend Flask

Backend Flask pour l'application Leoni Agents avec agents IA spécialisés.

## 🚀 Fonctionnalités

- **Agent d'Analyse d'Erreurs** : Analyse automatique des fichiers de programme et d'erreur
- **Agent de Génération SQL** : Génération de scripts SQL à partir de spécifications
- **API REST** : Endpoints bien structurés avec validation
- **Intégration OpenAI** : Utilisation de GPT-4 pour l'intelligence artificielle
- **CORS configuré** : Compatible avec le frontend Angular

## 📋 Prérequis

- Python 3.8+
- Clé API OpenAI

## 🛠️ Installation

1. **<PERSON><PERSON><PERSON> le projet**
```bash
cd leoni-agents-flask
```

2. **Créer un environnement virtuel**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# ou
venv\Scripts\activate  # Windows
```

3. **Installer les dépendances**
```bash
pip install -r requirements.txt
```

4. **Configuration**
```bash
cp .env.example .env
# Éditer .env avec votre clé OpenAI
```

5. **Lancer l'application**
```bash
python app.py
```

L'API sera disponible sur `http://localhost:5000`

## 📡 Endpoints API

### Analyse d'Erreurs

- **POST** `/api/error-analysis`
  - Analyse les fichiers programme et erreur
  - Body: `{"programFile": {...}, "errorFile": {...}}`

- **GET** `/api/error-analysis`
  - Informations sur l'agent d'analyse

### Génération SQL

- **POST** `/api/sql-generation`
  - Génère un script SQL
  - Body: `{"specification": "...", "database_type": "mysql", "include_comments": true}`

- **GET** `/api/sql-generation`
  - Informations sur l'agent SQL

### Santé

- **GET** `/`
  - Point de santé de l'API

## 🏗️ Structure du Projet

```
leoni-agents-flask/
├── app.py                      # Application Flask principale
├── models/
│   └── types.py               # Modèles de données
├── services/
│   ├── error_analysis_service.py
│   └── sql_generation_service.py
├── utils/
│   ├── error_parser.py        # Parsing des erreurs
│   ├── text_utils.py          # Utilitaires texte
│   ├── validators.py          # Validation des requêtes
│   └── response_formatter.py  # Formatage des réponses
├── config/
│   └── openai_config.py       # Configuration OpenAI
├── requirements.txt
├── .env.example
└── README.md
```

## 🧪 Tests

```bash
pytest
```

## 🚀 Déploiement

### Avec Gunicorn
```bash
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### Avec Docker
```bash
docker build -t leoni-agents-flask .
docker run -p 5000:5000 leoni-agents-flask
```

## 🔧 Configuration

Variables d'environnement importantes :

- `OPENAI_API_KEY` : Clé API OpenAI (obligatoire)
- `FLASK_ENV` : Environnement Flask (development/production)
- `CORS_ORIGINS` : Origines autorisées pour CORS
- `MAX_CONTENT_LENGTH` : Taille maximale des fichiers

## 📝 Exemples d'utilisation

### Analyse d'erreurs
```bash
curl -X POST http://localhost:5000/api/error-analysis \
  -H "Content-Type: application/json" \
  -d '{
    "programFile": {
      "name": "program.c",
      "content": "int main() { return 0; }"
    },
    "errorFile": {
      "name": "error.log",
      "content": "Error at line 5: syntax error"
    }
  }'
```

### Génération SQL
```bash
curl -X POST http://localhost:5000/api/sql-generation \
  -H "Content-Type: application/json" \
  -d '{
    "specification": "Créer une table utilisateurs avec nom, email et date de création",
    "database_type": "mysql",
    "include_comments": true
  }'
```
