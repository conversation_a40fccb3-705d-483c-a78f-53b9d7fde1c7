{"version": 3, "file": "ce.js", "sourceRoot": "", "sources": ["ce.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,KAAK,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,EAAC,MAAM,EAAC,UAAU,EAAC,MAAM,CAAC,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,EAAC,MAAM,EAAC,UAAU,EAAC,MAAM,CAAC,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,MAAM,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,UAAU,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,aAAa,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,gCAAgC,EAAC,oCAAoC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,aAAa,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,aAAa,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"ce\",[[\"AM\",\"PM\"],u,u],u,[[\"кӀи\",\"ор\",\"ши\",\"кха\",\"еа\",\"пӀе\",\"шуо\"],u,[\"кӀира\",\"оршот\",\"шинара\",\"кхаара\",\"еара\",\"пӀераска\",\"шуот\"],[\"кӀи\",\"ор\",\"ши\",\"кха\",\"еа\",\"пӀе\",\"шуо\"]],[[\"кӀ\",\"о\",\"ш\",\"кх\",\"е\",\"пӀ\",\"ш\"],[\"кӀи\",\"ор\",\"ши\",\"кха\",\"еа\",\"пӀе\",\"шуо\"],[\"кӀира\",\"оршот\",\"шинара\",\"кхаара\",\"еара\",\"пӀераска\",\"шуот\"],[\"кӀи\",\"ор\",\"ши\",\"кха\",\"еа\",\"пӀе\",\"шуо\"]],[[\"Я\",\"Ф\",\"М\",\"А\",\"М\",\"И\",\"И\",\"А\",\"С\",\"О\",\"Н\",\"Д\"],[\"янв\",\"фев\",\"мар\",\"апр\",\"май\",\"июн\",\"июл\",\"авг\",\"сен\",\"окт\",\"ноя\",\"дек\"],[\"январь\",\"февраль\",\"март\",\"апрель\",\"май\",\"июнь\",\"июль\",\"август\",\"сентябрь\",\"октябрь\",\"ноябрь\",\"декабрь\"]],u,[[\"в. э. тӀ. я\",\"в. э\"],u,[\"Ӏийса пайхамар вина де кхачале\",\"Ӏийса пайхамар вина дийнахь дуьйна\"]],1,[6,0],[\"y-MM-dd\",\"y MMM d\",\"y MMMM d\",\"y MMMM d, EEEE\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"Терхьаш дац\",\":\"],[\"#,##0.###\",\"#,##0 %\",\"#,##0.00 ¤\",\"#E0\"],\"RUB\",\"₽\",\"Российн сом\",{\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"],\"PHP\":[u,\"₱\"],\"RON\":[u,\"лей\"],\"RUB\":[\"₽\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}