{"version": 3, "file": "bg.js", "sourceRoot": "", "sources": ["bg.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,eAAe,CAAC,CAAC,CAAC,SAAS,EAAC,UAAU,EAAC,SAAS,EAAC,UAAU,EAAC,SAAS,EAAC,YAAY,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,OAAO,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nexport default [[[\"полунощ\",\"сутринта\",\"на обяд\",\"следобед\",\"вечерта\",\"през нощта\"],u,u],u,[\"00:00\",[\"04:00\",\"11:00\"],[\"11:00\",\"14:00\"],[\"14:00\",\"18:00\"],[\"18:00\",\"22:00\"],[\"22:00\",\"04:00\"]]];\n"]}