{"version": 3, "file": "nus.js", "sourceRoot": "", "sources": ["nus.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,OAAO,CAAC,EAAC,CAAC,WAAW,EAAC,WAAW,EAAC,WAAW,EAAC,aAAa,EAAC,aAAa,EAAC,cAAc,EAAC,aAAa,CAAC,EAAC,CAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,OAAO,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,KAAK,EAAC,QAAQ,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,eAAe,EAAC,KAAK,EAAC,SAAS,EAAC,MAAM,EAAC,MAAM,EAAC,UAAU,EAAC,aAAa,EAAC,SAAS,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,EAAC,iBAAiB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,oBAAoB,EAAC,eAAe,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,eAAe,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,KAAK,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"nus\",[[\"RW\",\"TŊ\"],u,u],u,[[\"C\",\"J\",\"R\",\"D\",\"Ŋ\",\"D\",\"B\"],[\"<PERSON>ä<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>ɛw\",\"Diɔ̱k\",\"Ŋuaan\",\"<PERSON>hiee<PERSON>\",\"Bäkɛl\"],[\"<PERSON><PERSON><PERSON> kuɔth\",\"<PERSON><PERSON> la̱t\",\"<PERSON>ɛw lätni\",\"Diɔ̱k lätni\",\"Ŋuaan lätni\",\"<PERSON>hiee<PERSON> lätni\",\"Bäkɛl lätni\"],[\"<PERSON>äŋ\",\"Ji<PERSON>\",\"<PERSON>ɛw\",\"Diɔ̱k\",\"Ŋuaan\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"]],u,[[\"T\",\"<PERSON>\",\"D\",\"G\",\"D\",\"K\",\"P\",\"T\",\"T\",\"L\",\"K\",\"T\"],[\"Tiop\",\"Pɛt\",\"Duɔ̱ɔ̱\",\"Guak\",\"Duä\",\"Kor\",\"Pay\",\"Thoo\",\"Tɛɛ\",\"Laa\",\"Kur\",\"Tid\"],[\"Tiop thar pɛt\",\"Pɛt\",\"Duɔ̱ɔ̱ŋ\",\"Guak\",\"Duät\",\"Kornyoot\",\"Pay yie̱tni\",\"Tho̱o̱r\",\"Tɛɛr\",\"Laath\",\"Kur\",\"Tio̱p in di̱i̱t\"]],u,[[\"AY\",\"ƐY\"],u,[\"A ka̱n Yecu ni dap\",\"Ɛ ca Yecu dap\"]],1,[6,0],[\"d/MM/y\",\"d MMM y\",\"d MMMM y\",\"EEEE d MMMM y\"],[\"h:mm a\",\"h:mm:ss a\",\"z h:mm:ss a\",\"zzzz h:mm:ss a\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"SSP\",\"£\",\"SSP\",{\"GBP\":[\"GB£\",\"£\"],\"JPY\":[\"JP¥\",\"¥\"],\"SSP\":[\"£\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}